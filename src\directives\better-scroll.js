import BetterScroll from '@better-scroll/core';

export default {
  name: 'vue-better-scroll',
  install(Vue, defaultOptions = {}) {
    Vue.directive('better-scroll', {
      bind: function (el, binding) {
        // const options = Object.assign({}, defaultOptions, binding.value);
        // const $bs = new BetterScroll(el, options)
        // el.$bs = $bs;
        // console.log(el, binding)
      },
      inserted: function (el, binding) {
        // debugger
        // console.log(el.innerHTML)
        Vue.nextTick(() => {
          const options = Object.assign({
            click: true,
          }, defaultOptions, binding.value);
          const $bs = new BetterScroll(el, options)
          el.$bs = $bs;
        })
      },
      update: function () {},
      componentUpdated: function () {},
      unbind: function (el, binding) {
        if (el.$bs) {
          el.$bs.destroy();
        }
      }
    });    
  }
};
