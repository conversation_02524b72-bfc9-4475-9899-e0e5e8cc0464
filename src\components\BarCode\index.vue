<template>
  <!-- <canvas
  :height="height"
  :width="width"></canvas> -->
  <svg class="barcode"
    :jsbarcode-format="options.format"
    :jsbarcode-value="options.value"
    :jsbarcode-width="options.width"
    :jsbarcode-margin="options.margin"
    :jsbarcode-marginTop="options.marginTop"
    :jsbarcode-height="options.height">
  </svg>
</template>

<script>
import BarCode from 'jsbarcode';

function convertPx(value) {
  if (window.lib) {
    // 设计稿基于375宽度设计，此处基于此尺寸进行转换
    return lib.flexible.rem2px(value / 37.5);
  }
  // debugger;
  return value;
}

export default {
  name: 'BarCode',
  props: {
    value: String,
    options: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  mounted() {
    this.render();
  },
  computed: {
    barcode() {
      return this.value + this.bgColor + this.fgColor;
    }
  },
  watch: {
    barcode() {
      this.render();
    }
  },
  methods: {
    render() {
      const el = this.$el;
      // JsBarcode(el)
      //   // .options({font: "OCR-B"}) // Will affect all barcodes
      //   .CODE128(this.value)
      //   .blank(20) // Create space between the barcodes
      //   .render();
      const options = Object.assign({
        format: 'code128',
        lineColor: '#000',
        width: 1.8,
        height: 74,
        marginTop: 0,
        displayValue: false
      }, this.options);
      const settings = Object.assign({}, options, {
        width: convertPx(options.width),
        height: convertPx(options.height),
      })
      JsBarcode(el, this.value, settings);
    },
  }
};
</script>
