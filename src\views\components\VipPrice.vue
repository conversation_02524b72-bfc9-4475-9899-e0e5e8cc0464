<style lang="scss" scoped>
.vip-price-tag {
  padding: 0 4px 0 4px;
  padding-left: 22px;
  background: #584D42;
  background: url(../images/icon-vip.png) 4px 42% no-repeat #584D42;
  background-size: 16px;
  color: #EAC55A;
  font-size: 14px;
  border-radius: 5px 0 5px 0;
  white-space: nowrap;
  transform: scale(0.8);
  margin-left: -10px;
  font-weight: 700;
}
</style>
<template>
  <span class="vip-price-tag"></span>
</template>
<script>
export default {
  name: 'VipPrice',
  props: {
    vip: {
      type: Boolean,
      default: false,
    },
    banners: {
      type: Array,
      default() {
        return [
          require('@pkg/vehicle-business/assets/images/vip0-banner.jpg'),
          require('@pkg/vehicle-business/assets/images/vip1-banner.jpg'),
        ];
      },
    },
  },
  computed: {
    vipBanner() {
      return this.banners[1];
    },
    unVipBanner() {
      return this.banners[0];
    },
  },
}
</script>
