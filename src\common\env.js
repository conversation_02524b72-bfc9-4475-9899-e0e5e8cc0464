/**
 * 系统常量
 */
export const isInJglh =
  /jglh/i.test(navigator.userAgent) || /jgrm/i.test(navigator.userAgent);
export const isInJGLH = isInJglh;
export const isDevtools = /devtools/i.test(navigator.userAgent);
export const isAndroid = /android/i.test(navigator.userAgent);
export const isInWeixin = /MicroMessenger/i.test(navigator.userAgent); // 是否是微信环境
export const isInUnionPayMP = /com.unionpay/i.test(navigator.userAgent); // 是否是云闪付环境
export const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);
export const isIPhoneX = /iphonex/i.test(navigator.userAgent); // iPhoneX手机的交广领航webview UA信息包含iphonex字符串
export const isProduction = /radio.jgrm.net/i.test(location.host);
export const isInAliApp = navigator.userAgent.indexOf('AliApp') > -1; // 是否是支付宝小程序
export const isInWeApp =
  (window.__wxjs_environment === 'miniprogram' ||
    /miniProgram/.test(navigator.userAgent)) &&
  !isInAliApp; // 是否是微信小程序,支付宝小程序中也有miniprogram
export const isInPC = window.screen.width >= 1080; // 是否是PC环境
export const isInWeixinH5 = isInWeixin && !isInWeApp; // 是否是微信网页环境
export const isInJglhCar = navigator.userAgent.indexOf('Jglh_car') > -1; // 是否是车机
export const isInHarmony = /HarmonyJGRM/i.test(navigator.userAgent); // 是否是鸿蒙

export default {
  android: isAndroid,
  devtools: isDevtools,
  production: isProduction,
  ios: isIOS,
  iphonex: isIPhoneX,
  pc: isInPC,
  jglh: isInJGLH,
  weixin: isInWeixin,
  weapp: isInWeApp,
  alipay: isInAliApp,
  unionpay_mp: isInUnionPayMP,
  weixin_h5: isInWeixinH5,
};
