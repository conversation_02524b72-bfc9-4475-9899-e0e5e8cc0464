import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/account/address/add',
    name: 'address-add',
    component: resolve => {
      import(
        /* webpackChunkName: "address-add" */ '@/views/packages/address/EditAddress.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/account/address/:id/edit',
    name: 'address-edit',
    component: resolve => {
      import(
        /* webpackChunkName: "address-edit" */ '@/views/packages/address/EditAddress.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/address/edit',
    name: 'mall-order-address-edit',
    component: resolve => {
      import(
        /* webpackChunkName: "order-address-edit" */ '@/views/packages/address/EditAddress.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/account/address/:mode/:id?',
    name: 'address-list',
    component: resolve => {
      import(
        /* webpackChunkName: "address-list" */ '@/views/packages/address/AddressList.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
