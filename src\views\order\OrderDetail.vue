<template>
  <container @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="订单详情">
      <x-button  slot="left" type="back"></x-button>
    </x-header>
    <!-- 未禁用系统下拉刷新功能的情况下，此处暂时禁用下拉刷新，目前从审车页面支付成功后webview有下拉刷新功能 -->
    <content-view ref="view" :refresh-action="0" @refresh="refresh" class="order-detail-view" :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <component v-if="order.orderType == OrderType.WASH" :is="'order-detail-wash'" :order="order" @update="onOrderUpdate"></component>
        <!-- <component v-else-if="order.orderType == OrderType.VEHICLE_INSPECTION" :is="'order-detail-inspection'" :order="order"  @update="onOrderUpdate"></component> -->
        <component v-else :is="'order-detail-maintain'" :order="order"  @update="onOrderUpdate"></component>
      </template>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
  .container ::v-deep {
    .panel .panel-content{
      padding: 0;
    }
  }
</style>
<script>
import { dialog, toast, loading } from '@/bus';
import { formatDate } from '@/utils';
import OrderDetailMaintain from './_OrderDetail_Maintain.vue';
import OrderDetailWash from './_OrderDetail_Wash.vue';
import { OrderStatus, OrderType, AppStatus, PaymentChannel } from '@/enums';
import { getOrderDetail } from '@/api';

function getInitialData() {
  return {
    OrderStatus,
    AppStatus,
    OrderType,
    order: null,
    refreshAction: Date.now(),
    status: AppStatus.LOADING,
  };
}

let isFirstIn = true;

export default {
  name: 'order-detail',
  components: {
    'order-detail-wash': OrderDetailWash,
    'order-detail-maintain': OrderDetailMaintain,
  },
  data() {
    return getInitialData();
  },
  computed: {
  },
  methods: {
    getPageData() {
      const orderId = this.$route.params.id;
      return getOrderDetail(orderId).then(order => {
        /* order.orderStatus = OrderStatus.CONFIRMING;
          order.reorderType=1;
          order.reorderPrice=200; */
        this.order = order;
        this.status = AppStatus.READY;
      }, e => {
        console.error(e);
        this.status = AppStatus.ERROR;
      });
    },
    init() {
      // if (!isFirstIn) {
      //   Object.assign(this.$data, getInitialData());
      // }
      // isFirstIn = false;
      this.getPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
      // this.$destroy();
    },
    onOrderUpdate() {
      this.$refs.view.scrollTo(0);
      this.getPageData();
    },
    onResume() {
      this.getPageData();
    },
    refresh() {
      this.getPageData().then(res => {
        this.refreshAction = Date.now();
      });
    },
    go(url) {
      this.$router.push(url);
    },
  }
};
</script>
