// import JglhSDK from '@/lib/jglh-sdk/jglh-sdk';
import JglhSDK from '@jgrm/jglh-js-sdk';
import {
  requestWeixinAuth,
  setWeixinShareInfo,
  payByWeixinPub,
  hideWeixinMenus,
  requestWeixinAuthWithUid,
} from './weixin';
import ENV, { isProduction, isInJglh, isInAliApp, isIOS } from '@/common/env';
import { showLoginDialog } from '@/bus';
import { isValidURL, getThirdAppParams } from '@/utils';
import { saveTokenToStorage, getTokenFromStorage } from '@/store/storage';
import alipayUtils from './alipay';

const isInWeiXin = ENV.weixin;
const isInApp = ENV.jglh;
const isInIOS = ENV.ios;
const isInAndroid = ENV.android;
const isInWeApp = ENV.weapp;
const isInUnionPayMP = ENV.unionpay_mp;

// console.log('NativeJS:', JglhSDK);

const jglh = new JglhSDK();
const UpSDK = window.upsdk; // 云闪付小程序SDK
// vue某版本此处若使用sessionStorage，则页面在webview中无法后退，原因未知，升级后正常了
// 使用sessionStorage时，在测试时发现界面切换有卡顿现象，使用localStorage无此问题
// const storage = localStorage;

// const SESSION_NAME = 'session';

function promiseTimeout(promiseFn) {
  const TIMEOUT = 5000;
  return new Promise((resolve, reject) => {
    let isTimeout = true;
    const code = setTimeout(() => {
      console.error('timeout!');
      if (isTimeout) reject('定位失败');
    }, TIMEOUT);
    promiseFn()
      .then(data => {
        isTimeout = false;
        clearTimeout(code);
        resolve(data);
      })
      .catch(e => {
        reject(e);
      });
  });
}

/**
 * 调起交广领航的登录界面
 * 曾经采用 先尝试从客户端获取session, 若得到session认为是已登录状态，不再重复登录
 * 但后期服务器接口改造，此判断方式已不可靠，故调用此方法进行强制登录操作
 * @params {Object} options 配置参数
 * @param {Boolean} options.force 是否强制登录, 默认为true
 * @returns {Promise}
 */
export function appLogin() {
  return new Promise((resolve, reject) => {
    jglh.login(uid => {
      console.log('login success ...', uid);
      jglh.getSession(session => {
        setSession(session);
        resolve(session);
      });
    });
  });
}

export function scanCode() {
  if (isInJglh) {
    jglh.scanQR(function (flag) {
      // alert('调试中：' + flag)
    });
  }
}

/**
 * 统一登录方法
 * @param {object|string} options: 可能是一个对象，也可能是一个url
 */
export function login(options) {
  if (isInJglh) {
    return appLogin(...arguments);
  } else if (isInWeApp) {
    // 小程序处理
    return new Promise((resolve, reject) => {
      window.JglhWeApp.login();
    });
  } else if (isInAliApp) {
    // 小程序处理
    return new Promise((resolve, reject) => {
      alipayUtils.login();
    });
  } else if (isInWeiXin || isInUnionPayMP) {
    const nextPage = isValidURL(options) ? options : location.href;
    return Promise.resolve().then(function () {
      requestWeixinAuth(nextPage);
      return true;
    });
  } else if (getThirdAppParams().platform) {
    showLoginDialog();
  } else {
    // TDOO: 网页登录
    console.error('当前环境不支持网页登录！');
  }
}

function setSession(session) {
  /**
   *  或不应判断session，否则若登录失效，session依然有效
   *  但有时webview会被系统杀死（android端表现为webview顶部出现横向黑条，iOS端表现为webview假死），导致getSession拿不到数据，但此时用户是登录状态，此时将session清除会导致
   *  用户可能无法正常进行后续操作
   */
  if (!session) session = '';
  /* if (!session) {
    console.warn('!session:', session);
    return;
  } */
  console.log('session:', typeof session, ',', session);
  // storage.setItem(SESSION_NAME, session);
  saveTokenToStorage(session);
}

export function syncSession() {
  return new Promise((resolve, reject) => {
    if (isInApp) {
      jglh.getSession(sessionId => {
        setSession(sessionId);
        resolve(sessionId);
      });
    } else {
      resolve();
    }
  });
}
export function getAMap() {
  return new Promise((resolve, reject) => {
    if (window.AMap) {
      resolve(window.AMap);
      return;
    }
    const script = document.createElement('script');
    // 8.27 原来引入高德js是http的，本业务是部署到https服务器上，会加载出错，所以将高德js引入改为https
    script.src =
      'https://webapi.amap.com/maps?v=1.3&key=cd617da39e3c8bfcf128d433f3afc8d8';
    script.onload = function () {
      function checkAMap() {
        if (!window.AMap) {
          console.info('delay 100 ms check ...');
          setTimeout(checkAMap, 100);
        } else {
          resolve(window.AMap);
        }
      }
      checkAMap();
    };
    document.getElementsByTagName('head')[0].appendChild(script);
  });
}
// 2021-12-22引入最新版本高德JS API 2.0
// 也可以使用高德官方加載器AMapLoader加載
export function getAMapLatest() {
  return new Promise((resolve, reject) => {
    if (window.AMap && window.AMap.version) {
      resolve(window.AMap);
      return;
    }
    const script = document.createElement('script');
    // 8.27 原来引入高德js是http的，本业务是部署到https服务器上，会加载出错，所以将高德js引入改为https
    script.src =
      'https://webapi.amap.com/maps?v=2.0&key=bcbc51d610133a0fdb31ba5dfcea9752';
    script.onload = function () {
      function checkAMap() {
        if (!window.AMap) {
          console.info('delay 100 ms check ...');
          setTimeout(checkAMap, 100);
        } else {
          resolve(window.AMap);
        }
      }
      checkAMap();
    };
    document.getElementsByTagName('head')[0].appendChild(script);
  });
}

// 加載高德AMapUI 组件库
export function getAMapUI() {
  return new Promise((resolve, reject) => {
    if (window.AMapUI && window.AMapUI.version) {
      resolve(window.AMapUI);
      return;
    }
    const script = document.createElement('script');
    script.src = 'https://webapi.amap.com/ui/1.1/main.js';
    script.onload = function () {
      function checkAMapUI() {
        if (!window.AMapUI) {
          console.info('delay 100 ms check ...');
          setTimeout(checkAMapUI, 100);
        } else {
          resolve(window.AMapUI);
        }
      }
      checkAMapUI();
    };
    document.getElementsByTagName('head')[0].appendChild(script);
  });
}

export function getSession() {
  // const session = storage.getItem(SESSION_NAME);
  // console.log('getsession:', session);
  // return session;
  return getTokenFromStorage();
}

export function isLoggedIn() {
  return !!getSession();
}

export function checkLoggedIn() {
  return new Promise((resolve, reject) => {
    jglh.getSession(session => {
      session ? resolve() : reject();
    });
  });
}

export function disablePullDownRefresh() {
  jglh.setPulldownRefreshFlag(false);
}

export function pay(option) {
  if (isInWeiXin) {
    return payByWeixinPub(option.data);
  } else {
    return jglh.pay(option);
  }
}

export function toEmergencySchedule(id) {
  return jglh.toEmergencySchedule(id);
}
/* export function pay2(id, channel) {
  return new Promise((resolve, reject) => {
    if (channel == PaymentChannel.WEIXIN_PUB)
  })
} */

export { payByWeixinPub };
export { requestWeixinAuthWithUid };

export function setTitle(title) {
  if (isInApp) {
    /**
     * jglh-3.9.3及之前
     * SPA应用返回会触发android webview的 onReceivedTitle 回调方法，Android端会在回调中更新titlebar的标题
     * 若android没有做防抖处理，可能会出现标题闪烁现象
     * 暂时采用将title设为空字符串来降低视觉上的闪烁效果
     * 2018年7月30日15:52:09，与android协商，将在下个版本：3.9.4中加入150ms防抖处理
     */
    // document.title = ' ';
    return jglh.setTitle(title);
  } else {
    document.title = title;
  }
}

/**
 * 将url解析为pushWebView方法的参数对象
 * @param {string} url url地址
 */
export function parseJglhURL(url) {
  return jglh.parseJglhURL(...arguments);
}

/**
 * 获取设备信息
 */
export function getSystemInfo() {
  return new Promise((resolve, reject) => {
    if (canIUseJglh('getSystemInfo')) {
      jglh.getSystemInfo({
        success: function (res) {
          resolve(res);
        },
        fail(err) {
          reject(err);
        },
      });
    } else {
      reject('');
    }
  });
}

/**
 * 获取APP设置信息-用户个性化配置
 */
export function getAppSettingInfo() {
  return new Promise((resolve, reject) => {
    if (jglh.getAppSettingInfo) {
      jglh.getAppSettingInfo({
        success: function (res) {
          resolve(res);
        },
        fail(err) {
          reject(err);
        },
      });
    } else {
      reject('');
    }
  });
}

/**
 * 获取设备id
 */
export function getDeviceId() {
  return new Promise((resolve, reject) => {
    if (canIUseJglh('getSystemInfo')) {
      jglh.getSystemInfo({
        success: function (res) {
          resolve(res.id);
        },
        fail(err) {
          reject(err);
        },
      });
    } else {
      reject('');
    }
  });
}

/**
 * 禁用android端的webview进度条
 * @deprecated App端未实现
 */
export function disableProcessBar() {
  jglh.setProcessBarStatus(0);
}
/**
 * 禁用android端的webview自动更新标题栏
 * @deprecated App端未实现
 */
export function disableAutoUpdateTitle() {
  jglh.setAutoUpdateTitleStatus(0);
}

/**
 * 获取用户当前坐标信息（高德坐标）
 * @returns {Promise}
 */
let geoTimer = null; // 位置获取定时器, 5s内未获取到位置信息，返回默认值
export function getGeoData() {
  return new Promise((resolve, reject) => {
    clearTimeout(geoTimer);
    geoTimer = setTimeout(() => {
      resolve({
        longitude: 113.678817,
        latitude: 34.764644,
        city: '郑州市',
      });
    }, 8000);
    if (isInWeiXin) {
      window.wx.getLocation({
        type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success(res) {
          let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
          let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
          let speed = res.speed; // 速度，以米/每秒计
          let accuracy = res.accuracy; // 位置精度
          const locationParams = encodeURIComponent(
            [longitude, latitude].join(',')
          );
          fetch(
            `https://restapi.amap.com/v3/geocode/regeo?key=ec82f2eb43a8cb8c108b81cc2657b381&location=${locationParams}`,
            {
              method: 'GET',
              mode: 'cors',
            }
          )
            .then(response => response.json())
            .then(info => {
              clearTimeout(geoTimer);
              if (info.status == 1) {
                let address = info.regeocode.addressComponent;
                resolve({
                  adcode: address.adcode,
                  address: info.regeocode.formatted_address,
                  province: address.province,
                  city: address.city,
                  district: address.district,
                  country: address.country,
                  township: address.township,
                  ...res,
                });
              } else {
                resolve(res);
              }
            })
            .catch(err => {
              clearTimeout(geoTimer);
              resolve(res);
              console.error(err);
            });
        },
        fail(e) {
          console.error('位置获取失败', e);
          clearTimeout(geoTimer);
          resolve({
            longitude: 113.678817,
            latitude: 34.764644,
            city: '郑州市',
          });
          // reject(e && e.errMsg);
        },
      });
    } else if (isInUnionPayMP) {
      unionpayGetLocation()
        .then(res => {
          // 获取到的地理信息
          // console.log('获取到的地理信息', res)
          let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
          let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
          const locationParams = encodeURIComponent(
            [longitude, latitude].join(',')
          );
          fetch(
            `https://restapi.amap.com/v3/geocode/regeo?key=ec82f2eb43a8cb8c108b81cc2657b381&location=${locationParams}`,
            {
              method: 'GET',
              mode: 'cors',
            }
          )
            .then(response => response.json())
            .then(info => {
              clearTimeout(geoTimer);
              if (info.status == 1) {
                let address = info.regeocode.addressComponent;
                resolve({
                  city: address.city,
                  province: address.province,
                  adcode: address.adcode,
                  district: address.district,
                  country: address.country,
                  township: address.township,
                  ...res,
                });
              } else {
                resolve(res);
              }
            })
            .catch(err => {
              clearTimeout(geoTimer);
              resolve(res);
              console.error(err);
            });
        })
        .catch(err => {
          console.error('位置获取失败', err);
          resolve({
            longitude: 113.678817,
            latitude: 34.764644,
            city: '郑州市',
          });
        });
    } else if (isInAliApp) {
      alipayUtils
        .getLocation()
        .then(res => {
          // 获取到的地理信息
          // console.log('获取到的地理信息', res)
          let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
          let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
          const locationParams = encodeURIComponent(
            [longitude, latitude].join(',')
          );
          fetch(
            `https://restapi.amap.com/v3/geocode/regeo?key=ec82f2eb43a8cb8c108b81cc2657b381&location=${locationParams}`,
            {
              method: 'GET',
              mode: 'cors',
            }
          )
            .then(response => response.json())
            .then(info => {
              clearTimeout(geoTimer);
              if (info.status == 1) {
                let address = info.regeocode.addressComponent;
                resolve({
                  city: address.city,
                  province: address.province,
                  adcode: address.adcode,
                  district: address.district,
                  country: address.country,
                  township: address.township,
                  ...res,
                });
              } else {
                resolve(res);
              }
            })
            .catch(err => {
              clearTimeout(geoTimer);
              resolve(res);
              console.error(err);
            });
        })
        .catch(err => {
          console.error('位置获取失败', err);
          clearTimeout(geoTimer);
          resolve({
            longitude: 113.678817,
            latitude: 34.764644,
            city: '郑州市',
          });
        });
    } else {
      clearTimeout(geoTimer);
      jglh.getGeoData(geo => {
        // 默认值工会大厦
        console.log('jglh.getGeoData===', geo);
        const jglhVersion = getVersion();
        if (jglhVersion > 462 && jglhVersion < 469 && isIOS && geo) {
          // geo.address = '选择位置';
          getAddressFromCoordinates({
            latitude: geo.latitude,
            longitude: geo.longitude,
          })
            .then(res => {
              resolve({
                ...geo,
                address: res.address,
              });
            })
            .catch(() => {
              resolve({
                ...geo,
                address: '选择位置',
              });
            });
          return;
        }
        resolve(
          geo || {
            longitude: 113.678817,
            latitude: 34.764644,
            city: '郑州市',
            province: '河南省',
            name: '经五路2号院',
            address: '河南省郑州市金水区经五路2号院',
          }
        );
      });
    }
  });
}
/**
 * @description: 经纬度转地址
 * @param {*} params.latitude 纬度
 * @param {*} params.longitude 经度
 * @param {*} params.geocoderOptions 传给高德的查询配置信息
 * @param {*} params.geocoderOptions.radius 以给定坐标为中心点，单位：米
 * @param {*} params.geocoderOptions.batch 是否批量查询，batch 设置为 false 时，只返回第一条记录
 * @param {*} params.geocoderOptions.extensions 逆地理编码时，返回信息的详略
 *    默认值：base，返回基本地址信息
 *    取值为：all，返回地址信息及附近poi、道路、道路交叉口等信息
 * @return {*}
 */
export function getAddressFromCoordinates(params, geocoderOptions = {}) {
  let latitude = params.latitude; // 纬度，浮点数，范围为90 ~ -90
  let longitude = params.longitude; // 经度，浮点数，范围为180 ~ -180。
  return new Promise((resolve, reject) => {
    getAMapLatest().then(AMap => {
      AMap.plugin('AMap.Geocoder', function () {
        var geocoder = new AMap.Geocoder(geocoderOptions);
        var lnglat = [longitude, latitude];
        geocoder.getAddress(lnglat, function (status, result) {
          if (status === 'complete' && result.info === 'OK') {
            // result为对应的地理位置详细信息
            let _regeocode = result.regeocode;
            resolve({
              adcode: _regeocode.addressComponent.adcode,
              province: _regeocode.addressComponent.province,
              city: _regeocode.addressComponent.city,
              district: _regeocode.addressComponent.district,
              name: _regeocode.addressComponent.neighborhood,
              township: _regeocode.addressComponent.township,
              address: _regeocode.formattedAddress,
              latitude,
              longitude,
            });
          } else {
            reject(result);
          }
        });
      });
    });
  });
}
/**
 * @description: 地址转经纬度
 * @param {*} params.address 地址
 * @param {*} params.geocoderOptions 传给高德的查询配置信息
 * @param {*} params.geocoderOptions.city 城市名（中文或中文全拼）、citycode、adcode
 * @param {*} params.geocoderOptions.batch 是否批量查询，batch 设置为 false 时，只返回第一条记录
 * @return {*}
 */
let locationTimer = null; // 定时器, 8s内未获取到位置信息，返回默认值
export function getLocationFromAddress(params, geocoderOptions = {}) {
  let address = params.address;
  return new Promise((resolve, reject) => {
    clearTimeout(locationTimer);
    locationTimer = setTimeout(() => {
      reject('获取位置超时');
    }, 8000);
    getAMapLatest().then(AMap => {
      AMap.plugin('AMap.Geocoder', function () {
        var geocoder = new AMap.Geocoder(geocoderOptions);
        geocoder.on('error', function (error) {
          clearTimeout(locationTimer);
          reject(error);
        });
        geocoder.getLocation(address, function (status, result) {
          clearTimeout(locationTimer);
          if (status === 'complete' && result.geocodes.length) {
            // result为对应的地理位置详细信息
            let _regeocode = result.geocodes[0];
            resolve({
              adcode: _regeocode.adcode,
              province: _regeocode.addressComponent.province,
              city: _regeocode.addressComponent.city,
              district: _regeocode.addressComponent.district,
              name: _regeocode.addressComponent.neighborhood,
              township: _regeocode.addressComponent.township,
              address: _regeocode.formattedAddress,
              latitude: _regeocode.location.lat,
              longitude: _regeocode.location.lng,
            });
          } else {
            reject(result);
          }
        });
      });
    });
  });
}
/**
 * @description: 根据地址获取POI信息，此处使用输入提示接口，返回数据更符合预期的结果
 * @param {*} params.address 地址
 * @param {*} params.geocoderOptions 传给高德的查询配置信息
 * @param {*} params.geocoderOptions.city 城市名（中文或中文全拼）、citycode、adcode
 * @return {*}
 */
let poisTimer = null; // 定时器, 8s内未获取到位置信息，返回默认值
export function getPOISFromAddress(params, geocoderOptions = {}) {
  let address = params.address;
  // let city = '&region=' + geocoderOptions.city || ''; // 地点搜索
  let city = '&city=' + geocoderOptions.city || ''; // 输入提示
  return new Promise((resolve, reject) => {
    clearTimeout(poisTimer);
    poisTimer = setTimeout(() => {
      reject('未获取到位置信息');
    }, 8000);
    fetch(
      // `https://restapi.amap.com/v5/place/text?key=ec82f2eb43a8cb8c108b81cc2657b381&types=010000|020000|030000|050000|060000|070000|080000|090000|100000|110000|120000|130000|140000|150000|160000|170000|190000|990000&keywords=${address}${city}`, // 地点搜索服务2.0是一类 Web API 接口
      `https://restapi.amap.com/v3/assistant/inputtips?key=ec82f2eb43a8cb8c108b81cc2657b381&types=010000|020000|030000|050000|060000|070000|080000|090000|100000|110000|120000|130000|140000|150000|160000|170000|190000|990000&keywords=${address}${city}`, // 输入提示接口，提供根据用户输入的关键词查询返回建议列表
      {
        method: 'GET',
        mode: 'cors',
      }
    )
      .then(response => response.json())
      .then(info => {
        clearTimeout(poisTimer);
        if (info.status == 1) {
          // resolve(info.pois); // 地点搜索
          resolve(info.tips); // 输入提示
        } else {
          reject('未获取到位置信息');
        }
      })
      .catch(err => {
        clearTimeout(poisTimer);
        reject('未获取到位置信息');
        console.error(err);
      });
  });
}
/**
 * 获取用户坐标信息
 * @param {number} timeout 超时时间毫秒数
 */
export function getLocation(timeout = 5000) {
  return getGeoData();
  // return promiseTimeout(getGeoData);
}

/**
 * 获取两坐标间距离信息  步行、公交、驾车查询及行驶距离计算接口
 * https://lbs.amap.com/api/webservice/guide/api/direction#driving
 * @param {string} origin 起始点 116.481028,39.989643
 * @param {string} destination 目的地 116.465302,40.004717
 */
export function getDrivingDistance(origin, destination) {
  return new Promise((resolve, reject) => {
    fetch(
      `https://restapi.amap.com/v3/direction/driving?origin=${origin}&destination=${destination}&extensions=all&output=JSON&key=ec82f2eb43a8cb8c108b81cc2657b381`,
      {
        method: 'GET',
        mode: 'cors',
      }
    )
      .then(res => res.json())
      .then(info => {
        resolve(info.route);
      })
      .catch(err => {
        resolve(err);
        console.error(err);
      });
  });
}
export function pushWebView(options) {
  jglh.pushWebView(options);
}

export function pushNativeView(options) {
  jglh.pushNativeView(options);
}

export function popWebView(number) {
  // 截止iOS 3.9.3 Bug：popWebView未按要求实现，只能通过传入1实现后退且退到最后一步退出webview，不能直接退出webview
  if (isInIOS) {
    jglh.popWebView(1);
  } else {
    jglh.popWebView(number);
  }
}

export function clearHistory() {
  jglh.clearHistory();
}

/* export function getVersion() {
  const result = /JGRM1041_(\d+)/i.exec(navigator.userAgent);
  if (result instanceof Array && result.length === 2) {
    return Number(result[1]);
  }
  return 0;
} */

export function getVersion() {
  return jglh.getVersion();
}

export function getVersionName() {
  if (isInApp) {
    return `jglh-${isInAndroid ? 'android' : 'ios'}/` + jglh.getVersion();
  } else if (isInWeiXin) {
    try {
      return /.*\s(MicroMessenger\/[\d.]+)/i.exec(navigator.userAgent)[1];
    } catch (e) {
      return 'MicroMessenger/unknow';
    }
  } else return 0;
}

export function getUserID() {
  return new Promise((resolve, reject) => {
    jglh.getUID(uid => {
      uid ? resolve(uid) : resolve('anonymous');
    });
  });
}

export function playPhotos(option) {
  if (isInWeiXin) {
    window.wx.previewImage({
      current: option.photos[option.initIndex].url, // 当前显示图片的http链接
      urls: option.photos.map(item => item.url), // 需要预览的图片http链接列表
    });
  } else if (isInUnionPayMP) {
    return false;
  } else if (isInAliApp) {
    alipayUtils.previewImage({
      current: option.photos[option.initIndex].url, // 当前显示图片的http链接
      urls: option.photos.map(item => item.url), // 需要预览的图片http链接列表
    });
  } else {
    return jglh.playPhotos(option);
  }
}

/**
 * 保存base64图片到相册
 * @param {string} str  - 图片的base64字符串
 */
export function saveImageBase64(str) {
  if (!str) return;
  return new Promise((resolve, reject) => {
    if (isInJglh && canIUseJglh('saveImageBase64')) {
      jglh.saveImageBase64(str);
      resolve();
    } else {
      reject();
    }
  });
}

/**
 * 调起导航
 * @param {object} option
 * @param {number} option.longitude  经度
 * @param {number} option.latitude 纬度
 * @param {string} option.address  地址
 */
export function navigate(option) {
  let posInfo = {
    latitude: option.latitude, // 纬度，浮点数，范围为90 ~ -90
    longitude: option.longitude, // 经度，浮点数，范围为180 ~ -180。
  };
  if (option.address) {
    posInfo.address = option.address; // 地址详情说明
  }
  if (option.name) {
    posInfo.name = option.name || ''; // 位置名
  }
  if (isInWeiXin) {
    posInfo.scale = 27; // 地图缩放级别,整形值,范围从1~28。默认为最大
    posInfo.infoUrl = ''; // 在查看位置界面底部显示的超链接,可点击跳转
    window.wx && window.wx.openLocation(posInfo);
  } else if (isInUnionPayMP) {
    unionpayNavi(option);
  } else if (isInAliApp) {
    alipayUtils.openLocation(option);
  } else {
    return jglh.navigate(posInfo);
  }
}

/**
 * 判断当前环境是否支持指定的方法
 * @param {*} schema
 */
export function canIUseJglh(schema) {
  return jglh && jglh.canIUse(schema);
}

export function addWebViewListener(event, callback, preventDefault = false) {
  return jglh.addWebViewListener({
    event,
    callback,
    preventDefault,
  });
}

export function couldUseNative() {
  return jglh.couldUseNative();
}

export function initNativeJs(...args) {
  jglh.init(...args);
}

/**
 * 设置分享信息
 * 如果在交广领航内，调用app的分享接口
 * 如果在微信环境下，调用微信js-sdk的分享接口
 * @param {*} options
 */
export function setShareConfig(options) {
  if (options && options.title && options.title.length >= 25) {
    console.warn(
      `分享标题长度：${options.title.length}，微信分享标题字符长度大于25可能无法保证在所有手机上都完整显示`
    );
  }
  if (isInWeiXin) {
    setWeixinShareInfo(options);
    // 小程序内需要向小程序传递页面信息
    if (isInWeApp) {
      window.wx.miniProgram.postMessage({ data: options });
      // window.wx.miniProgram.navigateTo({ url: `/pages/web/index?url=${encodeURIComponent('about:blank')}` });
      // window.wx.miniProgram.navigateBack()
    }
  } else if (isInAliApp) {
    // 支付宝小程序分享
    alipayUtils.postMessage({
      type: 'shareConfig',
      data: options,
    });
  } else {
    jglh.setShareConfig(options);
  }
}

/**
 * 设置app中虚拟键盘状态
 * 目前只在app中实现
 * @param {*} options
 */
export function setSoftInput(options) {
  if (isInApp) {
    jglh.setSoftInput(options);
  }
}

/**
 * 云闪付小程序分享
 * @param {*} options
 */
function unionpayShare(options) {
  UpSDK.pluginReady(function () {
    // 前端API调用
    let hasWh = /[?]/g.test(options.link.substring(options.link.indexOf('#')));
    console.log('分享' + options.link + '+++++', hasWh);
    if (!hasWh) {
      options.link = options.link + '?';
    }
    UpSDK.showSharePopup({
      title: options.title,
      desc: options.desc,
      shareUrl: options.link,
      picUrl: options.imgUrl,
    });
  });
}

/**
 * 云闪付小程序获取用户当前坐标信息（高德坐标）
 * @returns {Promise}
 */
function unionpayGetLocation() {
  return new Promise((resolve, reject) => {
    UpSDK.pluginReady(function () {
      UpSDK.getLocationGps({
        success: function (res) {
          if (typeof res == 'string') {
            res = JSON.parse(res);
          }
          resolve(res);
          // successFn(JSON.parse(res))
        },
        fail: function (err) {
          if (typeof err == 'string') {
            err = JSON.parse(err);
          }
          reject(err);
          // failFn(JSON.parse(err))
        },
      });
    });
  });
}
/**
 * 云闪付小程序调起导航（高德坐标）
 * @returns {Promise}
 */
function unionpayNavi(option) {
  unionpayGetLocation()
    .then(res => {
      // 获取到的地理信息
      // console.log('获取到的地理信息', res)
      let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
      let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
      const locationParams = encodeURIComponent(
        [longitude, latitude].join(',')
      );
      fetch(
        `https://restapi.amap.com/v3/geocode/regeo?key=ec82f2eb43a8cb8c108b81cc2657b381&location=${locationParams}`,
        {
          method: 'GET',
          mode: 'cors',
        }
      )
        .then(res => res.json())
        .then(info => {
          if (info.status == 1) {
            let address = info.regeocode.formatted_address;
            // resolve({
            //   city: address.city,
            //   province: address.province,
            //   adcode: address.adcode,
            //   district: address.district,
            //   country: address.country,
            //   township: address.township,
            //   ...res,
            // });
            UpSDK.pluginReady(function () {
              UpSDK.navi({
                sLat: res.latitude, // 起点纬度
                sLon: res.longitude, // 起点经度
                sName: address, // 起点名称
                dLat: option.latitude, // 终点维度
                dLon: option.longitude, // 终点经度
                dName: option.name || option.address, // 终点名称
                success: function () {
                  // 成功回调
                },
                fail: function (msg) {
                  // 失败回调
                },
              });
            });
          }
        })
        .catch(err => {
          console.error(err);
        });
    })
    .catch(err => {
      console.error('位置获取失败', err);
    });
}

/**
 * 调起分享界面，仅在交广领航内有效
 * @param {object} options 同setShareConfig
 */
export function share(options) {
  if (isInUnionPayMP) {
    unionpayShare(options);
  } else if (isInAliApp) {
    setShareConfig(options);
    alipayUtils.startShare();
  } else {
    jglh.share(options);
  }
}

/**
 * 初始化app配置信息
 */
export function initAppConfig(shareInfo) {
  const shareConfig = Object.assign(
    {
      title: '交广领航车生活平台',
      desc: '交广领航车生活平台，严选商家、定期抽检、媒体监督，为用户找放心商家。',
      link: 'http://www.jgrm.net/mobile/jglh.html', // 必须与公众号JS安全域名一致
      imgUrl: 'https://img.jgrm.net/Fi6QQSe98E9zHC9EKCKBiTFiRNFo_xlogo',
    },
    shareInfo
  );
  setShareConfig(shareConfig);
  if (isInWeiXin && isProduction) {
    hideWeixinMenus([
      // 'menuItem:share:appMessage',
      // 'menuItem:share:timeline',
      'menuItem:share:qq',
      'menuItem:share:weiboApp',
      // 'menuItem:favorite',
      'menuItem:share:facebook',
      'menuItem:share:QZone',

      'menuItem:editTag',
      'menuItem:delete',
      'menuItem:copyUrl',
      'menuItem:originPage',
      'menuItem:readMode',
      'menuItem:openWithQQBrowser',
      'menuItem:openWithSafari',
      'menuItem:share:email',
      'menuItem:share:brand',
    ]);
  }
}

/**
 * 启用付款码环境
 *
 * @param {function} onUserCaptureScreen
 */
export function onPaymentEnv(onUserCaptureScreen = () => {}) {
  // v4.3.0 android 打包 api target level 更改为26导致申请控制亮度权限提示，暂时隐藏
  if (isInJglh && isInIOS) {
    jglh.getScreenBrightness({
      success(res) {
        console.info('_lastBrightness:', res);
        jglh.getScreenBrightness._lastBrightness = res.value;
        jglh.setScreenBrightness({
          value: 1,
        });
        // 2019年1月28日09:25:34：可能是App实现的缺陷，有时不使用交广领航也会导致手机保持常亮，故此处取消常亮设置
        jglh.setKeepScreenOn({
          keepScreenOn: false,
        });
        jglh.onUserCaptureScreen(onUserCaptureScreen);
      },
    });
  }
}

/**
 * 禁用支付模式
 */
export function offPaymentEnv() {
  if (isInJglh && isInIOS) {
    // 将亮度恢复到设置前的亮度
    jglh.setScreenBrightness({
      value: jglh.getScreenBrightness._lastBrightness || 0.6,
    });
    jglh.setKeepScreenOn({
      keepScreenOn: false,
    });
    jglh.onUserCaptureScreen(() => {});
  }
}

/**
 * 拨打电话
 * @param {string} phone 电话号码
 */
export function callPhone(phone) {
  if (isInAliApp) {
    makePhoneCall(phone);
    return;
  }
  window.open(`tel:${phone}`);
}

function wrapPromise(fn) {
  return new Promise((resolve, reject) => {
    try {
      fn.call(this, resolve, reject);
    } catch (err) {
      reject(err);
    }
  });
}

function getRejectAppVersionTip() {
  const version = getVersion();
  return `您的App版本${version}，不支持当前操作，请升级到最新版本后重试`;
}

/**
 * 选择视频
 * @param {*} option
 */
export function chooseVideo(option) {
  return wrapPromise((resolve, reject) => {
    if (isInJglh) {
      if (!canIUseJglh('chooseVideo')) {
        reject(getRejectAppVersionTip());
        return;
      }
      const options = Object.assign(
        {
          // sourceType: ['album','camera'], // 视频选择的来源
          upload: true,
          sourceType: 'album', // 视频选择的来源
          minDuration: 5, // 拍摄视频最短拍摄时间，单位秒
          maxDuration: 15, // 拍摄视频最长拍摄时间，单位秒
          camera: 'back', // 摄像头类型 back|front，默认back
          success: function (res) {
            resolve(res);
          },
          fail: function (err) {
            reject(err);
          },
        },
        option
      );
      jglh.chooseVideo(options);
    } else {
      reject('env not support chooseVideo...');
    }
  });
}

/**
 * 播放视频
 * @param {string} url 视频url
 */
export function playVideo(url) {
  return wrapPromise((resolve, reject) => {
    if (isInJglh) {
      if (!canIUseJglh('playVideo')) {
        reject(getRejectAppVersionTip());
        return;
      }
      jglh.playVideo({
        filePath: url,
      });
      resolve();
    } else {
      const err = '请在交广领航App内播放视频';
      reject(err);
    }
  });
}

/**
 * 选择地图位置
 */
export function chooseLocation() {
  return wrapPromise((resolve, reject) => {
    if (isInJglh) {
      if (!canIUseJglh('chooseLocation')) {
        reject(getRejectAppVersionTip());
        return;
      }
      jglh.chooseLocation({
        success: function (res) {
          resolve(res);
        },
        fail: function (err) {
          reject(err);
        },
      });
    } else {
      reject('env not support chooseLocation...');
      // console.log()
    }
  });
}

/**
 * @description: app中打开小程序
 * @return {*}
 */
export function openMiniProgram(params) {
  let path = params.path;
  let id = params.id || params.ghid;
  let mpEnv = params.env || 'release';
  // var appId = params.appid;
  return new Promise((resolve, reject) => {
    if (isInApp) {
      // jglh环境需要 ghid，小程序环境需要 appid
      jglh.navigateToMiniProgram({
        userName: id, // 要打开的小程序 原始id
        path: path, // 跳转路径如'page/index/index?id=123',
        envVersion: mpEnv, // develop|trial|release
        success: function (res) {
          // popWebView(0);
          resolve();
        },
        fail: function (err) {
          reject(err);
        },
      });
    } else if (isInWeApp) {
      // 微信webview环境下，需要通过微信标签，点击跳转小程序
      window.wx.miniProgram.navigateTo({
        url: path,
        success: function (res) {
          resolve();
        },
        fail: function (err) {
          console.error(err);
          reject(err);
        },
      });
    } else if (isInAliApp) {
      // 支付宝webview环境下，使用 web-view 与小程序通信交互，然后再小程序页面 js 中调用 my.navigateToMiniProgram 跳转到其他小程序。
      alipayUtils.postMessage({
        type: 'navigateMiniProgram',
        data: {
          appid: id,
          path: path,
        },
      });
      // alipayUtils.postMessage({
      //   data: {
      //     appid: '2017102009406486',
      //     path: 'pages/user/index',
      //   }
      // });
    } else {
      // 永久小程序URL Scheme 规则有变，暂不适用 公告地址：https://developers.weixin.qq.com/community/develop/doc/000aeab88a4ea0c5c89d81fde5b801
      reject('非交广领航、微信环境调用');
    }
  });
}

/**
 * @description: 打开有声书
 * @return {*}
 */
export function toSoundForce() {
  return new Promise((resolve, reject) => {
    if (canIUseJglh('toSoundForce')) {
      jglh.toSoundForce();
      resolve();
    } else {
      reject();
    }
  });
}

/**
 * @description: 打开滴滴加油、充电
 * @return {*}
 */
export function openDidi(options) {
  return new Promise((resolve, reject) => {
    if (canIUseJglh('openDidi')) {
      jglh.openDidi(options);
      resolve();
    } else {
      reject();
    }
  });
}

/**
 * @description: 打电话
 * @param {*} phone
 * @return {*}
 */
export function makePhoneCall(phone) {
  alipayUtils.postMessage({
    type: 'callAPI',
    api: 'makePhoneCall',
    data: {
      number: phone,
    },
  });
}

/**
 * 识别条形码、二维码
 * @param {object} option
 * @param {string} option.codeType - 二维码的类型，barCode 为一维码，qrCode 为二维码 all 全部
 */
export function codeScanResult(options) {
  return wrapPromise((resolve, reject) => {
    if (isInJglh) {
      if (!canIUseJglh('codeScanResult')) {
        reject(getRejectAppVersionTip());
        return;
      }
      jglh.codeScanResult({
        success: function (res) {
          resolve(res);
        },
        fail: function () {
          reject('未识别到有效信息');
        },
        codeType: (options && options.codeType) || 'barCode',
      });
    } else {
      const err = '请在交广领航App内扫码';
      reject(err);
    }
  });
}
