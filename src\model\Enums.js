import Enum from './Enum';
export default class Enums {
  constructor(objs) {
    this.setEnums(objs);
    Object.keys(this.__enums).forEach(key => {
      this[key] = this.__enums[key];
    });
  }

  setEnums(objs) {
    Object.defineProperty(this, '__enums', {
      value: objs,
      enumerable: false,
    });
    // this.__enums = objs;
  }

  getEnums() {
    return this.__enums;
  }

  // getEnum(value, defaultValue = null) {
  //   let theEnum = defaultValue;
  //   for (const key of Object.keys(this.enums)) {
  //     if (this.enums[key].equals(value)) {
  //       theEnum = this.enums[key];
  //       break;
  //     }
  //   }
  //   return theEnum;
  // }

  getEnum(value, defaultValue = null) {
    return Object.values(this.__enums).find(enumValue => enumValue.equals(value)) || defaultValue;
  }

  // getEnum(value, defaultValue = null) {
  //   let theEnum = defaultValue;
  //   Object.keys(this.__enums).some(key => {
  //     if (this.__enums[key].equals(value)) {
  //       theEnum = this.__enums[key];
  //       return true;
  //     }
  //   });
  //   return theEnum;
  // }

  getEnumByName(name) {
    let theEnum = null;
    for (const key of Object.keys(this.enums)) {
      if (this.enums[key].name === name) {
        theEnum = this.enums[key];
        break;
      }
    }
    return theEnum;
  }

  // getEnumByName(name) {
  //   let theEnum = null;
  //   Object.keys(this.__enums).some(key => {
  //     if (this.__enums[key].name === name) {
  //       theEnum = this.__enums[key];
  //       return true;
  //     }
  //   });
  //   return theEnum;
  // }

  list() {
    return Object.keys(this.__enums).map(key => {
      return this.__enums[key];
    });
  }

  add(obj) {
    if (obj instanceof this.child) {
      this.__enums.push(obj);
    } else {
      throw new Error(obj + 'is not instance of ' + this.child);
    }
  }
}
