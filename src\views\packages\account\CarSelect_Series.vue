<template>
  <container
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    @hide="onHide"
    :keep-alive="false"
  >
    <x-header title="选择车系">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="text" @click="$_router_pageToFeedback()"
        >反馈</x-button
      >
    </x-header>
    <content-view
      class="car-select-series"
      ref="view"
      :status="status"
      @reload="reload"
    >
      <template v-if="status == AppStatus.READY">
        <div class="item-list">
          <dl v-for="(group, key) in groups" :key="key">
            <dt v-html="key"></dt>
            <dd
              v-for="(item, index) in group"
              :key="index"
              class="flex-row item"
              @click="selectCarSeries(item)"
            >
              <div class="item-name" v-html="item.series"></div>
            </dd>
          </dl>
          <div v-if="!list.length" class="empty-tip">没有可选的车系</div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<style lang="scss">
@import '~styles/mixin/index.scss';

.car-select-series {
  .item-list {
    margin-top: 5px;
    background: white;
    dt {
      padding: 2px 10px;
      background: #f3f3f3;
      font-weight: 700;
      color: #6d6d6d;
    }
    .item-logo {
      width: 40px;
      height: 40px;
      margin-right: 5px;
    }
    .item {
      padding: 10px;
      align-items: center;
      @include border-bottom(#e4e4e4);
    }
  }
}
</style>
<script>
import { Events, dialog, back } from '@/bus';
import { getCarSystemList, getNewEnergyCarSystemList } from '@/api';
import { AppStatus, CarType } from '@/common/enums';
import { mixinAuthRouter } from '@/mixins';
// destroy不能如预期100%销毁实例，临时解决方案：不实用destroy，onLeave后reset当前页面
function getInitialData() {
  return {
    status: AppStatus.LOADING,
    AppStatus,
    list: [],
  };
}

export default {
  name: 'select-car-series',
  mixins: [mixinAuthRouter],
  data() {
    return getInitialData();
  },
  computed: {
    groups() {
      const list = this.list || [];
      const groups = {};
      list.forEach(item => {
        let group = item.manufacturer || item.brand;
        if (!groups[group]) {
          groups[group] = [item];
        } else {
          groups[group].push(item);
        }
      });
      return groups;
    },
  },
  methods: {
    initPageData() {
      const { brand } = this.$route.params;
      let fetchFunc =
        this.$route.query.carType == CarType.NEW_ENERGY
          ? getNewEnergyCarSystemList
          : getCarSystemList;
      fetchFunc(brand)
        .then(res => {
          this.list = res;
          this.status = AppStatus.READY;
          this.ready = true;
        })
        .catch(e => {
          console.log(e);
          this.status = AppStatus.ERROR;
        });
    },
    init() {
      this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onHide() {
      // console.log('on hide...', this.$options.name);
      this.status = AppStatus.LOADING;
    },
    selectCarSeries(item) {
      const from = this.$route.query.from;
      const carType = this.$route.query.carType;
      const url = `/car/series/${item.id}/models?from=${from}&carType=${carType}`;
      this.$router.replace(url);
      // this.$router.push(url);
    },
    onResume() {},
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
  },
};
</script>
