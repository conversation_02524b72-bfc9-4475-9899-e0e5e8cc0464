import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';
import { back } from '@/bus';

const routers = [
  {
    path: '/vehicle-business',
    name: 'VehicleBusinessIndex-v1',
    meta: {
      title: '交管车务',
    },
    component: resolve => {
      // import(/* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/business-index/BusinessIndex.vue')
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business/business-index/BusinessIndex.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle-business-v2',
    name: 'VehicleBusinessIndex-v2',
    meta: {
      title: '交管车务',
    },
    component: resolve => {
      // import(/* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/business-index/BusinessIndex.vue')
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business/business-index/BusinessIndex.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle-business-v2/online-inspection/shop/:id',
    name: 'OnlineInspectionShop-v2',
    meta: {
      title: '上线审车商家',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "inspector-shop" */ '@/views/packages/vehicle-business-v2/business-index/OnlineInspectionShop.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle-business-v2/self-driving',
    name: 'HandleSelfDriving',
    meta: {
      title: '办理自驾审车',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/business-index/HandleSelfDriving.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle-business-v2/valet-driving',
    name: 'HandleValetDriving',
    meta: {
      title: '办理上门代审',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/business-index/HandleValetDriving.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle-business-v2/submit-materials',
    name: 'SubmitMaterials',
    meta: {
      title: '材料确认',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/business-index/SubmitMaterials.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle/stations',
    name: 'VehicleStations',
    meta: {
      title: '选择检测站',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/inspection-shop-list.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/address/input',
    name: 'addressInput',
    meta: {
      title: '输入地址',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/address-input.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle/submit',
    name: 'VehicleSubmit',
    meta: {
      title: '确认订单',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/confirm-order.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle/agreement',
    name: 'VehicleAgreement',
    meta: {
      title: 'agreement',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business-v2/agreement.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vehicle-business/reserve/order',
    name: 'VehicleOrderReserve',
    meta: {
      title: '预约订单',
    },
    component: resolve => {
      import(
        /* webpackChunkName: "vehicle-business-v2" */ '@/views/packages/vehicle-business/order/OrdersReserve.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];

export default routers;
// export default BaseRouter;
