import { createEnums } from './utils';

// -1：全部 0：代付款，1：可使用，2：待评价 5：退款
export const OrderStatus = createEnums({
  UNPAID: ['未付款', 0, ''],
  // PAID: ['已付款', 1, ''],

  TO_BE_SERVE: ['未消费', 1, ''],
  // SERVED: ['已服务', 2, ''],

  UN_COMMENTED: ['未评价', 2, ''],
  COMMENTED: ['已评价', 3, ''],

  REFUNDING: ['退款中', 4, ''],
  REFUNDED: ['已退款', 5, ''],

  INVALID: ['已失效', -1, '未付款'],
  EXPIRED: ['已过期', 6, '有效期已过'],
  TIMEOUT: ['已失效', 7, '订单超时失效'],
  REFUND_APPLIED: ['待退款', 8, '已申请退款'],

  // 保养订单状态
  UN_CONFIRM: ['未确认', 9, '待确认'],
  CONFIRMING: ['未确认', 13, '已确认，待支付'],
  CONFIRMED: ['已确认', 10, '已确认，待服务'],
  SERVING: ['服务中', 11, '服务中'],
  SERVIED: ['未提车', 12, '服务结束，待提车'],
});

export const OrderListStatus = createEnums({
  ALL: ['全部', -1, ''],

  UNPAID: ['未付款', 0, ''],
  // PAID: ['已付款', 1, ''],

  UNUSED: ['未使用', 1, ''],
  // SERVED: ['已服务', 2, ''],

  TO_BE_COMMENT: ['待评价', 2, ''],

  // REFUNDING: ['退款中', 4, ''],
  REFUND: ['退款', 5, ''],

  // REFUND_APPLIED: ['待退款', 8, '已申请退款'],
});

export const OrderRefundStatus = createEnums({
  REFUNDING: ['处理中', 4, '退款处理中'],
  REFUNDED: ['已退款', 5, '已退款'],
});

export const CarMT = createEnums({
  LEVEL1: ['车辆安全性能维护', 'level1', '车辆安全性能维护'],
  LEVEL2: ['车辆基础性能养护', 'level2', '车辆基础性能养护'],
  LEVEL3: ['车辆性能健康恢复', 'level3', '车辆性能健康恢复'],
});

export const CarMaintainType = createEnums({
  LEVEL1: ['车辆安全性能维护', 'level1', '车辆安全性能维护'],
  LEVEL2: ['车辆基础性能养护', 'level2', '车辆基础性能养护'],
  LEVEL3: ['车辆性能健康恢复', 'level3', '车辆性能健康恢复'],
});

/**
 * 商家服务
 */
export const ShopService = createEnums({
  MAINTAIN_LEVEL1: ['车辆安全性能维护', 1, '小保养'],
  MAINTAIN_LEVEL2: ['车辆基础性能养护', 2, '中保养'],
  MAINTAIN_LEVEL3: ['车辆性能健康恢复', 3, '大保养'],
  SHEET_METAL: ['钣金', 4, '车辆性能健康恢复'],
  REPAIR: ['维修', 5, '车辆性能健康恢复'],
  WASH: ['洗车', 6, '车辆清洗'],
});

export const CommentType = createEnums({
  ALL: ['所有', 'all', '所有评论'],
  WASH: ['洗车', 'wash', '洗车评论'],
  MAINTAIN: ['保养', 'mt', '保养评论'],
});

export const OrdersType = createEnums({
  ALL: ['所有', 'all', '所有'],
  WASH: ['汽车美容', 'wash', '汽车美容'],
  MAINTAIN: ['车辆养护', 'mt', '车辆养护'],
  INSPECTION: ['审车', 'inspection', '审车'],
});

export const OrderType = createEnums({
  WASH: ['洗车', 0, '洗车订单'],
  CHARGE: ['充值', 1, '充值订单'],
  MAINTAIN: ['保养订单', 2, '保养订单'],
  MAINTAIN_RESERVE: ['保养预约', 3, '保养预约订单'], // MaintainReserve
  SHEET_METAL_OR_REPAIR: ['钣金或维修预约', 4, '钣金维修预约订单'],
  VEHICLE_INSPECTION: ['审车订单', 5, '审车预约订单'],
});

// 修改订单后补单类型
export const ReOrderType = createEnums({
  REFUND: ['退款', -1, '退差价'],
  PAY: ['补缴', 1, '补差价'],
});

// 保养门店认证状态
export const ShopLevel = createEnums({
  PRIMARY: ['初级认证', 0, ''],
  MID: ['中级认证', 1, ''],
  HIGH: ['高级认证', 2, ''],
});

// 车辆类型
export const CarType = createEnums({
  OIL: ['油车', 'oil', ''],
  NEW_ENERGY: ['新能源车', 'new_energy', ''],
});
