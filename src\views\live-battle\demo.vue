<template>
  <div id="app" class="container py-4">
    <div class="card">
      <div class="card-body">
        <!-- 连接状态 -->
        <div
          class="alert"
          :class="connectionStatusClass"
          v-if="showConnectionStatus"
        >
          {{ connectionStatusMessage }}
        </div>

        <!-- 登录表单 -->
        <div>
          <h2 class="h4 mb-3">请输入信息加入答题</h2>
          <form @submit.prevent="joinQuiz">
            <div class="form-group mb-3">
              <label for="roomId">房间ID</label>
              <input
                type="text"
                id="roomId"
                v-model="roomId"
                class="form-control"
                placeholder="请输入房间ID"
              />
            </div>
            <div class="form-group mb-3">
              <label for="actionId">活动ID</label>
              <input
                type="text"
                id="actionId"
                v-model="actionId"
                class="form-control"
                placeholder="请输入活动ID"
              />
            </div>
            <div class="form-group mb-3">
              <label for="uid">用户ID</label>
              <input
                type="text"
                id="uid"
                v-model="uid"
                class="form-control"
                placeholder="请输入用户ID"
              />
            </div>
            <div class="form-group mb-3">
              <label for="serverUrl">服务器地址</label>
              <input
                type="text"
                id="serverUrl"
                v-model="serverUrl"
                class="form-control"
                placeholder="例如: http://localhost:8080/quiz-ws"
              />
            </div>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="isConnecting"
            >
              {{ isConnecting ? '连接中...' : '连接websocket' }}
            </button>
          </form>
          <div class="flex-btn">
            <button @click="createRoom">创建房间</button>
            <button @click="joinRoom">加入房间</button>
            <button @click="disconnect">退出</button>
          </div>
        </div>

        <!-- <div class="message-list">
          <h3>接收到的消息：</h3>
          <div v-if="messages && messages.length === 0" class="no-messages">
            暂无消息
          </div>
          <div
            v-else
            class="message"
            v-for="(msg, index) in messages"
            :key="index"
          >
            {{ msg }}
          </div>
        </div> -->
        <!-- 答题界面 -->
        <div v-if="isConnected && currentQuestion">
          <quiz-question
            :question="currentQuestion"
            @submit="handleAnswerSubmit"
          />
        </div>

        <!-- 结果页面 -->
        <div v-if="isConnected && quizResults">
          <div class="score">
            <h2>得分: {{ myScore }}分</h2>
            <h2>对手得分: {{ opponentScore }}分</h2>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { Button, Toast } from 'vant';
import QuizQuestion from './components/QuizQuestion.vue';
import QuizResults from './components/QuizResults.vue';
import { createRoom, joinRoom, getRoomsList } from './api';

export default {
  name: 'LiveBattle',
  components: {
    QuizQuestion,
    QuizResults,
    [Toast.name]: Toast,
  },
  data() {
    let actionId = this.$route.query.id;
    let roomId = this.$route.query.roomId;
    let isOwner = this.$route.query.isOwner === '1';
    let uid = localStorage.getItem('uid');
    return {
      actionId: actionId || '81',
      roomId: roomId || '', // 15580ee004dc4fd88591264a15925c86
      uid: uid || 'cb887bac76d643419ecdcfe01f5972d4',
      serverUrl: '/ws/pk',
      timerId: null,
      isHouseOwner: isOwner, // 是否是房主
    };
  },
  computed: {
    ...mapState('quiz', {
      connectionStatus: state => state.connection,
      quizResults: state => state.quiz.results,
      quizStatus: state => state.quiz.status,
      userId: state => state.user.userId,
      currentQuestionRemainingSeconds: state =>
        state.quiz.currentQuestionRemainingSeconds,
    }),
    ...mapGetters('quiz', ['currentQuestion', 'progress']),
    isConnected() {
      return this.connectionStatus.isConnected;
    },
    isConnecting() {
      return this.connectionStatus.isConnecting;
    },
    myScore() {
      if (this.quizResults && this.userId) {
        return this.isHouseOwner
          ? this.quizResults.promoter.score
          : this.quizResults.opponent.score;
      }
      return 0;
    },
    opponentScore() {
      if (this.quizResults && this.userId) {
        return this.isHouseOwner
          ? this.quizResults.opponent.score
          : this.quizResults.promoter.score;
      }
      return 0;
    },
    connectionError() {
      return this.connectionStatus.error;
    },
    connectionStatusClass() {
      if (this.connectionError) {
        return 'alert-danger';
      }
      return this.isConnected ? 'alert-success' : 'alert-warning';
    },
    connectionStatusMessage() {
      if (this.connectionError) {
        return `连接错误: ${this.connectionError}`;
      }
      if (this.isConnecting) {
        return '正在连接到服务器...';
      }
      return this.isConnected ? '已连接到服务器' : '未连接到服务器';
    },
    showConnectionStatus() {
      return this.isConnecting || this.connectionError || this.isConnected;
    },
  },
  mounted() {
    this.init();
  },
  beforeRouteUpdate(to, from, next) {
    if (to.path === '/live/battle/demo') {
      if (to.query.isOwner === '1') {
        this.isHouseOwner = true;
      }
      if (to.query.roomId) {
        this.roomId = to.query.roomId;
      }
      this.init();
    }
    next();
  },
  methods: {
    ...mapActions('quiz', [
      'connect',
      'disconnect',
      'startQuiz',
      'submitAnswer',
    ]),
    ...mapMutations('quiz', ['SET_USER']),
    async init() {
      try {
        // 如果已有房间ID，直接加入
        if (this.roomId) {
          return this.joinQuiz();
        }

        // 获取房间列表并过滤掉自己创建的房间
        const rooms = (await getRoomsList({ actionId: this.actionId })) || [];
        const availableRooms = rooms.filter(r => r.user1Id !== this.uid);

        // 路由参数
        const routeParams = {
          path: '/live/battle/demo',
          query: {
            actionId: this.actionId,
            roomId: '',
          },
        };

        if (availableRooms.length > 0) {
          // 加入现有房间
          this.roomId = availableRooms[0].id;
          routeParams.query.roomId = this.roomId;
        } else {
          // 创建新房间
          const room = await this.createRoom();
          routeParams.query = {
            ...routeParams.query,
            roomId: this.roomId,
            isOwner: 1,
          };
        }

        // 路由跳转
        await this.$router.push(routeParams);
      } catch (error) {
        console.error('初始化房间失败:', error);
        Toast('初始化房间失败，请重试');
      }
    },
    joinQuiz() {
      if (!this.actionId || !this.serverUrl || !this.uid) {
        Toast('请填写活动ID、服务器地址和用户ID');
        return;
      }

      // 生成随机用户ID
      const userId = 'user_' + Math.random().toString(36).substr(2, 9);

      // 设置用户信息
      this.$store.commit('quiz/SET_USER', {
        id: userId,
        name: this.roomId,
        roomId: this.roomId,
        userId: this.uid,
        isHouseOwner: this.isHouseOwner,
      });

      // 连接到服务器
      this.connect(this.serverUrl);
    },
    createRoom() {
      const actionId = this.actionId;
      const uid = this.uid;
      return createRoom({
        actionId,
        uid,
      }).then(room => {
        this.isHouseOwner = true;
        this.roomId = room.id;
      });
    },
    joinRoom() {
      const roomId = this.roomId;
      const uid = this.uid;
      joinRoom(roomId, {
        uid,
      }).then(room => {
        console.log('🚀 ~ joinRoom ~ room:', room);
      });
    },
    handleAnswerSubmit(answer) {
      this.submitAnswer(answer);
    },
    resetQuiz() {
      this.$store.commit('SET_RESULTS', null);
      this.$store.commit('CLEAR_ANSWERS');
    },
    startTimer() {
      // this.stopTimer();
      // this.timerId = setInterval(() => {
      //   this.decrementTimer();
      // }, 1000);
    },
    stopTimer() {
      if (this.timerId) {
        clearInterval(this.timerId);
        this.timerId = null;
      }
    },
  },
  watch: {
    // quizActive(newVal) {
    //   if (newVal) {
    //     this.startTimer();
    //   } else {
    //     this.stopTimer();
    //   }
    // },
  },
  beforeDestroy() {
    this.stopTimer();
    if (this.isConnected) {
      this.disconnect();
    }
  },
};
</script>
<style lang="scss" scoped>
.flex-btn {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}
form {
  text-align: center;
}
.form-group {
  margin-bottom: 15px;
  text-align: center;
}
input {
  flex: 1;
  padding: 8px;
}

button {
  padding: 8px 15px;
  cursor: pointer;
  margin: 0 10px;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  padding: 10px;
  margin-bottom: 15px;
}

.no-messages {
  color: #999;
  font-style: italic;
}

.message {
  border-bottom: 1px solid #eee;
  padding: 5px 0;
}
</style>
