import { doGet, doPost, postJSON } from '../request/';
import APIs from '../apis';

const debug = false;

const baseParam = {
  sid: 'HN0001'
};

APIs.extend({
  // 获取话题标签列表
  '/forum/tags': '/Radio/label/list',

  // 发表帖子
  '/forum/TopicSave': '/Radio/topic/save',

  // 获取所有人的动态
  '/forum/list': '/Radio/topic/list',

  // 删除帖子
  '/forum/delTopic': '/Radio/my/topic/delete',

  // 点赞
  '/forum/great': '/Radio/topic/great',

  // 获取动态详情
  '/forum/info': '/Radio/topic/detail',
  
  // 获取点赞列表
  '/forum/getGreat': '/Radio/topic/communication/list',

  // 提交评论
  '/forum/replay': '/Radio/topic/reply',

  // 获取通知信息
  '/forum/notice': '/Radio/topic/notice/num/search',

  // 获取我的动态列表
  '/forum/mylist': '/Radio/my/topic/list',

  // 获取动态通知信息
  '/forum/noticeList': '/Radio/topic/unread/list/search',

  // 获取我参与的活动列表
  '/forum/myActive': '/Radio/topic/ofme/list',

  // 取消点赞
  '/forum/ungreat': '/Radio/topic/ungreat',

  // 删除评论
  '/forum/depReplay': '/Radio/topic/my/reply/delete',

  // 举报帖子
  '/forum/report': '/Radio/topic/{topicId}/report',

});

function createUrl(name, data, option) {
  return APIs.get(name, data, option) + '?v=394';
}

// 举报
export function formReport(id, content) {
  return doPost(createUrl('/forum/report', { topicId: id }, { debug: debug }), { content: content });
}

// 删除评论
export function delReplay(id) {
  return doPost(createUrl('/forum/depReplay', {}, { debug: debug }), { id: id });
}

// 取消点赞
export function ungreat(id) {
  return doPost(createUrl('/forum/ungreat', {}, { debug: debug }), { id: id });
}

// 我参与的列表
export function getMyActiveList(options) {
  let defaults = {
    rows: 10,
    page: 1
  };
  const params = Object.assign(defaults, options);
  return doGet(createUrl('/forum/myActive', {}, { debug: debug }), params);
}

// 获取动态通知信息
export function getNoticeList(options) {
  let defaults = {
    rows: 10,
    page: 1
  };
  const params = Object.assign(defaults, options);
  return doGet(createUrl('/forum/noticeList', {}, { debug: debug }), params);
}

// 获取我的动态列表
export function getMyList(options) {
  let defaults = {
    rows: 10,
    page: 1
  };
  const params = Object.assign(defaults, options);
  return doGet(createUrl('/forum/mylist', {}, { debug: debug }), params);
}

// 获取通知信息
export function getNoticeInfo() {
  return doGet(createUrl('/forum/notice', {}, { debug: debug }));
}

// 提交评论
export function replay(data) {
  return doPost(createUrl('/forum/replay', {}, { debug: debug }), data);
}

// 获取评论列表
export function getReplay(options) {
  let defaults = {
    rows: 10,
    page: 1,
    type: 1
  };
  console.log(options);
  const params = Object.assign(defaults, options);
  return doGet(createUrl('/forum/getGreat', {}, { debug: debug }), params);
}

// 获取点赞列表
export function getGreat(options) {
  let defaults = {
    rows: 10,
    page: 1,
    type: 0
  };
  const params = Object.assign(defaults, options);
  return doGet(createUrl('/forum/getGreat', {}, { debug: debug }), params);
}

// 获取动态详情
export function getInfo(id) {
  return doGet(createUrl('/forum/info', {}, { debug: debug }), { id: id });
}

// 点赞
export function great(id) {
  return doPost(createUrl('/forum/great', {}, { debug: debug }), { id: id });
}

// 删除帖子
export function delTopic(id) {
  return doPost(createUrl('/forum/delTopic', {}, { debug: debug }), { id: id });
}

// 获取所有人的动态信息
export function getList(options) {
  let defaults = {
    row: 10,
    page: 1
  };
  const params = Object.assign(defaults, options, baseParam);
  return doGet(createUrl('/forum/list', {}, { debug: debug }), params);
}

// 保持动态信息
export function saveTopic(data) {
  return doPost(createUrl('/forum/TopicSave', {}, { debug: debug }), data);
}

// 获取话题标签列表
export function getTags() {
  return doGet(createUrl('/forum/tags', {}, { debug: debug }));
} 
