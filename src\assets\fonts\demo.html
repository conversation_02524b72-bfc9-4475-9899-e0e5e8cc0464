
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <link rel="stylesheet" href="iconfont.css">
</head>
<body>
    <div class="main">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">

                <li>
                <i class="icon iconfont">&#xe600;</i>
                    <div class="name">搜索</div>
                    <div class="code">&amp;#xe600;</div>
                    <div class="fontclass">.sousuo</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe601;</i>
                    <div class="name">警告</div>
                    <div class="code">&amp;#xe601;</div>
                    <div class="fontclass">.jinggao</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe602;</i>
                    <div class="name">关闭</div>
                    <div class="code">&amp;#xe602;</div>
                    <div class="fontclass">.guanbi</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe612;</i>
                    <div class="name">电话</div>
                    <div class="code">&amp;#xe612;</div>
                    <div class="fontclass">.dianhua</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe603;</i>
                    <div class="name">公告</div>
                    <div class="code">&amp;#xe603;</div>
                    <div class="fontclass">.gonggao</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe604;</i>
                    <div class="name">左箭头</div>
                    <div class="code">&amp;#xe604;</div>
                    <div class="fontclass">.zuojiantou</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe605;</i>
                    <div class="name">右箭头</div>
                    <div class="code">&amp;#xe605;</div>
                    <div class="fontclass">.youjiantou</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe606;</i>
                    <div class="name">上箭头</div>
                    <div class="code">&amp;#xe606;</div>
                    <div class="fontclass">.shangjiantou</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe607;</i>
                    <div class="name">下箭头</div>
                    <div class="code">&amp;#xe607;</div>
                    <div class="fontclass">.xiajiantou</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe608;</i>
                    <div class="name">收藏</div>
                    <div class="code">&amp;#xe608;</div>
                    <div class="fontclass">.shoucang</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe609;</i>
                    <div class="name">时间</div>
                    <div class="code">&amp;#xe609;</div>
                    <div class="fontclass">.iconfont74</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe60a;</i>
                    <div class="name">维护管理</div>
                    <div class="code">&amp;#xe60a;</div>
                    <div class="fontclass">.weihuguanli</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe60b;</i>
                    <div class="name">交通</div>
                    <div class="code">&amp;#xe60b;</div>
                    <div class="fontclass">.jiaotong</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe60c;</i>
                    <div class="name">Loading</div>
                    <div class="code">&amp;#xe60c;</div>
                    <div class="fontclass">.loading</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe613;</i>
                    <div class="name">地图</div>
                    <div class="code">&amp;#xe613;</div>
                    <div class="fontclass">.ditu</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe60d;</i>
                    <div class="name">收藏</div>
                    <div class="code">&amp;#xe60d;</div>
                    <div class="fontclass">.shoucang1</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe60e;</i>
                    <div class="name">回复评价</div>
                    <div class="code">&amp;#xe60e;</div>
                    <div class="fontclass">.p-reply-comment</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe60f;</i>
                    <div class="name">正确</div>
                    <div class="code">&amp;#xe60f;</div>
                    <div class="fontclass">.zhengque</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe610;</i>
                    <div class="name">回复</div>
                    <div class="code">&amp;#xe610;</div>
                    <div class="fontclass">.huifu</div>
                </li>

                <li>
                <i class="icon iconfont">&#xe611;</i>
                    <div class="name">通知</div>
                    <div class="code">&amp;#xe611;</div>
                    <div class="fontclass">.tongzhi</div>
                </li>

        </ul>


        <div class="helps">
            第一步：使用font-face声明字体
            <pre>
@font-face {font-family: 'iconfont';
    src: url('iconfont.eot'); /* IE9*/
    src: url('iconfont.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('iconfont.woff') format('woff'), /* chrome、firefox */
    url('iconfont.ttf') format('truetype'), /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
    url('iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
}
</pre>
第二步：定义使用iconfont的样式
            <pre>
.iconfont{
    font-family:"iconfont" !important;
    font-size:16px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;}
</pre>
第三步：挑选相应图标并获取字体编码，应用于页面
<pre>
&lt;i class="iconfont"&gt;&amp;#x33;&lt;/i&gt;
</pre>
        </div>

    </div>
</body>
</html>
