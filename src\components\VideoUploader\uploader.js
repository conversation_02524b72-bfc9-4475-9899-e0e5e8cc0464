// import FileAPI from '@/lib/FileAPI.html5'
import upload from '../Uploader/weui-uploader/upload'
import { captureExpectionWithData } from '@/utils/raven';
import captureVideoFrame from './lib/capture-video-frame';

const URL = window.URL || window.webkitURL || window.mozURL;

const FileStatus = {
  NONE: 'none',
  UPLOADING: 'uploading',
  FAILED: 'failed',
  UPLOADED: 'uploaded'
}

function createFileItem(data) {
  return {
    id: Math.random() * 1000,
    percent: 100,
    origin: null,
    ...data,
    msg: '',
    status: FileStatus.UPLOADED
  }
}

function transformFileList(files) {
  return files.map(function (item, i) {
    return createFileItem({
      file: item.file,
      poster: item.poster
    });
  })
}

function getThumbnail(file) {
  return captureVideoFrame(file, 'image/jpge', 0.5).then(blob => {
    return URL.createObjectURL(blob);
  })
}

function getIdByFile({type, name, size, lastModified}) {
  return [type, name, size, lastModified].join('_')
}

function formatFileSize(size) {
  return Math.round(size / 1024) + 'KB'
}

function noop() {}
function noopPromise() {
  return new Promise((resolve, reject) => {
    resolve();
  });
}

export default {
  name: 'VideoUploader',
  props: {
    onAdd: {
      type: Function,
      default: noop,
    },
    // Function<Promise>
    onRemove: {
      type: Function,
      default: noopPromise,
    },
    onError: {
      type: Function,
      default: noop,
    },
    onPreview: {
      type: Function,
      default: noop,
    },
    onUploaded: {
      type: Function,
      default: noop,
    },
    // Function<Promise>
    beforeUpload: {
      type: Function,
      default: noopPromise,
    },
    imageUrlGetter: {
      type: Function,
      default(value) {
        return value;
      },
    },
    autoUpload: {
      type: Boolean,
      default: false,
    },
    files: {
      type: Array,
      default() {
        return [];
      },
    },
    uploadUrl: {
      type: String,
      default: '/',
    },
    uploadName: {
      type: String,
      default: 'file',
    },
    params: {
      type: Object,
      default() {
        return {};
      },
    },
    name: {
      type: String,
      default: 'file',
    },
    // 单张图片大小 byte
    maxFileSize: {
      type: Number,
      default: 1024 * 1024 * 10,
    },
    // 最大上传数量
    maxFiles: {
      type: Number,
      default: 1,
    },
    // value: {
    //   type: Array,
    // },
  },
  data() {
    // 默认文件列表
    return {
      list: [],
      uploadQueue: new Map(),
    }
  },
  computed: {
    values() {
      return this.list.filter(item => item.status == FileStatus.UPLOADED).map(item => item.file);
    },
    avaliableAdditionalImageCount() {
      return this.maxFiles - this.list.length;
    },
    couldMultipleSelect(){
      return this.multiple && this.maxFiles > 1;
    },
  },
  watch: {
    files(val, oldVal) {
      console.warn('files change:', val, oldVal);
      const a = JSON.stringify(val);
      const b = JSON.stringify(oldVal);
      if (a !== b) {
        this.$nextTick(() => {
          this.setList(transformFileList(val))
        })
      }
    },
  },
  mounted () {
    console.log('ready...')
    const fileInput = this.$el.querySelector('input[type="file"]');
    this.initFileInput(fileInput)

    const list = transformFileList(this.files);
    this.setList(list);
  },
  methods: {
    clear() {
      this.list.forEach(item => {
        this.removeFile(item);
      })
    },
    setList(list) {
      this.list = list;
      this.doChange();
    },
    isFileSizeOk(size) {
      return size <= this.maxFileSize;
    },
    getFileClass(item) {
      return {
        'file file-video': true,
        'file-uploading': item.status == FileStatus.UPLOADING,
        'file-upload-fail': item.status == FileStatus.FAILED,
        'file-uploaded': item.status == FileStatus.UPLOADED,
      }
    },

    handleClick(item, index) {
      if (item.status == FileStatus.FAILED) {
        this.reUploadFile(item)
      } else if (item.status == FileStatus.UPLOADED) {
        const files = this.list.filter(item => item.status === FileStatus.UPLOADED).map(item => item.file)
        this.onPreview(item.file, index, files);
      }
    },

    /**
     * 上传重试
     */
    reUploadFile(item){
      const file = item.origin;
      this.updateFile(file, {
        status: FileStatus.UPLOADING,
        percent: 0
      })
      this.uploadOneFile(file, 'reupload');
    },

    clearFileInput(){
      this.$file.value = ''
    },

    initFileInput($file) {
      let that = this;
      this.$file = $file;
      $file.addEventListener('change', (evt) => {
        console.log('file change...', evt)
        var files = Array.prototype.slice.call($file.files); // Retrieve file list
        if (files.length && files.length > that.avaliableAdditionalImageCount) {
          let tip = '最多能选择添加' + (that.avaliableAdditionalImageCount) + '个视频'
          that.handleError(tip)
          console.warn(tip)
          return
        }
        files.forEach(file => {
          if (file.type && !/^video/.test(file.type)) {
            that.handleError(file.name + '不是合法的视频文件！')
            return false
          }
          else if (!that.isFileSizeOk(file.size)) {
            let tip = '单个视频最大' + formatFileSize(that.maxFileSize)
            that.handleError(tip)
            return false
          }
          // 添加文件时会先生成视频缩略图，可能较为耗时，等待生成完成再上传否则可能出现上传已成功，但缩略图还没生成的情况
          that.addFile(file).then(res => {
            that.uploadOneFile(file);
          })
          return true
        })
      })
    },
    addFile(file){
      const that = this
      const fileId = getIdByFile(file)
      // 获取缩略图较为耗时
      return getThumbnail(file).then(thumbnail => {
        that.list.push({
          id: fileId,
          percent: 0,
          origin: file,
          // file: minImg && minImg.toDataURL(),
          file: '',
          poster: thumbnail,
          msg: '',
          status: FileStatus.NONE,
        })
      }).catch(e => {
        that.handleError(e);
      })
    },
    updateFile(file, data) {
      const fileId = getIdByFile(file)
      this.list = this.list.map(item => {
        // console.log(item.id, fileId, item.id == fileId)
        if (item.id == fileId) {
          // 上传成功后释放内存
          if (data.status === FileStatus.UPLOADED) {
            URL.revokeObjectURL(item.file);
          }
          item = Object.assign(item, data)
        }
        return item
      });
      if (data && data.status == FileStatus.UPLOADED) {
        console.log('updateFile: ', file, fileId, data)
        this.uploadQueue.delete(file);
      }
      this.doChange();
      this.doInput();
    },
    doInput() {
      const list = this.list.filter(item => item.status === FileStatus.UPLOADED).map(item => {
        return {
          poster: item.poster,
          file: item.file,
        }
      })
      this.$emit('input', list);
    },
    doChange() {
      this.$emit('change', this.list);
    },
    uploadOneFile(file, action = 'upload') {
      const that = this;
      const next = function () {
        return new Promise((resolve, reject) => {
          that.uploadFile(file).then(result => {
            that.updateFile(file, {
              file: result,
              status: FileStatus.UPLOADED
            });
            that.onAdd.call(null, result);
            resolve(result);
          }).catch(err => {
            captureExpectionWithData(err, {
              file,
              action,
            }, 'file-upload');
            that.handleError(err);
            that.updateFile(file, {
              status: FileStatus.FAILED,
              msg: err
            });
            reject(err);
          });
        })
      };
      this.beforeUpload(file, action).then(res => {
        if (this.autoUpload || action === 'reupload') {
          next();
        } else {
          this.uploadQueue.set(file, next);
        }
      }).catch(this.onError);
    },
    submit() {
      const queue = [];
      const queueManager = this.uploadQueue;
      for (let next of queueManager.values()) {
        queue.push(next());
      }
      return Promise.all(queue).then(files => {
        queueManager.clear();
      })
    },
    uploadFile(originFile) {
      const that = this;
      return new Promise((resolve, reject) => {
        that.updateFile(originFile, {
          status: FileStatus.UPLOADING,
        });
        const options = {
          url: that.uploadUrl,
          type: 'file',
          file: originFile,
          fileVal: that.uploadName,
          auto: true,
          xhrFields: {},
          onBeforeSend(file, data, headers) {
            return Object.assign(data, that.params);
          },
          onProgress (file, percent){
            that.updateFile(file, {
              percent: percent,
              status: FileStatus.UPLOADING,
            })
          },
          onError (file, err) {
            reject(err);
          },
          onSuccess: function (file, res) {
            that.clearFileInput()
            let fileStatus = FileStatus.FAILED
            if (res) {
              let result = that.onUploaded.call(null, res);
              fileStatus = FileStatus.UPLOADED
              resolve(result)
            }
            // that.updateFile(file, {
            //   status: fileStatus
            // })
          }
        }
        upload(options);
      })
    },
    removeFile(fileItem) {
      return this.onRemove(fileItem).then(res => {
        this.uploadQueue.delete(fileItem.origin);
        return Promise.resolve();
      }).then(res => {
        const nlist = this.list.filter(function (item) {
          return item.id != fileItem.id;
        })
        this.setList(nlist);
        this.doInput();
      })
    },
    handleError(err){
      this.clearFileInput();
      this.onError.call(null, err);
    },
  },
}
