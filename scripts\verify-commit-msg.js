const chalk = require('chalk')
const msgPath = process.env.HUSKY_GIT_PARAMS
console.warn(msgPath)
const msg = require('fs').readFileSync(msgPath, 'utf-8').trim()

const commitRE = /^(revert: )?(feat|fix|polish|docs|style|refactor|perf|test|workflow|ci|chore|types|build|save)(\(.+\))?: .{1,50}/

if (!commitRE.test(msg) && !msg.startsWith('Merge branch')) {
  console.log()
  console.error(
    `  ${chalk.bgRed.white(' ERROR ')} ${chalk.red('invalid commit message format.(提交信息格式不合规范)')}\n\n` +
    chalk.red('  Proper commit message format is required for automated changelog generation. Examples:\n\n') +
    `    ${chalk.green('feat(compiler): add \'comments\' option')}\n` +
    `    ${chalk.green('fix(v-model): handle events on blur (close #28)')}\n\n` +
    // chalk.red(`  See .github/COMMIT_CONVENTION.md for more details.\n`) +
    chalk.green('  Proper commit type: revert|feat|fix|polish|docs|style|refactor|perf|test|workflow|ci|chore|types|build|save.\n') +
    chalk.red(` 提示： 执行 ${chalk.cyan('npm run commit')} 可进入交互式commit模式.\n`)
  )
  process.exit(1)
}
