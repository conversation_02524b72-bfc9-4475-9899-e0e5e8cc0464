import { handleError } from '../error-handler';

export default [
  {
    path: '/qr-pay/help',
    name: '乘车码使用帮助',
    component: resolve => {
      import(/* webpackChunkName: "qrpay-help" */ '@/views/packages/qr-pay/Help.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/qr-pay',
    name: '付款码',
    component: resolve => {
      import(/* webpackChunkName: "bus-ticket" */ '@/views/packages/qr-pay/BusTicket.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/qr-pay/card/bind',
    name: '绑定银行卡',
    component: resolve => {
      import(/* webpackChunkName: "card-bind" */ '@/views/packages/qr-pay/CardBind.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/qr-pay/card/list/:mode?',
    name: '我的银行卡',
    component: resolve => {
      import(/* webpackChunkName: "card-list" */ '@/views/packages/qr-pay/CardList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/qr-pay/trade/list',
    name: '我的消费记录',
    component: resolve => {
      import(/* webpackChunkName: "card-trade-list" */ '@/views/packages/qr-pay/CardTradeList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/qr-pay/trade/:qr',
    name: 'QRTradeDetail',
    component: resolve => {
      import(/* webpackChunkName: "card-trade-info" */ '@/views/packages/qr-pay/CardTradeDetail.vue').then(resolve).catch(handleError);
    },
  },
];
