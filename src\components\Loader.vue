<template>
    <div v-show="show">
      <div class="weui-mask_transparent"></div>
      <div class="weui-toast">
        <i class="weui-loading weui-icon_toast"></i>
        <p class="weui-toast__content">{{content}}</p>
      </div>
    </div>
</template>
<style scoped>
  .loading {
    position: fixed;
    padding: 30px;
    text-align: center;
    left: 50%;
    transform: translateX(-50%) translateY(50%);
    top: 50%;
  }
</style>
<script>
    export default {
      name: 'Loader',
      props: {
        show: Boolean,
        content: {
          type: String,
          default: '加载中...',
        },
      },
      watch: {
        show(val, oldVal) {
          // this.$overlay(val);
        }
      }
    };
</script>
