<template>
  <div class="biz-address-picker">
    <slot>
      <template v-if="addressModel">
        <div class="address" @click="$_auth_push(`/account/address/select/${addressModel.id}`)">
          <div class="address-contact">{{addressModel.name}} {{addressModel.phone}}</div>
          <div class="address-detail">{{addressModel.province}} {{addressModel.city}} {{addressModel.area}} {{addressModel.address}}</div>
        </div>
      </template>
      <div v-else class="address" @click="$_auth_push(`/account/address/select`)">{{placeholder}}</div>
      <div v-if="disabled" class="disabled-mask"></div>
    </slot>
  </div>
</template>

<script>
import { mixinAuthRouter } from '@/mixins';
import { dialog, toast, loading, onSelectAddress } from '@/bus';
import { getAddressList, getAddress } from '@/api/modules/address';

/**
 * 地址选择器
 */
export default {
  name: 'BizAddressPicker',
  mixins: [mixinAuthRouter],
  props: {
    placeholder: {
      type: String,
      default: '请选择',
    },
    defaultAddress: {
      type: [String, Number],
    },
    resume: {
      type: [String, Number],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      addressModel: null,
    };
  },
  watch: {
    addressModel(val) {
      this.$emit('input', val);
    },
    resume(val) {
      // 解决多页面同时使用BizAddressPicker时无法更新选择地址的bug
      // 例：商品详情(含有地址选择)——>提交订单(含有地址选择)——>返回商品详情，会导致商品详情的地址选择失效
      // 原因是进入提交订单时，onSelectAddress会将详情页的地址选择函数注销，bus——>biz.js——>onSelectAddress函数
      onSelectAddress(item => {
        this.addressModel = item;
        console.info('onresume select address :', item);
      });
    }
  },
  mounted() {
    onSelectAddress(item => {
      this.addressModel = item;
      console.info('select address :', item);
    });
    this.init();
  },
  methods: {
    init() {
      getAddressList().then(addressList => {
        this.addressList = addressList;
        const result = addressList.reduce((last, item, index, array) => {
          if (item.id == this.defaultAddress) {
            last = { ...item };
          } else if (item.defaulted) {
            last = { ...item };
          }
          return last;
        }, {});
        this.addressModel = result.id ? result : addressList[0];
      }).catch(err => {
        console.error(err);
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.biz-address-picker{
  position: relative;
}
  .address {
    .address-contact {
      font-weight: 700;
    }
    .address-detail {
      font-size: 0.8em;
      color: #6f6f6f;
      max-width: 100%;
      white-space: nowrap;
      text-overflow:ellipsis;
      overflow: hidden;
    }
  }
  .disabled-mask{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
  }
</style>
