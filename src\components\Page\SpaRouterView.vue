
<template>
  <keep-alive :include="aliveViews">
    <router-view></router-view>
  </keep-alive>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import { TransitionMode } from '@/enums';
import { bind, emit, Events, back, loading, onRouteView, onRouteBack } from '@/bus';
import { captureExpectionWithData } from '@/utils';
import { popWebView, couldUseNative } from '@/bridge';
import ENV from '@/common/env';

export default {
  name: 'SpaRouterView',
  data() {
    return {
      isBackButtonDisabled: false,
      firstLoad: true,
      transitionName: 'fade',
      isPageBackingEnd: false,
      index: '/',
      paths: [],
    };
  },
  computed: {
    ...mapGetters(['aliveViews', 'routePaths']),
  },
  watch: {
    $route(to, from) {
      console.log('watch:', to.path);
      if (to.path === '/error') {
        // this.$router.replace(this.index);
        console.log('error route:', from);
        return;
      }
      let mode = TransitionMode.INIT;
      if (this.firstLoad) {
        this.paths.push(to.path);
      } else if (this.isPageBackingEnd) {
        // this.transitionName = 'pop';
        // mode = TransitionMode.BACK;
        // emit(Events.VIEW_BACK_END);
        this.paths.pop();
      } else {
        // mode = TransitionMode.FORWARD;
        // this.transitionName = 'push';
        this.paths.push(to.path);
      }
      this.firstLoad = false;
    },
  },
  mounted() {
    // back()方法触发，如按下左上角返回键
    // TODO: 返回控制的逻辑过于繁琐复杂，容易出现问题，需要重新设计返回控制
    const couldUseNativeMethod = couldUseNative();
    bind(Events.VIEW_BACK_START, (shouldGoBack) => {
      // console.log('isBackButtonDisabled:', this.isBackButtonDisabled, 'isPageBackingEnd:', this.isPageBackingEnd);
      // 如果禁用了返回按钮，直接return，不执行后续操作
      if (this.isBackButtonDisabled || this.isPageBackingEnd) {
        captureExpectionWithData('back event problem!', {
          'couldUseNativeMethod': couldUseNativeMethod,
          'isBackButtonDisabled': this.isBackButtonDisabled,
          'isPageBackingEnd': this.isPageBackingEnd,
          'shouldGoBack': shouldGoBack,
        })
      }
      // if (this.isBackButtonDisabled) return;

      // 返回动画未结束，不能执行返回操作，否则container组件状态判断会有问题
      if (this.isPageBackingEnd) return;

      this.isPageBackingEnd = true;
      // this.setTransitionName('pop');
      // this.setTransitionMode(TransitionMode.BACK);

      if (shouldGoBack !== false) {
        // 目前，通过replace方式跳转路由时，routePaths不正常
        if (couldUseNativeMethod /* && this.routePaths.length === 1 */) {
          popWebView(-1);
        } else {
          this.$router.back();
        }
      }
    });

    // 页面切换动画完成
    const afterPageTransitionEnd = () => {
      this.isPageBackingEnd = false;
      this.setTransitionName('pop');
      this.setTransitionMode(TransitionMode.BACK);
    }

    /**
     * Page 组件中会在动画结束后触发 VIEW_TRANSITION_END 事件
     * 为什么不在router-view外部使用transition组件？
     * 页面切换动画需要大约250ms时间，具体取决于css中设置的动画时长，
     * Page组件会在动画结束后触发ready事件，规定页面所有后续操作都在ready事件触发后进行，
     * 如果在vue的mounted事件中操作，可能会由于终端性能不足导致页面切换动画卡顿
     * 所以最终在Page.vue中使用transition组件，而不是在router-view外部使用
     */
    bind(Events.VIEW_TRANSITION_END, e => {
      afterPageTransitionEnd();
    });

    // 每次子路由页面返回后触发
    bind(Events.SUB_ROUTE_BACK, e => {
      afterPageTransitionEnd();
    });

    // android5以及以下系统webview，不能正常触发，
    document.addEventListener('visibilitychange', (event) => {
      this.onPageVisiblityChange(document.hidden);
    });

    /**
     * 页面默认动画为pop，进行push或replace操作时更改动画模式为push
     * 为什么不适用router.hooks? 因为router.hooks中无法判断页面是后退还是前进
     * android虚拟按键会触发back，也会导致路由发生变化
     */
    const beforePageGo = () => {
      this.isPageBackingEnd = false;
      this.setTransitionName('push');
      this.setTransitionMode(TransitionMode.FORWARD);
    }
    // 拦截 push和replace方法，注入 beforePageGo逻辑
    const $router = this.$router;
    this.$router.push = function(...args) {
      console.log('push...')
      beforePageGo();
      // clearTimeout(this._push_timeCode);
      // 注：transition组件的name值变更后似乎需要一定时间反应，若反应时间不够，其动画可能与其name不一致
      // 测试发现，delay值较小时，在iOS12端的webview中，切换页面，能稳定复现transition组件的此问题，增大此值到一定程度，异常现象降低，但似乎偶尔还会出现异常
      // iOS12以下，暂未见异常
      // 使用setTimeout，将push操作推迟到下一轮event-loop，确保 beforePageGo 中的操作生效后再进行页面切换，确保动画正常
      this._push_timeCode = setTimeout(() => {
        // VueRouter.prototype.push.call(this, ...args);
        Object.getPrototypeOf($router).push.call(this, ...args)
      }, 10);
    }
    this.$router.replace = function(...args) {
      console.log('replace...')
      beforePageGo();
      this._push_timeCode = setTimeout(() => {
        Object.getPrototypeOf($router).replace.call(this, ...args)
      }, 10);
    }

    this.$router.beforeEach((to, from, next) => {
      console.log('before-each')
      // 禁用了返回按钮后阻止路由跳转，一般用于展示浮层组件时使用
      if (this.isBackButtonDisabled) {
        next(false);
        loading(false);
      } else {
        next()
      }
    });
    this.$router.afterEach(() => {
      console.log('afterEach: ', ...arguments)
    })
    // 返回事件的控制
    bind(Events.DISABLE_BACK, e => {
      this.isBackButtonDisabled = true;
    });
    bind(Events.ENABLE_BACK, e => {
      this.isBackButtonDisabled = false;
    });

    onRouteView(this.routeTo);
    onRouteBack(this.routeBack);
  },
  updated() {
    this.firstLoad = false;
  },
  methods: {
    ...mapMutations(['setTransitionName', 'setTransitionMode']),
    onPageVisiblityChange(hidden = false) {
      console.warn('visibilitychange', hidden);
      if (hidden) {
        emit(Events.PAGE_HIDDEN);
      } else {
        emit(Events.PAGE_VISIBLE);
      }
    },

    /**
       * 若要跳转的路径是上一级，使用back返回
       * 否则使用replace
       */
    routeTo(path) {
      const prevPath = this.routePaths.slice(-2)[0];
      // console.warn(this.routePaths, prevPath, path, prevPath === path);
      if (prevPath === path) {
        if (ENV.jglh) {
          back();
          // popWebView(0);
        } else {
          back();
        }
      } else {
        this.$router.replace(path);
      }
    },
    routeBack() {
      if (this.routePaths.length === 1) {
        if (ENV.jglh) {
          popWebView(0);
        } else {
          back();
        }
      } else {
        back();
      }
    },
    getHomeURL(value) {
      const homes = {
        '/': 1,
        '/orders': 1,
        '/account/home': 1,
        '/shop': 1,
      };
      return value in homes ? value : '/';
    },
  },
};
</script>
