/**
 * 充电站相关混入
 * 提供充电站页面的通用功能和方法
 */

import { getLocation } from '@/bridge';
import { toast } from '@/bus';
import { 
  formatDistance, 
  getTagType, 
  getStatusText, 
  getServiceIcon,
  generateNavigationUrl,
  storage,
} from '../utils';
import { 
  STORAGE_KEYS, 
  ERROR_MESSAGES,
  SORT_OPTIONS,
} from '../constants';

export default {
  data() {
    return {
      // 地理位置信息
      geo: {
        lat: null,
        lng: null,
      },
      
      // 位置获取状态
      locationStatus: 'idle', // idle, loading, success, error
    };
  },
  
  computed: {
    // 排序选项
    sortOptions() {
      return SORT_OPTIONS;
    },
  },
  
  methods: {
    // 工具函数
    formatDistance,
    getTagType,
    getStatusText,
    getServiceIcon,
    
    /**
     * 获取用户地理位置
     * @returns {Promise} 位置信息
     */
    async getUserLocation() {
      if (this.locationStatus === 'loading') {
        return Promise.reject(new Error('正在获取位置中...'));
      }
      
      this.locationStatus = 'loading';
      
      try {
        // 先尝试从缓存获取
        const cachedLocation = storage.get(STORAGE_KEYS.USER_LOCATION);
        const now = Date.now();
        
        // 如果缓存存在且未过期（5分钟内）
        if (cachedLocation && (now - cachedLocation.timestamp) < 5 * 60 * 1000) {
          this.geo = {
            lat: cachedLocation.lat,
            lng: cachedLocation.lng,
          };
          this.locationStatus = 'success';
          return this.geo;
        }
        
        // 获取新的位置信息
        const location = await getLocation();
        
        this.geo = {
          lat: location.latitude,
          lng: location.longitude,
        };
        
        // 缓存位置信息
        storage.set(STORAGE_KEYS.USER_LOCATION, {
          lat: location.latitude,
          lng: location.longitude,
          timestamp: now,
        });
        
        this.locationStatus = 'success';
        return this.geo;
        
      } catch (error) {
        this.locationStatus = 'error';
        console.error('获取位置失败:', error);
        toast().tip(ERROR_MESSAGES.LOCATION_DENIED);
        throw error;
      }
    },
    
    /**
     * 导航到充电站
     * @param {Object} station 充电站信息
     */
    navigateToStation(station) {
      try {
        // 优先使用原生地图导航
        if (window.jsBridge && window.jsBridge.openMap) {
          window.jsBridge.openMap({
            latitude: station.lat,
            longitude: station.lng,
            name: station.name,
            address: station.address,
          });
        } else {
          // 备用方案：打开网页版地图
          const url = generateNavigationUrl(station.lng, station.lat, station.name);
          window.open(url);
        }
      } catch (error) {
        console.error('导航失败:', error);
        toast().tip(ERROR_MESSAGES.NAVIGATION_ERROR);
      }
    },
    
    /**
     * 拨打充电站电话
     * @param {string} phone 电话号码
     */
    callStation(phone) {
      if (!phone) {
        toast().tip('暂无联系电话');
        return;
      }
      
      try {
        window.location.href = `tel:${phone}`;
      } catch (error) {
        console.error('拨打电话失败:', error);
        toast().tip('拨打电话失败');
      }
    },
    
    /**
     * 分享充电站信息
     * @param {Object} station 充电站信息
     */
    shareStation(station) {
      const shareData = {
        title: station.name,
        text: `${station.name} - ${station.address}`,
        url: window.location.href,
      };
      
      if (navigator.share) {
        navigator.share(shareData).catch(error => {
          console.error('分享失败:', error);
          this.fallbackShare(shareData);
        });
      } else {
        this.fallbackShare(shareData);
      }
    },
    
    /**
     * 备用分享方案
     * @param {Object} shareData 分享数据
     */
    fallbackShare(shareData) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(shareData.url).then(() => {
          toast().tip('链接已复制到剪贴板');
        }).catch(() => {
          toast().tip('分享功能暂不支持');
        });
      } else {
        toast().tip('分享功能暂不支持');
      }
    },
    
    /**
     * 计算充电站距离
     * @param {Object} station 充电站信息
     * @returns {number} 距离（米）
     */
    calculateStationDistance(station) {
      if (!this.geo.lat || !this.geo.lng || !station.lat || !station.lng) {
        return null;
      }
      
      const R = 6371000; // 地球半径（米）
      const dLat = (station.lat - this.geo.lat) * Math.PI / 180;
      const dLng = (station.lng - this.geo.lng) * Math.PI / 180;
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.geo.lat * Math.PI / 180) * Math.cos(station.lat * Math.PI / 180) *
        Math.sin(dLng / 2) * Math.sin(dLng / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    },
    
    /**
     * 检查充电站是否营业
     * @param {Object} station 充电站信息
     * @returns {boolean} 是否营业
     */
    isStationOpen(station) {
      if (station.status !== 'open') return false;
      if (station.businessHours === '24小时营业') return true;
      
      // 简单的营业时间判断
      const now = new Date();
      const currentHour = now.getHours();
      return currentHour >= 6 && currentHour < 22;
    },
    
    /**
     * 获取充电站可用性信息
     * @param {Object} station 充电站信息
     * @returns {Object} 可用性信息
     */
    getStationAvailability(station) {
      const totalAvailable = (station.fastCharging?.available || 0) + 
                            (station.slowCharging?.available || 0);
      const totalPiles = (station.fastCharging?.total || 0) + 
                        (station.slowCharging?.total || 0);
      
      if (totalPiles === 0) {
        return { status: 'unknown', text: '未知', color: '#999' };
      }
      
      const ratio = totalAvailable / totalPiles;
      
      if (ratio >= 0.5) {
        return { status: 'available', text: '充足', color: '#52c41a' };
      } else if (ratio >= 0.2) {
        return { status: 'limited', text: '紧张', color: '#faad14' };
      } else if (ratio > 0) {
        return { status: 'few', text: '稀少', color: '#ff7a45' };
      } else {
        return { status: 'unavailable', text: '暂无', color: '#ff4d4f' };
      }
    },
    
    /**
     * 保存用户偏好设置
     * @param {string} key 设置键名
     * @param {any} value 设置值
     */
    saveUserPreference(key, value) {
      storage.set(key, value);
    },
    
    /**
     * 获取用户偏好设置
     * @param {string} key 设置键名
     * @param {any} defaultValue 默认值
     * @returns {any} 设置值
     */
    getUserPreference(key, defaultValue = null) {
      return storage.get(key, defaultValue);
    },
  },
  
  // 组件销毁时清理
  beforeDestroy() {
    // 清理定时器等资源
    if (this.locationTimer) {
      clearTimeout(this.locationTimer);
    }
  },
};
