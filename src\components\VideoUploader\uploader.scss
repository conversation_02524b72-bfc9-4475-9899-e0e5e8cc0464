/*upload style*/
.file-container {
  font:12px/1.5 "\5fae\8f6f\96c5\9ed1","Microsoft YaHei",tahoma,arial,"Hiragino Sans GB",\5b8b\4f53;
}

.file {
  position: relative;
  width: 75px;
  height: 75px;
  // border: 1px solid #E8E7E7;
  border-radius: 2px;
  margin-right: 1%;
  margin-bottom:5px;
  float: left;
  box-sizing: border-box;
  // display: inline-block;
}
/* .file-video {
  &::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: url(./images/play.svg) center center no-repeat rgba(0, 0, 0, 0.5);
    background-size: 50%;
  }
} */
.video-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  color: white;
  transform: translate(-50%, -50%);
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .6);
    border-radius: 50%;
  }
  &::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 0 10px 20px;
    border-color: transparent transparent transparent white;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

.file img {
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  z-index:2;
}

.file-uploading {
  &::after {
    content: attr(percent) "%";
    position: absolute;
    left: 0;
    top: 0;
    z-index: 5;
    text-align: center;
    width: 100%;
    height: 100%;
    color: rgb(255, 255, 255);
    background: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    display: flex;
  }
  .uploading-frame {
    height: 100%;
    position: absolute;
    bottom: 0;
    z-index: 5;
    text-align: center;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
  }
  .video-icon {
    display: none;
  }
}

.file-upload-fail:after {
  content: "重试";
  position: absolute;
  left: 0;
  top: 0;
  z-index: 4;
  text-align: center;
  width: 100%;
  height: 100%;
  color: rgb(255, 255, 255);
  background: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  display: flex;
  color: rgb(255, 0, 0);
  font-weight: 700;
  letter-spacing: 2px;
  text-shadow: 1px 1px 1px rgb(0, 0, 0);
  font-family: serif;
}

.file-remove {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 14px;
  border-radius: 50%;
  color: white;
  background: rgba(0, 0, 0, 0.8);
  width: 10px;
  height: 10px;
  padding: 3px;
  line-height: 10px;
  text-align: center;
  text-decoration: none;
  z-index: 10;
  &:hover {
    text-decoration: none;
    background: #D61717;
  }
}

.file-select {
  display:flex;
  align-items: center;
  justify-content: center;
  background: #eaeaea;
  flex-direction: column;
  border: 1px dashed #c1c1c1;
  input[type="file"] {
    opacity: 0;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    touch-action: manipulation; /* 解决iOS端点击延迟问题  */
  }
  &:before {
    text-align: center;
    top: 0;
    color: gray;
    z-index: 0;
    white-space: pre;
    // -webkit-box-flex: 1;
    // flex: 1;
    // width: 100%;
    content: "";
    background:url(./images/video.svg) center center no-repeat transparent;
    background-size: 110%;
    width: 35px;
    height: 35px;
  }
  &::after {
    content: attr(title);
    color: #616161;
    line-height: 2;
    font-size: 14px;
  }
}
