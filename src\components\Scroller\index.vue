<style lang="scss" scoped>
  .scroller {
    position: relative;
    overflow-y: hidden;
  }
  .scroller-inner {
    touch-action: manipulation;
  }
 .pulldown {
    height:50px;
    line-height:50px;
    color:gray;
    text-align: center;
    overflow: hidden;
    position: absolute;
    left: 0;
    width: 100%;
    top: 0;
  }
  .pulldown-line{
    display: block;
    width: 0;
    height: 2px;
    background: #03A9F4;
    position: absolute;
    top: 1px;
    left: 0;
    display:none;
  }
  /* .pulldown {
    &::before {
      content: attr(data-text);
      text-align: center;
      position: absolute;
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      transform: translateY(-100%) translateZ(0);
      text-align: center;
      color: #999
    }
  } */
</style>
<template>
  <div
    class="scroller"
    id="scroller"
  >
    <div class="pulldown" :style="pulldownStyle">
      <div class="pulldown-content" v-text="pulldownTip" ></div>
    </div>
    <div class="scroller-inner">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  /**
   * 本组件摒弃了 hammerjs，使用better-scroll代替
   * better-scroll相对优点：
   * 1. 有更好的滚动性能，
   * 2. 有下拉刷新插件无须自己实现且在微信webview中也可正常使用下拉刷新手势
   * better-scroll存在的问题：
   * 1. 由于bs使用transform实现页面滚动，页面中如果使用了fixed定位的元素，布局可能无法撑满视口
   * 2. 在iOS端滚动与橡皮筋效果冲突
  */
import BScroll from '@better-scroll/core'
import PullDown from '@better-scroll/pull-down'
import MouseWheel from '@better-scroll/mouse-wheel'
import { AppStatus } from '@/enums';

// events: reload, scroll, scroll-down, refresh
const PULLDOWN_THRESHOLD = 50;
export default {
  name: 'ScrollView',
  props: {
    refreshAction: {
      type: Number,
      default: 0,
    },
  },
  components: {},
  data() {
    return {
      AppStatus,
      beforePullDown: true,
      isPullingDown: false,
      touching: false,
      scrollY: 0,
      enablePulldownRefresh: !!this.refreshAction,
    };
  },
  mounted() {
    this.init();
  },
  updated() {
    // this.$bs && this.$bs.refresh();
    // console.log('updated')
  },
  activated() {
    // this.$el.scrollTop = this.scrollTop;
  },
  computed: {
    pulldownStyle() {
      const shouldShowPulldownTip = this.enablePulldownRefresh && (this.isPullingDown || (this.touching && this.scrollY > 10));
      return {
        visibility: shouldShowPulldownTip ? 'visible' : 'hidden',
      }
    },
    pulldownTip() {
      let text = '下拉刷新';
      // console.log(this.scrollY)
      if (this.isPullingDown) {
        return '正在刷新...';
      }
      if (this.scrollY > PULLDOWN_THRESHOLD) return '释放立即刷新';
        // return this.isPullingDown ? '释放立即刷新' : '正在刷新...';
      return text;
    },
  },
  watch: {
    refreshAction() {
      // this.scrollY = 0;
      console.warn('refreshAction change...')
      setTimeout(() => {
        this.stopRefresh();
        // this.refresh();
      }, 300);
    }
  },
  methods: {
    init() {
      const enablePulldownRefresh = this.enablePulldownRefresh;
      // const enablePulldownRefresh = true;
      let options = {
        probeType: 3,
        scrollY: true,
        scrollX: false,
        click: false,
        dblclick: true,
        click: true,
        tap: true,
        useTransition: true,
        // eventPassthrough: 'horizontal', // 默认为空，不设置与swiper结合使用时手势有一定程度冲突
        swipeBounceTime: 250, // 默认500
        momentumLimitDistance: 30, // 默认15
        bounce: {
          top: false,
          bottom: false,
        },
        freeScroll: false,
        momentum: true,
        bounceTime: 350, // 回弹动画的动画时长
        // deceleration: 0.0025, // momentum 动画的减速度 默认0.0015
        // outOfBoundaryDampingFactor: 1/5,
        specifiedIndexAsContent: 1,
        autoBlur: false,
        // preventDefault: false,
        // tagException: {
        //   className: /input/
        // },
        mouseWheel: {
          speed: 20,
          invert: false,
          easeTime: 300
        }
      };
      BScroll.use(MouseWheel)
      if (enablePulldownRefresh) {
        BScroll.use(PullDown)
        Object.assign(options, {
          bounce: {
            top: true,
            bottom: false,
          },
          pullDownRefresh: true,
          pullDownRefresh: {
            threshold: PULLDOWN_THRESHOLD,
            stop: 50
          },
        })
      }
      console.log('pulldown:', enablePulldownRefresh);
      const bs = new BScroll(this.$el, options);
      this.$bs = bs;
      // console.log('init:', bs)
      bs.on('scroll', position => {
        // console.log(position)
        this.scrollY = position.y;
        // console.log(this.scrollY)
        this.onScroll(Math.abs(this.scrollY));
        // this.$emit('scroll', position.y);
      })
      bs.on('beforeScrollStart', e => {
        this.touching = true
        // console.log('beforeScrollStart...')
        this.refresh();
      })
      bs.on('touchEnd', e => {
        this.touching = false
      })
      if (enablePulldownRefresh) {
        bs.on('pullingDown', (e) => {
          this.beforePullDown = false;
          this.isPullingDown = true;
          this.$emit('refresh')
          // console.log('pullingDown:', e, this, this.isPullingDown)
        })
      }
      bs.on('touchEnd', (e) => {
        // console.log('touchEnd:', e, this, this.isPullingDown)
      })
      // if (!this.refreshAction) {
      //   bs.closePullDown();
      // }
      // bs.finishPullDown()
      // bs.openPullDown({})
      // bs.autoPullDownRefresh()
    },
    refresh() {
      this.$bs.refresh();
      console.log('refresh scroller...');
    },
    stopRefresh() {
      this.$bs.finishPullDown();
      this.isPullingDown = false;
    },
    scrollTo(y) {
      this.$bs.scrollTo(y);
    },
    scrollBy(y) {
      this.$bs.scrollBy(0, y);
    },
    scrollToElement(el, time, offsetX, offsetY, easing) {
      this.$bs.scrollToElement(...arguments);
    },
    onScroll(y) {
      // this.scrollTop = this.$el.scrollTop;
      this.$emit('scroll', y);
      const scrollBottom = this.$bs.scrollerHeight - Math.abs(this.scrollY) - this.$el.clientHeight;
      // console.log('scrollBottom:', scrollBottom);

      if (scrollBottom >= 0 && this.$bs.movingDirectionY === 1) {
        // e.stopPropagation();
        this.scrollDown(scrollBottom);
      }
    },
    scrollDown(distance) {
      const that = this;
      clearTimeout(this.__scroll_down_code);
      that.__scroll_down_code = setTimeout(() => {
        that.$emit('scroll-down', distance);
        // this.refresh();
      }, 50);
    },
  },
};
</script>

