<template>
  <container class="account-my-cars" @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="我的爱车">
      <x-button  slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status" :refreshAction="refreshAction" @refresh="refresh" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div class="weui-cells weui-cells_radio">
          <label  v-for="(item, $index) in cars" :key="$index"  class="weui-cell weui-check__label" :for="'x'+$index" >
            <div class="weui-cell__bd flex-row">
              <c-picture class="car-logo" :src="item.carModelInfo.brandLogo"></c-picture>
              <div class="car-content">
                <h4 class="car-title">{{item.carModelInfo.brand}}</h4>
                <p class="car-note">{{item.carModelInfo.name}}</p>
              </div>
            </div>
            <div class="weui-cell__ft">
              <input type="radio" class="weui-check"  v-model="car" :value="item.id" name="car" :id="'x'+$index">
              <span class="weui-icon-checked"></span>
            </div>
          </label>
          <label v-if="cars && cars.length < ADD_CAR_LIMIT"  @click="go('/car/brands')" class="weui-cell weui-check__label car-add__label" >
            <div class="weui-cell__bd flex-row">
              <c-picture class="car-logo car-add-icon">
                <svg class="icon-plus">
                  <use xlink:href="#icon-plus"></use>
                </svg>
              </c-picture>
              <div class="car-content">
                <h4 class="car-title">添加新车</h4>
              </div>
            </div>
          </label>
        </div>

        <div v-if="cars && cars.length" class="button-sp-area">
          <a href="javascript:;" class="weui-btn weui-btn_primary" @click="confirmCar">确定</a>
        </div>

    </template>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';

  .account-my-cars {
    .weui-cells_radio{
      margin:0;
    }
    .picture.car-logo {
      width: 60px;
      height: 60px;
      // border-radius: 50%;
      // background-color: #e2e2e2;
      margin-right: 5px;
      position:relative;
    }
    .car-content{
      display:flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
    }
    .car-title{
      font-weight:400;
      color:black;
    }
    .car-note{
      color:gray;
      font-size: 0.8em;
    }
    .car-add__label{
      .car-title{
        color:#5DA9FB;
      }
      .car-add-icon{
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        /*width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-right:5px;*/
        background:#E3E4E4;
        >.icon-plus{
          font-size:2em;
          fill:#9B9D9F;
        }
      }
    }
    .button-sp-area{
      padding: 5px;
      margin-top:20px;
    }
  }
</style>
<script>
import { formatDate } from '@/utils';
import { Panel } from '@/components';
import { AppStatus } from '@/enums';
import { getMyCars, addMyCar } from '@/api';
import { bus, back, loading, toast, dialog, Events } from '@/bus';

export default {
  name: 'my-cars',
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 1,
      ADD_CAR_LIMIT: 5,
      car: null,
      page: {
        cars: null,
      },
    };
  },
  computed: {
    cars() {
      return this.page.cars;
    },
  },
  components: {
    Panel,
  },
  mounted() {
    bus.$on(Events.AFTER_CAR_MODEL_SELECTED, car => {
      if (this.status != AppStatus.READY) return;
      console.log('AFTER_CAR_MODEL_SELECTED', this.$options.name);
      this.onCarModelSelected(car);
    });
  },
  watch: {
    cars(val, oldVal) {
      if (!this.car && this.cars.length > 0) {
        this.car = this.cars[0].id;
      }
    }
  },
  methods: {
    initPageData() {
      this.car = this.$route.params.id;
      return getMyCars().then(res => {
        this.page.cars = res;
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    init() {
      return this.initPageData();
    },
    refresh() {
      this.init().then(() => {
        this.refreshAction = Date.now();
      }).catch(e => {
        toast().tip('刷新失败');
        this.refreshAction = Date.now();
      });
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    confirmCar() {
      bus.$emit(Events.AFTER_MY_CAR_SELECTED, this.car);
      back();
    },
    onCarModelSelected(car) {
      loading(true, '正在添加...');
      addMyCar(car.id).then(res => {
        loading(false);
      }).catch(e => {
        loading(false);
        toast().tip('添加失败！');
      })
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.initPageData();
    },
    go(url) {
      this.$router.push(url);
    },
  }
};
</script>
