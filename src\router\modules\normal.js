import { handleError } from '../error-handler';

export default [
  {
    path: '/',
    name: '养车首页',
    component(resolve) {
      import(/* webpackChunkName="home" */ '@/views/Home').then(resolve).catch(handleError);
    },
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/cmi',
    name: '平台介绍',
    component: resolve => {
      import(/* webpackChunkName="car-wash-about" */ '@/views/packages/beauty/CarWashAbout').then(resolve).catch(handleError);
    }
  },
  {
    path: '/shop/:id/comment/:type?',
    name: '商家评论',
    component: resolve => {
      import(/* webpackChunkName="shop-comments" */ '@/views/ShopComments').then(resolve).catch(handleError);
    }
  },
  {
    path: '/shop/:id/map',
    name: '商家位置',
    component: resolve => {
      import(/* webpackChunkName="shop-map" */ '@/views/ShopMap').then(resolve).catch(handleError);
    },
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/order/pay/:id',
    name: '收银台',
    component: resolve => {
      import(/* webpackChunkName="checkout-counter" */ '@/views/CheckoutCounter').then(resolve).catch(handleError);
    },
    meta: { requiresAuth: true },
    children: [
      {
        path: 'payment',
        component: resolve => {
          import(/* webpackChunkName="payment" */ '@/views/Child_Payment').then(resolve).catch(handleError);
        }
      }
    ]
  },
  {
    path: '/pay/result/:id',
    name: '支付结果',
    component: resolve => {
      import(/* webpackChunkName="pay-result" */ '@/views/PayResult').then(resolve).catch(handleError);
    },
    meta: { requiresAuth: true }
  },
  {
    path: '/error',
    name: '错误',
    component: resolve => {
      import(/* webpackChunkName="error" */ '@/views/Error').then(resolve).catch(handleError);
    },
  },
  {
    path: '*',
    redirect: '/error',
  },
]
