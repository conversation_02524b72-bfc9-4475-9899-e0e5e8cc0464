import { isInWeApp, isInWeixin } from '@/common/env';
import { playMode, playListMode } from '../common/enums';

const state = {
  auth: {
    loggedIn: false,
    session: '',
    lastUpdate: Date.now(),
  },
  initUserInfo: {
    vip: false,
    vipExpireTime: Date.now(),
    invitationCode: '',
  },
  user: {
    vip: false,
    vipExpireTime: Date.now(),
    invitationCode: '',
  },
  shareInfo: {
    title: '交广领航车生活平台',
    desc: '交广领航，只为您的有车生活变得更加美好！',
    link: 'http://www.jgrm.net/mobile/jglh.html', // 必须与公众号JS安全域名一致
    // imgUrl: 'https://img.jgrm.net/Ftrbwpd2TN8nfadMJR_V1gT4eWP-', // 分享图片不能太大，否则在iOSApp端分享不显示图片
    // imgUrl: 'https://img.jgrm.net/FjDKmpLt7QGo_edo-sFSogjMZpee', // 2019年新logo
    // imgUrl: 'https://img.jgrm.net/Fjc29MDp18riTs8BnpkOmCKcBktl_cover.jpg', // 2020年新logo
    // imgUrl: 'https://img.jgrm.net/FnAF6s5Oe772yADDQjCNOi4fM5VQ_cover.jpg', // 小程序分享logo 5:4
    imgUrl: isInWeApp
      ? 'https://img.jgrm.net/FnAF6s5Oe772yADDQjCNOi4fM5VQ_cover.jpg'
      : 'https://img.jgrm.net/FhG3UsDYORQTfBy7I9FOfGUPU981_cover.jpg', // 2020年新logov2
  },
  views: {
    transitionMode: 'forward',
    transitionName: 'fade',
    home: {
      unread: 0,
      weather: {},
      news: [],
    },
    selectedCar: null,
    router: {},
    routePaths: [],
    routeList: [],
  },
  audioPlayerInfo: {
    radioInfo: '', // 栏目信息
    playing: false, // 播放状态默认为暂停
    fullScreen: false, // 播放器默认为收起
    currentSong: {}, // 当前播放音频
    playList: [], // 播放列表
    playListSequence: playListMode.asc, // 播放列表排序方式
    playListPage: 1, // 播放列表第几页
    audioTotal: 0, // 总共多少音频
    sequenceList: [], // 顺序列表
    mode: playMode.sequence, // 播放模式默认为顺序播放
    currentIndex: -1, // 当前播放索引
  },
  supportWebP: null, // 是否支持 WebP
  appSetting: {}, // app个性化设置
};

export default state;
