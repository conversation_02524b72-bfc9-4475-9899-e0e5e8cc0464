<template>
  <container class="blind-date-user-detail" @ready="onReady" @leave="onLeave" @resume="onResume">
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="share-btn" @click="handleShare">
        <i class="icon_jglh icon-fenxiang1"></i>
      </div>
    </x-header>

    <content-view :status="status" @reload="reload">
      <template v-if="status === AppStatus.READY">
        <div class="profile-page" v-if="userInfo.status === 1 || isPreview">
          <!-- 轮播图 -->
          <van-swipe class="top-swipe" :autoplay="3000" indicator-color="#fff">
            <van-swipe-item v-for="(img, index) in images" :key="index">
              <!-- <img :src="img" class="swipe-img" /> -->
              <c-picture class="swipe-img" :src="img" @click="playPhotos(images, index)" :type="`?imageView2/1/w/800/h/800/format/${supportWebP ? 'webp' : 'jpg'
                }/q/90`">
              </c-picture>
            </van-swipe-item>
          </van-swipe>
          <div class="user-info-box">
            <div class="user-info-box-bg">
              <!-- 用户信息 -->
              <div class="info-box">
                <div class="basic">
                  <div class="name">{{ userInfo.nickName }}</div>
                  <div class="desc">
                    {{ userInfo.age }}岁 · {{ userInfo.gender }} ·
                    {{ userInfo.occupation }} ·
                    {{ userInfo.location }}
                  </div>
                  <div class="tags" v-if="
                    userInfo.realNameVerified || userInfo.educationVerified
                  ">
                    <div class="tag tag--success" v-if="userInfo.realNameVerified" round>
                      <span class="icon_jglh icon-icon_duihao-mian"></span>
                      实名认证
                    </div>
                    <!-- <div
                      class="tag tag--warning"
                      v-if="!userInfo.realNameVerified"
                      round
                    >
                      <span class="icon_jglh icon-icon_duihao-mian"></span>
                      未实名认证
                    </div> -->
                    <div class="tag tag--primary" v-if="userInfo.educationVerified" round>
                      <span class="icon_jglh icon-xueshimao"></span>
                      学历认证
                    </div>
                    <!-- <div
                      class="tag tag--warning"
                      v-if="!userInfo.educationVerified"
                      round
                    >
                      <span class="icon_jglh icon-xueshimao"></span>
                      学历未认证
                    </div> -->
                  </div>

                  <!-- 人气指标 -->
                  <div class="popularity-stats">
                    <div class="stat-item">
                      <van-icon name="fire" color="#ff5858" size="16" />
                      人气值：<span class="stat-value">{{
                        userInfo.popularityCount || 0
                        }}</span>
                    </div>
                    <div v-if="userInfo.heartCount" class="stat-item">
                      <van-icon name="like" color="#ff5858" size="16" />
                      心动次数：<span class="stat-value">{{ userInfo.heartCount || 0 }}次</span>
                    </div>
                    <div v-if="userInfo.flowerCount" class="stat-item">
                      <van-icon name="gift" color="#ff5858" size="16" />
                      送花次数：<span class="stat-value">{{ userInfo.flowerCount || 0 }}次</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="section">
                <div class="section-title">基本信息</div>
                <div class="info-grid">
                  <div class="info-item" v-if="userInfo.height">
                    <span class="info-label">身高：</span>
                    <span class="info-value">{{ userInfo.height }}cm</span>
                  </div>
                  <div class="info-item" v-if="userInfo.weight">
                    <span class="info-label">体重：</span>
                    <span class="info-value">{{ userInfo.weight }}kg</span>
                  </div>
                  <div class="info-item" v-if="userInfo.occupation">
                    <span class="info-label">职业：</span>
                    <span class="info-value">{{ userInfo.occupation }}</span>
                  </div>
                  <div class="info-item" v-if="userInfo.bloodType">
                    <span class="info-label">血型：</span>
                    <span class="info-value">{{ userInfo.bloodType }}</span>
                  </div>
                  <div class="info-item" v-if="userInfo.education">
                    <span class="info-label">学历：</span>
                    <span class="info-value">{{ userInfo.education }}</span>
                  </div>
                  <div class="info-item" v-if="userInfo.maritalStatus">
                    <span class="info-label">婚姻状况：</span>
                    <span class="info-value">{{ userInfo.maritalStatus }}</span>
                  </div>
                  <div class="info-item" v-if="userInfo.location">
                    <span class="info-label">所在地：</span>
                    <span class="info-value">{{ userInfo.location }}</span>
                  </div>
                  <div class="info-item" v-if="userInfo.hometown">
                    <span class="info-label">家乡：</span>
                    <span class="info-value">{{ userInfo.hometown }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">购房情况：</span>
                    <span class="info-value">{{ userInfo.hasHouse || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">购车情况：</span>
                    <span class="info-value">{{ userInfo.hasCar || '-' }}</span>
                  </div>
                  <div class="info-item" v-if="userInfo.showIncomeLevel === 1">
                    <span class="info-label">收入水平：</span>
                    <span class="info-value">{{ userInfo.incomeLevel || '-' }}</span>
                  </div>
                </div>
              </div>

              <!-- 个人介绍 -->
              <div class="section">
                <div class="section-title">个人介绍</div>
                <div class="section-content">
                  {{ userInfo.introduction }}
                </div>
              </div>

              <!-- 兴趣爱好 -->
              <div class="section">
                <div class="section-title">兴趣爱好</div>
                <div class="section-content">
                  {{ userInfo.hobbies }}
                </div>
                <!-- <div class="section-tags">
                  <div
                    class="tag tag--default"
                    v-for="(tag, index) in hobbies"
                    :key="index"
                    round
                    class="tag"
                    >{{ tag }}</div
                  >
                </div> -->
              </div>

              <!-- 择偶要求 -->
              <div class="section">
                <div class="section-title">择偶要求</div>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="info-label">ta的年龄：</span>
                    <span class="info-value">{{ userInfo.hopeAgeRange == '不限' ? '不限' : userInfo.hopeAgeRange + '岁'
                    }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">ta的月收入：</span>
                    <span class="info-value">{{ userInfo.hopeIncomeLevel == '不限' ? '不限' : userInfo.hopeIncomeLevel + '元'
                    }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">ta的身高：</span>
                    <span class="info-value">{{ userInfo.hopeHeight == '不限' ? '不限' : userInfo.hopeHeight + 'cm'
                      }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">ta的学历：</span>
                    <span class="info-value">{{ userInfo.hopeEducation }}</span>
                  </div>
                  <div class="info-item" style="grid-column: span 2;">
                    <span class="info-label">ta的婚姻状况：</span>
                    <span class="info-value">{{ userInfo.hopeMaritalStatus }}</span>
                  </div>
                </div>
                <div class="section-content" style="margin-top: 12px;">
                  <div class="info-label" style="margin-bottom: 4px;">其他要求：</div>
                  {{ userInfo.hopeOtherRequirements ? userInfo.hopeOtherRequirements : '无' }}
                </div>
              </div>

              <!-- 择偶标准 -->
              <!-- <div class="section" v-if="userInfo.standardOfSpouseSelection">
                <div class="section-title">择偶标准</div>
                <div class="section-content">
                  {{ userInfo.standardOfSpouseSelection }}
                </div>
              </div> -->
            </div>
          </div>

          <!-- 底部按钮 -->
          <div class="bottom-fixed">
            <van-button type="default" class="btn-left" @click="toggleLike"><van-icon
                :name="userInfo.alreadySendHeartbeatFlag ? 'like' : 'like-o'" size="20px" :color="userInfo.alreadySendHeartbeatFlag ? '#FF4785' : '#9CA3AF'
                  " style="margin-right: 6px" />心动</van-button>
            <van-button type="primary" class="btn-right" @click="sendFlower"><van-icon name="gift-o" size="20px"
                style="margin-right: 6px" />
              送花
            </van-button>
          </div>
        </div>

        <div class="profile-locked-page" v-else-if="profile.status !== 1 && isCurrentUserProfile">
          <div class="profile-locked-container">
            <div class="locked-icon">
              <van-icon name="lock" size="50" color="#f15b5b" />
            </div>
            <div class="locked-title">用户资料已锁定</div>
            <div class="locked-message">
              <template v-if="profile.status === 0">
                您的资料正在审核中，审核通过后才能查看资料详情
              </template>
              <template v-else-if="profile.status === 2">
                您的资料审核未通过，请修改后重新提交审核
              </template>
              <template v-else-if="profile.status === 3">
                您已将个人资料设为隐藏状态，显示资料后才能查看
              </template>
              <template v-else-if="profile.status === -1">
                您已退出相亲功能，请重新开启后查看
              </template>
              <template v-else>
                您的资料状态异常，无法查看其他用户资料
              </template>
            </div>
            <div class="locked-action">
              <van-button type="primary" size="large" @click="handleProfileAction" class="action-button">
                <template v-if="profile.status === 0"> 返回首页 </template>
                <template v-else-if="profile.status === 2"> 修改资料 </template>
                <template v-else-if="profile.status === 3"> 显示资料 </template>
                <template v-else-if="profile.status === -1">
                  重新开启
                </template>
                <template v-else> 完善资料 </template>
              </van-button>
            </div>
          </div>
        </div>

        <div class="profile-locked-page" v-else>
          <div class="profile-locked-container">
            <div class="locked-icon">
              <van-icon name="eye-o" size="50" color="#f15b5b" />
            </div>
            <div class="locked-title">资料未公开</div>
            <div class="locked-message">该用户资料暂时无法查看</div>
            <div class="locked-action">
              <van-button type="primary" size="large" @click="goBack" class="action-button">
                返回上一页
              </van-button>
            </div>
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { AppStatus } from '@/enums';
import { playPhotos } from '@/bridge';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { toast, loading } from '@/bus';
import { Button, Swipe, SwipeItem, Icon, Toast, Dialog } from 'vant';
import { mapState } from 'vuex';
import {
  getUserDetail,
  sendFlower,
  sendHeartbeat,
  cancelHeartbeat,
  showUser,
} from './api';
export default {
  name: 'BlindDateUserDetail',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    [Swipe.name]: Swipe,
    [SwipeItem.name]: SwipeItem,
    [Icon.name]: Icon,
    [Toast.name]: Toast,
    [Dialog.name]: Dialog,
  },
  data() {
    let id = +this.$route.params.id;
    let isPreview = this.$route.query.preview; // 管理端预览标志

    return {
      AppStatus,
      images: [],
      hobbies: ['室内设计', '摄影', '手工艺', '旅行', '咖啡', '瑜伽'],
      status: AppStatus.LOADING,
      pageTitle: '详情',
      userInfo: {},
      collected: false,
      id,
      isPreview,
    };
  },
  computed: {
    ...mapState(['supportWebP']),
    ...mapState({
      profile: state => state.blindDate.profile,
    }),
    isCurrentUserProfile() {
      return this.profile.id === this.id;
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      getUserDetail(this.id)
        .then(res => {
          if (res) {
            this.userInfo = res;
            if (!this.userInfo.likeCount) {
              this.userInfo.likeCount = Math.floor(Math.random() * 100) + 10;
            }
            this.images =
              (res.profileImages && res.profileImages.length > 0
                ? res.profileImages.split(',')
                : []) || [];
            this.status = AppStatus.READY;
            this.updateShareInfo();
          }
        })
        .catch(err => {
          console.log(err);
          this.status = AppStatus.ERROR;
          Toast(err.msg);
        });
    },
    updateShareInfo() {
      if (this.userInfo.status === 1) {
        this.share();
      }
    },
    share(action = 'config') {
      if (this.userInfo.status !== 1) {
        return;
      }

      const title = `${this.userInfo.nickName || '恋爱对象'} - 个人资料`;
      const desc = this.userInfo.introduction;
      const logo = this.images.length > 0 ? getImageURL(this.images[0]) : '';
      const url = getAppURL(this.$route.fullPath);

      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };

      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    handleShare() {
      if (this.userInfo.status === 1) {
        this.share('show');
      } else {
        Toast('该资料暂不可分享');
      }
    },
    playPhotos(images, initIndex = 0) {
      playPhotos({
        initIndex: initIndex,
        photos: images.map(item => {
          return {
            title: '',
            url: getImageURL(item, '_xl'),
          };
        }),
      });
    },
    onReady() {
      // this.status = AppStatus.READY;
    },
    onLeave() {
      this.status = AppStatus.LEAVING;
    },
    onResume() {
      this.status = AppStatus.READY;
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    sendFlower() {
      this.$_auth_requireLoggedIn().then(() => {
        sendFlower(this.id)
          .then(res => {
            console.log(res);
            Toast('送花成功');
            this.init();
          })
          .catch(err => {
            Toast(err.msg);
          });
      });
    },
    toggleLike() {
      if (this.userInfo.gender == this.profile.gender) {
        Toast('同性之间无法发送心动');
        return;
      }
      if (this.isCurrentUserProfile) {
        Toast('不能对自己发送心动');
        return;
      }
      if (this.userInfo.alreadySendHeartbeatFlag) {
        Dialog.confirm({
          title: '温馨提示',
          message: '心动是缘分的开始，取消后可能会错过进一步了解的机会哦',
          confirmButtonColor: '#f15b5b',
        }).then(() => {
          cancelHeartbeat({ receiverId: this.id })
            .then(res => {
              console.log(res);
              Toast('取消心动成功');
              this.init();
            })
            .catch(err => {
              console.log(err);
              Toast(err.msg);
            });
        });
      } else {
        this.$_auth_requireLoggedIn().then(() => {
          if (this.profile.id) {
            sendHeartbeat({ receiverId: this.id })
              .then(res => {
                // console.log(res);
                // Toast('发送心动成功');
                this.init();
                if (res && res.id) {
                  Dialog.confirm({
                    title: '温馨提示',
                    message: '恭喜！TA也对你发送了心动，缘分已达成～\n点击查看你们的 CP 详情，开启专属互动吧！',
                    confirmButtonColor: '#f15b5b',
                    confirmButtonText: '去查看',
                  }).then(() => {
                    this.$_router_push('/blindDate/cp?id=' + res.id);
                  });
                } else {
                  Dialog.alert({
                    title: '温馨提示',
                    message: '已成功向TA 发送心动，若 TA 也对你心动，将收到 CP 提醒哦！你可在 “我的 - 对我心动列表” 中查看',
                    confirmButtonColor: '#f15b5b',
                    confirmButtonText: '知道了',
                  });
                }
              })
              .catch(err => {
                console.log(err);
                Toast(err.msg);
              });
          } else {
            Dialog.confirm({
              title: '提示',
              message: '请先完善个人资料',
              confirmButtonColor: '#f15b5b',
            }).then(() => {
              const hasAgreed =
                localStorage.getItem('blind_date_agreement_accepted') ===
                'true';

              if (hasAgreed) {
                // 已同意协议，直接跳转至申请页面
                this.$router.push('/blindDate/apply');
              } else {
                // 未同意协议，跳转至协议页面
                this.$router.push('/blindDate/agreement');
              }
            });
          }
        });
      }
    },
    // 处理资料锁定状态的操作
    handleProfileAction() {
      switch (this.profile.status) {
        case 0: // 审核中
          this.$_router_push('/blindDate'); // 返回相亲首页
          break;
        case 2: // 审核未通过
          this.$_router_push('/blindDate/apply?id=' + this.profile.id); // 跳转至修改资料
          break;
        case 3: // 用户隐藏
          Dialog.confirm({
            title: '提示',
            message: '确定要显示您的资料吗？显示后其他用户可以查看您的详细信息',
            confirmButtonText: '确定显示',
            confirmButtonColor: '#f15b5b',
          })
            .then(() => {
              // 这里应调用后端API修改状态，示例代码
              Toast.loading({
                message: '处理中...',
                forbidClick: true,
              });
              showUser({ id: this.id })
                .then(res => {
                  console.log(res);
                  Toast.success('资料已显示');
                  // 刷新状态
                  this.$store.dispatch('blindDate/fetchProfile').then(() => {
                    this.init();
                  });
                })
                .catch(err => {
                  console.log(err);
                  Toast(err.msg);
                });
            })
            .catch(() => { });
          break;
        case -1: // 已退出
          Dialog.confirm({
            title: '提示',
            message: '确定要重新加入相亲吗？',
            confirmButtonText: '确定加入',
            confirmButtonColor: '#f15b5b',
          })
            .then(() => {
              Toast.loading({
                message: '处理中...',
                forbidClick: true,
              });
              // 应替换为实际API调用
              setTimeout(() => {
                this.$_router_push('/blindDate/apply');
              }, 1000);
            })
            .catch(() => { });
          break;
        default: // 其他状态
          this.$router.back();
          break;
      }
    },
    goBack() {
      this.$router.back();
    },
  },
};
</script>

<style lang="scss" scoped>
.blind-date-user-detail {
  background: #f9fafb;
}

.share-btn {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon_jglh {
    font-size: 24px;
  }
}

.profile-page {
  padding-bottom: 60px;

  .user-info-box {
    position: relative;
    z-index: 1;
    background: #f9fafb;
    padding: 0 15px 0px;

    .user-info-box-bg {
      transform: translateY(-18px);

      &>div {
        border-radius: 16px;
        background: linear-gradient(0deg,
            rgba(0, 0, 0, 0.001),
            rgba(0, 0, 0, 0.001)),
          #ffffff;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .top-swipe ::v-deep {
    height: 300px;

    .van-swipe__indicators {
      bottom: 30px;
    }

    .swipe-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .info-box {
    background: #fff;
    padding: 16px;

    .name {
      font-size: 18px;
      font-weight: bold;
      line-height: 24px;
      letter-spacing: normal;
      color: #000000;
    }

    .desc {
      font-size: 14px;
      font-weight: normal;
      line-height: 24px;
      letter-spacing: normal;
      color: #4b5563;
    }

    .tags {
      display: flex;
      gap: 8px;
      margin-top: 8px;
      font-size: 12px;

      .tag {
        display: flex;
        align-items: center;
        border-radius: 15px;
        padding: 0px 8px;

        .icon_jglh {
          margin-right: 4px;
          font-size: 12px;
        }
      }

      .tag--success {
        background: #f0fdf4;
        border-color: #f0fdf4;
        color: #16a34a;
      }

      .tag--primary {
        background: #eff6ff;
        border-color: #eff6ff;
        color: #2563eb;
      }

      .tag--default {
        background: #f3f4f6;
        border-color: #f3f4f6;
        color: #4b5563;
      }

      .tag--warning {
        background: #fef9c3;
        border-color: #fef9c3;
        color: #ca8a04;
      }
    }

    // 人气指标样式
    .popularity-stats {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-top: 12px;
      color: #666;
      font-size: 12px;
      align-items: center;
      line-height: 1;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .stat-value {
        color: #333;
        font-weight: 500;
      }
    }
  }

  .section {
    background: #fff;
    margin-top: 12px;
    padding: 16px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: #000000;
      margin-bottom: 8px;
    }

    .section-content {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #111827;

      .info-label {
        color: #6b7280;
      }
    }

    .section-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .tag {
        padding: 4px 8px;
        font-size: 12px;

        &.tag--default {
          background: #f3f4f6;
          border-color: #f3f4f6;
          color: #4b5563;
        }
      }
    }

    .tag {
      font-size: 13px;
      padding: 4px 10px;
    }

    // 基本信息网格布局
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px 16px;

      .info-item {
        font-size: 14px;
        display: flex;
        align-items: center;

        .info-label {
          color: #6b7280;
          margin-right: 4px;
          flex-shrink: 0;
        }

        .info-value {
          color: #111827;
          font-weight: 500;
        }
      }
    }
  }

  .bottom-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    display: flex;
    padding: 10px 16px;
    z-index: 10;

    .van-button {
      border-radius: 4px;

      .van-button__text {
        display: inline-flex;
        align-items: center;
      }
    }

    .btn-left {
      flex: 1;
      margin-right: 12px;
    }

    .btn-right {
      flex: 2;
      background-color: #ff4081;
      border-color: #ff4081;
      color: #fff;
    }
  }
}

// 新增样式 - 锁定状态页面
.profile-locked-page {
  min-height: calc(100vh - 46px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  box-sizing: border-box;

  .profile-locked-container {
    background: #fff;
    border-radius: 16px;
    padding: 30px 20px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05);
    text-align: center;
    box-sizing: border-box;

    .locked-icon {
      margin-bottom: 20px;

      .van-icon {
        background: #fff1f1;
        border-radius: 50%;
        padding: 15px;
      }
    }

    .locked-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }

    .locked-message {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
      margin-bottom: 25px;
    }

    .locked-action {
      .action-button {
        background: #f15b5b;
        border-color: #f15b5b;
        width: 80%;
        height: 40px;
        border-radius: 20px;
      }
    }
  }
}
</style>
