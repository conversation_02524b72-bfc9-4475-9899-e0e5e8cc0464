<template>
  <container class="account-my-car-edit" @ready="init" @leave="onLeave" @resume="onResume">
    <x-header :title="pageTitle">
      <x-button  slot="left" type="back"></x-button>
      <!--<x-button  slot="right" type="delete" @click="deleteMyCar">删除</x-button>-->
    </x-header>

    <content-view :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div v-if="carModel && carModel.id" class="flex-row card" @click="goSelectNewCarModel">
          <c-picture class="card-logo" :src="carModel.brandLogo"></c-picture>
          <div class="card-content">
            <h4 class="card-content__title">{{carModel.brand}}</h4>
            <p class="card-content__desc">{{carModel.carSysName}}</p>
          </div>
        </div>
        <div class="weui-cells weui-cells_form">
            <div class="weui-cell weui-cell_select car-model-select">
                <div class="weui-cell__hd">
                    <label for="" class="weui-label">车型</label>
                </div>
                <div class="weui-cell__bd"  @click="goSelectCarModel" >
                    <span class="car-model-name" v-html="carModel.name || '请选择'"></span>
                </div>
            </div>
            <div class="weui-cell">
                <div class="weui-cell__hd">
                  <label for="" class="weui-label">车牌号码</label>
                </div>
                <div class="weui-cell__bd">
                  <car-plate-number-input v-model="form.number"></car-plate-number-input>
                </div>
            </div>
            <template v-if="pageMode === PageMode.EDIT">
              <div class="weui-cell weui-cell_select">
                  <div class="weui-cell__hd"><label for="" class="weui-label">上路时间</label></div>
                  <div class="weui-cell__bd">
                      <!--<span>{{formatDate(form.date)}}</span>-->
                      <date-picker v-model="form.date" :options="{id: Date.now(), container: '#date-picker-container'}"></date-picker>
                      <div v-if="status == AppStatus.READY" id="date-picker-container"></div>
                  </div>
              </div>
              <div class="weui-cell">
                  <div class="weui-cell__hd"><label class="weui-label">行驶里程</label></div>
                  <div class="weui-cell__bd">
                      <input class="weui-input" type="number"  v-model="form.distance" pattern="[0-9]*" placeholder="选填">
                  </div>
                  <div class="weui-cell__ft2">
                      <span>km</span>
                  </div>
              </div>
              <div class="weui-cell">
                  <div class="weui-cell__hd"><label class="weui-label">车辆识别码</label></div>
                  <div class="weui-cell__bd">
                      <input class="weui-input" type="number"  v-model="form.id6" pattern="[0-9]*" placeholder="选填">
                  </div>
              </div>
            </template>
        </div>
        
        <div class="button-sp-area">
          <a href="javascript:;" class="weui-btn weui-btn_primary" @click="submit">保存</a>
        </div>
      </template>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';

  .weui-cell_select-before .weui-select{
    padding-left: 0;
    width: 100px;
  }
  .weui-select{
    height: auto;
    line-height:auto;
  }

  .card{
    align-items: center;
    padding: 5px;
    background:white;
    .card-logo{
      width: 60px;
      height:60px;
      margin-right: 5px;
    }
    .card-content{
      flex: 1;
      position:relative;
      &::after{
        content: "\e605";
        font-family: iconfont;
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        color: gray;
      }
      .card-content__desc{
        color:gray;
        font-size:0.9em;
      }
    }
  }
  .car-model-name{
    margin-right: 10px;
  }
  .car-model-select {
    .weui-cell__bd{
      padding-right: 10px;
    }
  }
  .select-inline {
    display:inline-block;
    padding-right: 15px;
    position: relative;
    select{
      -webkit-appearance: none;
      border: 0;
      font-size: 1em;
      height: 26px;
      line-height: 26px;
      min-width: 20px;
    }
    &::after{
      content: " ";
      display: inline-block;
      border-style: solid;
      position: absolute;
      right: 2px;
      top: 50%;
      transform: translateY(-50%);
      border-width: 5px 5px 0;
      border-color: black transparent transparent;
    }
  }

  .button-sp-area{
    padding:5px;
    margin-top: 10px;
  }
</style>
<script>
import DatePicker from '@/components/DatePicker';
import CarPlateNumberInput from '@/components/CarPlateNumberInput';
import { formatDate } from '@/utils';
import Runner from '@/lib/runner';
import { AppStatus } from '@/enums';
import FormValidater from '@/model/FormValidater';
import { getMyCar, addMyCarV2, updateMyCar, removeMyCar, getCarList, getCarModelInfo } from '@/api';
import { listen, Events, loading, dialog, toast, back } from '@/bus';

const runner = new Runner();

// 页面模式：添加模式和编辑模式
const PageMode = {
  ADD: 'add',
  EDIT: 'edit',
}

export default {
  name: 'account-my-car-edit',
  components: {
    DatePicker,
    CarPlateNumberInput,
  },
  data() {
    const pageMode = this.$route.params.model ? PageMode.ADD : PageMode.EDIT;
    const pageTitle = pageMode === PageMode.ADD ? '添加爱车' : '修改爱车';
    return {
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 1,
      CAR_ADD_LIMIT: 5,
      PageMode,
      pageMode: pageMode,
      form: {
        id: null,
        type: null,
        number: null,
        id6: null,
        date: null,
        distance: null,
      },
      carModel: {},
      page: {
        car: null,
      },
      pageTitle: pageTitle,
    }
  },
  computed: {
    carModelName() {
      const name = this.carModel.name || '';
      const systemName = this.carModel.carSysName;
      return name.replace(systemName, '');
    },
    formAddCar() {
      return {
        model: this.carModel.id,
        number: this.form.number,
      }
    },
  },
  mounted() {
    const sourceId = this.$options.name;
    listen(Events.AFTER_CAR_MODEL_SELECTED, sourceId, (carModel) => {
      runner.set(this, () => {
        if (this.status != AppStatus.READY) return;
        this.$nextTick(() => {
          this.carModel = carModel;
        })
      });
    })
  },
  watch: {
    carModel(carModel, oldVal) {
      if (carModel) {
        this.form.type = carModel.id;
      }
    }
  },
  methods: {
    initPageData() {
      if (this.pageMode === PageMode.ADD) {
        const id = this.$route.params.model;
        return getCarModelInfo(id).then(res => {
          this.carModel = res;
          this.status = AppStatus.READY;
        }, err => {
          console.error(err);
          this.status = AppStatus.ERROR;
        });
      } else if (this.pageMode === PageMode.EDIT) {
        const id = this.$route.params.id;
        return getMyCar(id).then(res => {
          this.page.car = res;
          const car = res;
          this.carModel = car.carModelInfo || {};
          this.form = {
            id: id,
            type: car.modelId,
            distance: car.mileage,
            number: car.carRegistNumber,
            date: car.buyCarTime,
            id6: car.identificationCode,
          }
          this.status = AppStatus.READY;
        }, err => {
          console.error(err);
          this.status = AppStatus.ERROR;
        });
      }
    },
    init() {
      return this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      // this.initPageData();
      runner.run();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    validateForm(form) {
      const rules = {
        'number': {
          name: '车牌号码',
          required: true,
          pattern: /^.[a-z][a-z0-9]{5,6}$/i,
          emptyTip: '请输入车牌号码',
          invalidTip: '车牌号码格式不正确',
        },
        'distance': {
          name: '行驶里程',
          required: false,
          pattern: /^\d+$/i,
          emptyTip: '请输入行驶里程',
          invalidTip: '行驶里程只能是整数',
        },
      }
      return new FormValidater(rules).validate(form);
    },
    submit() {
      if (this.pageMode === PageMode.ADD) {
        this.addCar();
      } else if (this.pageMode === PageMode.EDIT) {
        this.updateCar();
      }
    },
    updateCar() {
      const carId = this.$route.params.id;
      const params = {
        ...this.form,
        id: carId,
      };

      const checkResult = this.validateForm(params);
      if (!checkResult.ok) {
        toast().tip(checkResult.tip);
        return;
      }

      loading(true, '正在保存...');
      console.log(params);
      updateMyCar(params).then(res => {
        loading(false);
        back();
      }, err => {
        loading(false);
        err && dialog().alert(err, {
          title: '',
        });
      });
    },
    deleteMyCar() {
      const carID = this.$route.params.id;
      dialog().confirm('确定要删除这辆车吗？', {
        title: '提示',
        ok() {
          loading(true, '正在提交...');
          removeMyCar(carID).then(res => {
            loading(false);
            back();
          }, err => {
            loading(false);
            err && dialog().alert(err, {
              title: '',
            });
          });
        }
      })
    },
    addCar() {
      const checkResult = this.validateForm(this.formAddCar);
      if (!checkResult.ok) {
        toast().tip(checkResult.tip);
        return;
      }
      loading(true, '正在添加...');
      addMyCarV2(this.formAddCar).then(res => {
        loading(false);
        back();
      }).catch(e => {
        loading(false);
        toast().tip('添加失败！');
      })
    },
    setCarType(item) {
      console.log('item', item);
      this.form.type = item[0];
    },
    go(url) {
      this.$router.push(url);
    },
    goSelectNewCarModel() {
      const from = this.$options.name;
      this.go(`/car/brands?from=${from}`)
    },
    goSelectCarModel() {
      if (!this.carModel.id) return this.goSelectNewCarModel();
      const from = this.$options.name;
      this.go(`/car/series/${this.carModel.carSystemId}/models/-1?from=${from}`)
    },
    formatDistance(value) {
      const result = parseInt(value / 1000);
      return `${result}km`;
    },
    formatDate(...args) {
      return formatDate(...args);
    }
  }
};
</script>
