// 主要用于维护在特定时间段内禁用某些业务
// 业务枚举
const ServiceEnum = Object.freeze({
  CAR_INSPECTION: 'CarInspection',
  Forum: 'forum',
  MAINTENANCE: 'maintenance',
  // 添加其他业务...
});

// 维护时间段的数据结构
const maintenancePeriods = [
  {
    title: '审车业务',
    service: ServiceEnum.CAR_INSPECTION,
    start: '2025-04-20T00:00:00', // ISO 8601 格式
    end: '2025-04-21T00:00:00',
    message: '温馨提示：交广领航审车业务升级维护，给您带来不便敬请谅解！',
    // '温馨提示：交广领航审车业务于1月26日-2月5日暂停服务，感谢您多年来的支持和信任，祝您新春愉快，蛇年大吉！',
  },
  {
    title: '保养业务',
    service: ServiceEnum.MAINTENANCE,
    start: '2025-01-26T00:00:00', // ISO 8601 格式
    end: '2025-02-05T00:00:00',
    message:
      '温馨提示：交广领航保养业务于1月26日-2月7日暂停服务，感谢您多年来的支持和信任，祝您新春愉快，蛇年大吉！',
  },
  {
    title: '车圈',
    service: ServiceEnum.Forum,
    start: '2025-01-28T00:00:00', // ISO 8601 格式
    end: '2025-02-05T00:00:00',
    message: '社区功能升级维护，给您带来不便敬请谅解！', // 车圈功能只能查看，不能发布动态。
  },
  // 其他业务...
];

/**
 * 检查某个业务是否可用
 * @param {string} service - 业务名称
 * @returns {Object} - 业务状态和提示信息
 */
function checkServiceStatus(service) {
  const now = new Date();

  for (const period of maintenancePeriods) {
    if (period.service === service) {
      const start = new Date(period.start);
      const end = new Date(period.end);
      if (now >= start && now <= end) {
        return { available: false, message: period.message }; // 返回不可用状态和提示信息
      }
    }
  }
  return { available: true }; // 业务可用
}

// 导出数据结构和函数
export { checkServiceStatus, ServiceEnum };
