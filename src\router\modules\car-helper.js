import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/dmv/index',
    name: '车驾管服务',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/index.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/service',
    name: '车驾管客服',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/service-index.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/car/buy',
    name: '买车表单',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/buy-car.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/car/sell',
    name: '卖车表单',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/sell-car.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/car/service',
    name: '车服务',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/car-service.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/submit/success',
    name: '提交成功',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/submit-success.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/records/list',
    name: '我的咨询记录',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/records-list.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/comment/submit',
    name: '服务评价',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/comment-submit.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/comment/complete',
    name: '服务评价成功',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/comment-success.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/dmv/comment/list',
    name: '服务评价列表',
    component: resolve => {
      import(/* webpackChunkName: "dmv-service" */ '@/views/packages/car-helper/comments-list.vue').then(resolve).catch(handleError);
    },
  },
];
