<template>
  <div class="shop flex-row">
    <c-picture v-if="shop.logo" :src="shop.logo" class="shop-logo">
    </c-picture>
    <div class="shop-content flex-col">
      <h3 class="shop-title">{{shop.title?shop.title:shop.name}}
      </h3>
      <div class="shop-body">
          <p class="shop-address">
            <svg class="icon-locate"><use xlink:href="#icon-locate"></use></svg>
            <span>{{shop.address?shop.address:shop.locAddr}}</span>
          </p>
      </div>
    </div>
    <slot name="right">
      <div class="shop-nav" @click.stop="goNavigate">
        <span class="shop-distance">{{shop.distance ? formatLength(shop.distance) : '导航'}}</span>
      </div>
    </slot>
  </div>
</template>
<style lang="scss" scoped>
  .shop{
    position:relative;
    padding:5px;
  }
  .shop-title {
    font-size: 16px;
    font-weight:400;
    color:#393939;
    line-height: 1.2;
    .number{
      color:#F23240;
      padding-right:2px;
    }
  }
  .shop-logo {
    width: 60px;
    height: 60px;
    margin:2px 0 0 3px;
  }
  .shop-body{
    position:relative;
    font-size:0.8em;
    color:#A7A7A8;
  }
  .shop-content {
    flex: 1;
    margin-left:5px;
    min-width:0;
    justify-content: center;
  }
  .shop-address {
    min-width: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width:90%;
    margin-top:5px;
  }

  .shop-nav {
    width:55px;
    text-align: center;
    border-left:1px solid #efefef;
    padding:5px 10px;
    overflow:hidden;
    &:active {
      background: #e0e0e0;
    }
    &::before {
      font-family: iconfont;
      display:block;
      content: "\e679";
      font-size: 20px;
      color: #4E85FB;
    }
  }

</style>
<script>
import { Rater } from '@/components';
import { formatDistance } from '@/utils';
import { navigate } from '@/bridge';

export default {
  name: 'shop-card',
  props: {
    shop: {
      type: Object,
    },
  },
  computed: {

  },
  methods: {
    formatLength(...args) {
      return formatDistance(...args);
    },
    goNavigate() {
      if (this.shop.locAddr) {
        const { locAddr, name, lat, lng, companyName } = this.shop;
        navigate({
          address: locAddr,
          name: companyName || name,
          longitude: lng,
          latitude: lat,
          callback() {},
        });
      } else {
        const { address, name, lat, lng, companyName } = this.shop;
        navigate({
          address,
          name: companyName || name,
          longitude: lng,
          latitude: lat,
          callback() {},
        });
      }
    }
  },
  components: {
    Rater
  },
  data() {
    return {};
  }
};
</script>
