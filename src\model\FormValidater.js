// rules: Array[ name : string, pattern: RegExp, emptyTip: string, invalidTip: string ]
import { isFunction } from '@/utils/type';

export default class FormValidater {
  constructor(rules = {}) {
    this.rules = rules;
    this.rule = null;
  }

  validate(form = {}) {
    const rules = this.rules;
    let tip = '';
    console.log('validate: ', form);
    const ok = Object.keys(form).every(key => {
      if (key in rules) {
        const rule = rules[key];
        const value = form[key];
        this.rule = rule;

        // 没有值
        // console.log(rule);
        if (isFunction(rule.validator)) {
          const result = rule.validator(value, rule);
          if (result !== undefined && result !== true) {
            tip = result;
            return false;
          }
          return true;
        } else if (!value && value !== 0) {
          if (rule.required) {
            tip = rule.emptyTip;
            return false;
          }
        } else if (rule.pattern) {
          const result = rule.pattern.test(value);
          if (!result) tip = rule.invalidTip;
          return result;
        }
      }
      return true;
    })
    return {
      ok,
      tip,
    }
  }
}
