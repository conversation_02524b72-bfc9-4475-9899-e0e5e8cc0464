<template>
  <container @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="洗车服务详情">
      <x-button  slot="left" type="back"></x-button>
    </x-header>
    <content-view ref="view" :status="status" @reload="reload" @scroll="onScroll">
      <template v-if="status == AppStatus.READY">
        <swiper :autoplay="pageActive" :slides="wash.images" @click="playShopPhotos"></swiper>
        <div class="service-info">
          <h3 class="service-title">{{wash.title}}</h3>
          <p class="service-desc">{{wash.subtitle}}</p>
        </div>

        <panel title="洗车服务流程" class="shop-car_wash">
          <div class="service-detail" v-html="wash.desc">
          </div>
        </panel>
      </template>
    </content-view>
  </container>
</template>

<style lang="scss">
  .service-info {
      background: white;
      padding: 10px;
  }
  .shop-car_wash {
    .panel-title {
      color:black;
    }
  }
  .service-desc {
      color: gray;
  }

</style>

<script>
import { mapState, mapActions } from 'vuex';
import { formatDate } from '@/utils';
import { AppStatus, ImageType } from '@/common/enums';
import { playPhotos } from '@/bridge';
import AppearDetector from '@/lib/appearDetector';
import { getImageURL } from '@/common/image';
import { Header, HeaderButton, Container, ContentView, Panel } from '@/components';
import Swiper from '@/components/Swiper/index';

export default {
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      pageActive: true,
    };
  },
  mounted() {
    this.initAppearDetector();
  },
  activated() {
    this.pageActive = true;
  },
  deactivated() {
    this.pageActive = false;
  },
  computed: {
    ...mapState({
      wash(state) {
        const data = state.views.shop_car_wash;
        if (!(data.images instanceof Array)) {
          data.images = data.images.split(',');
        }
        return data;
      },
    })
  },
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    ContentView,
    Swiper,
    Panel,
  },
  methods: {
    ...mapActions(['getShopCarWashData']),
    go(url) {
      this.$router.push(url);
    },
    onLeave() {
      this.status = AppStatus.LOADING;
      this.$destroy(); 
    },
    onResume() {
      // this.status = AppStatus.READY;
    },
    init() {
      const shopId = this.$route.params.id;
      this.getShopCarWashData(shopId).then(res => {
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
      });
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, ImageType.MEDIUM),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos
      };
      playPhotos(option);
    },
    playShopPhotos(index) {
      this.playPhotos(this.wash.images, index);
    },
    initAppearDetector() {
      const detector = new AppearDetector({
        onAppear(el, cvm) {
          if (cvm) {
            cvm.$emit('appear');
          }
        }
      });
      this.detector = detector;
    },
    onScroll() {
      this.detector.lazyLoad();
    },
    addToAppearManager(e) {
      this.detector.addWatch(e.$el, e);
    },
  }
};
</script>
