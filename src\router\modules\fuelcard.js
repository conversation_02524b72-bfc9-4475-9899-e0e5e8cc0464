import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/fuelcard/cardindex',
    name: '办加油卡',
    component: resolve => {
      import(/* webpackChunkName: "cardintro" */ '@/views/packages/fuelcard/CardIntro.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/cardintro',
    name: '省钱油卡',
    component: resolve => {
      import(/* webpackChunkName: "cardintro" */ '@/views/packages/fuelcard/CardIndex.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/mycard',
    name: '我的油卡',
    component: resolve => {
      import(/* webpackChunkName: "mycard" */ '@/views/packages/fuelcard/MyCard.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/record',
    name: '申办记录',
    component: resolve => {
      import(/* webpackChunkName: "record" */ '@/views/packages/fuelcard/Record.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/recordinfo/:id',
    name: '申办记录详情',
    component: resolve => {
      import(/* webpackChunkName: "recordinfo" */ '@/views/packages/fuelcard/RecordInfo.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/refunds/:id',
    name: '退款申请',
    component: resolve => {
      import(/* webpackChunkName: "refunds" */ '@/views/packages/fuelcard/Refunds.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/reportproblem/:id',
    name: '问题反馈',
    component: resolve => {
      import(/* webpackChunkName: "reportproblem" */ '@/views/packages/fuelcard/ReportProblem.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/cardinfo/:id',
    name: '油卡详情',
    component: resolve => {
      import(/* webpackChunkName: "cardinfo" */ '@/views/packages/fuelcard/CardInfo.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/rechargerecord/:id',
    name: '充值记录',
    component: resolve => {
      import(/* webpackChunkName: "rechargerecord" */ '@/views/packages/fuelcard/RechargeRecord.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/bindcard',
    name: '油卡绑定',
    component: resolve => {
      import(/* webpackChunkName: "bindcard" */ '@/views/packages/fuelcard/BindCard.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/recharge/:cardTypeId/:id',
    name: '油卡充值',
    component: resolve => {
      import(/* webpackChunkName: "recharge" */ '@/views/packages/fuelcard/Recharge.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/prestore',
    name: 'fuelcard-prestore',
    component: resolve => {
      import(/* webpackChunkName: "prestore" */ '@/views/packages/fuelcard/Prestore.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/applycard',
    name: '办理油卡',
    component: resolve => {
      import(/* webpackChunkName: "applycard" */ '@/views/packages/fuelcard/ApplyCard.vue').then(resolve).catch(handleError);
    }
  },
  {
    path: '/fuelcard/updateapplycard/:id',
    name: 'fuelcard-updateapplycard',
    component: resolve => {
      import(/* webpackChunkName: "updateapplycard" */ '@/views/packages/fuelcard/UpdateApplyCard.vue').then(resolve).catch(handleError);
    }
  },
  {
    path: '/fuelcard/pay/result/:id',
    name: '支付详情',
    component: resolve => {
      import(/* webpackChunkName: "result" */ '@/views/packages/fuelcard/PayResult.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/qa',
    name: '油卡常见问题',
    component: resolve => {
      import(/* webpackChunkName: "fuelcardQA" */ '@/views/packages/fuelcard/fuelcardQA.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/invoice/:cardId',
    name: '开发票-选择开票订单',
    component: resolve => {
      import(/* webpackChunkName: "invoice" */ '@/views/packages/fuelcard/Invoice.vue').then(resolve).catch(handleError);
    },
  },
  { // 开票信息提交
    path: '/fuelcard/applyinvoice',
    name: 'applyinvoice',
    component: resolve => {
      import(/* webpackChunkName: "applyinvoice" */ '@/views/packages/fuelcard/ApplyInvoice.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/pastinvoice/:cardId',
    name: '开发票-开票历史',
    component: resolve => {
      import(/* webpackChunkName: "pastinvoice" */ '@/views/packages/fuelcard/PastInvoice.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/invoiceinfo/:id',
    name: '开发票-开票详情',
    component: resolve => {
      import(/* webpackChunkName: "invoiceinfo" */ '@/views/packages/fuelcard/InvoiceInfo.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/invoicerecharge',
    // name: '开发票-开票详情-充值记录',
    name: 'InvoiceTecharge',
    component: resolve => {
      import(/* webpackChunkName: "invoicerecharge" */ '@/views/packages/fuelcard/InvoiceRecharge.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/feedback/:cardUserId',
    name: '油卡问题反馈列表',
    component: resolve => {
      import(/* webpackChunkName: "feedback" */ '@/views/packages/fuelcard/Feedback.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/fuelcard/feedbackinfo/:id',
    name: '油卡问题反馈详情',
    component: resolve => {
      import(/* webpackChunkName: "feedbackinfo" */ '@/views/packages/fuelcard/FeedbackInfo.vue').then(resolve).catch(handleError);
    },
  },
];
