<template>
  <div class="activity-warp-list" @click="toGoodsDetail(value)">
    <div class="item-box">
      <biz-image :src="cover" class="goods-image" :lazy="true"> </biz-image>
      <div class="content">
        <div class="title-wrap">
          <div class="title">
            <span
              class="promotion-icon"
              :style="{ background: this.propData.keyWordsSetting.color }"
              v-if="this.propData.keyWordsSetting.keywords"
              >{{ this.propData.keyWordsSetting.keywords }}</span
            >
            {{ value.title }}
          </div>
        </div>

        <div class="vip-price">
          <div class="price" v-if="saveMoney > 0">¥{{ value.salesPrice }}</div>
          <span class="lable" v-if="saveMoney > 0">VIP:</span>
          <span class="text"
            ><span class="lable">¥</span> {{ value.vipPrice || 0 }}</span
          >
          <span class="save" v-if="saveMoney > 0">可省{{ saveMoney }}</span>
          <div class="vip-certificate" v-if="Object.keys(isContent).length > 0">
            <span class="coupon">{{ isContent.reelAmount }}元券</span>
            <span class="coupon-text"
              >预计最低
              <span class="rmb">{{
                (value.vipPrice - isContent.reelAmount).toFixed(2)
              }}</span></span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';

export default {
  name: 'ActivityItem',
  mixins: [mixinAuthRouter, mixinShare],
  components: {},
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
    itemInfoData: {
      type: Object,
      default() {
        return {};
      },
    },
  },

  data() {
    return {
      couponData: [],
      // couponData: [
      //   {
      //     name: '40元券',
      //     restrictionsAmount: 500,
      //     reelAmount: 40,
      //   },
      //   {
      //     name: '30元券',
      //     restrictionsAmount: 300,
      //     reelAmount: 30,
      //   },
      //   {
      //     name: '15元券',
      //     restrictionsAmount: 200,
      //     reelAmount: 15,
      //   },
      //   {
      //     name: '5元券',
      //     restrictionsAmount: 50,
      //     reelAmount: 5,
      //   },
      //   {
      //     name: '3元券',
      //     restrictionsAmount: 29,
      //     reelAmount: 3,
      //   },
      //   {
      //     name: '2元券',
      //     restrictionsAmount: 2.01,
      //     reelAmount: 2,
      //   },
      // ],
    };
  },

  computed: {
    propData() {
      return this.itemInfoData;
    },
    cover() {
      return (this.value.image || '').split(',')[0];
    },
    saveMoney() {
      return (this.value.salesPrice - this.value.vipPrice).toFixed(2);
    },
    // 优惠券是否满足vip价格
    isContent() {
      let maxValue = {};
      if (this.couponData.length > 0) {
        let arr = []; // 获取满足vip价格的数组
        this.couponData.forEach(item => {
          if (item.restrictionsAmount <= this.value.vipPrice) {
            arr.push(item);
          }
        });
        // 获取满足vip价格最大的对象
        if (arr.length > 0) {
          maxValue = arr.reduce((prev, current) => {
            return prev.reelAmount >= current.reelAmount &&
              prev.restrictionsAmount >= current.restrictionsAmount
              ? prev
              : current;
          });
          return maxValue;
        } else {
          return maxValue;
        }
      } else {
        return maxValue;
      }
    },
  },
  created() {},
  mounted() {
    this.couponData = this.itemInfoData.discountsReels;
    // console.log(this.propData, 'propData')
  },
  methods: {
    // 查看商品详情
    toGoodsDetail(item) {
      this.$_router_pageTo(`/mall/goods/${item.id}`, {
        theme: 'light',
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.activity-warp-list {
  background: #ffffff;
  border-radius: 10px;
  padding: 15px 24px 17px 15px;
  box-sizing: border-box;
  .item-box {
    display: flex;
    // align-items: center;
    .goods-image {
      width: 90px;
      height: 90px;
      border-radius: 10px;
      flex-shrink: 0;
    }
    .content {
      margin-left: 15px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title-wrap {
        flex: 1;
      }
      .title {
        text-align: justify;
        color: #333333;
        font-size: 14px;
        line-height: 18px;
        @include multiline-ov;
        .promotion-icon {
          // background: linear-gradient(90deg, #F034FF, #AD2FF7);
          border-radius: 2px;
          font-size: 11px;
          color: #ffffff;
          // display: inline-block;
          display: inline;
          white-space: normal;
          line-height: 14px;
          padding: 0 2px;
        }
      }

      .vip-price {
        display: inline-block;
        line-height: normal;
        // margin-top: 3px;
        // margin-bottom: 6px;
        .price {
          font-size: 12px;
          line-height: 14px;
          // margin-top: 6px;
          color: #666666;
        }
        span {
          display: inline-block;
          line-height: normal;
        }
        .lable,
        .text {
          font-weight: bold;
          color: #fd4925;
        }
        .lable {
          font-size: 12px;
        }
        .text {
          font-size: 18px;
        }
        .save {
          font-size: 10px;
          color: #ffffff;
          background: #fd4925;
          border-radius: 8px 8px 8px 0px;
          margin-left: 5px;
          display: inline-block;
          padding: 2px 7px;
          vertical-align: text-bottom;
        }
      }
      .vip-certificate {
        line-height: normal;
        .coupon {
          display: inline-block;
          background: linear-gradient(-90deg, #fd6937, #fa4135);
          border-radius: 2px;
          font-size: 10px;
          color: #ffffff;
          padding: 1px 6px;
        }
        .coupon-text {
          font-size: 10px;
          font-weight: bold;
          color: #333333;
          background: #ffefef;
          border-radius: 2px;
          padding: 1px 6px;
          margin-left: 5px;
          .rmb {
            color: #fd4925;
          }
        }
      }
    }
  }
}
</style>
