import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';

const MODULE = '/washcard';
function getRoutePath(path) {
  return `${MODULE}${path}`;
}

const routers = [
  // {
  //   path: '/combined/washcard',
  //   redirect: '/washcard/market',
  // },
  {
    path: '/combined/washcard/market',
    name: 'CombinedCarWashCardMarket',
    meta: {
      title: '洗车卡列表',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-combined" */ '@/views/packages/carwash-card-combined/pages/wash-card-market/CarWashCardMarket.vue').then(resolve).catch(handleError);
    },
  },
  // {
  //   path: '/combined/washcard/exchange',
  //   name: 'CombinedCarWashCardExchange',
  //   meta: {
  //     title: '洗车卡兑换',
  //   },
  //   component: resolve => {
  //     import(/* webpackChunkName: "wash-card-combined" */ '@/views/packages/carwash-card-combined/pages/WashCardExchange.vue').then(resolve).catch(handleError);
  //   },
  // },
  {
    path: '/combined/washcard/:id/buy',
    name: 'CombinedCarWashCardBuy',
    meta: {
      title: '购买洗车卡',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-combined" */ '@/views/packages/carwash-card-combined/pages/wash-card/WashCardBuy.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/combined/washcard/buy/record',
    name: 'CombinedCarWashCardBuyRecord',
    meta: {
      title: '购买记录',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-combined" */ '@/views/packages/carwash-card-combined/pages/wash-card/WashCardBuyRecord.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/combined/washcard/buy/result',
    name: 'CombinedCarWashCardResult',
    meta: {
      title: '洗车卡',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-combined" */ '@/views/packages/carwash-card-combined/pages/wash-card/WashCardResult.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/combined/washcard/:id/rules',
    name: 'CombinedCarWashCardRules',
    meta: {
      title: '洗车卡使用规则',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-combined" */ '@/views/packages/carwash-card-combined/pages/wash-card/WashCardRules.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/combined/washcard/help',
    name: 'WashCardHelp',
    meta: {
      title: '使用指南',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-combined" */ '@/views/packages/carwash-card-combined/pages/wash-card/WashCardHelp.vue').then(resolve).catch(handleError);
    },
  },
];

export default routers;
// export default BaseRouter;
