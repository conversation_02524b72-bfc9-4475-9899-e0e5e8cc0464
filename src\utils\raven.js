/**
 * 使用Raven.js捕获异常并上报
 * @param {*} args 
 */
export function captureException(e, options) {
  window.Raven && window.Raven.captureException(e, options);
  // console.warn('No Raven !');
}

export function captureExpectionWithData(msg, data, fingerprint) {
  const raven = window.Raven;
  if (raven) {
    raven.captureBreadcrumb({
      message: msg,
      data: data
    });

    captureException(msg, {
      fingerprint: [fingerprint || msg]
    });
  } else {
    console.warn('Sentry:', msg, data, 'fingerprint', '=', fingerprint);
    console.warn('No Raven !');
  }
}
