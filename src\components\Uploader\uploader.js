// import FileAPI from '@/lib/FileAPI.html5'
import upload from './weui-uploader/upload';
import { compress } from './weui-uploader/image';
import { getOrientation, RotateDeg } from './utils';
import { captureExpectionWithData } from '@/utils/raven';

const URL = window.URL || window.webkitURL || window.mozURL;

/**
 * 检测当前环境是否支持图片自动翻转
 * https://juejin.cn/post/6844904194894200846
 * https://github.com/blueimp/JavaScript-Load-Image/blob/master/js/load-image-orientation.js
 * https://github.com/blueimp/JavaScript-Load-Image/issues/97
 */
function checkSupportAutoImageOrientation() {
  return new Promise((resolve, reject) => {
    var testImageURL =
      'data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAA' +
      'AAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBA' +
      'QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE' +
      'BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAIAAwMBEQACEQEDEQH/x' +
      'ABRAAEAAAAAAAAAAAAAAAAAAAAKEAEBAQADAQEAAAAAAAAAAAAGBQQDCAkCBwEBAAAAAAA' +
      'AAAAAAAAAAAAAABEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8AG8T9NfSMEVMhQ' +
      'voP3fFiRZ+MTHDifa/95OFSZU5OzRzxkyejv8ciEfhSceSXGjS8eSdLnZc2HDm4M3BxcXw' +
      'H/9k=';
    var img = document.createElement('img');
    img.onload = function () {
      // Check if the browser supports automatic image orientation:
      const support = img.width === 2 && img.height === 3;
      resolve(support);
    };
    img.onerror = function (err) {
      reject(err);
    };
    img.src = testImageURL;
  });
}

const FileStatus = {
  NONE: 1,
  UPLOADING: 2,
  FAILED: 3,
  UPLOADED: 4,
};

function createFileData(fileId) {
  return {
    id: Math.random() * 1000,
    percent: 100,
    origin: null,
    file: fileId,
    msg: '',
    status: FileStatus.UPLOADED,
  };
}

function transformFileList(files) {
  // 默认文件列表
  let fileList = [],
    values = [];
  if (typeof files === 'string' && files.length > 0) values = [files];
  else if (files instanceof Array) values = files;

  fileList = values.map(function (item, i) {
    if (typeof item === 'string') return createFileData(item);
    return item;
  });
  return fileList;
}

function getThumbnail(file) {
  return new Promise((resolve, reject) => {
    if (URL && URL.createObjectURL) {
      resolve(URL.createObjectURL(file));
    } else {
      getOrientation(file, orientation => {
        const deg = RotateDeg[orientation] || 0;
        const imageFile = FileAPI.Image(file);
        imageFile
          .resize(1, 200, 'min')
          .rotate(deg)
          .get(function (e, minImg) {
            resolve(minImg && minImg.toDataURL(), deg);
          });
      });
    }
  });
}

function createFileId() {
  const t = Date.now();
  const random = Math.round(Math.random() * 1000);
  return [t, random].join('_');
}

function getIdByFile(file) {
  const { type, name, size, lastModified } = file;
  let fileId = file.id || createFileId();
  if (!file.id) {
    file.id = fileId;
  }
  return fileId;
  // return [type, name, size, lastModified].join('_')
}

function formatFileSize(size) {
  return Math.round(size / 1024) + 'KB';
}

function toJSON(data) {
  try {
    return JSON.parse(data);
  } catch (e) {
    return data;
  }
}

function noop() {}
function noopPromise() {
  return new Promise((resolve, reject) => {
    resolve();
  });
}

export default {
  name: 'uploader',
  props: {
    onAdd: {
      type: Function,
      default: noop,
    },
    // Function<Promise>
    onRemove: {
      type: Function,
      default: noopPromise,
    },
    onError: {
      type: Function,
      default: noop,
    },
    onPreview: {
      type: Function,
      default: noop,
    },
    onUploaded: {
      type: Function,
      default: noop,
    },
    // Function<Promise>
    beforeUpload: {
      type: Function,
      default: noopPromise,
    },
    imageUrlGetter: {
      type: Function,
      default(value) {
        return value;
      },
    },
    autoUpload: {
      type: Boolean,
      default: false,
    },
    files: {
      type: Array,
      default() {
        return [];
      },
    },
    uploadUrl: {
      type: String,
      default: '/',
    },
    uploadName: {
      type: String,
      default: 'file',
    },
    params: {
      type: Object,
      default() {
        return {};
      },
    },
    name: {
      type: String,
      default: 'file',
    },
    // 单张图片大小 byte
    maxFileSize: {
      type: Number,
      default: 1024 * 1024 * 10,
    },
    // 最大上传数量
    maxFiles: {
      type: Number,
      default: 1,
    },
    // 是否可多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 图片质量，0-1
    quality: {
      type: Number,
      default: 0.85,
    },
    value: {
      type: Array,
    },
    scale: {
      type: Array,
      default() {
        return [0.1, 10];
      },
    },
    width: {
      type: Array,
      default() {
        return [1, 9999];
      },
    },
    height: {
      type: Array,
      default() {
        return [1, 9999];
      },
    },
  },
  data() {
    // 默认文件列表
    // const files = this.files || ;
    const fileList = transformFileList(this.value);
    // if (this.maxFiles) settings.maxFiles = this.maxFiles;
    // if (this.maxFileSize) settings.maxFileSize = this.maxFileSize;
    // console.log('fileList:', this.value);
    return {
      list: fileList,
      uploadQueue: new Map(),
      supportAutoImageOrientation: false,
    };
  },
  computed: {
    imageList() {
      return this.list.map(item => {
        const url =
          item.status === FileStatus.UPLOADED
            ? this.imageUrlGetter(item.file)
            : item.file;
        return {
          ...item,
          url: url,
        };
      });
    },
    values() {
      return this.list
        .filter(item => item.status == FileStatus.UPLOADED)
        .map(item => item.file);
    },
    avaliableAdditionalImageCount() {
      return this.maxFiles - this.list.length;
    },
    couldMultipleSelect() {
      return this.multiple && this.maxFiles > 1;
    },
  },
  watch: {
    value(val, oldVal) {
      const [a, b] = [String(val), String(this.values)];
      // console.log('values change:', a !== b);
      if (a !== b) {
        // console.warn(a, 'vs', b);
        this.$nextTick(() => {
          // console.warn('comp:', val);
          this.list = transformFileList(val);
        });
      }
    },
    list(val) {
      this.$emit('change', val);
    },
  },
  mounted() {
    console.log('ready...');
    checkSupportAutoImageOrientation().then(s => {
      this.supportAutoImageOrientation = s;
    });
    // 跨域上传需要设为false
    // FileAPI.withCredentials = false;
    /* this.$watch('files', function () {
      this.$set('list', transformFileList(files))
    }) */
    const fileInput = this.$el.querySelector('input[type="file"]');
    this.initFileInput(fileInput);
  },
  methods: {
    clear() {
      this.list = [];
    },
    isFileSizeOk(size) {
      return size <= this.maxFileSize;
    },

    isDimensionOk(width, height) {
      return this.checkDimension(width, height) === true;
    },

    checkDimension(width, height) {
      const {
        width: widthConfig,
        height: heightConfig,
        scale: scaleConfig,
      } = this;

      if (widthConfig instanceof Array) {
        if (width < widthConfig[0] || width > widthConfig[1])
          return '图片宽度不合规范！';
      } else if (width != widthConfig) return '图片宽度不合规范！';

      if (heightConfig instanceof Array) {
        if (height < heightConfig[0] || height > heightConfig[1])
          return '图片高度不合规范！';
      } else if (height != widthConfig) return '图片高度不合规范！';

      const scale = width / height;
      if (scaleConfig instanceof Array) {
        if (scale < scaleConfig[0] || scale > scaleConfig[1])
          return '图片宽高比不合规范！';
      } else if (scale != scaleConfig) return '图片宽高比不合规范！';
      return true;
    },

    getFileClass(item) {
      return {
        file: true,
        'file-uploading': item.status == FileStatus.UPLOADING,
        'file-upload-fail': item.status == FileStatus.FAILED,
        'file-uploaded': item.status == FileStatus.UPLOADED,
      };
    },

    handleClick(item, index) {
      if (item.status == FileStatus.FAILED) {
        this.reUploadFile(item);
      } else if (item.status == FileStatus.UPLOADED) {
        const files = this.list
          .filter(item => item.status === FileStatus.UPLOADED)
          .map(item => item.file);
        this.onPreview(item.file, index, files);
      }
    },

    /**
     * 上传重试
     */
    reUploadFile(item) {
      const file = item.origin;
      this.updateFile(file, {
        status: FileStatus.UPLOADING,
        percent: 0,
      });
      this.uploadOneFile(file, 'reupload');
    },

    clearFileInput() {
      this.$file.value = '';
    },

    initFileInput($file) {
      let that = this;
      this.$file = $file;
      $file.addEventListener('change', evt => {
        console.log('file change...', evt);
        var files = Array.prototype.slice.call($file.files); // Retrieve file list
        if (files.length && files.length > that.avaliableAdditionalImageCount) {
          let tip =
            '最多能选择添加' + that.avaliableAdditionalImageCount + '张图片';
          that.handleError(tip);
          console.warn(tip);
          return;
        }
        files.forEach(file => {
          // 某些浏览器可能得不到文件大小信息，如IE9-
          // android4.4下file.type为空字符串
          if (file.type && !/^image/.test(file.type)) {
            that.handleError(file.name + '不是合法的图片文件！');
            return false;
          } else if (!that.isFileSizeOk(file.size)) {
            let tip = '单张图片最大' + formatFileSize(that.maxFileSize);
            that.handleError(tip);
            return false;
          }
          // else if (!that.isDimensionOk(info.width, info.height)) {
          //   let tip = that.checkDimension(info.width, info.height)
          //   that.handleError(tip)
          //   return false
          // }
          that.addFile(file).then(() => {
            that.uploadOneFile(file);
          });
          return true;
        });
      });
    },
    addFile(file) {
      const that = this;
      const fileId = getIdByFile(file);
      return getThumbnail(file)
        .then(thumbnail => {
          that.list.push({
            id: fileId,
            percent: 0,
            origin: file,
            // file: minImg && minImg.toDataURL(),
            file: thumbnail,
            msg: '',
            status: FileStatus.NONE,
          });
        })
        .catch(e => {
          that.handleError(e);
        });
    },
    updateFile(file, data) {
      const fileId = getIdByFile(file);
      this.list = this.list.map(item => {
        if (item.id == fileId) {
          // 上传成功后释放内存
          if (data.status === FileStatus.UPLOADED) {
            URL.revokeObjectURL(item.file);
          }
          item = Object.assign(item, data);
        }
        return item;
      });
      if (data && data.status == FileStatus.UPLOADED) {
        this.uploadQueue.delete(file);
      }
      this.doInput();
    },
    doInput() {
      const list = this.list
        .filter(item => item.status === FileStatus.UPLOADED)
        .map(item => item.file);
      this.$emit('input', list);
    },
    uploadOneFile(file, action = 'upload') {
      const that = this;
      const next = function () {
        return new Promise((resolve, reject) => {
          that
            .uploadFile(file)
            .then(result => {
              that.updateFile(file, {
                file: result,
                status: FileStatus.UPLOADED,
              });
              that.onAdd.call(null, result);
              resolve(result);
            })
            .catch(err => {
              captureExpectionWithData(
                err,
                {
                  file,
                  action,
                },
                'file-upload'
              );
              that.handleError(err);
              that.updateFile(file, {
                status: FileStatus.FAILED,
                msg: err,
              });
              reject(err);
            });
        });
      };
      this.beforeUpload(file, action)
        .then(res => {
          if (this.autoUpload || action === 'reupload') {
            next();
          } else {
            this.uploadQueue.set(file, next);
          }
        })
        .catch(this.onError);
    },
    submit() {
      const queue = [];
      const queueManager = this.uploadQueue;
      for (let next of queueManager.values()) {
        queue.push(next());
      }
      return Promise.all(queue).then(files => {
        queueManager.clear();
      });
    },
    uploadFile(originFile) {
      const that = this;
      return new Promise((resolve, reject) => {
        that.updateFile(originFile, {
          status: FileStatus.UPLOADING,
        });
        const options = {
          url: that.uploadUrl,
          type: 'file',
          file: originFile,
          fileVal: that.uploadName,
          auto: that.autoUpload,
          xhrFields: {},
          // 对不支持自动翻转的终端进行修正
          fixOrientation: !that.supportAutoImageOrientation,
          compress: {
            quality: that.quality, // jpeg quality
            width: that.width[1],
            height: that.height[1],
          },
          onBeforeSend(file, data, headers) {
            return Object.assign(data, that.params);
          },
          onProgress(file, percent) {
            // console.log(file. percent);
            that.updateFile(file, {
              percent: percent,
            });
          },
          onError(file, err) {
            reject(err);
          },
          onSuccess: function (file, res) {
            that.clearFileInput();
            let fileStatus = FileStatus.FAILED;
            if (res) {
              let result = that.onUploaded.call(null, res);
              fileStatus = FileStatus.UPLOADED;
              resolve(result);
            }
            that.updateFile(file, {
              status: fileStatus,
            });
          },
        };
        compress(originFile, options, blob => {
          // debugger
          options.file = blob;
          upload(options);
        });
      });
    },
    removeFile(fileItem) {
      return this.onRemove(fileItem)
        .then(res => {
          this.uploadQueue.delete(fileItem.origin);
          return Promise.resolve();
        })
        .then(res => {
          this.list = this.list.filter(function (item) {
            return item.id != fileItem.id;
          });
          this.doInput();
        });
    },
    handleError(err) {
      this.clearFileInput();
      this.onError.call(null, err);
    },

    /**
     * 处理文件选择区域点击
     * 在用户点击选择文件前清空input值，确保能重新选择相同文件
     */
    handleFileSelect() {
      // 清空input值，确保选择相同文件时也能触发change事件
      this.clearFileInput();
    },
  },
};
