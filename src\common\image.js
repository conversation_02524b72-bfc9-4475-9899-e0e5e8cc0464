import { setItem, getItem } from '@/store/storage';

/**
 * 异步加载一个图片，返回图片地址和宽高信息
 * @param {*} src
 */
export function loadImage(src) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    img.onload = function onload() {
      resolve({
        src,
        width: img.width,
        height: img.height,
      });
    };
    img.onerror = function onerror(e) {
      reject(src);
    };
    if (img.width > 0) {
      resolve({
        src,
        width: img.width,
        height: img.height,
      });
    }
  });
}

/**
 * 根据资源获取图片url，支持七牛云图片id，或图片url，图片路径
 * 曾经有老版本app（3.8.x）预览图片不支持https协议的图片地址
 * @param {string} value 图片ID或路径
 * @param {string} type 可选，七牛云图片参数
 * @param {string} protocol 图片协议
 */
export function getImageURL(value, type = '?', protocol = 'https:') {
  // console.log(...arguments);
  if (!value) return '';
  // file, data, bolb, http, https ...
  if (/^\w+:/.test(value)) return value;

  // `./` 开头 认为是相对路径
  if (/^\./.test(value)) return value;

  // `/` 开头认为是绝对路径
  if (/^\/\//.test(value)) return value;

  // `img/` 目录开头的，认为是相对路径
  if (/^img\//.test(value)) return value;

  // android某版本有些图片id是路径，如/storage/6633-6466/DCIM/Camera/20161002_154738.jpg
  if (/^\//.test(value) && !/storage/.test(value)) return value;

  // 其他值都认为是
  return `${protocol}//img.jgrm.net/${value}${type}`;
}

const UPLOAD_TOKEN = 'iup_token';

export function getImageUploadToken() {
  try {
    const data = JSON.parse(getItem(UPLOAD_TOKEN));
    if (data.time > Date.now()) return data.token;
    else {
      return null;
    }
  } catch (e) {
    return null;
  }
}

export function setImageUploadToken(token) {
  // const UPLOAD_TOKEN = 'iup_token';
  const DURATION = 1000 * 60 * 60;
  const data = JSON.stringify({
    time: Date.now() + DURATION,
    token,
  });
  setItem(UPLOAD_TOKEN, data);
}

// 获取图片的宽高比
export function getAspectRatio(value) {
  return new Promise((resolve, reject) => {
    // 检查URL是否已提供
    if (!value) {
      reject('图片链接不能为空');
    }

    // 检查URL是否以http开头
    if (!/^http/.test(value)) {
      // 如果不是http开头，处理其它情况
      value = getImageURL(value);
    }
    // 创建一个新的Image对象
    var img = new Image();

    // 添加onload事件处理程序
    img.onload = function () {
      // 图片加载完成后，获取宽高比
      var aspectRatio = img.width / img.height;
      resolve(aspectRatio);
    };

    // 添加onerror事件处理程序
    img.onerror = function () {
      reject('Failed to load image.');
    };

    // 设置来源URL，注意这将开始加载图片
    img.src = value;
  });
}
