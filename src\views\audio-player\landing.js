/* eslint-disable */
import { mapGetters, mapMutations, mapActions } from 'vuex';
import { setOrderChannel } from '@/store/storage';
import { dialog, back } from '@/bus';
import { isInPC } from '@/common/env';
import Player from './components/Player';

export default function (name) {
  return {
    name: name,
    provide: {
      DEFAULT_AVATAR: require('@/assets/images/account/avatar.png'),
    },
    computed: {
      ...mapGetters(['aliveViews']),
    },
    beforeMount() {
    },
    mounted() {
      // console.log(`${name} landing...`, this.$route)
      // 配置渠道
      const channel = this.$route.query.channel;
      if (channel) {
        setOrderChannel(channel);
      }
      if (isInPC) {
        dialog().alert('建议您通过手机端访问本页面，以获得更佳使用体验', {
          title: '温馨提示'
        })
      }
    },
    methods: {
      ...mapActions(['select_play']),
    },
    render() {
      // 大驼峰转成连接符形式
      const parentClass = name.replace(/[A-Z]/g, (w, i) => {
        return (i > 0 ? '-' : '') + w.toLowerCase()
      })
      const styles = {
        position: 'absolute',
        width: '100%',
        height: '100%',
        top: 0,
        left: 0,
        bottom: 0,
      }
      return (<Div style={styles} class={`${parentClass}`}>
        <KeepAlive include={this.aliveViews}>
          <RouterView></RouterView>
        </KeepAlive>
        <Player></Player>
      </Div>)
    }
  }
};
