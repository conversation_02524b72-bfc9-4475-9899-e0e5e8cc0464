import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/ticket/select',
    name: 'ticket-select',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-select" */ '@/views/packages/ticket/TicketSelect.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/ticket/list',
    name: 'ticket-list',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-list" */ '@/views/packages/ticket/TicketList.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 领劵中心 */ path: '/ticket/center',
    name: 'ticket-center',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-center" */ '@/views/packages/ticket/pages/TicketList.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 优惠劵详情 */ path: '/ticket/couponinfo/:id',
    name: 'ticket-info',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-info" */ '@/views/packages/ticket/pages/CouponInfo.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 优惠劵使用 */ path: '/ticket/usecoupon/:id',
    name: 'ticket-use',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-use" */ '@/views/packages/ticket/pages/UseCoupon.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 适用商品 */ path: '/ticket/goodslist',
    name: 'ticket-goods',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-use" */ '@/views/packages/ticket/pages/ticket-goods.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 优惠券适用门店 */ path: '/ticket/shops',
    name: 'ticket-shops',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-use" */ '@/views/packages/ticket/pages/ticket-shops.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 适用门店 */ path: '/ticket/shoplist/:id',
    name: 'ticket-shop',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-use" */ '@/views/packages/ticket/pages/ShopList.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 核销成功 */ path: '/ticket/success',
    name: 'ticket-success',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-use" */ '@/views/packages/ticket/pages/Success.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    /* 核销成功 */ path: '/coupon/exchange',
    name: 'coupon-exchange',
    component: resolve => {
      import(
        /* webpackChunkName: "ticket-use" */ '@/views/packages/ticket/CouponExchange.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
