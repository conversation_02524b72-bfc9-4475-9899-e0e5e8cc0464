<template>
  <div class="prize-pop-content">
    <slot name="content"></slot>
    <div v-if="receiveDesc" class="receive-desc">{{ receiveDesc }}</div>
    <div class="pop-bottom-btn">
      <slot name="buttons">
        <van-button class="" round block type="danger" @click="handleConfirm">{{ confirmButtonText }}</van-button>
      </slot>
    </div>
    <div class="tips">{{ prizeTip }}</div>
  </div>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';
import { Button } from 'vant';

export default {
  name: 'PrizePopBase',
  components: {
    [Button.name]: Button
  },
  props: {
    prize: {
      type: Object,
      default: () => ({})
    },
    prizeType: {
      type: Number,
      required: true
    },
    prizeTip: {
      type: String,
      default: ''
    }
  },
  computed: {
    confirmButtonText() {
      if (
        this.prizeType == PrizeTypeEnum.COUPON ||
        this.prizeType == PrizeTypeEnum.WELFARE_COUPON
      ) {
        return '立即使用';
      } else if (this.prizeType == PrizeTypeEnum.NORMAL) {
        return '完善地址';
      } else {
        return '开心收下';
      }
    },
    receiveDesc() {
      return '';
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm');
    },
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="scss" scoped>
.prize-pop-content {
  line-height: 1;

  .receive-desc {
    font-size: 18px;
    color: #111111;
    text-align: center;
    line-height: 1.5;
  }

  .pop-bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    margin-top: 12px;

    .van-button {
      flex: 1;
      width: 100%;
      font-size: 15px;
      line-height: 52px;
      height: 52px;

      &:not(:first-child) {
        margin-left: 15px;
      }
    }

    .close-btn {
      background: transparent;
      border-color: #fd4925;
      color: #fd4925;
    }

    .van-button--danger {
      background: linear-gradient(270deg, #fd4925 0%, #fd1a41 100%);
    }
  }

  .tips {
    margin-top: 12px;
    font-size: 11px;
    color: #666666;
    line-height: 13px;
    text-align: center;
  }
}
</style>
