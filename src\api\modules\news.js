import { doGet, doPost } from '../request/';
import APIs from '../apis';

// 暂时使用v=393
APIs.extend({
  '/news/categories': '/Radio/news/menu/list?sid=HN0001',
  '/news/list': '/Radio/news/newslistbytype',
  '/news/categories/insurance':
    '/Radio/insurance/consumerprotection/news/menu/list',
  '/news/list/insurance':
    '/Radio/insurance/consumerprotection/news/newslistbytype',
});

/**
 * 获取新闻分类列表
 */
export function getNewsCategories() {
  const url = APIs.get('/news/categories');
  return doGet(url);
}

/**
 * 获取新闻列表
 * @param { object } params
 */
export function getNewsList2(params) {
  const data = Object.assign(
    {
      t: Date.now(),
      type: 1,
      orient: 0,
      max: 10,
      sid: 'HN0001',
    },
    params
  );
  const url = APIs.get('/news/list');
  return doGet(url, data);
}

/**
 * 获取新闻分类列表
 */
export function getNewsCategoriesInsurance() {
  const url = APIs.get('/news/categories/insurance');
  return doGet(url);
}

/**
 * 获取新闻列表
 * @param { object } params
 */
export function getNewsListInsurance(params) {
  const data = Object.assign(
    {
      t: Date.now(),
      type: 1,
      orient: 0,
      max: 10,
      sid: 'HN0001',
    },
    params
  );
  const url = APIs.get('/news/list/insurance');
  return doGet(url, data);
}
