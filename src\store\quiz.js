import QuizService from '@/api/quizService';
import { createRoom, joinRoom } from '@/views/live-battle/api';

const MESSAGE_HANDLERS = {
  start: 'HANDLE_START',
  answer: 'HANDLE_ANSWER',
  end: 'HANDLE_END',
};

const state = {
  connection: {
    isConnected: false,
    isConnecting: false,
    error: null,
  },
  user: {
    name: '',
    roomId: '',
    userId: '',
    isHouseOwner: false, // 是否房主
  },
  quiz: {
    status: 'waiting', // waiting, active, result, ended —— “等待对手”、“正在答题”、“结果展示”、“已结束”
    questions: [],
    players: {}, // 玩家信息
    currentIndex: 1, // 当前题目索引
    answers: [],
    results: null, // 双方比分
    currentQuestionRemainingSeconds: 0, // 当前题剩余时间
    serverStartTime: 0,
    totalDuration: 0, // PK答题限时时间（秒）
    perQuestionDuration: 0, // 每题答题限时时间（秒）
    questionVersion: '', // 题目版本标识
    currentTimer: null, // 当前题目计时器
    currentQuestionEndTime: 0, // 当前题目结束时间戳
    showingResult: false, // 是否正在显示结果
    resultShowDuration: 1500, // 结果显示时间（毫秒）
  },
};

const mutations = {
  SHOW_ANSWER_RESULT(state) {
    state.quiz.showingResult = true;
    state.quiz.status = 'result';
  },

  HIDE_ANSWER_RESULT(state) {
    state.quiz.showingResult = false;
    state.quiz.status = 'active';
  },
  SET_USER(state, user) {
    state.user = { ...state.user, ...user };
  },
  SET_RESULTS(state, results) {
    state.quiz.results = results;
  },
  SET_PLAYERS(state, players) {
    state.quiz.players = players;
  },
  SET_CONNECTION(state, payload) {
    state.connection = { ...state.connection, ...payload };
  },

  SET_QUIZ_DATA(state, { key, value }) {
    state.quiz[key] = value;
  },

  ADD_ANSWER(state, answer) {
    state.quiz.answers = [...state.quiz.answers, answer];
  },

  RESET_QUIZ(state) {
    state.quiz = {
      status: 'waiting',
      questions: [],
      players: {},
      currentIndex: 1,
      answers: [],
      results: null,
      currentQuestionRemainingSeconds: 0,
      serverStartTime: 0,
      totalDuration: 0,
      perQuestionDuration: 0,
      questionVersion: '',
      currentTimer: null,
      currentQuestionEndTime: 0,
      showingResult: false,
      resultShowDuration: 1500,
    };
  },

  CLEAR_TIMER(state) {
    clearInterval(state.quiz.currentTimer);
    state.quiz.currentTimer = null;
  },
  UPDATE_QUESTION_REMAINING_TIME(state) {
    if (state.quiz.currentQuestionEndTime) {
      state.quiz.currentQuestionRemainingSeconds = calculateRemainingTime(
        state.quiz.currentQuestionEndTime
      );
    } else {
      state.quiz.currentQuestionRemainingSeconds = 0;
    }
  },
};

const calculateRemainingTime = endTime => {
  const remaining = endTime - Date.now();
  return Math.max(Math.floor(remaining / 1000), 0);
};
const actions = {
  connect({ commit, state, dispatch }, serverUrl) {
    if (state.connection.isConnecting) return;

    commit('SET_CONNECTION', { isConnecting: true, error: null });

    try {
      QuizService.resetConnect();
      return QuizService.connect(
        serverUrl,
        { userId: state.user.userId, roomId: state.user.roomId },
        {
          onConnect: () => {
            commit('CLEAR_TIMER');
            commit('RESET_QUIZ');
            commit('SET_CONNECTION', {
              isConnected: true,
              isConnecting: false,
            });
            dispatch('subscribeMessages');
          },
          onDisconnect: () => {
            dispatch(MESSAGE_HANDLERS.end, {});
            commit('SET_CONNECTION', { isConnected: false });
          },
          onError: error => {
            dispatch(MESSAGE_HANDLERS.end, {});
            commit('SET_CONNECTION', {
              isConnected: false,
              error: error.message,
            });
          },
        }
      );
    } catch (error) {
      commit('SET_CONNECTION', { isConnected: false, error: error.message });
    }
  },

  subscribeMessages({ commit, state, dispatch }) {
    QuizService.subscribe(`/topic/${state.user.roomId}`, payload => {
      const handler = MESSAGE_HANDLERS[payload.event];
      if (handler) dispatch(handler, payload);
    });
    setTimeout(() => {
      dispatch('joinRoom');
    }, 500);
  },

  joinRoom({ state, commit }) {
    const roomId = state.user.roomId;
    const uid = state.user.userId;
    joinRoom(roomId, {
      uid,
    }).then(room => {
      if (room && room.status == 'FINISHED') {
        commit('SET_QUIZ_DATA', { key: 'status', value: 'ended' });
        QuizService.disconnect();
      }
    });
  },
  // 开始游戏
  startQuiz({ commit, state, dispatch }) {
    commit('SET_QUIZ_DATA', { key: 'status', value: 'active' });
    dispatch('startCurrentQuestionTimer');
  },

  [MESSAGE_HANDLERS.start]({ state, commit, dispatch }, payload) {
    const serverData = payload.data;
    const questions = serverData.questions || [];
    const totalDuration = serverData.replyTime * 60 || 60; // 服务端返回的是分钟，转换为秒
    const perQuestionDuration = Math.floor(totalDuration / questions.length);

    commit('SET_QUIZ_DATA', { key: 'questions', value: questions });
    commit('SET_QUIZ_DATA', {
      key: 'serverStartTime',
      value: serverData.startTimestamp,
    });
    commit('SET_QUIZ_DATA', { key: 'totalDuration', value: totalDuration });
    commit('SET_QUIZ_DATA', {
      key: 'perQuestionDuration',
      value: perQuestionDuration,
    });
    commit('SET_QUIZ_DATA', {
      key: 'questionVersion',
      value: `${serverData.roomId}_${serverData.startTimestamp}`,
    });
    // 设置玩家信息
    commit('SET_PLAYERS', {
      promoter: serverData.promoter,
      opponent: serverData.opponent,
    });
    // 设置当前进度
    let index = state.user.isHouseOwner
      ? serverData.promoter.currentQuestion
      : serverData.opponent.currentQuestion;
    commit('SET_QUIZ_DATA', {
      key: 'currentIndex',
      value: index > 0 ? index : 1,
    });
    // 如果当前题目没有结束时间，则设置结束时间
    if (!state.quiz.currentQuestionEndTime) {
      const startTime = Date.now();
      const endTime = startTime + perQuestionDuration * 1000;
      commit('SET_QUIZ_DATA', {
        key: 'currentQuestionEndTime',
        value: endTime,
      });
    }
    // 设置当前比分
    commit('SET_RESULTS', {
      promoter: {
        score: serverData.promoter.score,
      },
      opponent: {
        score: serverData.opponent.score,
      },
    });

    // if (state.quiz.status !== 'active') {
    //   commit('SET_QUIZ_DATA', { key: 'status', value: 'active' });
    //   dispatch('startCurrentQuestionTimer');
    // }
  },

  [MESSAGE_HANDLERS.answer]({ commit }, payload) {
    // 新增用户校验逻辑SET_RESULTS
    if (payload.data && payload.data.answerResultDTO) {
      commit('SET_RESULTS', payload.data.answerResultDTO);
    }
  },

  [MESSAGE_HANDLERS.end]({ commit }, payload) {
    commit('CLEAR_TIMER');
    commit('SET_QUIZ_DATA', { key: 'status', value: 'ended' });
    if (payload.data && payload.data.pkResult) {
      commit('SET_RESULTS', payload.data.pkResult);
    }
    QuizService.disconnect();
  },

  startCurrentQuestionTimer({ commit, state, dispatch }) {
    // 如果正在显示结果，则不启动计时器
    if (state.quiz.showingResult) return;

    commit('CLEAR_TIMER');

    const startTime = Date.now();
    const endTime = startTime + state.quiz.perQuestionDuration * 1000;
    commit('SET_QUIZ_DATA', { key: 'currentQuestionEndTime', value: endTime });
    // 立即更新一次剩余时间
    commit('UPDATE_QUESTION_REMAINING_TIME');

    const timer = setInterval(() => {
      // 如果正在显示结果，暂停倒计时
      if (state.quiz.showingResult) return;

      const remaining = calculateRemainingTime(endTime);
      // 立即更新一次剩余时间
      commit('UPDATE_QUESTION_REMAINING_TIME');

      if (remaining <= 0) {
        commit('CLEAR_TIMER');
        // TODO 此处可能需要筛选出当前用户的答案，answers中可能有对方的答案
        // 检查是否已经提交答案
        const currentQuestionId =
          state.quiz.questions[state.quiz.currentIndex - 1]?.id;
        const hasAnswered = state.quiz.answers.some(
          a =>
            a.questionId === currentQuestionId && a.userId === state.user.userId
        );

        if (!hasAnswered) {
          // 未答题，提交空答案
          dispatch('submitAnswer', { value: null });
        } else {
          // 已答题，直接下一题
          dispatch('nextQuestion');
        }
      }
    }, 1000);

    commit('SET_QUIZ_DATA', { key: 'currentTimer', value: timer });
  },

  nextQuestion({ commit, state, dispatch }) {
    // 如果正在显示结果，则不执行切换
    if (state.quiz.showingResult) return;

    if (state.quiz.currentIndex >= state.quiz.questions.length) {
      dispatch(MESSAGE_HANDLERS.end, {});
      return;
    }

    commit('SET_QUIZ_DATA', {
      key: 'currentIndex',
      value: state.quiz.currentIndex + 1,
    });
    dispatch('startCurrentQuestionTimer');
  },

  submitAnswer({ commit, state, dispatch }, answer) {
    if (!state.connection.isConnected || state.quiz.status !== 'active') {
      return false;
    }

    const currentQuestion = state.quiz.questions[state.quiz.currentIndex - 1];
    const questionId = currentQuestion?.id;

    if (!questionId) return false;

    const answerData = {
      questionId: state.quiz.questions[state.quiz.currentIndex - 1]?.id,
      answer: answer.value,
      roomId: state.user.roomId,
      uid: state.user.userId,
    };

    // 显示答案结果
    commit('SHOW_ANSWER_RESULT');

    commit('ADD_ANSWER', answerData);

    QuizService.send('/app/pk/answer', answerData);
    // 延迟切换题目以便显示结果
    setTimeout(() => {
      if (state.quiz.currentIndex < state.quiz.questions.length) {
        commit('HIDE_ANSWER_RESULT');
      }
      dispatch('nextQuestion');
    }, state.quiz.resultShowDuration);

    return true;
  },

  disconnect({ commit }) {
    commit('CLEAR_TIMER');
    commit('SET_CONNECTION', { isConnected: false });
    commit('RESET_QUIZ');
    return QuizService.disconnect();
  },
};

const getters = {
  currentQuestion: state => state.quiz.questions[state.quiz.currentIndex - 1],
  totalTimeLeft: state => {
    const remaining =
      state.quiz.serverStartTime + state.quiz.totalDuration - Date.now();
    return Math.max(remaining, 0);
  },
  progressTime: state => {
    if (state.quiz.perQuestionDuration === 0) return 0;
    return (
      (state.quiz.currentQuestionRemainingSeconds /
        state.quiz.perQuestionDuration) *
      100
    );
  },
  progress: state => {
    if (state.quiz.questions.length === 0) return 0;
    return (state.quiz.currentIndex / state.quiz.questions.length) * 100;
  },
  formattedTime: state => {
    const totalSeconds = Math.ceil(getters.totalTimeLeft(state) / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  },
  // 是否正在显示结果
  isShowingResult: state => state.quiz.showingResult,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
