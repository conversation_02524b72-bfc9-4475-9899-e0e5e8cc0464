<template>
  <div class="feature-card-title" :class="{blue: color == 'blue'}">
    <h2>{{ title }}</h2>
  </div>
</template>

<script>

export default {
  name: 'FeatureCardTitle',
  components: {},
  props: {
    title: {
      type: String,
      default() {
        return '审车服务';
      },
    },
    color: {
      type: String,
      default() {
        return 'yellow'; // blue
      },
    },
  },

  data() {
    return {
      couponData: []
    };
  },

  computed: {
    propData () {
      return this.itemInfoData
    },
  },
  created() {

  },
  mounted () {
  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.feature-card-title{
  position: absolute;
  left: -6px;
  top: -36px;
  padding: 9px 12px;
  box-sizing: border-box;
  width: 267px;
  height: 133px;
  background: linear-gradient(90deg, #FE8209,#FE4925);
  box-shadow: inset -1px 0px 3px 0px #FFFFFF;
  border-radius: 13px;
  &.blue{
    background: linear-gradient(90deg, #033ED1, #020F90);
  }
  >h2{
    padding-left: 20px;
    font-size: 16px;
    font-weight: bold;
    line-height: 20px;
    color: #FFFFFF;
    background: url(../assets/images/feature-title.png) no-repeat left center;
    background-size: 12px 19px;
  }
}
</style>
