import APIModel from '@/api/APIModel';

/**
 * 相亲模块API接口
 */
const api = new APIModel({
  'blind-date/users': '/Radio/dating/user/list/for/app', // 获取相亲用户列表
  'blind-date/user-detail': '/Radio/dating/user/detail/vo/{id}', // 获取用户详情
  'blind-date/activities': '/Radio/blind-date/activities', // 获取活动列表
  'blind-date/activity-detail': '/Radio/blind-date/activities/{id}', // 获取活动详情
  'blind-date/activity-apply': '/Radio/dating/user/save', // 报名活动
  'blind-date/activity-apply/needpay': '/Radio/dating/user/save/with/needpay', // 报名活动-需要支付
  'blind-date/rank': '/Radio/dating/user/flower/ranking', // 获取排行榜数据
  'blind-date/profile': '/Radio/dating/user/getDatingUserByUid', // 根据uid获取信息
  'blind-date/update-id-card': '/Radio/dating/user/idCard/update', // 更新用户身份证信息
  'user/sendFlower': '/Radio/dating/user/sendFlower/{id}', // 给用户送花
  'user/sendHeartbeat': '/Radio/dating/user/sendHeartbeat', // 发送心动
  'user/cancelHeartbeat': '/Radio/dating/user/cancelHeartbeat', // 取消心动
  'dating-activities/paging/list':
    '/Radio/dating/user/dating-activities/paging/list', // 获取相亲活动列表
  'ongoing/dating-activities': '/Radio/dating/user/ongoing/dating-activities', // 获取进行中的相亲活动
  'dating/user/hide': '/Radio/dating/user/hide', // 用户设置为隐藏.
  'dating/user/show': '/Radio/dating/user/unhide/{id}', // 用户设置为显示.
  'dating/user/exit': '/Radio/dating/user/exit/{id}', // 退出相亲
  'sentHeartbeat/list': '/Radio/dating/user/sentHeartbeat/list', // 获取用户发送的心动记录
  'dating/pay/setting/current/info': '/Radio/dating/pay/setting/current/info', // 获取相亲支付设置
  'dating/pay/record/paid/list': '/Radio/dating/pay/record/paid/list', // 获取用户已经支付的记录
  'dating/pay/record/create': '/Radio/dating/pay/record/create', // 创建相亲支付记录
  'cp/list': '/Radio/dating/characterPairing/cp/list', // 分页查询CP列表
  'my/cp/list': '/Radio/dating/characterPairing/my/cp/list', // 获取我的CP列表
  'characterPairing/info': '/Radio/dating/characterPairing/info', // 根据CP组合id查询CP详情
  'characterPairing/datingCharacterPairingDJHelpApply/save':
    '/Radio/dating/characterPairing/datingCharacterPairingDJHelpApply/save', // 保存主持人申请CP匹配记录申请
  userProcessDatingCharacterPairing:
    '/Radio/dating/characterPairing/userProcessDatingCharacterPairing', // 用户处理[看好/不看好]CP
  'receivedHeartbeats/unread/count':
    '/Radio/dating/user/receivedHeartbeats/unread/count', // 获取用户收到的心动记录-未读数量
  'receivedHeartbeats/unread/list':
    '/Radio/dating/user/receivedHeartbeats/unread/list', // 获取用户收到的心动记录-未读列表
  'sendHeartbeat/read': '/Radio/dating/user/sendHeartbeat/read/{id}', // 被心动者查看收到的心动记录-标记为已读
  'user/receivedHeartbeats': '/Radio/dating/user/receivedHeartbeats', // 获取用户收到的心动记录
});

// 获取相亲用户列表
export function getUserList(params = {}) {
  return api.postJSON('blind-date/users', params);
}

// 获取用户详情
export function getUserDetail(id) {
  const url = api.render('blind-date/user-detail', { id });
  return api.doGet(url);
}

// 获取活动列表
export function getActivityList(params = {}) {
  return api.doGet('blind-date/activities', params);
}

// 获取活动详情
export function getActivityDetail(id) {
  return api.doGet('blind-date/activity-detail', { id });
}

// 报名活动
export function applyActivity(data) {
  return api.postJSON('blind-date/activity-apply', data);
}

// 报名活动-需要支付
export function applyActivityWithNeedPay(data) {
  return api.postJSON('blind-date/activity-apply/needpay', data);
}

// 获取排行榜数据
export function getRankList(params = {}) {
  return api.doGet('blind-date/rank', params);
}

// 获取个人资料
export function getProfile() {
  return api.doGet('blind-date/profile');
}

// 送花
export function sendFlower(id) {
  const url = api.render('user/sendFlower', { id });
  return api.doPost(url);
}

// 发送心动
export function sendHeartbeat(data) {
  return api.doPost('user/sendHeartbeat', data);
}

// 取消心动
export function cancelHeartbeat(data) {
  return api.doPost('user/cancelHeartbeat', data);
}

// 获取相亲活动列表
export function getDatingActivitiesList(params = {}) {
  return api.doGet('dating-activities/paging/list', params);
}

// 获取进行中的相亲活动
export function getOngoingDatingActivitiesList(params = {}) {
  return api.doGet('ongoing/dating-activities', params);
}

// 更新用户身份证信息
export function updateIdCardInfo(data) {
  return api.doPost('blind-date/update-id-card', data);
}

// 用户设置为隐藏
export function hideUser(data) {
  return api.doPost('dating/user/hide', data);
}

// 用户设置为显示
export function showUser(data) {
  const url = api.render('dating/user/show', data);
  return api.doPost(url);
}

// 退出相亲
export function exitDating(data) {
  const url = api.render('dating/user/exit', data);
  return api.doPost(url);
}

// 获取用户发送的心动记录
export function getSentHeartbeatList(params = {}) {
  return api.doGet('sentHeartbeat/list', params);
}

// 获取相亲支付设置
export function getPaymentSettings() {
  return api.doGet('dating/pay/setting/current/info');
}

// 获取用户已支付记录
export function getPaymentRecords() {
  return api.doGet('dating/pay/record/paid/list');
}

// 创建相亲支付记录
export function createPaymentRecord(paySettingId) {
  return api.doPost('dating/pay/record/create', { paySettingId });
}

// 分页查询CP列表
export function getCpList(params = {}) {
  return api.doGet('cp/list', params);
}

// 获取我的CP列表
export function getMyCpList(params = {}) {
  return api.doGet('my/cp/list', params);
}

// 根据CP组合id查询CP详情
export function getCpInfo(datingCharacterPairingId) {
  return api.doGet('characterPairing/info', { datingCharacterPairingId });
}

// 保存主持人申请CP匹配记录申请
export function saveDatingCharacterPairingDJHelpApply(data) {
  return api.doPost(
    'characterPairing/datingCharacterPairingDJHelpApply/save',
    data
  );
}

// 用户处理[看好/不看好]CP
export function userProcessDatingCharacterPairing(data) {
  return api.doPost('userProcessDatingCharacterPairing', data);
}

// 获取用户收到的心动记录-未读数量
export function getReceivedHeartbeatsUnreadCount() {
  return api.doGet('receivedHeartbeats/unread/count');
}

// 获取用户收到的心动记录-未读列表
export function getReceivedHeartbeatsUnreadList(params = {}) {
  return api.doGet('receivedHeartbeats/unread/list', params);
}

// 被心动者查看收到的心动记录-标记为已读
export function readSendHeartbeat(id) {
  const url = api.render('sendHeartbeat/read', { id });
  return api.doPost(url);
}

// 获取用户收到的心动记录
export function getReceivedHeartbeats(params = {}) {
  return api.doGet('user/receivedHeartbeats', params);
}
