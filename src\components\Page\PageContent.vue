<template>
  <div class="page-content">
    <slot name="head"></slot>
    <scroller
      v-if="status === AppStatus.READY"
      ref="scroller"
      :refreshAction="refreshAction"
      @refresh="onRefresh"
      @scroll="onScroll"
      @scroll-down="onScrollDown"
    >
      <slot></slot>
    </scroller>
    <page-loader
      v-else
      :status="status"
      :error="error"
      @reload="onReload"
    >
    </page-loader>
    <slot name="foot"></slot>
  </div>
</template>
<script>
  import { AppStatus } from '@/enums';
  import PageLoader from '../Page/PageLoader.vue';
  import Scroller from '../Scroller/index.vue';
  import { isInJGLH, isInWeixin, isIOS, isInWeApp, isAndroid } from '@/common/env';

  // 目前在ios版微信或android版本微信X5内核网页内，与微信下拉手势冲突，禁用下拉刷新
  const couldNotUseRefreshGesture = isInWeixin && (isIOS || /TBS/i.test(navigator.userAgent));
  export default {
    name: 'PageContent',
    props: {
      status: {
        type: [Number, Object, String],
      },
      error: {
        type: [String],
      },
      // 滚动到底部事件触发距离
      onReachBottomDistance: {
        type: Number,
        default: 300,
      },
      refreshAction: {
        type: Number,
        default: 0,
      },
    },
    components: {
      Scroller,
      PageLoader,
    },
    data() {
      return {
        AppStatus,
        busy: false,
      };
    },
    mounted() {
      // window.addEventListener('resize', this.onWindowResize)
    },
    beforeDestroy() {
      // window.removeEventListener('resize', this.onWindowResize)
    },
    activated() {
    },
    computed: {
      // 因目前的下拉刷新方案与微信网页环境冲突，暂时禁止在微信中下拉刷新
      theRefreshAction() {
        return couldNotUseRefreshGesture ? 0 : this.refreshAction;
      }
    },
    methods: {
      onScroll(top) {
        if (this.busy) return;
        this.$emit('scroll', top);
      },
      onFocus(e) {
        // alert(isAndroid);
        // 只有Android端需要执行此操作
        if (isAndroid) {
          console.log(e);
          setTimeout(() => {
            this.scrollInputIntoViewIfNeeded(e.target);
          }, 500);
        }
      },
      onWindowResize() {
        console.log('window resize');
        // this.scrollInputIntoViewIfNeeded(document.activeElement);
        this.getScroller().scrollToElement(document.activeElement);
      },
      scrollInputIntoViewIfNeeded(el) {
        // const el = e.target;
        this.getScroller().scrollToElement(el);
        // console.log('scrollInputIntoViewIfNeeded');
        // const isInput = /input|textarea/i.test(el.tagName);
        // if (isInput) {
        //   console.log(el, isInput);
        //   const rect = el.getBoundingClientRect();
        //   const viewHeight = window.innerHeight;
        //   const differ = viewHeight - rect.bottom - 20;
        //   if (differ < 0) {
        //     this.scroll(Math.abs(differ));
        //   }
        // }
      },
      onRefresh() {
        this.$emit('refresh');
      },
      getScroller() {
        return (this.getScrollerVM() || {}).$el;
      },
      getScrollerVM() {
        return this.$refs.scroller;
      },
      onScrollBottom(top) {
        this.$emit('scroll-bottom', top);
      },
      onScrollDown(distance) {
        if (distance <= this.onReachBottomDistance) {
          console.log('reach-bottom:', distance);
          this.$emit('reach-bottom', distance);
          this.$emit('scroll-bottom', distance); // 未来废弃此事件
        }
      },
      onReload() {
        this.$emit('reload');
      },
      scrollTo(value) {
        this.getScroller().scrollTo(value);
      },
      scrollToElement(el) {
        this.getScrollerVM().scrollToElement(...arguments);
      },
      animateScrollTo(value, timing = 250) {
        this.scrollTo(value);
      },
      scroll(value) {
        this.getScroller().scrollBy(value);
      },
      getHeight() {
        return this.$el.clientHeight();
      },
      getScrollTop() {
        return  this.getScroller().scrollY;
        // return this.$refs.scroller.$el.scrollTop;
      },
    },
  };
</script>

<style lang="scss" scoped>

</style>
