import { doGet, doPost, postJSON, doRequest } from './request/';
// import { isFunction, isPlainObject } from '@/utils/type';
import render from './url-render';
// const host = location.origin;

export class API {
  // 可选，接收一个Map结构的数据对象 {别名：接口地址...}
  constructor(apis = {}) {
    this.apis = apis;
  }

  /**
   * @deprecated
   * @param {string} name 别名或接口地址
   * @param {object} data 数据
   */
  getRequestURL(name, data) {
    return this.render(...arguments);
  }

  /**
   * 渲染一个url模板 ，如 : /api/test/{id}/detail
   * @param {string} name 别名或接口地址
   * @param {object} data 数据对象，属性深度只支持到一级
   */
  render(name, data) {
    const api = this.apis[name] || name;
    if (!api) throw new Error(`${name} 未定义！`);
    return render(`${api}`, data);
  }

  /**
   * get方式请求接口
   * @param {string} name 别名或接口地址
   * @param {object} data 数据对象，属性深度只支持到一级
   */
  doGet(name, params) {
    return doGet(this.render(name, params), params);
  }

  /**
   * post方式请求接口
   * @param {string} name 别名或接口地址
   * @param {object} data 数据对象，属性深度只支持到一级
   */
  doPost(name, params) {
    return doPost(this.render(name, params), params);
  }

  /**
   * 以JSON数据格式post方式请求接口
   * @param {string} name 别名或接口地址
   * @param {object} data 数据对象，属性深度只支持到一级
   */
  postJSON(name, params) {
    return postJSON(this.render(name, params), params);
  }
}

export default API;
