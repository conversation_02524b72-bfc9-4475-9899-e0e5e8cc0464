import { initWeiXinConfig, getOrderPayInfo } from '@/api';
import { isInJglh, isInWeixin, isInWeApp } from '@/common/env';
import { isLongPayEnabled } from '@/store/storage';

interface ShareConfigData {
  title: string;
  desc: string;
  link: string;
  imgUrl: string;
}

interface NavigateMiniProgramData {
  appid: string;
  path: string;
  extraData?: object;
}

interface CallAPIData {
  api: string;
  complete?: () => void;
}

interface WebViewMessage {
  type: 'shareConfig' | 'navigateMiniProgram' | 'callAPI';
  data: ShareConfigData | NavigateMiniProgramData | CallAPIData;
  api?: string;
}

interface OpenMpPage {
  url: string;
  type?: string;
  query?: string;
}

interface Payment {
  oid: string;
  success: string;
  fail: string;
  category: string;
}

interface PreviewImage {
  current: string;
  urls: string[];
}

interface OpenLocation {
  longitude: Number;
  latitude: Number;
  name: string;
  address: string;
  scale?: Number;
}

interface MakePhoneCall {
  number: string;
}

// 封装支付宝 JSSDK 方法集合
const alipayUtils = {
  /**
   * 跳转到登录页面
   * 登录成功后采用跳转刷新页面的方式
   * @param {string} backurl 回跳地址
   */
  login: function (backurl: string) {
    window.my.navigateTo({
      url:
        '/pages/login/index?backurl=' +
        encodeURIComponent(backurl || location.href),
    });
  },
  /**
   * 跳转到小程序页面
   * @param {string} options 跳转配置
   */
  openMiniProgramPage: function (options: OpenMpPage) {
    let _options = Object.assign(
      {
        type: 'navigate', // 或redirect, 默认navigate
        url: '/pages/index/index', // 跳转页面地址, 默认首页
        query: '', // 传递参数 a=1&b=2
      },
      options
    );
    if (_options.type == 'navigate') {
      window.my.navigateTo({
        url: _options.url + (_options.query ? '?' + _options.query : ''),
      });
    } else {
      window.my.redirectTo({
        url: _options.url + (_options.query ? '?' + _options.query : ''),
      });
    }
  },
  /**
   * 调起小程序支付页面
   * @param {object} info 支付信息
   * @param {string} info.oid 系统订单号
   * @param {string} info.fail 支付未成功回跳地址
   * @param {string} info.success 支付成功回跳地址
   * @param {string} info.category 订单类型
   */
  payment: function (info: Payment) {
    let page =
      '/pages/payment-ali/index?orderId=' +
      info.oid +
      '&success=' +
      encodeURIComponent(info.success) +
      '&fail=' +
      encodeURIComponent(info.fail) +
      '&category=' +
      encodeURIComponent(info.category);
    // console.log(page);
    window.my.navigateTo({
      url: page,
    });
  },
  /**
   * 向小程序发送消息
   * @param {string} params 消息内容
   */
  postMessage: function (params: WebViewMessage) {
    window.my.postMessage(params);
  },
  /**
   * 获取用户当前坐标信息（高德坐标）
   * @returns {Promise}
   */
  getLocation: function() {
    return new Promise((resolve, reject) => {
      window.my.getLocation({
        success: function(res: any) {
          if (typeof res == 'string') {
            res = JSON.parse(res)
          }
          resolve(res);
          // successFn(JSON.parse(res))
        },
        fail: function(err: any) {
          if (typeof err == 'string') {
            err = JSON.parse(err)
          }
          reject(err)
          // failFn(JSON.parse(err))
        }
      })
    })
  },
  previewImage: function (params: PreviewImage) {
    window.my.previewImage(params);
  },
  openLocation: function (params: OpenLocation) {
    // let _params = {
    //   ...params,
    //   success: res => {
    //     console.log('🚀 ~ res:', res)
    //   },
    //   fail: res => {
    //     console.log('🚀 ~ res:', res)
    //   },
    // }
    window.my.openLocation(params);
  },
  startShare: function () {
    window.my.startShare();
  },
  makePhoneCall: function (params: MakePhoneCall) {
    window.my.makePhoneCall(params);
  },
  /**
   * 打开新窗口
   * @param {string} url 要打开的url地址
   */
  pushWebview: function (url: string) {
    window.my.navigateTo({
      url: '/pages/web/index?url=' + encodeURIComponent(url),
    });
  },
  // 关闭当前窗口
  closeWebview: function () {
    window.my.navigateBack();
  },
}

export default alipayUtils;
