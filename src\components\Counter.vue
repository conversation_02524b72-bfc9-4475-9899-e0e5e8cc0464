<style lang="scss" style="scoped">
  $border-color: #c1c1c1;
  .counter {
    display: inline-block;
    .counter-inner {
      border-radius: 2px;
      border: 1px solid $border-color;
      .counter-btn, .counter-value {
        text-align: center;
      }
      .counter-btn {
        // padding: 0 8px;
        flex: 1;
        font-style:normal;
        width: 26px;
        height: 26px;
        line-height: 26px;
        font-weight: 700;
        &.disabled {
          color: #cacaca;
        }
      }
      .counter-value {
        width: 50px;
        border: 1px solid $border-color;
        border-width: 0 1px;
      }
    }
  }
</style>

<template>
  <div class="counter" @click.stop.prevent="()=>{}">
    <div class="counter-inner flex-row center">
      <i class="counter-btn minus" :class="{ 'disabled': !couldOperate(-1)}" @click="add(-1)">
        <svg class="icon-minus">
          <use xlink:href="#icon-minus"></use>
        </svg>
       </i>
      <span class="counter-value">{{value}}</span>
      <i class="counter-btn add" :class="{ 'disabled': !couldOperate(1)}" @click="add(1)">
        <svg class="icon-plus">
          <use xlink:href="#icon-plus"></use>
        </svg>
      </i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'counter',
  props:{
    value: {
      type: Number,
      default: 1,
    },
    min: {
      type: Number,
      default: 1,
    },
    max: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {};
  },
  methods: {
    compute(value) {
      const result = this.value + value;
      if (result < this.min) return this.value;
      if (result > this.max && this.max && value == 1) return this.value; // 有最大值限制时，可以减 不可以加
      return result;
    },
    add(value) {
      this.setValue(this.compute(value));
    },
    setValue(value) {
      if (value != this.value) {
        this.$emit('input', value);
      }
    },
    couldOperate(value) {
      return this.compute(value) != this.value;
    }
  }
}
</script>
