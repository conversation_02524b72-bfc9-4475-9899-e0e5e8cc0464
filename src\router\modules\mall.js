import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/mall',
    // redirect: '/mall/503',
    name: '领航商城',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-index" */ '@/views/packages/mall/GoodsList/MallIndex.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/sidebar',
    // redirect: '/mall/503',
    name: '侧边商品导航',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-index" */ '@/views/packages/mall/GoodsList/GoodsSidebar.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/503',
    name: '领航商城-维护中',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-503" */ '@/views/packages/mall/Mall503.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/search/:mode?',
    name: '领航商城搜索',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-search" */ '@/views/packages/mall/GoodsList/MallSearch.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/goods/video',
    name: '商品视频介绍',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/Goods/VideoProductIntro.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/goods/:id',
    name: '商品详情',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/Goods/Goods.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/points/list',
    name: '积分商城',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/PointsMallIndex.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/points/goods/:id',
    name: '积分商品详情',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/Goods/GoodsPoints.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/points/result/:id',
    name: '积分兑换商品结果页',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/status-page/PointsForGoods.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/points/coupon/:id',
    name: '积分优惠券详情',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/Goods/CouponPoints.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/exchange/list',
    name: '积分兑换记录',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/ExchangeList.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/goods/:id/comments',
    name: '商品评价列表',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods-comments" */ '@/views/packages/mall/GoodsComments/comments-list.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/goods/:id/spec/:specID/buy',
    name: '结算订单页面',
    component: resolve => {
      import(/* webpackChunkName: "mall-buy" */ '@/views/packages/mall/Buy.vue')
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/goods/group/buy',
    name: '团购结算订单页面',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-buy" */ '@/views/packages/mall/BuyGroup.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/points/goods/:id/spec/:specID/buy',
    name: '积分商品结算订单页面',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-buy" */ '@/views/packages/mall/Buypoints.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/promotion/goods/:id/spec/:specID/buy',
    name: '促销商品结算订单页面',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-buy" */ '@/views/packages/mall/BuyPromotion.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/list',
    name: '团购订单',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-orders" */ '@/views/packages/mall/MallOrderList.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  // 支付成功
  {
    path: '/mall/pay/success/:id',
    name: 'mall-pay-success',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order" */ '@/views/packages/mall/MallPaySuccess.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/:id/comment/success',
    name: '商品评价成功',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods-comments" */ '@/views/packages/mall/GoodsComments/GoodsCommentSuccess.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/:id/comment',
    name: 'mall-order-comment',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order-comment" */ '@/views/packages/mall/MallOrderComment.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/guide',
    name: 'mall-order-guide',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order-comment" */ '@/views/packages/mall/MallOrderCommentGuide.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/aftersale/pre/:id',
    name: '申请售后',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order-aftersale" */ '@/views/packages/mall/after-sales/AfterSalesApplyPre.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/aftersale/:id',
    name: '申请售后',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order-aftersale" */ '@/views/packages/mall/after-sales/AfterSalesApply.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/order/:id',
    name: 'mall-order-detail',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order" */ '@/views/packages/mall/MallOrder.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/pickup/order/:id',
    name: 'pick-up-order',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order" */ '@/views/packages/mall/VerifySelfPickUpOrder.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/aftersale/list',
    name: '售后订单列表',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order-aftersale" */ '@/views/order/OrderListRefund.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/aftersale/detail/:id',
    name: '售后订单详情',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-aftersale-detail" */ '@/views/packages/mall/after-sales/AfterSalesDetail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/aftersale/express/:id',
    name: '售后订单快递信息提交',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-aftersale-express" */ '@/views/packages/mall/after-sales/AfterSalesExpress.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/aftersale/schedule/:id',
    name: '售后订单进度',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-aftersale-schedule" */ '@/views/packages/mall/after-sales/AfterSalesSchedule.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/browsing/history',
    name: 'mall-browsing-history',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order-comment" */ '@/views/packages/mall/browsing-history/list.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/collect/goods',
    name: 'mall-collect-goods',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-order-comment" */ '@/views/packages/mall/collect-goods/list.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/shopping/cart',
    name: '购物车',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-orders" */ '@/views/packages/mall/shopping-cart/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/bargain/center/:id',
    name: 'mallBargainCenter',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-bargain" */ '@/views/packages/mall/bargain/bargain-list.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/bargain/detail/:id',
    name: 'mallBargainDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-bargain" */ '@/views/packages/mall/bargain/bargain-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/bargain/orders',
    name: '砍价我的助力',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-bargain" */ '@/views/packages/mall/bargain/my-bargains.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/mall/bargain/rules',
    name: 'mallBargainRules',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-bargain" */ '@/views/packages/mall/bargain/action-info.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/bargain/group/center/:id',
    name: 'bargainGroupCenter',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/bargain-group/bargain-list.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/bargain/group/detail/:id',
    name: 'bargainGroupDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/bargain-group/bargain-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/bargain/group/orders',
    name: '拼团我的助力',
    // redirect: '/group/buying/orders',
    redirect: to => {
      return {
        path: '/group/buying/orders',
        query: {
          i: '2',
        },
      };
    },
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/bargain-group/my-bargains.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/bargain/group/rules',
    name: 'bargainGroupRules',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/bargain-group/action-info.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/group/buying/center/:id',
    name: 'groupBuyingCenter',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/group-buying/bargain-list.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/group/buying/detail/:id',
    name: 'groupBuyingDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/group-buying/bargain-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/group/buying/orders',
    name: '团购我的助力',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/group-buying/my-bargains.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/group/buying/rules',
    name: 'groupBuyingRules',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/group-buying/action-info.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/flash/sale/center/:id',
    name: '限时秒杀',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-group" */ '@/views/packages/mall/flash-sale/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/pkg/order/buy',
    name: '套餐购买',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/BuyPackage.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/savings/card',
    name: '省钱卡',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/savings-card/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/reward/browse',
    name: 'RewardBrowse',
    component: resolve => {
      import(
        /* webpackChunkName: "mall-goods" */ '@/views/packages/mall/GoodsList/RewardBrowse.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
