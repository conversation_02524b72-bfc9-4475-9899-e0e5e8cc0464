<template>
  <div class="report-item">
    <div class="item-content">
      <div class="content-left">
        <div v-if="type == 'report'" class="msg-row">
          <div class="title">
            <van-icon name="clock-o" color="#111111" size="16" />
          </div>
          <div class="content">
            发布时间：{{ formatDate(value.createTime, 'yyyy-MM-dd HH:mm') }}
          </div>
        </div>
        <div class="msg-row">
          <div class="title">
            <van-icon name="orders-o" color="#111111" size="16" />
            <span v-if="type == 'report'">上报信息</span>
            <span v-else>可以提供</span>
            <span class="split">|</span>
          </div>
          <div class="title-right">
            <div v-if="type == 'report'" class="content">
              {{ value.content }}
            </div>
            <div v-else class="content">{{ value.helps }}</div>
            <div v-if="value.situation" class="content">
              ({{ value.situation }})
            </div>
          </div>
        </div>
        <div class="msg-row">
          <div class="title">
            <van-icon name="location-o" color="#111111" size="16" />
            <span>{{ distance }}公里</span>
            <span class="split">|</span>
          </div>
          <div class="title-right">
            <div v-if="type == 'report'" class="content">
              {{ value.location }}
            </div>
            <div v-else class="content">{{ value.seat }}</div>
          </div>
        </div>
        <div v-if="value.photo" class="comment-images">
          <c-picture
            v-for="(image, index) in value.photo.split(',')"
            :key="index"
            :src="image"
            class="comment-img"
            :lazy="false"
            @click.stop="playPhotos(value.photo.split(','), index)"
          >
          </c-picture>
        </div>
      </div>
    </div>
    <div v-if="type !== 'report'" class="content-bottom">
      <!-- <div class="phone-box" @click="callPhone(value.number)">
        <van-icon name="phone" color="#2cb189" size="16" />
        <span>去联系</span>
      </div> -->
      <div class="location-box" @click="goNavigate">
        <van-icon name="location-o" color="#2cb189" size="16" />
        <span>导航</span>
      </div>
      <!-- <biz-image
        class="tel-btn"
        placeholder="none"
        src=""
        :colorful="true"
        @click="callPhone(value.number)"
      >
      </biz-image> -->
    </div>
    <div v-if="value.status > 0" class="tag" :style="tagStyle"></div>
  </div>
</template>

<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { callPhone, playPhotos, navigate } from '@/bridge';
import { dialog, toast } from '@/bus';
import {
  isInJglh,
  isInWeixin,
  isInWeApp,
  isInUnionPayMP,
  isIOS,
} from '@/common/env';
import { formatDate, getAppURL, getFullURL, getDistance } from '@/utils';
import { Button, Icon, Tab, Tabs } from 'vant';
import { getImageURL } from '@/common/image';

export default {
  name: 'ReportItem',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    [Icon.name]: Icon,
  },
  props: {
    type: {
      type: String,
      default() {
        return '';
      },
    },
    value: {
      type: Object,
      default() {
        return {};
      },
    },
    location: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      // distance: '',
      isIOS: isIOS,
    };
  },
  watch: {
    // points: function (val, old) {
    //   console.log('🚀 ~ val:', val)
    //   if (val[0] && val[3]) {
    //     let _distance = getDistance(...val);
    //     console.log('🚀 ~ _distance:', _distance)
    //     this.distance = _distance.toFixed(2); // 直线距离
    //   }
    // },
  },

  computed: {
    points() {
      let points = [];
      points.push(this.location.latitude); // 纬度
      points.push(this.location.longitude);
      points.push(this.value.lat);
      points.push(this.value.lng);
      return points;
    },
    distance() {
      let points = [];
      let _distance = 0;
      // points.push(this.location.latitude); // 纬度
      // points.push(this.location.longitude);
      // points.push(this.value.lat);
      // points.push(this.value.lng);
      // if (points[0] && points[3]) {
      //   _distance = getDistance(...points).toFixed(2); // 直线距离
      // }
      if (this.value.distance) {
        return (this.value.distance / 1000).toFixed(2);
      }
      return _distance;
    },
    tagStyle() {
      let styles = {};
      if (!this.value.status) {
        return styles;
      }
      if (this.value.status === 1) {
        styles.backgroundImage = `url(${require('@pkg/actions/assets/images/report_tag1.png')})`;
      }
      if (this.value.status === 2) {
        styles.backgroundImage = `url(${require('@pkg/actions/assets/images/report_tag2.png')})`;
      }
      styles.backgroundPosition = 'center center';
      styles.backgroundSize = '100% auto';
      styles.backgroundRepeat = 'no-repeat';
      // 返回样式对象
      return styles;
    },
  },
  created() {},
  mounted() {},
  methods: {
    ...{ formatDate },
    callPhone(phone) {
      if (!phone) return;
      callPhone(phone);
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, '?'),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },

    goNavigate() {
      if (!isInJglh && !isInWeixin && !isInUnionPayMP) {
        toast().tip('请在最新交广领航APP中打开');
        return;
      }
      if (this.value.lng && this.value.lat) {
        navigate({
          address: this.location.address || this.value.seat,
          name: this.value.seat,
          longitude: this.value.lng,
          latitude: this.value.lat,
          callback() {},
        });
      }
      toast('未获取到坐标信息');
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.report-item {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 10px;
  padding: 16px;
  position: relative;
  .tag {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 70px;
    height: 70px;
    z-index: 1;
  }
  .van-icon {
    margin-right: 5px;
    line-height: 18px;
  }
  .content-bottom {
    padding: 10px 0 0;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #dddddd;
    .phone-box {
      width: 90px;
      height: 28px;
      background: #f4f8fb;
      border-radius: 14px;
      text-align: center;
      line-height: 28px;
      color: #2cb189;
      font-size: 14px;
    }
    .location-box {
      width: 90px;
      height: 28px;
      background: #f4f8fb;
      border-radius: 14px;
      text-align: center;
      line-height: 28px;
      color: #2cb189;
      font-size: 14px;
    }
    // padding: 10px 0 0;
    // display: flex;
    // justify-content: flex-end;
    // border-top: 1px solid #dddddd;
    // .tel-btn {
    //   width: 90px;
    //   height: 28px;
    //   background: url('../assets/images/phone.png') no-repeat center center;
    //   background-size: 100% 100%;
    // }
  }
  .content-left {
    flex: 1;
  }
  .msg-row {
    display: flex;
    align-items: flex-start;
    font-size: 14px;
    color: #111111;
    line-height: 18px;
    margin-bottom: 10px;
    .title {
      display: flex;
      align-items: center;
    }
    .split {
      margin: 0 5px;
    }
    .title-right {
      flex: 1;
      & > div {
        display: inline-block;
      }
    }
  }
  .comment-images {
    margin-top: 10px;
  }
  .comment-img {
    width: 60px;
    height: 60px;
    background-size: 60px;
    margin-right: 5px;
    margin-bottom: 5px;
  }
}
</style>
