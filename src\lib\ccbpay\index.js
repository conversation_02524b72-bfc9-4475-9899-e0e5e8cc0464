/**
 * 1. 手机号用AES加密，测试环境加密密钥先约定为这个吧：HjPxoou731P!0.24
  对接没问题后，再给生产密钥。
  2. 加密手机号参数不要直接phone，用这个 uMphlNo,加密后用urlencode一下加密串，防止"=="号产生异常
  3. 测试环境地址：
  https://ctms.uctd.com.cn/ctmswp/api/v1/oauth/jsjglhapp/goToCouponListLogin?uMphlNo=XXXXXXXXXXXX
  生产地址：
  https://longpay.ccb.com/ctmswp/api/v1/oauth/jsjglhapp/goToCouponListLogin?uMphlNo=XXXXXXXXXXXX
 */

export * from './pay';

// 2021年1月28日09:31:23：使用4.0.0版本导致打包体积偏大，降级到3.3.0版本
import CryptoJS from 'crypto-js';

// 加密密钥， not safe in client
// const KEY = 'HjPxoou731P!0.24';
const KEY = 'CjPmxooupijsk.24';

import { isProduction } from '@/common/env';

function aesEncrypt(text) {
  // console.log(CryptoJS)
  const keyBytes = CryptoJS.enc.Utf8.parse(KEY)
  const textBytes = CryptoJS.enc.Utf8.parse(text)
  return CryptoJS.AES.encrypt(textBytes, keyBytes, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
  }).toString();
}

// // 获取建行免登陆领券链接地址
export function getCCBCouponListPage(phone) {
  const uMphlNo = encodeURIComponent(aesEncrypt(phone));
  return `https://longpay.ccb.com/ctmswp/api/v1/oauth/jsjglhapp/goToCouponListLogin?uMphlNo=${uMphlNo}`;
  // 生产环境链接
  // if (isProduction) return `https://longpay.ccb.com/ctmswp/api/v1/oauth/jsjglhapp/goToCouponListLogin?uMphlNo=${uMphlNo}`;
  // return `https://ctms.uctd.com.cn/ctmswp/api/v1/oauth/jsjglhapp/goToCouponListLogin?uMphlNo=${uMphlNo}`;
}

// console.log('aes18203679025: ', aesEncrypt('18203679025'))
