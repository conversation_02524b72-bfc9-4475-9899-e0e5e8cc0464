<template>
  <div class="swiper-container">
    <div class="swiper-wrapper">
      <slot
        name="item"
        v-for="(item, index) in slides"
        :index="index"
        :item="item"
      >
      </slot>
      <slot name="last"></slot>
    </div>
    <!-- Add Pagination -->
    <div v-if="pagination" class="swiper-pagination"></div>
    <slot></slot>
  </div>
</template>
<style lang="scss" scoped>
@import './index.scss';

.swiper-container {
  width: 100%;
  height: 200px;
}
.swiper-pagination-bullet {
  width: 10px;
  height: 3px;
  border-radius: 0;
}
.swiper-pagination-bullet-active {
  background: white;
}
</style>
<script>
console.error('此组件已废弃，请使用 index2.vue');

import Swiper from 'swiper';
const ValueMode = {
  INDEX: 'index',
  VALUE: 'value',
};
export default {
  name: 'slider',
  props: {
    slides: Array,
    valueMode: {
      type: String,
      default() {
        return ValueMode.VALUE;
      },
    },
    pagination: {
      type: Boolean,
      default: true,
    },
    value: [Number, Object, String],
    allowEmpty: Boolean,
    options: {
      type: Object,
    },
  },
  components: {},
  mounted() {
    this.$nextTick(() => {
      this.init();
    });
  },
  destroyed() {
    this.swiper.destroy(true, false);
  },
  data() {
    return {
      autoplayed: false,
    };
  },
  watch: {
    slides(val, oldVal) {
      console.log('update slide size ...');
      this.$nextTick(() => {
        this.refresh();
      });
    },
  },
  activated() {
    if (this.autoplayed) {
      this.setAutoplay(true);
    }
  },
  deactivated() {
    this.autoplayed = this.swiper.autoplaying;
  },
  methods: {
    init() {
      const $el = this.$el;
      const that = this;
      const settings = Object.assign(
        {
          pagination: '.swiper-pagination',
          slidesPerView: 'auto',
          centeredSlides: true,
          paginationClickable: true,
          spaceBetween: 20,
          autoplay: 3000,
          effect: 'slide',
          // centeredSlides: true,
          onSlideChangeStart(s) {
            // console.log('autoplay...');
          },
          onSlideChangeEnd(s) {
            that.setActiveSlide(s.activeIndex);
          },
        },
        this.options
      );
      setTimeout(() => {
        const swiper = this.swiper;
        if (swiper) swiper.destroy(true, false);
        this.swiper = new Swiper($el, settings);
      }, 1);
    },
    refresh() {
      this.swiper.update();
    },
    next() {
      this.swiper.slideNext();
    },
    prev() {
      this.swiper.slidePrev();
    },
    slideTo(index) {
      this.swiper.slideTo(index);
    },
    setAutoplay(flag) {
      const swiper = this.swiper;
      swiper.stopAutoplay();
      if (flag) {
        swiper.startAutoplay();
      }
    },
    setActiveSlide(index) {
      // if (index < 0) index = 0;
      if (index + 1 > this.slides.length) {
        if (!this.allowEmpty) {
          return;
        }
      }
      let emitData = this.slides[index];
      if (this.valueMode == ValueMode.INDEX) {
        emitData = index > 0 ? index : 0;
      }
      this.$emit('input', emitData);
      this.$emit('change', emitData);
    },
  },
};
</script>
