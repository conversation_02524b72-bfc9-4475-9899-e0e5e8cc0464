<template>
  <transition
    @after-enter="onAfterEnter"
    @after-leave="onAfterLeave"
    :name="theTransitionName"
  >
    <div class="container" :class="{ 'container-fullscreen': mode == PageMode.FULLSCREEN, 'no-header': noHeader }" :transition-name="theTransitionName">
      <slot></slot>
    </div>
  </transition>
</template>
<style lang="scss">
.no-header ::v-deep{
  .content-wrapper{
    margin-top: 0;
  }
}
</style>
<script>
  import { mapGetters, mapActions } from 'vuex';
  import { TransitionMode } from '@/enums';
  import { isIOS, isInWeApp, isInWeixin, isInAliApp } from '@/common/env';
  import { bind, unbind, emit, Events as BusEvent } from '@/bus';

  const TRANSITION_TIME = 350;
  const Events = {
    READY: 'ready', // 初始化完成就绪
    RESUME: 'resume',  // 恢复可见状态
    HIDE: 'hide',  // 隐藏未销毁
    LEAVE: 'leave', // 即将离开被销毁
  };

  const PageMode = {
    FULLSCREEN: 'fullscreen', // 全屏模式，导航栏通到顶部
    NORMAL: 'normal',  // 普通模式，带导航栏
  };

  /**
   * props.transition 动画类型
   * props.forceUpdate
   * props.keepAlive 是否保持生存状态不被销毁
   * :
   */
  export default {
    name: 'container',
    props: {
      transition: {
        type: String,
      },
      forceUpdate: {
        type: Boolean,
        default: false
      },
      keepAlive: {
        type: Boolean,
        default: true,
      },
      mode: {
        type: String,
        default: 'normal',
      },
      header: {
        type: Boolean,
        default: false,
      }
    },
    data() {
      return {
        PageMode,
        countOfAfterPages: -1,
        pageActivated: false,
        isActive: true,
        status: null,
      };
    },
    computed: {
      ...mapGetters(['transitionName', 'transitionMode']),
      theTransitionName() {
        return this.transition || this.transitionName;
      },
      noHeader() {
        return (isInWeixin || isInWeApp || isInAliApp) && !this.header;
      },
    },
    mounted() {
      // this.onViewIn();
      bind(BusEvent.PAGE_VISIBLE, () => {
        if (this.isActive) {
          this.callEvent(Events.RESUME, 100);
        }
      })
      console.log('container mounted...')
      const that = this;
      // TODO
      // iOS下，页面首次加载，有时候(70%左右)container组件中transition标签的after-enter钩子函数未调用，导致页面一直现在正在加载
      // 尚未找到原因，临时采用以下方案：首次加载增加一个超时时间检测，未加载则主动调用onViewIn
      const shouldDelayCheck = (!window.isAppReady  && isIOS) || !this.pageActivated;
      if (shouldDelayCheck) {
        setTimeout(() => {
          if (!that.status) {
            console.log('activated onViewIn...');
            that.onViewIn(0);
          }
        }, TRANSITION_TIME * 2);
      }
    },
    activated() {
      // console.log('view:', this.$parent.$options.name, 'activated', this);
      // this.onViewIn();
      const that = this;
    },
    deactivated() {
      // console.log('view:', this.$parent.$options.name, 'deactivated', this);
      // this.onViewOut();
    },
    beforeDestroy() {
      // console.log(this.$options.name, 'destory...');
      // this.onViewOut();
    },
    methods: {
      ...mapActions(['addRoute', 'removeRoute']),
      changePageCount(count) {
        let value = this.countOfAfterPages + count;
        if (Math.abs(value) > 1) {
          value = value / Math.abs(value);
        }
        this.countOfAfterPages = value;
      },
      onBeforeEnter() {
        console.log('before enter...')
      },
      onEnter() {
        console.log('enter...')
      },
      onAfterEnter() {
        console.log('onAfterEnter onViewIn...');
        this.onViewIn(0);
      },
      onAfterLeave() {
        this.onViewOut(0);
      },
      setKeepAlive(value) {
        this.keepAlive = value;
      },
      onViewIn(delay = TRANSITION_TIME) {

        window.isAppReady = true;

        console.log('container view in ...');
        if (this.forceUpdate) {
          this.callEvent(Events.READY, delay);
        } else if (this.transitionMode == TransitionMode.FORWARD) {
          this.changePageCount(1);
          // if (this.countOfAfterPages <= 0) {
          this.callEvent(Events.READY, delay);
          // }
        } else if (this.transitionMode == TransitionMode.BACK) {
          this.changePageCount(-1);
          if (!this.pageActivated && this.countOfAfterPages <= 0) {
            this.callEvent(Events.READY, delay);
          } else {
            this.callEvent(Events.RESUME, delay);
          }
        } else {
          this.callEvent(Events.READY, delay);
        }
        this.pageActivated = true;
      },
      onViewOut(delay = TRANSITION_TIME) {
        console.log('container view out...');
        if (this.forceUpdate) {
          this.callEvent(Events.LEAVE, delay);
        } else if (this.transitionMode == TransitionMode.BACK) {
          if (this.countOfAfterPages <= 0) {
            this.callEvent(Events.LEAVE, delay);
          }
          this.changePageCount(-1);
        } else if (this.transitionMode == TransitionMode.FORWARD) {
          this.changePageCount(1);
          this.callEvent(Events.HIDE, delay);
        } else {
          this.callEvent(Events.LEAVE, delay);
        }

        setTimeout(() => {
          emit(BusEvent.VIEW_TRANSITION_END);
        }, 50);
      },
      callEvent(event, delay) {
        let timeout = delay;
        // if (event == Events.READY) timeout += 300;
        this.isActive = [Events.READY, Events.RESUME].some(item => item == event);
        console.log('callEvent:', this.$parent.$options.name, event);
        this.status = event;
        if (event == Events.READY && this.countOfAfterPages != 0) {
          console.warn('计数错误，自动修正:', this.countOfAfterPages);
          this.countOfAfterPages = 0;
        }
        setTimeout(() => {
          this.$emit(event);
          if (event == Events.READY) {
            const payload = {
              key: this.$parent.$options.name,
              value: this.$route.path,
            }
            this.addRoute(payload);
          }
          if (event == Events.LEAVE || (event == Events.HIDE && !this.keepAlive)) {
            this.removeRoute(this.$parent.$options.name);
            this.$destroy();
          }
        }, timeout);
      },
    },
  };
</script>
