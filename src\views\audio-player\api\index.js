import APIModel from '@/api/APIModel';

/**
 * 接口文档：http://jgrm.net:10230/swagger-ui.html#/%E7%94%B5%E5%8F%B0%E6%A0%8F%E7%9B%AE-%E8%8A%82%E7%9B%AE%E7%AE%A1%E7%90%86/detailUsingGET_1
 */
const api = new APIModel({
  'audio/profile': '/Radio/live/program/info', // 获取栏目信息接口
  'audio/share/profile': '/Radio/live/program/share', // 获取栏目分享接口
  'audio/list': '/Radio/live/programItem/listByPage', // 音频列表接口
  'audio/detail': '/Radio/live/programItem/detail', // 音频详情
  'audio/share/detail': '/Radio/live/programItem/share', // 分享音频详情

})

export function getAudioProfile (params) {
  return api.doGet('audio/profile', params);
}
export function getShareAudioProfile (params) {
  return api.doGet('audio/share/profile', params);
}

export function getAudioList (params) {
  return api.postJSON('audio/list', params);
}

export function getAudioDetail (params) {
  return api.doGet('audio/detail', params);
}

export function getShareAudioDetail (params) {
  return api.doGet('audio/share/detail', params);
}
