<template>
  <div class="list-empty">
    <div class="list-empty-icon" :style="iconStyle"></div>
    <slot></slot>
  </div>
</template>
<style lang="scss" scoped>
.list-empty {
  color: rgb(202, 202, 202);
  font-weight: 500;
  margin-top: 15vh;
}
.list-empty-icon {
  background: url(~@/assets/images/common/list-empty.png) center center no-repeat;
  height: 150px;
  margin-bottom: 20px;
  background-size: 220px, auto, contain;
}
</style>
<script>
  export default {
    name: 'list-placeholder',
    props: {
      icon: {
        type: String,
        default: '',
      }
    },
    computed: {
      iconStyle() {
        const style = {};
        if (this.icon) {
          style.backgroundImage = `url(${this.icon})`;
        }
        return style;
      }
    }
  };
</script>
