import * as types from './mutation_types';
import {
  syncSession,
  disablePullDownRefresh,
  setShareConfig,
  share,
  initAppConfig,
} from '@/bridge';
import { initWeiXinConfig, checkSession, getUserInfo } from '@/api';
import { isInWeixin } from '@/common/env';
import { toast } from '@/bus';
import { captureExpectionWithData } from '@/utils';
import { isWebPSupported } from '@/utils/caniuse';

function prepareAppShareConfig(info) {
  return new Promise((resolve, reject) => {
    if (isInWeixin) {
      // 微信内无论初始化成功还是失败都返回成功，否则使用非法域名测试时页面无法顺利初始化
      return initWeiXinConfig(location.href)
        .then(resolve)
        .catch(err => {
          resolve();
          console.error(err);
          // toast().tip(err && err.errMsg);
          captureExpectionWithData(err && err.errMsg, {}, 'weixin-config');
        });
    }
    return resolve();
  });
}

const actions = {
  /**
   * 初始化app
   * @param commit
   */
  init({ commit, state }) {
    console.log('init...');
    disablePullDownRefresh();
    return prepareAppShareConfig()
      .then(res => {
        initAppConfig(state.shareInfo);
        console.log('prepareAppShareConfig success');
        return syncSession(sessionId => {
          console.log('syncSession:', sessionId);
          commit(types.UPDATE_AUTH, sessionId);
          return sessionId;
        });
      })
      .catch(err => {
        console.error('prepareAppShareConfig:', err);
      });
  },

  /**
   * 检查session状态是否有效
   * @param {*} param0
   * @param {boolean} forceCheck 是否忽略缓存强制校验
   */
  checkSession({ commit, state }, forceCheck) {
    const MAX_AGE = 1000 * 60 * 5;
    const shouldUseCachedStatus =
      !forceCheck &&
      state.auth.loggedIn &&
      Date.now() - state.auth.lastUpdate < MAX_AGE;
    if (shouldUseCachedStatus) return Promise.resolve({ ok: true });
    return checkSession().then(res => {
      commit(types.UPDATE_LOGIN_STATE, res.ok);
      return res;
    });
  },
  syncUserInfo({ commit, state }) {
    const MAX_AGE = 1000 * 60 * 5;
    // const shouldUseCachedStatus = Date.now() - state.auth.lastUpdate < MAX_AGE;
    // if (shouldUseCachedStatus) return Promise.resolve({ok: true});
    return getUserInfo().then(res => {
      // 2023-07-10 添加 是否是分销团长身份 type == 17时是团长
      let userInfo = res;
      userInfo.isDistributorLeader = !!((res.type & 16) == 16);
      commit(types.UPDATE_USER_INFO, userInfo);
      return userInfo;
    });
  },
  initAppConfig({ commit, state }, data) {
    initAppConfig(state.shareInfo);
  },

  setShareInfo({ commit, state }, data) {
    const mergeData = Object.assign({}, state.shareInfo, data);
    commit(types.UPDATE_SHARE_INFO, mergeData);
    setTimeout(() => {
      initAppConfig(mergeData);
    }, 100);
  },

  /**
   * 调起分享界面
   * @param {*} param0
   * @param {*} data
   */
  share({ commit, state }, data) {
    const mergeData = Object.assign({}, state.shareInfo, data);
    share(mergeData);
  },
  // 设置app个性化配置
  setAppSetting({ commit, state }, data) {
    commit(types.SET_APP_SETTING, data);
  },
};

// 操作路由入栈和路由出栈记录，使得keep-alive组件只缓存入栈的组件，销毁出栈的组件
const routes = {
  addRoute({ commit, state }, payload) {
    commit(types.ADD_ROUTE, payload);
  },
  removeRoute({ commit, state }, key) {
    commit(types.REMOVE_ROUTE, key);
  },
};

const others = {
  setCar({ commit, state }, value) {
    commit(types.SET_CURRENT_CAR, value);
  },
};

// 简史生活音频
const audio = {
  select_play({ commit }, { playlist, index }) {
    commit(types.SET_PLAYLIST, playlist);
    if (index >= 0) {
      commit(types.SET_CURRENTINDEX, index);
      commit(types.SET_PLAYING_STATUS, true);
      commit(types.SET_FULLSCREEN, true);
    }
  },
};

// 项目优化
const optimization = {
  // 检测当前环境是否支持 WebP
  checkSupportWebP({ commit }) {
    isWebPSupported().then(result => {
      commit(types.SET_WEBP_SUPPORT, result);
    });
  },
};
export default {
  ...actions,
  ...routes,
  ...others,
  ...audio,
  ...optimization,
};
