import { initWeiXinConfig, getOrderPayInfo } from '@/api';
import { isInJglh, isInWeixin, isInWeApp } from '@/common/env';
import { isLongPayEnabled } from '@/store/storage';

// 移除url中指定参数
function getCurrentEnvName() {
  const envNames = {
    'web': !isInJglh && !isInWeixin,
    'jglh': isInJglh,
    'weixin': isInWeixin && !isInWeApp,
    'weapp': isInWeApp,
  }
  const currentEnv = Object.keys(envNames).filter(key => envNames[key])[0];
  return currentEnv || 'unknow';
}

/**
 * 清洗处理web页面url参数
 * @param {*} url
 */
export function handleJglhAndWeixinWebViewConfigParam(url) {
  try {
    if (window.URL && window.URLSearchParams) {
      let theURL = new URL(url);
      // lh_wvc参数用于原生app打开webview时使用，其他场景无用，此处移除掉
      theURL.searchParams.delete('lh_wvc');
      // 追加env参数，主要用于占位，规避微信支付无法调起问题
      theURL.searchParams.delete('env');
      theURL.searchParams.append('env', getCurrentEnvName());
      // 页面加载后会根据payment参数决定是否开启龙支付，开启后收银台可以使用龙支付付款
      // 此需求为临时性需求，为不影响其他正常业务，此开关由前端通过url参数控制
      // 由于开关参数写入了sessionStorage，此处需要打开webview时保留龙支付开关状态
      if (isLongPayEnabled()) {
        theURL.searchParams.append('longpay', 1);
      }
      return theURL.href;
    }
  } catch (err) {
    console.error(err);
    return url;
  }
}

/**
 * 请求授权, `/actions/app/auth` 是另一个项目的授权中转页面，包含微信网页授权，云闪付小程序授权
 * @param {*} nextPage
 */
export function requestWeixinAuth(nextPage) {
  return new Promise((resolve, reject) => {
    const url = encodeURIComponent(handleJglhAndWeixinWebViewConfigParam(nextPage));
    const query = ['auth_from=' + btoa(url), 't=' + Date.now()].join('&');
    // const host =
    location.href =
      `${location.protocol}//${location.hostname}` +
      '/actions/app/auth?' +
      query;
  });
}

/**
 * 请求授权, `/actions/app/auth` 是另一个项目的授权中转页面，包含微信网页授权，云闪付小程序授权
 * @param {*} nextPage
 */
export function requestWeixinAuthWithUid(nextPage, uid) {
  // return new Promise((resolve, reject) => {
  const url = encodeURIComponent(handleJglhAndWeixinWebViewConfigParam(nextPage));
  const query = ['auth_from=' + btoa(url), 't=' + Date.now(), 'uid=' + btoa(uid)].join('&');
  // const host =
  // location.href =
  //   `${location.protocol}//${location.hostname}` +
  //   '/actions/app/auth?' +
  //   query;
  const targetUrl =
      `${location.protocol}//${location.hostname}` +
      '/actions/app/auth?' +
      query;
    // resolve(targetUrl)
  return targetUrl
  // });
}
// 设置分享信息
export function setWeixinShareInfo(options) {
  const shareInfo = {
    title: options.title,
    link: options.link,
    imgUrl: options.imgUrl || options.img,
    desc: options.desc || options.title, // 若无分享描述，则使用标题作为分享描述
    success: options.success || function() {},
    cancel: options.cancel || function() {}
  };
  const wx = window.wx;
  // 朋友圈没有分享描述，此处自动拼接到title上
  // const shareTimelineDesc = (shareInfo.title === shareInfo.desc || !shareInfo.desc) ? '' : ` | ${shareInfo.desc}`;
  // console.warn('shareTimelineDesc:', shareTimelineDesc);
  wx.onMenuShareAppMessage(shareInfo);
  wx.onMenuShareTimeline({ ...shareInfo, title: `${shareInfo.title}` });
  wx.onMenuShareQQ(shareInfo);
  wx.onMenuShareWeibo(shareInfo);
  wx.onMenuShareQZone(shareInfo);
}

/**
 * 微信支付
 * @param {*} info
 */
export function payByWeixinPub(info) {
  const that = this;
  return new Promise((resolve, reject) => {
    const wx = window.wx;
    if (wx) {
      if (!info.orderId) info.orderId = info.order_id;
      getOrderPayInfo(info)
        .then(payInfo => {
          const payOptions = {
            timestamp: payInfo.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
            nonceStr: payInfo.nonceStr, // 支付签名随机串，不长于 32 位
            package: payInfo.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
            signType: payInfo.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
            paySign: payInfo.paySign, // 支付签名
            success: function(res) {
              // 支付成功后的回调函数
              resolve(res);
            },
            cancel(e) {
              reject(e && e.errMsg);
            },
            fail(e) {
              reject(e && e.errMsg);
            }
          };
          // alert(JSON.stringify(payOptions, null, 4))
          wx.chooseWXPay(payOptions);
        })
        .catch(e => {
          reject(e);
        });
    } else {
      reject('微信JS-SDK未配置');
    }
  });
}

export function hideWeixinMenus(menus) {
  const wx = window.wx;
  wx.hideMenuItems({
    menuList: menus
  });
  /* return initWeiXinConfig(location.href).then(function(res) {
  }); */
}
