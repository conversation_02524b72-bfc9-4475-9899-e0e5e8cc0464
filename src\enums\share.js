import { createEnums } from './utils';

export const PaymentChannel = createEnums({
  ACCOUNT: ['账户余额', 'jglh_account', '账户余额支付', true], // 默认禁用余额支付
  // THIRD: ['第三方支付', 'third', '支付宝或微信支付', true],
  WEIXIN: ['微信支付', 'wx', '微信5.0以上版本用户使用', false],
  ALIPAY: ['支付宝', 'alipay', '推荐有支付宝账户的用户使用', false],
  ALIPAY_MP: ['支付宝', 'alipay_xcx', '支付宝小程序支付', true],
  WEIXIN_PUB: ['微信支付.', 'wxpub', '仅限微信环境中使用', true],
  WEIXIN_FT: ['微信支付', 'wft', '微付通', true],
  WEIXIN_MP: ['微信支付', 'wx_xcx', '微信小程序支付', true],
  UNION_PAY_MOBILE: [
    '云闪付',
    'union_pay_mobile',
    '银联云闪付用户可使用',
    true,
  ], // APP云闪付支付控件
  UNION_PAY_CASHIER: ['云闪付', 'union_pay_cashier', '银联云收银台', true], // APP云闪付支付控件 2022.3添加
  UNION_PAY: ['银联支付', 'union_pay', '银联手机网页支付', true],
  UNION_PAY_MP: ['云闪付', 'union_pay_xcx', '云闪付小程序支付', true], // 云闪付小程序支付控件
  RELL: ['优惠券', 'reel', '优惠券', true],
  RED_POCKET: ['我的红包', 'red_envelope', '我的红包', true],
  ZERO_PAY: ['零元支付', 'zero_pay', '零元支付', true],
  CCBLONG_PAY: ['建行龙支付', 'CCB', '推荐有建行龙支付的用户使用', true],
  MAINTAIN_CARD: ['养车卡', 'mtcard', '养车卡', true],
  POINTS_PAY: ['金币支付', 'points_pay', '金币支付', true],
  WX_VIDEO_XCX: ['视频号小程序', 'wx_video_xcx', '视频号小程序', true],
  CARD_CONVERT: ['审车卡', 'card_convert', '审车卡', true],
});

export const NativeView = createEnums({
  SET_PAYMENT_PASSWORD: ['设置支付密码', 'set_payment_password', ''],
  RESET_PAYMENT_PASSWORD: ['重置支付密码', 'reset_payment_password', ''],
  NOTIFICATION_OF_ORDERS: ['订单通知', 'notification_of_order', ''],
  YOUZAN_WEBVIEW: ['有赞商城', 'youzan_webview', ''],
  TRAFFIC_MAP: ['路况', 'traffic_map', ''],
  LIVE_CHAT: ['直播', 'live_chat', ''],
});

export const UserType = createEnums({
  CheZhuKa: ['车主卡', 'chezhuka', ''],
  Jglh: ['交广领航', 'jglh', ''],
});

export const ImageType = createEnums({
  BANNER: [
    'banner',
    '?imageMogr2/thumbnail/!750x400r/gravity/Center/crop/750x400/interlace/1/blur/1x0/quality/85',
    '_banner',
  ],
  // LOGO: ['LOGO', 'xslogo', ''],
  LOGO: ['LOGO', '?imageView2/1/w/150/h/150/format/jpg/q/100', '_xlogo'],
  LARGE: ['LARGE', '?imageView2/2/w/2000/h/3000/q/70', '_xl'],
  MEDIUM: ['MEDIUM', '?imageView2/2/w/1000/h/800/q/80', '_xm'],
  SMALL: ['SMALL', '?imageView2/2/w/200/h/100/q/80', '_xs'],
});

export const AppStatus = {
  NONE: 'none',
  FINISH: 'finish',
  LOADING: 'loading',
  READY: 'ready',
  ERROR: 'error',
};

// export const AppStatus = createEnums({
//   NONE: ['none', -2, ''],
//   FINISH: ['none', -3, ''],
//   LOADING: ['loading', 0, ''],
//   READY: ['ready', 1, ''],
//   ERROR: ['error', -1, ''],
// });

export const TransitionMode = createEnums({
  INIT: ['init', 0, ''],
  FORWARD: ['forward', 1, ''],
  BACK: ['back', 2, ''],
});
