import render from './url-render.js';
import { isPlainObject } from '@/utils';

const DEBUG = false;
const HOST = window.HOST || location.hostname;
const API_ROOT = window.API_BASE || `//${HOST}`;
// const API_ROOT = DEBUG ? `http://${location.host}/static/json/` : `http://${HOST}`;

function getDebugURL(api) {
  return `//${location.host}/static/json` + api.replace(/\//g, '_').replace(/^_|_$/g, '') + '.json'
}

const APIs = {
  get(name, data, options = {}) {
    const settings = Object.assign({ debug: DEBUG, base: '' }, options);
    const url = this.maps[name];
    if (!url) throw new Error(name + ' not found!');
    if (settings.debug) {
      return `/static/json/${name}.json`;
    }
    const host = window.HOST || location.host;
    const api = isPlainObject(url) ? url.url : url;
    return render(`${location.protocol}//${host}${settings.base}${api}`, data);
  },
  get2(name, params, options = { debug: DEBUG }) {
    const url = this.maps[name];
    if (!url) throw new Error(name + '不存在！');
    if (options.debug) return getDebugURL(name);
    return API_ROOT + url;
  },
  dget(name, data) {
    return this.get(name, data, { debug: true });
  },
  extend(obj) {
    console.error('此方法即将废弃，请使用 `APIModel`，参考 `src/views/packages/vehicle-business/api`');
    this.maps = Object.assign(this.maps, obj);
  },
  maps: {
    // 汽车详情
    '/car/detail': '/app/car/model/info',
  }
}

export default APIs;
