<template>
  <container
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    @hide="onHide"
    :keep-alive="false"
  >
    <x-header title="选择品牌">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="text" @click="$_router_pageToFeedback()"
        >反馈</x-button
      >
    </x-header>
    <content-view
      ref="view"
      :status="status"
      @reload="reload"
      @scroll="onScroll"
    >
      <template slot="head" v-if="status == AppStatus.READY">
        <ul
          @touchmove.stop.prevent="locateGroup"
          @touchcancel="setGroup('')"
          @touchend="setGroup('')"
          class="letter-list flex-col"
          :class="{ active: !!activeGroup }"
        >
          <li
            @touchstart.stop="setGroup(group)"
            :data-group="group"
            v-for="(item, group) in groups"
            :key="group"
            v-html="group"
          ></li>
        </ul>
        <div v-if="activeGroup" class="active-group" v-html="activeGroup"></div>
      </template>

      <template v-if="status == AppStatus.READY">
        <div class="item-list">
          <dl v-for="(group, key) in groups" :id="'group_' + key" :key="key">
            <dt v-html="key"></dt>
            <dd
              v-for="(item, index) in group"
              :key="index"
              class="flex-row item"
              @click="selectCarBrand(item)"
            >
              <biz-image
                class="item-logo"
                :src="item.logo"
                type="?"
                :lazy="true"
              >
              </biz-image>
              <div class="item-name" v-html="item.name"></div>
            </dd>
          </dl>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { AppStatus, CarType } from '@/enums';
import { pushWebView } from '@/bridge';
import { Events, on, dialog, back } from '@/bus';
import { getCarBrandList, getNewEnergyCarBrandList } from '@/api';
import { mixinAuthRouter } from '@/mixins';
// destroy不能如预期100%销毁实例，临时解决方案：不实用destroy，onLeave后reset当前页面

function getInitialData() {
  return {
    AppStatus,
    status: AppStatus.LOADING,
    page: {
      list: null,
    },

    activeGroup: '',
    sw: '',
    word: '',
    car: null,
    searchMode: false,
    suggestions: [],
  };
}

const InputEvent = {
  SEARCH: '_input_search_brand_change',
};

export default {
  name: 'car-brand-select',
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      page: {
        list: null,
      },
      activeGroup: '',
      sw: '',
      word: '',
      car: null,
      searchMode: false,
      suggestions: [],
    };
  },
  mounted() {
    const sourceId = this.$options.name;
    on(Events.AFTER_ADD_CAR_SYSTEM, sourceId, car => {
      console.log('selected:', car);
      this.car = car;
    });
  },
  computed: {
    list() {
      return this.page.list;
    },
    groups() {
      const list = this.page.list || [];
      const groups = {};
      list.forEach(item => {
        let letter = item.initials;
        if (!groups[letter]) {
          groups[letter] = [item];
        } else {
          groups[letter].push(item);
        }
      });
      return groups;
      // 服务器排好序，减少计算和排序，减少页面卡顿
      /* return Object.keys(groups).sort().map(key => {
          return {
            name: key,
            list: groups[key],
          }
        }); */
    },
  },
  methods: {
    go(url) {
      this.$router.push(url);
    },
    initPageData() {
      let fetchFunc =
        this.$route.query.carType == CarType.NEW_ENERGY
          ? getNewEnergyCarBrandList
          : getCarBrandList;
      fetchFunc()
        .then(list => {
          this.page.list = list.map(item => {
            // 新能源车型logo路径不一样
            if (item.logo.startsWith('/images')) {
              item.logo = item.logo.replace('/images', 'images');
            }
            return item;
          });
          this.status = AppStatus.READY;
        })
        .catch(e => {
          this.status = AppStatus.ERROR;
          console.error(e);
        });
    },
    init() {
      this.initPageData();
    },
    setItem(item) {
      console.log(item);
    },
    selectCarBrand(brand) {
      const from = this.$route.query.from || '';
      const carType = this.$route.query.carType || '';
      const url = `/car/brand/${brand.id}/series?from=${from}&carType=${carType}`;
      this.$router.replace(url);
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onHide() {
      console.log('on hide...', this.$options.name);
      this.status = AppStatus.LOADING;
    },
    onResume() {},
    setGroup(g) {
      this.activeGroup = g;
      const groupId = `group_${g}`;
      const group = document.querySelector('#' + groupId);
      // console.log(this.$refs.view);
      if (group) {
        const offsetTop = group.offsetTop;
        // console.log(offsetTop);
        this.$refs.view.scrollTo(offsetTop);
      }
    },
    locateGroup(e) {
      let changedTouch = e.changedTouches[0];
      const x = window.innerWidth - 10;
      let dom = document.elementFromPoint(x, changedTouch.clientY);
      // console.log(changedTouch);
      const group = dom && dom.dataset.group;
      if (group && this.activeGroup != group) {
        this.setGroup(group);
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onScroll() {},
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.slide-leave {
  transition: transform 2000ms;
}
.slide-leave-active {
  transform: translate(100%, 0, 0);
}

$letter-index: 2;
.letter-list {
  position: fixed;
  height: 100%;
  right: 0;
  top: 0;
  list-style: none;
  user-select: none;
  z-index: $letter-index;
  justify-content: center;
  &.active {
    background: rgba(0, 0, 0, 0.2);
  }
  li {
    padding: 0 5px 0 10px;
    font-size: 0.9em;
    user-select: none;
  }
}
.active-group {
  z-index: $letter-index;
  position: fixed;
  width: 100px;
  height: 100px;
  background: rgba(0, 0, 0, 0.54);
  color: white;
  font-weight: 700;
  text-align: center;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  font-size: 3em;
  line-height: 100px;
  border-radius: 2px;
}

.item-list {
  background: white;
  margin-top: 5px;
  dt {
    padding: 2px 10px;
    background: #eeeff3;
    font-weight: 700;
    color: #6d6d6d;
  }
  .item-logo {
    width: 40px;
    height: 40px;
    margin-right: 5px;
  }
  .item {
    padding: 10px;
    align-items: center;
    @include border-bottom(#e4e4e4);
  }
}
</style>
