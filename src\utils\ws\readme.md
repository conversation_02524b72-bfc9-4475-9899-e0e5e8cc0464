# WebSocketClient 使用指南

这是一个 WebSocket 客户端实现，提供自动重连、心跳检测和消息确认等功能。以下是在 Vue 项目中使用该客户端的基本指南。

## 在 Vue 项目中使用

### 引入和实例化

```javascript
// 在你的 Vue 组件或服务文件中
import WebSocketClient from './path/to/webSocketClient.js';

export default {
  data() {
    return {
      ws: null
    }
  },
  created() {
    // 创建 WebSocket 实例
    this.ws = new WebSocketClient({
      url: 'wss://你的服务器地址/websocket',
      // 可选配置项
      heartbeatInterval: 30000, // 心跳间隔 30 秒
      reconnectInterval: 5000,  // 重连间隔 5 秒
      maxReconnectAttempts: 5   // 最大重连次数
    });

    // 建立连接
    this.ws.connect();

    // 设置事件监听器
    this.ws.onopen(() => {
      console.log('WebSocket 连接已建立');
    });

    this.ws.onmessage((event) => {
      const data = event.data;
      console.log('收到消息:', data);
      // 处理接收到的消息
    });

    this.ws.onclose((event) => {
      console.log('WebSocket 连接已关闭');
    });

    this.ws.onerror((event) => {
      console.error('WebSocket 连接出错');
    });
  },
  methods: {
    // 发送普通消息
    sendMessage(message) {
      this.ws.send(message);
    }
  },
  beforeUnmount() {
    // 组件销毁前关闭 WebSocket 连接
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

### 在 Vuex 中统一管理

如果你需要在多个组件中共享 WebSocket 连接，可以通过 Vuex 进行管理：

```javascript
// store/websocket.js
import WebSocketClient from './path/to/webSocketClient.js';

const state = {
  wsClient: null,
  connected: false,
  messages: []
};

const mutations = {
  SET_WS_CLIENT(state, client) {
    state.wsClient = client;
  },
  SET_CONNECTED(state, status) {
    state.connected = status;
  },
  ADD_MESSAGE(state, message) {
    state.messages.push(message);
  }
};

const actions = {
  initWebSocket({ commit, dispatch }) {
    const wsClient = new WebSocketClient({
      url: 'wss://你的服务器地址/websocket',
      heartbeatInterval: 30000
    });

    wsClient.onopen(() => {
      commit('SET_CONNECTED', true);
    });

    wsClient.onmessage((event) => {
      commit('ADD_MESSAGE', event.data);
      // 可以根据消息类型分发不同的 action
      dispatch('handleMessage', event.data);
    });

    wsClient.onclose(() => {
      commit('SET_CONNECTED', false);
    });

    wsClient.connect();
    commit('SET_WS_CLIENT', wsClient);
  },

  sendMessage({ state }, message) {
    if (state.wsClient && state.connected) {
      state.wsClient.send(message);
    }
  },

  closeWebSocket({ state, commit }) {
    if (state.wsClient) {
      state.wsClient.close();
      commit('SET_WS_CLIENT', null);
      commit('SET_CONNECTED', false);
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
```

### 在组件中使用 Vuex 管理的 WebSocket

```javascript
// 在组件中
export default {
  created() {
    // 初始化 WebSocket 连接
    this.$store.dispatch('websocket/initWebSocket');
  },
  methods: {
    sendMessage() {
      this.$store.dispatch('websocket/sendMessage', {
        content: '你好，服务器',
        type: 1
      });
    },
  beforeUnmount() {
    // 如果这是唯一使用 WebSocket 的组件，可以在这里关闭连接
    // this.$store.dispatch('websocket/closeWebSocket');
  }
}
```

## 主要功能说明

1. **自动重连**：连接断开后自动尝试重新连接
2. **心跳机制**：定期发送心跳包保持连接活跃
3. **网络状态监控**：监听浏览器的在线/离线状态自动处理连接
4. **事件驱动**：提供 onopen/onmessage/onclose/onerror 等事件钩子

根据项目需求选择合适的集成方式，确保在组件销毁时正确关闭 WebSocket 连接以避免内存泄漏。
