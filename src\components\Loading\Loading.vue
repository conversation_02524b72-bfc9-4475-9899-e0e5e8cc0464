<style lang="scss" scoped>
// 代码来自 https://youzan.github.io/vant/#/zh-CN/loading
@keyframes van-rotate {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes van-circular {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120;
  }
}
.van-loading {
  position: relative;
  font-size: 0;
  vertical-align: middle;
  text-align: center;
  margin-top: 20vh;
  margin-bottom: 20vh;
}
.van-loading__spinner {
  position: relative;
  display: inline-block;
  width: 30px;
  max-width: 100%;
  height: 30px;
  max-height: 100%;
  vertical-align: middle;
  animation: van-rotate 0.8s linear infinite;
  color: rgb(25, 137, 250);
}
.van-loading__spinner--circular {
  animation-duration: 2s;
}
.van-loading__circular {
  display: block;
  width: 100%;
  height: 100%;
}
.van-loading__circular circle {
  animation: van-circular 1.5s ease-in-out infinite;
  stroke: currentColor;
  stroke-width: 3;
  stroke-linecap: round;
}
</style>

<template>
  <div class="van-loading van-loading--circular">
    <span
      class="van-loading__spinner van-loading__spinner--circular"
    >
      <svg viewBox="25 25 50 50" class="van-loading__circular">
        <circle cx="50" cy="50" r="20" fill="none" />
      </svg>
    </span>
  </div>
</template>
<script>
export default {
  name: "AppLoader",
  data() {
    return {};
  }
};
</script>
