<template>
  <div class="order order-unused">
    <div class="flex-row order-info">
      <div class="img-container coupon-logo" :class="couponLogo"></div>
      <div class="order-content">
        <h3 class="order-title">{{order.orderTitle}}</h3>

        <template v-if="order.orderType == OrderType.VEHICLE_INSPECTION">
          <p class="order-attr" >预约时间: <span>{{formatDate(order.inspectionAppointmentTime, 'yyyy-MM-dd HH:mm')}}</span></p>
          <p class="order-attr">车牌号码: <span>{{order.carNo}}</span> </p>
          <p class="order-attr">手机号码: <span>{{order.phone}}</span> </p>
        </template>
        <template v-else>
          <!-- <p class="order-attr" v-if="order.orderType == OrderType.MAINTAIN">预约时间: <span>{{parseAppointmentTime(order.appointmentTime)}}</span></p> -->
          <p class="order-attr">有效期至: <span>{{order.expireTime ? formatDate(order.expireTime, 'yyyy-MM-dd HH:mm:ss') : '--'}}</span></p>
          <p class="order-attr">营业时段: <span>{{serviceHours}}</span> </p>
        </template>
      </div>
      <div :class="orderStatusClass">{{orderStatusText}}</div>
    </div>
    
    <div v-if="order.orderStatus == OrderStatus.TO_BE_SERVE || order.orderStatus == OrderStatus.CONFIRMED" class="center flex-col order-qr">
      <div class="consume-code">消费码：{{order.qrCodeContent}}</div>
      <qrcode @click.native="zoomQR(true)" :value="order.qrCodeContent" :size="200"></qrcode>
      <div class="qrcode-tip">
        友情提示：请服务商家使用交广领航商家版小程序进行扫码核销（微信小程序搜索“交广领航商家版”）。如有疑问请致电400-606-1041
      </div>
    </div>

    <div class="zoom-qr" v-if="zoom" @touchmove.prevent="()=>{}">
      <div class="qr-bg">
        <div class="qr-bg__close" @click="zoomQR(false)"></div>
        <div class="qr-bg__head">商家扫码即可消费</div>
        <qrcode :value="order.qrCodeContent || ''" :size="400">
        </qrcode>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  .zoom-qr {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    text-align: center;
    z-index: 1000;
    .qr-bg{
      .qr-bg__head{
        padding:10px 0;
        margin-bottom:10px;
      }
      .qr-bg__close{
        width:30px;
        height:30px;
        line-height:30px;
        position: absolute;
        right: 10px;
        top: 10px;
        text-align:center;
        &::after{
          content:'\e602';
          font-family: iconfont;
          color:gray;
        }
      }
      >canvas{
        width:200px;
        height:200px;
      }
      padding:0 50px 20px;
      background:white;
      position: relative;
      top: 40%;
      transform: translateY(-50%);
      margin: 0 auto;
      width:200px;
      border-radius: 3px;
    }
  }
  $border-color: #e6e6e6;
  .order-qr {
    // border-top: 1px solid $border-color;
    @include border-top($border-color, 'before');
    padding:10px 0 20px;
    >canvas{
      width: 150px;
      height: 150px;
    }
  }
  .pannel{
    border:1px solid $border-color;
    border-width:1PX 0;
  }
  .order {
    background: white;
    padding: 8px 0;
    border-bottom:1px solid $border-color;
  }
  .order-title{
    font-weight:400;
    font-size:16px;
  }
  .order-info {
   padding: 5px 10px;
  }
  .order-content{
    flex:1;
  }
  .order-status {
    display:flex;
    /*align-items:center;*/
    color:#FE912B;
  }
  .order-expired {
    color:gray;
  }
  .coupon-logo {
    width: 60px;
    height: 60px;
    margin-right: 10px;
    background-position:left center;
    background-size:99%;
  }
  .coupon-wash {
    background-image:url(~@/assets/images/icon-beauty.png);
  }
  .coupon-inspection {
    background-image:url(~@/assets/images/icon-inspection.png);
  }
  .coupon-maintain {
    background-image:url(~@/assets/images/icon-maintain.png);
  }
  .consume-code{
    text-align:center;
    padding:5px;
  }
  .order-attr{
    font-size:0.9em;
    margin-top: 2px;
  }
  .qrcode-tip {
    padding: 5px;
    font-size: 14px;
    line-height: 1.2;
    color: red;
    margin: 5px;
  }
</style>

<script>
import { formatDate, formatShopHours, parseAppointmentTime } from '@/utils';
import QRCode from '@/components/QRCode/index';
import { OrderStatus, OrderType } from '@/common/enums';

export default {
  name: 'coupon',
  props: {
    order: {
      type: Object,
    },

  },
  components: {
    'qrcode': QRCode
  },
  data() {
    return {
      OrderStatus,
      OrderType,
      zoom: false,
    };
  },
  computed: {
    serviceHours() {
      const shop = this.order.shop;
      const startTime = formatShopHours(shop.serviceStartTime)
      const endTime = formatShopHours(shop.serviceEndTime);
      return `${startTime} ~ ${endTime}`;
    },
    orderStatusText() {
      if (this.order.orderStatus == OrderStatus.UN_COMMENTED && this.order.orderType == OrderType.VEHICLE_INSPECTION) return '已核销';
      return OrderStatus.getEnum(this.order.orderStatus).getName();
    },
    couponLogo() {
      const orderType = this.order.orderType;
      return {
        'coupon-wash': orderType == OrderType.WASH,
        'coupon-inspection': orderType == OrderType.VEHICLE_INSPECTION,
        'coupon-maintain': orderType == OrderType.MAINTAIN || orderType == OrderType.SHEET_METAL_OR_REPAIR || orderType == OrderType.MAINTAIN_RESERVE,
      }
    },
    orderStatusClass() {
      const status = this.order.orderStatus;
      return {
        'order-status': true,
        'order-expired': status == OrderStatus.INVALID || status == OrderStatus.EXPIRED || status == OrderStatus.TIMEOUT,
      }
    },
  },
  methods: {
    ...{ formatDate, parseAppointmentTime },
    zoomQR(zoom = true) {
      this.zoom = zoom;
    }
  },
};
</script>
