<template>
  <container
    class="may-travel-zone"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="share">
        <i class="icon_jglh icon-fenxiang1" @click="share('show')"></i>
      </div>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="content-wrap">
        <div class="banner" @click="goToMall"></div>
        <div class="content">
          <div class="row-wrapper" @click="goToHelp">
            <div class="icon-box">
              <img src="./assets/images/travel_car.png" alt="car icon" />
            </div>
            <div class="text">车辆常见问题及解决办法</div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
          <div class="card-container">
            <div
              class="card-item"
              :class="{ 'card-item-traffic-map': index === 4 }"
              v-for="(item, index) in cardList"
              :key="index"
            >
              <div class="card-img-wrapper" @click="handleCardClick(item)">
                <img :src="item.imgUrl" class="card-img" />
              </div>

              <div
                v-if="isInWeixin && !isInWeApp && item.id"
                class="launch-weapp launch-weapp-mask"
              >
                <wx-launch-weapp
                  :username="jglhWeapp.username"
                  :path="jglhWeapp.path"
                ></wx-launch-weapp>
              </div>
            </div>
          </div>
        </div>
      </div>
    </content-view>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { pushNativeView } from '@/bridge';
import { getAppURL } from '@/utils';
import { isInJglh, isInWeixin, isInWeApp } from '@/common/env';
import { getImageURL } from '@/common/image';
import { toast } from '@/bus';
import { Icon } from 'vant';
import WxLaunchWeapp from '@/components/WxLaunchWeapp.vue';

export default {
  name: 'MayTravelZone',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Icon.name]: Icon,
    WxLaunchWeapp,
  },
  data() {
    return {
      AppStatus,
      isInWeApp,
      isInWeixin,
      status: AppStatus.LOADING,
      pageTitle: '五一出行专区',
      keepAlive: false,
      pageData: {},
      cardList: [
        {
          id: 'traffic_map',
          imgUrl: require('./assets/images/travel_card1.png'),
          link: '',
        },
        {
          id: '',
          imgUrl: require('./assets/images/travel_card2.png'),
          link: 'https://weixin.hngscloud.com/?lh_wvc=JTdCJTIydGl0bGVCYXIlMjIlM0FmYWxzZSU3RA%3D%3D',
        },
        {
          id: '',
          imgUrl: require('./assets/images/travel_card3.png'),
          link: `${location.origin}/actions/app/travel-map/index.html`,
        },
        {
          id: '',
          imgUrl: require('./assets/images/travel_card4.png'),
          link: '',
          hash: '/camera/index',
        },
        {
          id: '',
          imgUrl: require('./assets/images/travel_card5.png'),
          link: `${
            location.origin
          }/actions/app/tracffic/index.html?t=${new Date().getTime()}`,
          hash: '',
        },
      ],
      jglhWeapp: {
        appid: 'wx1507d26b861e2003',
        username: 'gh_2397d4a35829',
        path: '/pages/map/index',
      },
    };
  },
  methods: {
    init() {
      // 初始化数据
      this.status = AppStatus.READY;
      this.share();
    },
    handleCardClick(item) {
      if (item.id === 'traffic_map') {
        this.toAppMap();
        return;
      }
      if (item.link) {
        if (isInWeApp && item.link.indexOf('hngscloud') > -1) {
          toast().tip('请下载最新版交广领航APP');
        } else {
          this.$_router_pageTo(item.link, {
            titleBar: true,
            progressBar: false,
          });
        }
        return;
      }
      if (item.hash) {
        this.$_router_push(item.hash);
        return;
      }
      toast().tip('请下载最新版交广领航APP');
    },
    toAppMap() {
      if (isInJglh) {
        pushNativeView({
          id: 'traffic_map',
        });
      } else if (isInWeApp) {
        this.$_router_pageToMp({
          url: '/pages/map/index',
          query: '',
        });
      } else {
        this.$toast('请下载最新版交广领航APP');
      }
    },
    goToHelp() {
      this.$router.push(
        '/news?id=3&source=mayTravel&title=车辆常见问题及解决办法'
      );
    },
    goToMall() {
      // this.$router.push('/activity/topic/44');
      this.$router.push('/mall');
    },
    share(action = 'config') {
      const title = '五一出游,畅行无忧';
      const logo = '';
      const desc =
        '出行不再愁！了解车辆常见问题及解决办法，让您的旅程更加顺畅。';
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>

<style lang="scss" scoped>
.may-travel-zone {
  min-height: 100vh;
  background: #f5f5f5;

  .content-wrap {
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }

  .banner {
    width: 100%;
    height: 173px;
    background: url('./assets/images/travel-banner.jpg') no-repeat left top;
    background-size: 100% auto;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
  }

  .content {
    padding: 15px;
    margin-top: -40px;
    background: #ffffff;
    border-radius: 12px 12px 0 0;
    position: relative;
    z-index: 1;
    flex: 1;
    .row-wrapper {
      background-color: #eef7ff;
      border-radius: 8px;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      cursor: pointer;
      .icon-box {
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .icon-box img {
        width: 58px;
        height: 58px;
      }

      .text {
        flex: 1;
        font-size: 15px;
        color: #111111;
      }

      .arrow-icon {
        color: #111;
        font-size: 20px;
      }
    }

    .card-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .card-item {
        width: calc(50% - 7.5px);
        margin-bottom: 15px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        .card-img-wrapper {
          width: 100%;
        }
        .card-img {
          width: 100%;
          height: auto;
          display: block;
        }
        &.card-item-traffic-map {
          width: 100%;
        }
      }
      .launch-weapp {
        position: relative;
        height: 80px;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
      }
      .launch-weapp-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 9999;
        overflow: hidden;
      }
    }
  }

  .share {
    width: 45px;
    height: 45px;
    text-align: center;
    line-height: 46px;
    .icon_jglh {
      font-size: 24px;
    }
  }
}
</style>
