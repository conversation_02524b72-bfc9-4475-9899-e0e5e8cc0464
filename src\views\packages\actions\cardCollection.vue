<template>
  <container class="card-collection" @leave="onLeave" @resume="onResume" :keep-alive="keepAlive">
    <x-header :title="pageTitle" ref="header">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="share">
        <i class="icon_jglh icon-fenxiang1" @click="share('show')"></i>
      </div>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="container_box" :style="containerStyle">

        <div class="rules" @click="handleApply">规则</div>
        <div class="prize-record" v-if="showPrizeRecordButton" @click="goToPrizeRecords">
          中奖记录
        </div>
        <!-- 卡片展示区 -->
        <div class="card-display-area">
          <!-- 如果没有卡片则显示默认图 -->
          <div v-if="collectedCards.length === 0" class="default-card">
            <img :src="defaultCardImage" alt="默认卡片" />
          </div>
          <!-- 如果有卡片则使用swiper展示 -->
          <swiper v-else class="card-swiper" :options="swiperOption" :slides="collectedCards" ref="cardSwiper">
            <swiper-item v-for="card in collectedCards" :key="card.id" class="card-slide">
              <div class="card-item">
                <div class="card-image-wrapper">
                  <img :src="card.imageUrl" alt="福卡" />
                  <div class="card-glow"></div>
                </div>
                <div class="card-info">
                  <div class="card-brand-logo">
                    <img :src="card.brandLogo" alt="logo" />
                  </div>
                  <div class="card-brand-name">{{ card.brandName }}</div>
                  <div class="card-brand-text">送您一张{{ card.name }}</div>
                </div>
              </div>
            </swiper-item>
          </swiper>
        </div>

        <!-- 卡槽区域 -->
        <div class="card-slots">
          <div class="slots-container">
            <div v-for="card in cardList" :key="card.name" class="card-slot"
              :class="{ 'collected': isCardCollected(card.name) }" @click="showSlotDetail(card)">
              <div class="slot-image-wrapper">
                <img :src="getPlaceholderImage(card.type)" alt="卡槽"
                  :class="{ 'grayscale': !isCardCollected(card.name) }" />
                <div v-if="isCardCollected(card.name)" class="collected-badge">{{ calculateCollectedCount(card.name) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 抽卡按钮 -->
        <div class="draw-btn-container">
          <div class="draw-btn" @click="isCollectedAllCards ? drawGift() : drawCard()"
            :class="{ 'drawing': drawStatus, 'disabled': !canDraw }" ref="drawBtn">
            <span v-if="!drawStatus">{{ isCollectedAllCards ? '立即抽奖' : drawButtonText }}</span>
            <span v-else class="drawing-text">
              <i class="loading-icon"></i>抽取中...
            </span>
          </div>
          <div class="draw-tip" v-if="drawTip">{{ drawTip }}</div>
        </div>
      </div>

      <!-- 奖品弹窗 -->
      <PrizePop v-if="showPrize" :show="showPrize" :prize="prizeInfo" :confirmButtonText="prizeConfirmText"
        :cancelButtonText="prizeCancelText" @cancel="handlePrizeCancel" @confirm="handlePrizeConfirm"></PrizePop>

      <!-- 卡片详情弹窗 -->
      <van-popup v-model="showCardPopup" round class="card-popup" :close-on-click-overlay="false">
        <div class="card-popup-content">
          <!-- <div class="popup-close" @click="closeCardPopup">×</div>
          <div class="card-popup-header">

          </div> -->
          <div class="card-item pop-card-item" :class="{ show: showCardPopup }">
            <div class="card-image-wrapper">
              <img :src="currentCard.imageUrl" alt="福卡" />
              <div class="center-box">
                <div class="card-shine"></div>

              </div>
            </div>
            <div class="card-info">
              <div class="card-brand-logo">
                <img :src="currentCard.brandLogo" alt="logo" />
              </div>
              <div class="card-brand-name">{{ currentCard.brandName }}</div>
              <div class="card-brand-text">{{ currentCard.description }}</div>
            </div>
          </div>
          <div class="popup-actions">
            <div class="card-popup-btn primary" @click="confirmCardReceive">收下祝福</div>
          </div>
        </div>
      </van-popup>

      <!-- 完成所有卡片收集弹窗 -->
      <van-popup v-model="showCompletionPopup" round class="completion-popup" :close-on-click-overlay="false">
        <div class="completion-content">
          <div class="completion-title">恭喜您！</div>
          <div class="completion-message">已集齐所有福卡</div>
          <div class="completion-reward">
            <img src="./assets/images/gasha_prize01.png" alt="奖品" class="reward-image" />
            <div class="reward-text">可获得一次抽奖机会</div>
          </div>
          <div class="completion-actions">
            <div class="completion-btn" @click="claimCompletionReward">知道了</div>
          </div>
        </div>
      </van-popup>
    </content-view>
  </container>
</template>

<script>
import { isInJglh, isInWeixin, isInWeixinH5 } from '@/common/env';
import { getVersion, parseJglhURL } from '@/bridge';
import { AppStatus, PrizeType } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getImageURL, getAspectRatio } from '@/common/image';
import { formatDate, getAppURL } from '@/utils';
import { dialog, toast, loading } from '@/bus';
import { getSendBlessingDetail, drawSendBlessing, lotterySendBlessing } from './api';
import PrizePop from './components/PrizePop';
import BizRichText from '@/views/components/BizRichText.vue';
import ScreenMsg from './components/ScreenMsg';
import { Swiper, SwiperItem } from '@/components/Swiper';
import { Popup } from 'vant';

export default {
  name: 'CardCollection',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    PrizePop,
    BizRichText,
    ScreenMsg,
    Swiper,
    SwiperItem,
    [Popup.name]: Popup
  },
  data() {
    const actionId = this.$route.query.id || null;
    return {
      AppStatus,
      actionId,
      status: AppStatus.LOADING,
      keepAlive: true,
      pageTitle: '抽卡集福',
      actionDetail: {},
      actionInfo: {},
      shareInfo: {
        title: '💌 送你一张祝福卡！一起集卡兑大奖~',
        desc: '抽卡集福超好玩！集齐还能换好礼，点击试试你的运气！',
        shareImage: 'FmFHX5pWoYS17NkmG4LjG72nO-oP',
      },
      // 卡片列表（增强版）
      cardList: [],
      // 抽奖状态
      drawStatus: false,
      showPrize: false,
      showCardPopup: false,
      showCompletionPopup: false,
      currentCard: {},
      prizeInfo: {},
      // swiper配置
      swiperOption: {
        autoplay: false,
        // slidesPerView: 'auto',
        // spaceBetween: 30,
        // centeredSlides: true,
        watchSlidesProgress: true,
        resistanceRatio: 0, // 禁止边缘移动
        observer: true, // 开启动态检查器
        on: {
          init: function () {
            var slides = this.slides
            for (var i = 0; i < slides.length; i++) {
              let slide = slides.eq(i)
              slide.css('zIndex', 100 - i); // 设置slide的z-index层级
            }
            var offsetAfter = this.width * 0.95 // 每个slide的位移值
            for (let i = 0; i < slides.length; i++) {
              let slide = slides.eq(i)
              let progress = slides[i].progress

              if (progress <= 0) { // 右边slide位移
                slide.transform('translate3d(' + (progress) * offsetAfter + 'px, 0, 0) scale(' + (1 - Math.abs(progress) / 20) + ')');
                slide.css('opacity', (progress + 3)); // 最右边slide透明
              }

              if (progress > 0) {
                slide.transform('rotate(' + (-progress) * 5 + 'deg)'); // 左边slide旋转
                slide.css('opacity', 1 - progress); // 左边slide透明
              }
            }
          },

          resize: function (swiper) {
            swiper.update()
          },
          observerUpdate: function () {
            var slides = this.slides
            for (var i = 0; i < slides.length; i++) {
              let slide = slides.eq(i)
              slide.css('zIndex', 100 - i); // 设置slide的z-index层级
            }
            var offsetAfter = this.width * 0.95 // 每个slide的位移值
            for (let i = 0; i < slides.length; i++) {
              let slide = slides.eq(i)
              let progress = slides[i].progress

              if (progress <= 0) { // 右边slide位移
                slide.transform('translate3d(' + (progress) * offsetAfter + 'px, 0, 0) scale(' + (1 - Math.abs(progress) / 20) + ')');
                slide.css('opacity', (progress + 3)); // 最右边slide透明
              }

              if (progress > 0) {
                slide.transform('rotate(' + (-progress) * 5 + 'deg)'); // 左边slide旋转
                slide.css('opacity', 1 - progress); // 左边slide透明
              }
            }
          },
          setTranslate: function () {
            var slides = this.slides
            var offsetAfter = this.width * 0.95 // 每个slide的位移值
            for (var i = 0; i < slides.length; i++) {
              var slide = slides.eq(i)
              var progress = slides[i].progress

              if (progress <= 0) { // 右边slide位移
                slide.transform('translate3d(' + (progress) * offsetAfter + 'px, 0, 0) scale(' + (1 - Math.abs(progress) / 20) + ')');
                slide.css('opacity', (progress + 3)); // 最右边slide透明
              }

              if (progress > 0) {
                slide.transform('rotate(' + (-progress) * 5 + 'deg)'); // 左边slide旋转
                slide.css('opacity', 1 - progress); // 左边slide透明
              }
            }
          },
          setTransition: function (transition) {
            for (var i = 0; i < this.slides.length; i++) {
              var slide = this.slides.eq(i)
              slide.transition(transition);
            }
          },
        }
      }
    };
  },
  computed: {
    // 已收集的卡片
    collectedCards() {
      let cards = this.actionDetail.drawBlessingCardRecordList || [];
      return cards.map(card => {
        return {
          name: card.blessingCard,
          imageUrl: card.win ? getImageURL(card.blessingCardImage) : require('./assets/images/card_bless.png'),
          description: card.win ? `送您一张${card.blessingCard}` : card.blessingMessage,
          brandName: card.adPartner,
          brandLogo: getImageURL(card.adPartnerLogo),
        }
      }).reverse();
    },
    // 已收集的卡片ID
    collectedCardNames() {
      return this.collectedCards.map(card => card.name);
    },
    isCollectedAllCards() {
      // this.collectedCardNames数组去重，检查length
      let uniqueCardNames = [...new Set(this.collectedCardNames)];
      return uniqueCardNames.length > 0 && uniqueCardNames.length === this.cardList.length && this.actionInfo.status === 1;
    },
    // 是否显示中奖记录按钮this.isCollectedAllCards &&
    showPrizeRecordButton() {
      return this.$_auth_isLoggedIn;
    },
    // 默认卡片图片
    defaultCardImage() {
      return this.actionInfo.defaultCardImage || require('./assets/images/card_default.png');
    },
    // 是否可以抽卡
    canDraw() {
      if (this.drawStatus) return false;
      if (this.actionInfo.status === -1 || this.actionInfo.status === 2 || this.actionInfo.status === 0) return false;
      if (this.actionInfo.endTime && this.actionInfo.endTime < new Date().getTime()) return false;
      if (this.actionInfo.startTime && this.actionInfo.startTime > new Date().getTime()) return false;
      return true;
    },
    // 抽卡按钮文本
    drawButtonText() {
      if (this.actionInfo.status === -1 || this.actionInfo.status === 2 || (this.actionInfo.endTime && this.actionInfo.endTime < new Date().getTime())) return '活动已结束';
      if (this.actionInfo.startTime && this.actionInfo.startTime > new Date().getTime()) return '活动未开始';
      return this.actionInfo.drawButtonText || '立即抽卡';
    },
    // 抽卡提示
    drawTip() {
      if (this.actionInfo.status === -1 || this.actionInfo.status === 2 || this.actionInfo.status === 0) return '';
      if (this.actionInfo.status === 1 && this.actionInfo.startTime && this.actionInfo.startTime > new Date().getTime()) return `活动开始时间：${formatDate(this.actionInfo.startTime, 'YYYY-MM-dd HH:mm')}`;
      if (this.collectedCards.length === 0) return '开始你的福卡收集之旅';
      if (this.isCollectedAllCards) return '已集齐所有福卡，点击立即抽奖，赢取超级大奖！';
      return '继续努力，马上就能集齐啦！';
    },
    // 奖品确认按钮文本
    prizeConfirmText() {
      switch (this.prizeInfo.prizeType) {
        case PrizeType.COUPON:
          return '立即使用';
        case PrizeType.REDENVELOPE:
          return '领取红包';
        case PrizeType.GOLD:
          return '领取金币';
        default:
          return '确定';
      }
    },
    // 奖品取消按钮文本
    prizeCancelText() {
      return this.prizeInfo.prizeType === PrizeType.COUPON ? '稍后使用' : '我知道了';
    },
    // 页面样式
    containerStyle() {
      const styles = {};
      if (this.actionInfo.backgroundImage) {
        styles.backgroundImage = `url(${getImageURL(this.actionInfo.backgroundImage)})`;
        styles.backgroundPosition = 'left top';
        styles.backgroundSize = '100% auto';
        styles.backgroundRepeat = 'no-repeat';
      }
      if (this.actionInfo.backgroundColor) {
        styles.backgroundColor = this.actionInfo.backgroundColor;
      }
      return styles;
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    // 检查卡片是否已收集
    isCardCollected(cardName) {
      return this.collectedCardNames.includes(cardName);
    },
    // 计算卡片收集数量
    calculateCollectedCount(cardName) {
      return this.collectedCardNames.filter(name => name === cardName).length;
    },
    // 获取占位图片
    getPlaceholderImage(cardType) {
      const placeholders = {
        luck: require('./assets/images/card-placeholder_haoyun.png'),
        health: require('./assets/images/card-placeholder_jiankang.png'),
        safety: require('./assets/images/card-placeholder_pingan.png'),
        happiness: require('./assets/images/card-placeholder_xingfu.png'),
        wish: require('./assets/images/card-placeholder_ruyi.png')
      };
      return placeholders[cardType] || require('./assets/images/card-placeholder_ruyi.png');
    },
    // 显示卡片详情
    showCardDetail(card) {
      this.currentCard = card;
      this.showCardPopup = true;
    },
    // 显示卡槽详情
    showSlotDetail(card) {
      if (!this.isCardCollected(card.name)) {
        toast().tip(`还未获得${card.name}，继续抽卡吧！`);
      }
    },
    // 初始化
    init() {
      getSendBlessingDetail(this.actionId).then(res => {
        this.actionDetail = res;
        this.actionInfo = (res && res.sendBlessingActivity) || {};
        this.cardList = (this.actionInfo.blessingCards || []).map(card =>
        ({
          name: card.name,
          type: this.getTypeByCardName(card.name),
          imageUrl: getImageURL(card.image),
        })
        );

        // 预加载商家logo
        if (this.actionInfo.adPartners && this.actionInfo.adPartners.length > 0) {
          this.preloadImages(this.actionInfo.adPartners.map(partner => getImageURL(partner.partnerLogo)));
        }
        // 预加载福卡图片
        this.preloadImages(this.cardList.map(card => card.imageUrl));

        this.updateShareInfo(res.sendBlessingActivity || {});
        this.pageTitle = this.actionInfo.name || '抽卡集福';
        this.status = AppStatus.READY;
        this.share();
      }).catch(err => {
        console.error('获取活动详情失败:', err);
        this.status = AppStatus.ERROR;
      });
    },
    getTypeByCardName(cardName) {
      const cardMap = {
        '好运卡': 'luck',
        '健康卡': 'health',
        '平安卡': 'safety',
        '幸福卡': 'happiness',
        '如意卡': 'wish'
      };
      return cardMap[cardName] || 'common';
    },
    // 更新分享信息
    updateShareInfo(actionInfo) {
      this.shareInfo = {
        title: actionInfo.shareTitle || '💌 送你一张祝福卡！一起集卡兑大奖~',
        desc: actionInfo.shareDesc || '抽卡集福超好玩！集齐还能换好礼，点击试试你的运气！',
        shareImage: actionInfo.shareImage || '',
      };
    },
    // 抽卡
    drawCard() {
      if (!this.canDraw) {
        this.showDrawErrorTip();
        return;
      }
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      this.drawStatus = true;
      this.addDrawAnimation();

      drawSendBlessing(this.actionId)
        .then(res => {
          this.handleDrawCardResult(res);
        })
        .catch(err => {
          this.drawStatus = false;
          this.removeDrawAnimation();
          toast().tip(err.message || '抽卡失败，请重试');
        });
    },
    drawGift() {
      if (!this.canDraw) {
        this.showDrawErrorTip();
        return;
      }

      this.drawStatus = true;
      this.addDrawAnimation();
      lotterySendBlessing(this.actionId)
        .then(res => {
          this.handleDrawGiftResult(res);
        })
        .catch(err => {
          this.drawStatus = false;
          this.removeDrawAnimation();
          toast().tip(err.message || '抽奖失败，请重试');
        });
    },
    // 显示抽卡错误提示
    showDrawErrorTip() {
      if (this.actionInfo.status === -1) {
        toast().tip('活动已下线');
      } else if (this.actionInfo.endTime < new Date().getTime()) {
        toast().tip('活动已结束');
      } else if (this.actionInfo.startTime > new Date().getTime() || this.actionInfo.status === 0) {
        toast().tip('活动未开始');
      }
    },
    // 添加抽卡动画
    addDrawAnimation() {
      const drawBtn = this.$refs.drawBtn;
      if (drawBtn) {
        drawBtn.classList.add('drawing-animation');
      }
    },
    // 移除抽卡动画
    removeDrawAnimation() {
      const drawBtn = this.$refs.drawBtn;
      if (drawBtn) {
        drawBtn.classList.remove('drawing-animation');
      }
    },
    // 处理抽奖结果
    handleDrawGiftResult(res) {
      let prize = { ...res };
      prize.endTime = formatDate(
        this.actionInfo.endTime,
        'yyyy-MM-dd HH:mm:ss'
      );
      this.prizeInfo = prize;

      setTimeout(() => {
        this.drawStatus = false;
        this.removeDrawAnimation();
        if (prize.prizeType == 0) {
          toast().tip('什么也没抽到，再接再厉哦~');
        } else {
          this.showPrize = true;
        }
        this.init();
      }, 1500);
    },
    // 处理抽卡结果
    handleDrawCardResult(res) {
      setTimeout(() => {
        this.drawStatus = false;
        this.removeDrawAnimation();

        if (res) {
          if (res.blessingCard && !this.collectedCardNames.includes(res.blessingCard.name)) {
            this.collectedCardNames.push(res.blessingCard.name);
          }

          this.currentCard = {
            name: res.win ? res.blessingCard.name : '',
            imageUrl: res.win ? getImageURL(res.blessingCard.image) : require('./assets/images/card_bless.png'),
            description: res.win ? `送您一张${res.blessingCard.name}` : res.blessingMessage,
            brandName: res.adPartner.partnerName,
            brandLogo: getImageURL(res.adPartner.partnerLogo),
          };
          this.showCardPopup = true;
          this.init();
        } else {
          toast().tip('什么也没抽到，再接再厉哦~');
        }
      }, 1500);
    },
    handleApply() {
      if (this.actionInfo.description) {
        localStorage.setItem('rule_rich_text', this.actionInfo.description)
      }
      this.$_router_push('/blindBox/use?title=规则说明')
    },
    // 检查是否完成收集
    checkCompletion() {
      if (this.isCollectedAllCards) {
        setTimeout(() => {
          this.showCompletionPopup = true;
        }, 500);
      }
    },
    // 确认收到卡片
    confirmCardReceive() {
      this.closeCardPopup();
      // 检查是否集齐所有卡片
      this.checkCompletion();
    },
    // 关闭卡片弹窗
    closeCardPopup() {
      this.showCardPopup = false;
    },
    // 领取完成奖励
    claimCompletionReward() {
      this.showCompletionPopup = false;
      // this.drawGift();
    },
    // 处理奖品取消
    handlePrizeCancel() {
      this.showPrize = false;
    },
    // 处理奖品确认
    handlePrizeConfirm() {
      this.showPrize = false;
      if (this.prizeInfo.prizeType == PrizeType.COUPON || this.prizeInfo.prizeType == PrizeType.WELFARE_COUPON) {
        this.$_router_pageTo('/ticket/list', {
          theme: 'light',
        });
      } else if (this.prizeInfo.prizeType == PrizeType.NORMAL) {
        this.$_router_pageTo('/prize/address/submit?action=sendBlessing&recordId=' + this.prizeInfo.id, {
          theme: 'light',
        });
      }
    },
    // 跳转到中奖记录页面
    goToPrizeRecords() {
      this.$_router_pageTo(`/prize/records?actionId=${this.actionId}&actionType=sendBlessing`, {
        theme: 'light',
      });
    },
    // 分享
    share(action = 'config') {
      const title = this.shareInfo.title;
      const logo = getImageURL(this.shareInfo.shareImage);
      const desc = this.shareInfo.desc;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    // 重新加载
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    // 页面恢复
    onResume() {
      this.init();
    },
    // 页面离开
    onLeave() {
    },
    // 预加载图片方法
    preloadImages(imageUrls) {
      if (!imageUrls || !imageUrls.length || this.cardList.length) return;

      imageUrls.forEach(url => {
        if (!url) return;
        const img = new Image();
        img.src = url;
        // 不需要添加到DOM，只需要触发浏览器缓存即可
      });
      console.log('预加载商家logo完成:', imageUrls.length, '张图片');
    },
  },
  // 页面销毁前保存数据
  beforeDestroy() {
  }
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.card-collection {
  .card-item {
    width: 240px;
    cursor: pointer;
    transition: transform 0.3s ease;
    position: relative;

    .card-image-wrapper {
      position: relative;

      img {
        display: block;
        width: 100%;
        height: auto;
        position: relative;
        z-index: 1;
      }

      .card-glow {
        display: none;
      }
    }

    .card-info {
      width: 100%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50px);
      text-align: center;
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 1;
      z-index: 2;

      .card-brand-logo {
        width: 100px;
        height: 100px;
        background: #ffffff;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 10px;
        padding: 10px;
        box-sizing: border-box;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .card-brand-name {
        font-weight: bold;
        font-size: 20px;
        color: #5B2C10;
        margin-bottom: 3px;
        padding: 0 6px;
      }

      .card-brand-text {
        font-size: 14px;
        color: #5B2C10;
        line-height: 20px;
      }
    }
  }

  .container_box {
    width: 100%;
    min-height: 100%;
    box-sizing: border-box;
    background: #BDEBF3 url(./assets/images/card_page_bg.png) no-repeat top center; // 默认背景色，可通过接口配置
    background-size: 100% auto;
    padding-top: 150px;
    position: relative;

    .rules,
    .prize-record {
      background: #0571DB;
      // width: 60px;
      // height: 20px;
      padding: 8px 12px;
      line-height: 1;
      text-align: center;
      border-radius: 15px 0px 0px 15px;
      font-size: 12px;
      font-weight: 400;
      color: #FFFFFF;
      position: absolute;
      right: 0;
      top: 12px;
      z-index: 2;

      &:nth-child(2) {
        top: 46px;
      }
    }

    // 卡片展示区
    .card-display-area {
      min-height: 280px;

      .default-card {
        width: 100%;
        margin-bottom: 20px;

        img {
          display: block;
          width: 100%;
          height: auto;
        }

      }

      .card-swiper {
        height: 370px;
        opacity: 0;
        animation: fade-in 0.3s ease .8s forwards;

        .card-slide {
          display: flex;
          align-items: flex-start;
          justify-content: center;
        }

        .swiper-slide-active {
          .card-image-wrapper .card-glow {
            display: block;
            width: 300px;
            height: 50px;
            background: url(./assets/images/card_item_shadow.png) no-repeat center center;
            background-size: 100% 100%;
            position: absolute;
            bottom: -42px;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0.6;
          }
        }
      }
    }

    // 卡槽区域
    .card-slots {
      padding: 0 20px;
      margin-bottom: 20px;

      .slots-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 20px;
      }

      .card-slot {
        width: calc(20% - 20px);
        text-align: center;
        cursor: pointer;
        transition: transform 0.2s ease;
        position: relative;

        .slot-image-wrapper {
          position: relative;
          font-size: 0;

          img {
            width: 100%;
            height: auto;

            &.grayscale {
              opacity: 0.5;
            }
          }

          .collected-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 18px;
            height: 18px;
            background: #FF4646;
            border: 1px solid #FFFFFF;
            color: white;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
          }

          .new-badge {
            position: absolute;
            top: -8px;
            left: -8px;
            background: #ff4757;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
            animation: pulse 1.5s ease-in-out infinite;
          }
        }

        &.collected {
          .slot-image-wrapper img {
            opacity: 1;
          }
        }
      }
    }

    // 抽卡按钮区域
    .draw-btn-container {
      text-align: center;
      padding-bottom: 10px;

      .draw-count {
        font-size: 14px;
        color: white;
        margin-bottom: 10px;
        opacity: 0.9;
      }

      .draw-btn {
        display: inline-block;
        width: 300px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        background: linear-gradient(261deg, #FF403F 0%, #FD5B27 100%);
        box-shadow: inset -4px -2px 10px 0px #FFFFFF;
        border-radius: 30px 30px 30px 30px;
        color: white;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover:not(.disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
        }

        &:active:not(.disabled) {
          transform: translateY(0);
        }

        &.drawing {
          // background: linear-gradient(45deg, #95a5a6, #7f8c8d);
          cursor: not-allowed;

          .drawing-text {
            display: flex;
            align-items: center;
            justify-content: center;

            .loading-icon {
              width: 20px;
              height: 20px;
              border: 2px solid transparent;
              border-top: 2px solid white;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-right: 8px;
            }
          }
        }

        &.disabled {
          background: linear-gradient(45deg, #95a5a6, #7f8c8d);
          cursor: not-allowed;
          opacity: 0.7;
        }

        &.drawing-animation::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          animation: slide 1.5s infinite;
        }
      }

      .draw-tip {
        font-size: 12px;
        color: #555555;
        margin-top: 8px;
        opacity: 0.8;
      }

    }

  }

  // 卡片弹窗
  .card-popup {
    width: 240px;
    border-radius: 20px;
    overflow: hidden;
    background: transparent;
    overflow: visible;

    .card-popup-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      .popup-close {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #666;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #e0e0e0;
        }
      }

      .card-popup-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .brand-logo {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          margin-right: 12px;
          object-fit: cover;
        }

        .brand-name {
          font-size: 18px;
          font-weight: bold;
          color: #333;
        }
      }

      .pop-card-item {
        margin: 0 auto;
        transform: scale(0);
        opacity: 0;
        transition: all 0.3s ease-out;

        .card-image-wrapper {
          overflow: hidden;
        }

        .card-info {
          opacity: 0;
          animation: fade-in 0.3s ease 0.3s forwards;

          .card-brand-text {
            padding: 0 10px;
          }
        }

      }

      .pop-card-item.show {
        animation: pop-in 0.5s ease-out forwards;

      }

      .shine {
        position: absolute;
        top: 0;
        left: -75%;
        width: 50%;
        height: 100%;
        background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        transform: skewX(-25deg);
        animation: shine-move 1.2s ease-in-out 0.5s infinite;
      }

      .center-box {
        position: absolute;
        top: 10px;
        left: 10px;
        width: calc(100% - 20px);
        height: calc(100% - 20px);
        overflow: hidden;
      }

      .card-shine {
        position: absolute;
        top: 0;
        left: -75%;
        width: 50%;
        height: 100%;
        background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        transform: skewX(-25deg);
        animation: shine-move 1.2s ease-in-out 0.6s forwards;
        z-index: 3;
      }

      .card-popup-message {
        font-size: 16px;
        color: #333;
        text-align: center;
        margin-bottom: 25px;
        line-height: 1.5;
        padding: 0 10px;
      }

      .popup-actions {
        display: flex;
        gap: 12px;
        width: 100%;
        margin-top: 20px;

        .card-popup-btn {
          flex: 1;
          height: 45px;
          line-height: 45px;
          text-align: center;
          border-radius: 22px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          opacity: 0;
          animation: fade-in 0.8s ease 0.5s forwards;

          &.primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
            }
          }
        }
      }
    }
  }

  // 完成收集弹窗
  .completion-popup {
    width: 85%;
    max-width: 320px;
    border-radius: 20px;
    overflow: hidden;

    .completion-content {
      padding: 30px 20px;
      text-align: center;
      background: #ffffff;
      color: #333;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(261deg, #FF403F 0%, #FD5B27 100%);
      }

      .completion-icon {
        margin-bottom: 15px;
        position: relative;
        display: inline-block;

        .card-icon {
          width: 80px;
          height: 80px;
          object-fit: contain;
          animation: float 3s ease-in-out infinite;
        }

        .celebration-emoji {
          position: absolute;
          top: -10px;
          right: -10px;
          font-size: 28px;
          animation: pulse 1.5s ease-in-out infinite;
        }
      }

      .completion-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #FF403F;
      }

      .completion-message {
        font-size: 18px;
        margin-bottom: 20px;
        font-weight: 500;
        color: #5B2C10;
      }

      .completion-reward {
        margin-bottom: 25px;
        background: rgba(253, 91, 39, 0.08);
        padding: 15px;
        border-radius: 12px;

        .reward-image {
          width: 60px;
          height: 60px;
          object-fit: contain;
          margin-bottom: 8px;
        }

        .reward-text {
          font-size: 16px;
          color: #FF403F;
          font-weight: 500;
        }
      }

      .completion-btn {
        width: 80%;
        height: 50px;
        line-height: 50px;
        background: linear-gradient(261deg, #FF403F 0%, #FD5B27 100%);
        box-shadow: 0 4px 12px rgba(253, 91, 39, 0.3);
        border-radius: 25px;
        color: white;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.2s;
        margin: 0 auto;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 15px rgba(253, 91, 39, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

.share {
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 46px;

  .icon_jglh {
    font-size: 24px;
  }
}

// 动画效果
@keyframes pop-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  60% {
    transform: scale(1.2);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shine-move {
  0% {
    left: -75%;
  }

  100% {
    left: 125%;
  }
}

@keyframes fade-in {
  to {
    opacity: 1;
  }
}

@keyframes glow {
  0% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes slide {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

@keyframes bounce {

  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    transform: translate3d(0, -30px, 0);
  }

  70% {
    transform: translate3d(0, -15px, 0);
  }

  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0px);
  }
}
</style>
