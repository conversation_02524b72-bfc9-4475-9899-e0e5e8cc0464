<template>
  <div class="x-countdown">
    <slot name="prefix"></slot>
    <slot v-bind:durations="durations">{{durationStr}}</slot>
    <slot name="suffix"></slot>
  </div>
</template>
<style lang="scss" scoped>
  .x-countdown {
    display: inline-block;
  }
</style>
<script>
  import { getDuration } from '@/utils';

  /**
   * # ChangeLog
   * ## v1.1.0 2020年8月1日10:51:34
   * 1. 时钟支持通过外部定义。适用于与外部公用定时器时使用
   *
   * # TODO:
   * 1. 支持显示过去时，目前仅支持显示将来时
   */
  export default {
    name: 'countdown',
    props: {
      // 更新频率，未定义now时有效：默认1000ms
      frequency: {
        type: [Number],
        default: 1000,
      },
      // 倒计时相对时间
      time: {
        type: [Number, Date],
      },
      // 当前时间，若未定义此属性，组件内将启动一个定时器，否则不启用定时器
      now: {
        type: [Number, Date],
      },
    },
    data() {
      return {
        code: 0,
        durations: {},
      };
    },
    computed: {
      durationStr() {
        const durations = this.durations;
        if (durations && durations.$differ) {
          const [days, hours, minutes, seconds] = [durations.days(), durations.hours(), durations.minutes(), durations.seconds()];
          const [daysStr, hoursStr, minutesStr, secondsStr] = [days ? `${days}天` : '', hours ? `${hours}小时` : '', minutes ? `${minutes}分` : '', seconds ? `${seconds}秒` : ''];
          return [daysStr, hoursStr, minutesStr, secondsStr].join('');
        }
        return '';
      }
    },
    watch: {
      now() {
        this.update();
      }
    },
    mounted() {
      this.start();
    },
    beforeDestroy() {
      this.stop();
    },
    activated() {
      this.start();
    },
    deactivated() {
      this.stop();
    },
    methods: {
      start() {
        // 如果外部没有定义当前时间，启用内部定时器计时
        if (!this.now) {
          this.countdown();
        }
      },
      // 更新计时
      update() {
        const differ = new Date(this.time).getTime() - (this.now || Date.now());
        // 仅支持显示未来时
        if (differ > 0) {
          this.durations = getDuration(differ);
        }
        return differ;
      },
      countdown() {
        if (this.update() > 0) {
          this.code = setInterval(() => {
            this.update();
          }, this.frequency);
        }
      },
      stop() {
        clearInterval(this.code);
      }
    },
  };
</script>
