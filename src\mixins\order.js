import { getUserRedPocketAccountInfo } from '@/api/modules/account';
import { getAvailableTicketList, getAvailableTicketList2 } from '@/api/modules/ticket';
// import { getVipPackages, getVipInterests } from '@/api/modules/vip';
import { getVipPackages, getVipInterests, getVipPkgDetail } from '@/api/modules/vip-v2';

/**
 * 用于下单选券，各种优惠组合，配合 VipJoiningCheckbox，_OrderDiscountCalculator 组件使用
 */

// -1 表示暂不支持
const TicketCategory = {
  None: -1,
  CarBeauty: 0, // 汽车美容
  CarMaintain: 1, // 汽车保养
  // CarInspection: 2,
  OnlineCarInspection: 2, // 上线审车
  AgentCarInspection: -1, // 上门待审
  CarRegister: -1, // 新车上牌
  CarTransfer: -1, // 二手车过户
  CardReplacement: -1, // 证牌补换
  NoExamInspection: -1, // 免检审车
  GroupBuy: 3, // 团购
  VipBuying: 4, // 开通VIP会员
  // VipBuying: -1, // 开通VIP会员临时禁用
  WashCard: -1, // 洗车卡
  Food: -1, // 餐饮
  MobileRecharge: 0, // 话费充值
}

export const mixinOrder = {
  data() {
    return {
      // $_order_TicketCategory: TicketCategory,
      mixin_order_data: {
        tickets: {}, // 可选的优惠券
        account: {}, // 红包余额信息
        vipPkgData: {}, // VIP套餐信息
        interests: {}, // 会员权益信息
        vipCoupons: [], // 会员赠送的优惠券

        // 默认优惠券查询信息
        ticketQuery: {
          category: -1, // category: 0:美容  1：保养  2：审车， 3：团购  4：会员
          rid: 0, // 美容、保养、审车 传商家id 团购传商品id 会员传会员开通项目
          amount: 0, // 订单价格
        },
      },
    };
  },
  // Vue默认不会监控$开头的变量名，可通过computed实现响应变化
  computed: {
    // 是否可以购买商城版vip会员
    $_order_couldBuyVip() {
      // 还不是VIP会员 & 有可选的VIP会员套餐
      return !!(!this.$_auth_isVip && this.$_order_buyingVipPkgDesc);
    },
    // 是否可以购买车主版vip会员
    $_order_couldBuyVipCar() {
      // 还不是VIP会员 & 有可选的VIP会员套餐
      return !!(!this.$_auth_isCarVip && this.$_order_buyingVipPkgDesc);
    },
    $_order_TicketCategory() {
      return TicketCategory
    },
    // 选券，红包抵扣计算器所需参数对象
    $_order_calculatorProps() {
      const { tickets, account } = this.mixin_order_data;
      const accountBalance = account && account.balance;
      const ticketQuery = this.mixin_order_data.ticketQuery
      return {
        accountBalance,
        ticketCount: (tickets.list || []).length,
        ticketQuery,
      };
    },
    // 默认开通的vip套餐
    $_order_buyingVipPkg() {
      // const pkgs = this.mixin_order_data.vipPkgData.membershipList || [];
      // // 优先开通年费会员:365天的
      // const pkg = pkgs.filter(item => item.memberTime === 365)[0] || pkgs[0];
      // return pkg;
      this.mixin_order_data.vipPkgData.presentPrice = this.mixin_order_data.vipPkgData.salePrice // 兼容老版本会员字段名
      return this.mixin_order_data.vipPkgData;
    },
    // 组合购买vip提示信息
    $_order_buyingVipPkgDesc() {
      const pkg = this.$_order_buyingVipPkg;
      // const interests = this.mixin_order_data.interests.basicInterests.filter(item => {
      //   return item.title.indexOf('更多权益') === -1;
      // });
      const interests = this.mixin_order_data.vipPkgData.routineItems;
      const interestsCount = (interests || []).length;
      if (pkg) {
        let vipType = pkg.category
        if (vipType == 'mall') {
          return `${pkg.name}${pkg.presentPrice}元, 全年返1200元消费券`;
        }
        return `${pkg.name}${pkg.presentPrice}元, 专享${interestsCount || '多'}项特权`;
      }
      // if (pkg) return `${pkg.name}${pkg.presentPrice}元，专享${interestsCount || '多'}项特权`;
      return ''
    },
  },
  mounted() {
    // 自动配置分享信息
    // data中 以 _ 或 $ 开头的属性 不会 被 Vue 实例代理，需要通过$data.访问
    // https://cn.vuejs.org/v2/api/index.html#data
  },
  methods: {
    // 获取下单所需优惠信息
    $_order_getDiscountData(ticketQuery, vipType = 'mall') {
      const ticketQueryParams = Object.assign({}, this.mixin_order_data.ticketQuery, ticketQuery);
      // 配合商城商家结算，如果含有运费，则amount值需减去运费cost值
      if (ticketQuery && ticketQuery.cost && ticketQuery.category == 3) {
        ticketQueryParams.amount = (ticketQueryParams.amount - ticketQuery.cost).toFixed(2)
        delete ticketQueryParams.cost
      }
      // 2022-09-14 为适配多商品下单，rid、type字段修改为数组形式
      if (ticketQueryParams.rid) {
        ticketQueryParams.rids = (ticketQueryParams.rid + '').split(',') + ''
        delete ticketQueryParams.rid
      } else {
        ticketQueryParams.rids = [0]
        if (ticketQueryParams.rid === 0) {
          delete ticketQueryParams.rid
        }
      }
      if (ticketQueryParams.type) {
        ticketQueryParams.types = (ticketQueryParams.type + '').split(',') + ''
        delete ticketQueryParams.type
      } else {
        ticketQueryParams.types = [-1]
      }
      this.mixin_order_data.ticketQuery = ticketQueryParams;
      const requests = [
        getAvailableTicketList2(ticketQueryParams),
        getUserRedPocketAccountInfo(),
        // getVipPackages(vipType), // 2023-06-29弃用, 此接口后台查询较慢(recommendItems字段影响)，查询会员套餐使用getVipPkgDetail
        getVipPkgDetail(vipType),
        // getVipInterests(),, interests
      ]
      return Promise.all(requests).then(([tickets, account, pkgs]) => {
        this.mixin_order_data.tickets = tickets;
        this.mixin_order_data.account = account;
        this.mixin_order_data.vipPkgData = pkgs;
        this.mixin_order_data.vipCoupons = this.$_order_getAvailableVipCoupons(pkgs, ticketQuery);
        // this.mixin_order_data.interests = interests;
      }).catch(err => {
        return Promise.reject(err);
      });
    },
    $_order_preSubmit(params, routeType = 'push') {
      // if (!soid || !nextPath) throw new Error('参数不对！');
      // TODO: 校验参数格式
      //
      if (routeType === 'push') {
        this.$_auth_push({
          name: 'buy-ticket-select',
          params,
          // query: params.ticketQuery,
        });
      } else {
        this.$_auth_replace({
          name: 'buy-ticket-select',
          params,
        });
      }
    },
    $_order_getAvailableVipCoupons(res, ticketQuery) {
      let coupons = []
      let orderAmount = ticketQuery.amount
      // 配合商城商家结算，如果含有运费，则amount值需减去运费cost值
      if (ticketQuery && ticketQuery.cost && ticketQuery.category == 3) {
        orderAmount = ticketQuery.amount - ticketQuery.cost
      }
      // 判断是否开启会员权益卡券
      if (
        res &&
        res.openMemberGrantItems != null &&
        res.openMemberGrantItems.grantInterestsItems.length > 0
      ) {
        // 获取自动索引
        let grantInterestsItems;
        let index = res.openMemberGrantItems.grantInterestsItems.findIndex(
          (item) => item.receiveType == 'auto'
        );
        // 判断是否有自动，如果有优先展示自动，没有就展示手动
        if (index > -1) {
          grantInterestsItems = res.openMemberGrantItems.grantInterestsItems[index];
        } else {
          coupons = []; // 没有优惠券
          return
        }
        coupons = grantInterestsItems.triggerInterestsCoupons; // 优惠券
      } else {
        coupons = []; // 没有优惠券
      }
      return coupons.filter(item => {
        return item.restrictionsAmount <= orderAmount;
      })
    },
  },
};
