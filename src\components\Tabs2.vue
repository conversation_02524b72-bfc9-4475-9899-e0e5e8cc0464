<template>
  <div
    class="tab-container"
    :class="{
      scrollable: scrollable,
      'un-scrollable': !scrollable,
      'tab-sticky': sticky,
    }"
  >
    <div class="tab-items" ref="items">
      <div class="tab-items-inner">
        <div
          v-for="(item, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{
            'tab-item-active': index === currentIndex,
            'tab-item-hot': item.hot,
          }"
          :style="{ color: textColor }"
          @click="setActive(item, index)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <!-- <div class="tab-item-slider" :style="sliderStyle"></div> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
$active-color: #398eff;
$tab-item-padding: 8px;

$tab-active-bar-height: 2px;

.tab-container {
  width: 100%;
}
.tab-sticky {
  position: sticky;
  top: -1px; /* px */
  z-index: 10;
}
.un-scrollable {
  .tab-items-inner {
    display: flex;
    flex-direction: row;
  }
  .tab-item {
    flex: 1;
  }
}

.tab-items {
  position: relative;
  border-bottom: 1px solid #e9e9e9;
}
.scrollable {
  .tab-items {
    overflow-x: hidden;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
  }
  .tab-items-inner {
    width: max-content;
  }
}
.tab-item {
  // flex:1;
  text-align: center;
  float: left;
  position: relative;
  padding: $tab-item-padding;
  overflow: hidden;
  white-space: nowrap;
  &::after {
    content: '';
    height: $tab-active-bar-height; /* px */
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    background: transparent;
    width: 70px;
  }
}

.tab-item-active {
  color: $active-color;
  &::after {
    bottom: 0;
    background: $active-color;
  }
}

.tab-item-slider {
  content: '';
  height: $tab-active-bar-height;
  background: $active-color;
  flex: 1;
  position: absolute;
  width: 20%;
  left: 0;
  bottom: 0;
  transform: translateX(0);
  transition: transform 250ms;
}
</style>

<script>
import { animate } from '@/lib/animation';
import BetterScroll from '@better-scroll/core';
/**
 * tabs2对原tabs做了一些简化，传入传出值均改为索引而非item的id，以提升扩展性，易用性
 */
export default {
  name: 'tabs2',
  props: {
    tabs: Array,
    value: {
      type: [Number, String],
      default: 0,
    },
    index: {
      type: [Number],
      default: 0,
    },
    scrollable: {
      type: Boolean,
      default: false,
    },
    sticky: {
      type: Boolean,
      default: false,
    },
    // tab文字颜色
    textColor: {
      type: String,
      default: '',
    },
    itemMinWidth: 0,
  },
  data() {
    return {
      currentItem: {},
      currentIndex: this.index,
    };
  },
  computed: {
    sliderStyle() {
      return {
        width: `${(1 / this.tabs.length) * 100}%`,
        transform: `translateX(${this.currentIndex * 100}%)`,
        '-webkit-transform': `translateX(${this.currentIndex * 100}%)`,
      };
    },
  },
  watch: {
    index(index) {
      // this.onIndexChange(index);
      this.currentIndex = index;
    },
    currentIndex(val) {
      this.onIndexChange(val);
    },
    tabs(val) {
      if (this.$bs) {
        this.$bs.refresh();
      }
    },
  },
  mounted() {
    if (this.scrollable) {
      this.$nextTick(() => {
        // 初始化后需要滚动到当前选中的元素
        this.initScroller();
        this.scrollToElement();
      });
    }
  },
  updated() {
    if (this.$bs) {
      this.$bs.refresh();
    }
  },
  beforeDestroy() {
    if (this.$bs) {
      this.$bs.destroy();
    }
  },
  methods: {
    refreshTabs() {
      this.$bs.refresh();
    },
    initScroller() {
      this.$bs = new BetterScroll(this.$refs.items, {
        scrollX: true,
        click: true,
        probeType: 3, // listening scroll hook
      });
    },
    setActive(item, index) {
      this.currentItem = item;
      this.currentIndex = index;
      this.$emit('input', index);
      // this.$emit("change", index);
      // this.$nextTick(this.scrollToElement);
    },
    onIndexChange(index) {
      this.$emit('change', index);
      this.currentIndex = index;
      this.$nextTick(this.scrollToElement);
    },
    scrollToElement() {
      // console.log('scrollItemToCenter')
      if (!this.scrollable) return;
      const $items = this.$refs.items;
      const $active = $items.querySelector('.tab-item-active');
      if ($active) {
        this.$bs.scrollToElement($active, 300, true, true);
      }
    },
    scrollItemToCenter() {
      // console.log('scrollItemToCenter')
      if (!this.scrollable) return;
      const $items = this.$refs.items;
      const $active = $items.querySelector('.tab-item-active');
      if ($active) {
        const rect = $active.getBoundingClientRect();
        const centerX = $active.scrollLeft + $active.clientWidth / 2;
        const moveX = rect.left - $items.clientWidth / 2 + rect.width / 2;
        // console.log($active, moveX);
        // $items.scrollLeft = $items.scrollLeft + moveX;
        animate(
          $items.scrollLeft,
          $items.scrollLeft + moveX,
          300,
          'Quart.easeOut',
          (value, end) => {
            $items.scrollLeft = value;
          }
        );
      }
    },
    scrollItemIntoView() {
      if (!this.scrollable) return;
      const $items = this.$refs.items;
      const $active = $items.querySelector('.tab-item-active');
      if ($active) {
        const rect = $active.getBoundingClientRect();
        const boundary = {
          left: 50,
          right: window.innerWidth - 50,
        };
        const step = window.innerWidth / 2;
        // console.log(rect);
        if (rect.left < boundary.left) {
          animate(
            $items.scrollLeft,
            $items.scrollLeft - Math.abs(rect.left) - step,
            300,
            'Quart.easeOut',
            (value, end) => {
              $items.scrollLeft = value;
            }
          );
        } else if (rect.right > boundary.right) {
          animate(
            $items.scrollLeft,
            $items.scrollLeft + (rect.right - window.innerWidth) + step,
            300,
            'Quart.easeOut',
            (value, end) => {
              $items.scrollLeft = value;
            }
          );
        }
      }
    },
  },
};
</script>
