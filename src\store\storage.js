import { generateUUID } from '@/utils';

const storage = localStorage;

export function createKey(name) {
  const PREFIX = 'jglh:mcar:';
  return `${PREFIX}${name}`;
}

export function removeItem(key) {
  storage.removeItem(key);
}

export function setItem(key, value) {
  storage.setItem(key, value);
}

export function getItem(key) {
  return storage.getItem(key);
}

export default {
  removeItem,
  setItem,
  getItem,
};

const Keys = {
  SEARCH_HISTORY: createKey('_search_history'), // 洗车搜索记录
  CITY_CHANGE_HISTORY: createKey('_cc_history'), // 切换城市记录
  CURRENT_CITY: createKey('_city'), // 当前城市
  VIP_INVITE_CODE: createKey('vip-icode'), // 记录分享邀请码
  AUTH_TOKEN: 'session', // 权限认证token，因此key涉及多个模块，暂时不加前缀
  USER_TYPE: createKey('user_type'), // 用户类型：jglh|chezhuka 默认交广领航用户
  GOODS_RECOMMENDER: createKey('ref_user'), // 推荐商品用户
  ORDER_CHANNEL: createKey('channel'), // 订单渠道来源
  LONG_PAY_FLAG: createKey('longpay'), // 龙支付开关
  THIRD_APP_PARAMS: createKey('third_app'), // 第三方app传递的参数
  GOODS_MALL_KEYWORD: createKey('_goods_mall_keyword'), // 商品搜索关键词
  CAR_MT_KEYWORD: createKey('_car_mt_keyword'), // 养车搜索关键词
  CAR_WASH_KEYWORD: createKey('_car_wash_keyword'), // 洗车搜索关键词
  VIP_EXPIRE_TIP: createKey('_vip_expire_tip'), // 会员过期提醒
  JGLH_SEARCH_KEYWORD: createKey('_jglh_search_keyword'), // 交广领航搜索关键词
  JGLH_COUPON_SHOWN_TODAY: createKey('_coupon_shown_today'), // 商品详情浏览赠券弹窗日期
};

/**
 * 设置会员过期提醒
 * @param {boolean} value
 */
export function setVipExpireTip(value) {
  storage.setItem(Keys.VIP_EXPIRE_TIP, value || false);
}

/**
 * 获取会员过期提醒
 */
export function getVipExpireTip() {
  let value = storage.getItem(Keys.VIP_EXPIRE_TIP);
  if (value === 'false') {
    return false;
  }
  if (value === 'true') {
    return true;
  }
  return true;
}

/**
 * 获取搜索历史记录
 */
export function getSearchHistory() {
  const KEY = Keys.SEARCH_HISTORY;
  const history = JSON.parse(getItem(KEY)) || [];
  return Promise.resolve(history);
}

/**
 * 添加搜索历史记录
 * @param {string} name 搜索记录项
 */
export const SearchType = {
  WORD: 1,
  SHOP: 2,
};
export function addSearchHistory(type, data) {
  if (!type || !data) return;
  const KEY = Keys.SEARCH_HISTORY;
  const history = JSON.parse(getItem(KEY)) || [];
  const save = {
    type,
    data,
  };
  history.some((item, index) => {
    if (item.type === SearchType.WORD && item.data === data) {
      history.splice(index, 1);
      return true;
    }
    if (item.type === SearchType.SHOP && item.data.id === data.id) {
      history.splice(index, 1);
      return true;
    }
    return false;
  });
  history.splice(0, 0, save);
  setItem(KEY, JSON.stringify(history));
  return Promise.resolve(history);
}

/**
 * 清除搜索记录
 */
export function clearSearchHistory() {
  const KEY = Keys.SEARCH_HISTORY;
  removeItem(KEY);
}

/**
 * 获取当前城市
 */
export function getCity() {
  const DEFAULT_CITY = {
    id: '郑州市',
    name: '郑州',
    pinyin: 'zhengzhou',
  };
  const KEY = Keys.CURRENT_CITY;
  try {
    return JSON.parse(getItem(KEY)) || DEFAULT_CITY;
  } catch (e) {
    console.error(e);
    return DEFAULT_CITY;
  }
}

/**
 * 添加切换城市记录
 * @param {*} name
 */
export function addChangeCityHistory(city) {
  if (!city) return;
  if (city == 'undefined') return;
  if (!city || !city.id) return;
  const KEY = Keys.CITY_CHANGE_HISTORY;
  let history = JSON.parse(getItem(KEY)) || [];
  history.some((item, index) => {
    if (city.id === item.id) {
      history.splice(index, 1);
      return true;
    }
    return false;
  });
  history.splice(0, 0, city);
  setItem(KEY, JSON.stringify(history));
}

/**
 * 获取切换城市历史记录
 */
export function getChangeCityHistory() {
  const KEY = Keys.CITY_CHANGE_HISTORY;
  const history = JSON.parse(getItem(KEY)) || [];
  return history;
}

/**
 * 设置当前所属城市
 * @param {string} city 城市信息
 */
export function setCity(city) {
  const KEY = Keys.CURRENT_CITY;
  if (!city || !city.id) return;
  addChangeCityHistory(city);
  setItem(KEY, JSON.stringify(city));
}

/**
 * 设置终端打开的vip会员红包邀请码
 */
export function setVipInviteCode(value) {
  if (value === undefined || value === null) {
    removeItem(Keys.VIP_INVITE_CODE);
  } else {
    setItem(Keys.VIP_INVITE_CODE, value);
  }
}

/**
 * 获取分享vip会员红包页面的邀请码
 */
export function getVipInviteCode() {
  return getItem(Keys.VIP_INVITE_CODE) || '';
}

// 获取用户类型
export function getUserType() {
  return getItem(Keys.USER_TYPE) || 'jglh';
}

/**
 * 保存登录验证token，登录token值由于跨项目原因，采取特殊处理，使用的key值统一为session
 * @param {*} value
 */
export function saveTokenToStorage(value) {
  storage.setItem(Keys.AUTH_TOKEN, value);
}

/**
 * 获取登录验证token
 */
export function getTokenFromStorage() {
  // 2019年1月18日15:18:55读写session key 值变更，为不影响线上微信端已登录的用户，此处做回退降级处理
  return storage.getItem(Keys.AUTH_TOKEN);
}

/**
 * 设置推荐用户
 * @param {*} user
 */
export function setRecommender(user) {
  if (!user) {
    sessionStorage.removeItem(Keys.GOODS_RECOMMENDER);
  } else {
    sessionStorage.setItem(Keys.GOODS_RECOMMENDER, user);
  }
}

/**
 * 获取推荐用户
 * @param {*} user
 */
export function getRecommender() {
  return sessionStorage.getItem(Keys.GOODS_RECOMMENDER);
}

/**
 * 设置订单渠道
 * @param {*} user
 */
export function setOrderChannel(channel) {
  if (!channel) {
    sessionStorage.removeItem(Keys.ORDER_CHANNEL);
  } else {
    sessionStorage.setItem(Keys.ORDER_CHANNEL, channel);
  }
}

/**
 * 获取订单渠道
 */
export function getOrderChannel() {
  return sessionStorage.getItem(Keys.ORDER_CHANNEL) || '';
}

/**
 * 设置龙支付开关
 * @param {string} flag
 */
export function setLongPayFlag(flag) {
  if (!flag) {
    sessionStorage.removeItem(Keys.LONG_PAY_FLAG);
  } else {
    sessionStorage.setItem(Keys.LONG_PAY_FLAG, 1);
  }
}

/**
 * 页面是否已开启龙支付
 */
export function isLongPayEnabled() {
  return sessionStorage.getItem(Keys.LONG_PAY_FLAG) == 1;
}

/**
 * 生成一个uuid，并存储到localStorage中
 * 主要用于唯一标记客户端，供服务端做数据分析用
 */
export function getFingerprint() {
  let KEY = 'cid';
  try {
    let id = getItem(KEY);
    if (id) {
      return id;
    } else {
      id = generateUUID();
      setItem(KEY, id);
      return id;
    }
  } catch (err) {
    return '';
  }
}

export function saveFingerprint(value) {
  let KEY = 'cid';
  setItem(KEY, value);
}

/**
 * 设置第三方app加载时传递的参数
 * @param {*} params
 */
export function setThirdAppParams(params) {
  return sessionStorage.setItem(Keys.THIRD_APP_PARAMS, JSON.stringify(params));
}

/**
 * 获取第三方app加载时传递的参数
 */
export function getSessionThirdAppParams() {
  return JSON.parse(sessionStorage.getItem(Keys.THIRD_APP_PARAMS)) || {};
}

/**
 * 设置商品搜索关键词
 * @param {*} params
 */
export function setGoodsSearchKeyword(params) {
  if (!params) {
    storage.removeItem(Keys.GOODS_MALL_KEYWORD);
  } else {
    storage.setItem(Keys.GOODS_MALL_KEYWORD, JSON.stringify(params));
  }
}

/**
 * 获取商品搜索关键词
 */
export function getGoodsSearchKeyword() {
  return JSON.parse(storage.getItem(Keys.GOODS_MALL_KEYWORD)) || [];
}

/**
 * 设置养车搜索关键词
 * @param {*} params
 */
export function setCarMtKeyword(params) {
  if (!params) {
    storage.removeItem(Keys.CAR_MT_KEYWORD);
  } else {
    storage.setItem(Keys.CAR_MT_KEYWORD, JSON.stringify(params));
  }
}

/**
 * 获取养车搜索关键词
 */
export function getCarMtKeyword() {
  return JSON.parse(storage.getItem(Keys.CAR_MT_KEYWORD)) || [];
}

/**
 * 设置养车搜索关键词
 * @param {*} params
 */
export function setCarWashKeyword(params) {
  if (!params) {
    storage.removeItem(Keys.CAR_WASH_KEYWORD);
  } else {
    storage.setItem(Keys.CAR_WASH_KEYWORD, JSON.stringify(params));
  }
}

/**
 * 获取养车搜索关键词
 */
export function getCarWashKeyword() {
  return JSON.parse(storage.getItem(Keys.CAR_WASH_KEYWORD)) || [];
}

/**
 * 设置交广领航搜索关键词
 * @param {*} params
 */
export function setJglhSearchKeyword(params) {
  if (!params) {
    storage.removeItem(Keys.JGLH_SEARCH_KEYWORD);
  } else {
    storage.setItem(Keys.JGLH_SEARCH_KEYWORD, JSON.stringify(params));
  }
}

/**
 * 获取交广领航搜索关键词
 */
export function getJglhSearchKeyword() {
  return JSON.parse(storage.getItem(Keys.JGLH_SEARCH_KEYWORD)) || [];
}

/**
 * 设置商品详情浏览赠券弹窗日期
 * @param {string} date 日期
 */
export function setJglhCouponShownToday(date) {
  if (!date) {
    storage.removeItem(Keys.JGLH_COUPON_SHOWN_TODAY);
  } else {
    storage.setItem(Keys.JGLH_COUPON_SHOWN_TODAY, date);
  }
}

/**
 * 获取商品详情浏览赠券弹窗日期
 */
export function getJglhCouponShownToday() {
  return storage.getItem(Keys.JGLH_COUPON_SHOWN_TODAY) || '';
}
