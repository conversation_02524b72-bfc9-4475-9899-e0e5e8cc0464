<template>
  <uploader
    ref="uploader"
    v-bind="options"
    v-on="$listeners"
    :files="$attrs.files"
  >
  <slot></slot>
  </uploader>
</template>
<script>
import Uploader from '@/components/VideoUploader';
import { getQiniuUploadToken, uploadToQiniuByImageURL } from '@/api';
import { getVideoURL } from '@/common/video';
import { playPhotos } from '@/bridge';
import { dialog, toast, loading } from '@/bus';

export function getQiniuVideoCover(id, vhost) {
  const host = vhost || 'https://vod.jgrm.net';
  return `${host}/${id}?vframe/jpg/offset/1`;
}

function getThumbnail(value) {
  return getVideoURL(value);
}

// kebab case to camelcase javascript
function kebab2camel(key) {
  return key.replace(/(-\w)/g, m => {
    return m.replace('-', '').toUpperCase() 
  })
}

// 对象 key值 命名风格转换
function transformObjectCase(obj) {
  const ignores = ['input'];
  return Object.keys(obj).reduce((prev, key) => {
    if (ignores.indexOf(key) == -1) {
      prev[kebab2camel(key)] = obj[key];
    }
    return prev;
  }, {})
}

const onVideoUploadParamsChange = function(rcallback) {
  // token刷新周期：30分钟
  const FETCH_INTERVAL = 1000 * 60 * 30;
  let params = {};
  let failCount = 0;
  // let rcallback;
  const getTokenFromServer = () => {
    return getQiniuUploadToken(2).then(res => {
      params = res;
    })
  }

  const refreshToken = () => {
    getTokenFromServer().then(res => {
      failCount = 0;
      rcallback && rcallback(params);
      setTimeout(() => {
        refreshToken();
      }, FETCH_INTERVAL);
    }).catch(err => {
      // 获取失败一定时间后重试
      setTimeout(() => {
        refreshToken();
      }, 1000 * (20 + failCount * 10));
      failCount++;
    })
  }
  refreshToken();
};


export default {
  name: 'BizVideoUpload',
  components: {
    Uploader,
  },
  // props: {
  //   value: Object,
  // },
  data() {
    return {
      // files: this.value,
      videos: [],
      // 默认参数
      settings: {
        params: {},
        // 上传接口
        autoUpload: false,
        uploadUrl: '//upload.qiniup.com',
        // files: [],
        uploadName: 'file',
        maxFiles: 1,
        maxFileSize: 1024 * 1024 * 10,
        multiple: true,
        // beforeUpload: (file, action) => {
        //   return Promise.resolve.bind(Promise)
        // },
        onPreview: (img, index, list) => {
          // this.playPhotos(list, index)
        },
        onRemove(file) {
          return new Promise((resolve, reject) => {
            dialog().confirm('确定要删除？', {
              title: '提示',
              ok() {
                resolve();
              }
            })
          })
        },
        onError(err) {
          dialog().alert(err);
        },
        onUploaded(result) {
          // 上传成功，返回图片id给组件
          return result.key;
        },
        imageUrlGetter(img) {
          // console.log(img);
          return getThumbnail(img);
        },
      },
    }
  },
  computed: {
    /**
     * 实测组件属性使用 kebab-case 可以传给组件，但无法通过继承传递给组件，通过 camelCased 可以，故所有组件属性都优先使用 camelCased
     */
    options() {
      const vModel = this.value || {};
      return Object.assign({},
        this.settings, transformObjectCase(this.$attrs),
        transformObjectCase(this.$listeners),
        // {
        //   files: [
        //     {
        //       file: vModel.video,
        //       poster: vModel.poster,
        //     }
        //   ],
        // }
      )
    }
  },
  watch: {
    ['options.files'](val, oldVal) {
      console.log('options: ', ...arguments);
    },
  },
  mounted() {
    onVideoUploadParamsChange(data => {
      this.settings.params = {
        token: data.token,
        key: data.rid,
      }
    })
   },
  methods: {
    submit() {
      return this.$refs.uploader.submit();
    },
    onInput(value) {
      const vFile = value[0] || {};
      this.$emit('input', vFile);
      // if (!vFile) {
      //    this.$emit('input', {});
      //   return;
      // }
      // const videoCoverURL = getQiniuVideoCover(vid);
      // uploadToQiniuByImageURL(videoCoverURL).then(poster => {
      //   this.$emit('input', {
      //     ...vFile,
      //     poster: poster,
      //   });
      // }).catch(err => {
      //   console.error(err);
      //   this.$emit('input', {
      //     ...vFile,
      //     poster: null, // TODO 配置一个默认封面
      //   });
      // })
    }
  }
}
</script>