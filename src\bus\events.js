// 为了避免event名称冲突，便于管理，集中定义event名称
const BizEvents = {
  CITY_CHANGED: 'city_changed',

  PAY_SUCCESS: 'pay_success',

  AFTER_SET_PAYMENT_PASSWORD: 'after_set_payment_password',
  AFTER_ADD_CAR_SYSTEM: 'after_add_car_system',
  AFTER_CAR_MODEL_SELECTED: 'after_car_model_selected',
  AFTER_MY_CAR_SELECTED: 'after_my_car_selected',
  AFTER_PART_SELECTED: 'after_part_selected',

  // 选择审车点
  AFTER_INSPECTOR_SELECTED: 'after_inspector_selected',

  // 选择地址
  AFTER_ADDRESS_SELECTED: 'after_address_selected',

  // 选择代金券
  AFTER_TICKET_SELECTED: 'after_ticket_selected',

  // 选择充电站筛选条件
  AFTER_FILTER_SELECTED: 'after_filter_selected',

  // 更换我的爱车
  AFTER_CAR_CHANGED: 'after_car_changed',

  // 油卡办理预存支付
  FUELCARD_PRE_PAY: 'fuelcard_pre_pay',
};
// 手机号验证码登录事件
const LoginEvents = {
  SHOW_LOGIN_DIALOG: 'show_login_dialog',
  DIALOG_LOGIN_SUCCESS: 'dialog_login_success',
};

export const Events = {
  VIEW_BACK_START: 'view_back_start',
  VIEW_PUSH_START: 'view_push_start',
  SUB_ROUTE_BACK: 'sub_route_back',
  VIEW_TRANSITION_END: 'view_transition_end',
  DOUBLE_CLICK_HEAD: 'double_click_head',
  ROUTE_VIEW: 'route_view',
  ROUTE_BACK: 'route_back',
  TOAST: 'toast',
  LOADING: 'loading',
  ALERT: 'alert',
  CONFIRM: 'confirm',
  ACTIONSHEET: 'actionsheet',

  PAGE_HIDDEN: 'page_hidden', // 当前app界面进入不可见状态
  PAGE_VISIBLE: 'page_visible', // app界面变为可见

  DISABLE_BACK: 'disable_back', // 禁用back键
  ENABLE_BACK: 'enable_back', // 启用back键

  FORUMREPORT: 'forum_report',

  SCROLLER_SCROLLING: 'scroller_scrolling', // Scroller 组件滚动时

  ...BizEvents,
  ...LoginEvents,
};

export default Events;
