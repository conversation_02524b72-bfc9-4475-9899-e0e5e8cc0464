<template>
  <div v-show="show">
    <div class="weui-mask"></div>
    <div class="weui-dialog">
      <div v-if="title" class="weui-dialog__hd" name="head">
          <strong class="weui-dialog__title">{{title}}</strong>
      </div>
      <div class="weui-dialog__bd">
        <slot>{{content}}</slot>
      </div>
      <div class="weui-dialog__ft" slot="foot">
        <a href="javascript:;" class="weui-dialog__btn weui-dialog__btn_primary" @click="onClick">{{ok}}</a>
      </div>
    </div>
  </div>
</template>
<style>

</style>
<script>
  export default {
    name: 'alert',
    props: {
      counter: {
        type: Number,
        default: 1
      },
      title: {
        type: String,
        default: '',
      },
      ok: {
        type: String,
        default: '确定',
      },
      then: {
        type: Function,
      },
      content: {
        type: String,
        default: 'content'
      },
    },
    data() {
      return {
        show: false,
      };
    },
    mounted() {
      this.$on('hide', () => {
        this.show = false;
      });
    },
    methods: {
      close() {
        this.show = false;
      },
      onClick() {
        this.close();
        if (this.then) {
          this.then();
        }
      }
    },
    watch: {
      counter(val) {
        this.show = true;
      },
      show(val) {
        this.$overlay(val);
      }
    }
  };
</script>
