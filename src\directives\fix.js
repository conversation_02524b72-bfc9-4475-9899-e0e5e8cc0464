export default {
  name: 'fix',
  install(Vue, options) {
    Vue.directive('fix', {
      // 某些设备不固定高度会把内容撑开，所以统一固定高度
      inserted(el, binding, vnode) {
        const key = binding.arg;
        const config = binding.value;
        const result = getComputedStyle(el)[key];
        // console.log(binding)
        const delay = Object.assign({ delay: 0 }, config);
        setTimeout(() => {
          el.style[key] = result;
        }, delay)
      },
      componentUpdated(el, binding, vnode) {
        const key = binding.arg;
        const config = binding.value;
        const result = getComputedStyle(el)[key];
        // console.log(binding)
        const delay = Object.assign({ delay: 0 }, config);
        setTimeout(() => {
          el.style[key] = result;
        }, delay)
      },
    });
  }
};
