<template>
    <container @ready="init" @leave="onLeave">
      <x-header title="申请退款">
         <x-button  slot="left" type="back"></x-button>
      </x-header>

      <content-view class="view-refund" :status="status" @reload="reload">
        <template v-if="status == AppStatus.READY">
          <div class="refund-wrapper">
          <panel class="panel-refund">
            <div slot="title">退款原因<span>(至少选一项)</span></div>
            <div class="weui-cells weui-cells_radio">
              <label  v-for="(item, $index) in reasons" :key="$index" class="weui-cell weui-check__label" :for="'x'+$index" >
                <div class="weui-cell__bd flex-row">
                  <span>{{item.reasonContent}}</span>
                </div>
                <div class="weui-cell__ft">
                  <input type="checkbox" class="weui-check"  :value="item.id" v-model="reason.causes" name="reason" :id="'x'+$index">
                  <span class="weui-icon-checked"></span>
                </div>
              </label>
            </div>
          </panel>

          <panel class="panel-textarea"  title="退款说明(可选)">
            <div class="weui-cell">
              <div class="weui-cell__bd">
                <div class="textarea">
                  <textarea class="weui-textarea" ref="input" v-model="reason.content" placeholder="请输入退款原因" rows="3"></textarea>
                </div>
                <div class="weui-textarea-counter">
                  <span v-text="reason.content.length" :class="[reason.content.length> MAX_INPUT ?'counter-warn':'']">0</span>/{{MAX_INPUT}}
                </div>
              </div>
            </div>
          </panel>

          <div class="button-sp-area">
            <a href="javascript:;" class="weui-btn weui-btn_primary" @click="applyRefund">提交退款</a>
          </div>
        </div>
        </template>
      </content-view>
    </container>
</template>
<style lang="scss">
  /*.refund-wrapper {
    position:fixed;
    top:60px;
    width:100%;
    height:calc(50vh - 60px);
    overflow-y:scroll;
  }*/
  .counter-warn{
    color:red;
  }
  .panel-refund-content{
    display:none;
  }
  .view-refund{
    .panel-refund{
      .panel-title span{
        font-size:0.9em;
      }
    }
    .weui-icon-checked{
      border-radius: 0;
    }
    .weui-cells_radio{
      margin-top:0;
    }
    .weui-textarea {
      padding: 0 5px;
      box-sizing: border-box;
      &:focus {
      background: #f7f7f7;
      }
    }
    .panel-title{
      background:#EEEFF3;
    }
    .button-sp-area{
      margin:5px;
    }
  }
</style>
<script>
import { Header, HeaderButton, Container, ContentView, Panel } from '@/components';
import { toast, dialog, back, loading } from '@/bus';
import { AppStatus } from '@/common/enums';
import { getOrderRefundReasons, getOrderDetail, refundOrder } from '@/api';

const MAX_INPUT = 50;
export default {
  name: 'order-refund',
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    ContentView,
    Panel,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      MAX_INPUT,
      inputting: false,
      reasons: [],
      reason: {
        content: '',
        causes: [],
      }
    };
  },
  computed: {
  },
  methods: {
    init() {
      getOrderRefundReasons().then(res => {
        this.reasons = res;
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
      this.$destroy();
    },
    onResume() {
    },
    go(url) {
      this.$router.push(url);
    },
    applyRefund() {
      if (!this.reason.causes.length) {
        toast().tip('请选择退款原因');
        return;
      }

      if (this.reason.content && this.reason.content.length > MAX_INPUT) {
        toast().tip(`退款说明最多${MAX_INPUT}字！`);
        return;
      }

      const that = this;
      const orderId = this.$route.params.id;
      const orderType = this.$route.params.type;
      const params = {
        oid: orderId,
        applyContent: this.reason.content,
        reasonIds: this.reason.causes,
      }
      dialog().confirm('提交退款后您的钱款将在5个工作日内原路返回到您的账户中', {
        title: '确定要退款吗？',
        okText: '退款',
        ok() {
          loading(true, '正在提交...');
          refundOrder(orderType, params).then(res => {
            loading(false);
            back();
          }, err => {
            loading(false);
            err && dialog().alert(err, {
              title: '',
            });
          });
        }
      });
    },
    focusInput(e) {
      this.inputting = true;
      const input = this.$refs.input;
      // this.$nextTick(() => {
      // });
      input.focus();
    },
    blurInput() {
      this.inputting = false;
      const input = this.$refs.input;
      input.blur();
    },
  }
};
</script>
