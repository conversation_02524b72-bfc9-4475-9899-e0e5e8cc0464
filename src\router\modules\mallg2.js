import { handleError } from '../error-handler';

const routers = [
  {
    path: '/mallg2/shops/:category',
    name: 'FoodShopList',
    meta: {
      title: '商家列表',
    },
    component: resolve => {
      import(/* webpackChunkName: "mallg2-shops" */ '@/views/packages/mallg2/pages/FoodShopIndex.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/shops/:category?/search',
    name: 'FoodShopSearch',
    meta: {
      title: '商家搜索',
    },
    component: resolve => {
      import(/* webpackChunkName: "shops-search" */ '@/views/packages/mallg2/pages/FoodShopSearch.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/shop/:id',
    name: 'FoodShopMallg2',
    meta: {
      title: '门店详情',
    },
    component: resolve => {
      import(/* webpackChunkName: "food-shop" */ '@/views/packages/mallg2/pages/FoodShopInfo.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/goods/:id',
    name: 'MallG2Goods',
    meta: {
      title: '商品详情',
    },
    component: resolve => {
      import(/* webpackChunkName: "goods-mg2" */ '@/views/packages/mallg2/pages/goods/Goods.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/goods/:id/shops',
    name: 'MallG2GoodsShops',
    meta: {
      title: '可用门店',
    },
    component: resolve => {
      import(/* webpackChunkName: "goods-mg2-shops" */ '@/views/packages/mallg2/pages/goods/GoodsAvailableShopList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/inspector/Brief',
    name: '什么是质检员',
    component: resolve => {
      import(/* webpackChunkName: "feedbackinfo" */ '@/views/packages/mall/Goods/InspectorBrief.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/goods/:id/comments',
    name: 'MallG2GoodsComments',
    meta: {
      title: '商品评价',
    },
    component: resolve => {
      import(/* webpackChunkName: "goods-mg2-comments" */ '@/views/packages/mallg2/pages/comments/GoodsCommentList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/goods/:id/buy',
    name: 'GoodsBuy',
    meta: {
      title: '确认订单',
    },
    component: resolve => {
      import(/* webpackChunkName: "goods-buy" */ '@/views/packages/mallg2/pages/Buy.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/orders/:category?',
    props: true,
    name: 'MallG2Orders',
    meta: {
      title: '订单列表',
    },
    component: resolve => {
      import(/* webpackChunkName: "mg2-orders" */ '@/views/packages/mallg2/pages/MallOrders.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/order/:id',
    name: 'MallG2Order',
    meta: {
      title: '订单详情',
    },
    component: resolve => {
      import(/* webpackChunkName: "mg2-order" */ '@/views/packages/mallg2/pages/MallOrder.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/mallg2/order/:id/comment',
    name: 'MallG2OrderComment',
    meta: {
      title: '订单评价',
    },
    component: resolve => {
      import(/* webpackChunkName: "mg2-order-comment" */ '@/views/packages/mallg2/pages/MallOrderComment.vue').then(resolve).catch(handleError);
    },
  },
];

export default routers;
