import { OrderStatus, OrderCategory, OrderType } from '@/enums';
import { getMallOrderDetail } from './mall';
import { getFuelcardOrderDetail, getRechargeInfo } from './fuelcard';
import { getShopData } from './share';
import APIModel from '../APIModel';

const api = new APIModel({
  // 获取报名活动订单信息
  '/radio/activity/order/info': '/Radio/sign/apply/initApply',
  // 创建报名活动订单
  '/radio/activity/order/create': '/Radio/sign/apply/pay',

  // 获取洗车订单列表
  '/orders/wash': '/app/car/wash/order/list',

  // 获取订单列表
  '/orders': '/app/car/order/list',

  // 更新订单支付信息
  '/order/info/update': '/app/car/order/price/update',

  // '/orders': ':81/static/json/orders.json',
  // 获取订单详情
  '/order/detail': '/app/car/order/detail',

  // 提交订单评论
  '/order/comment/post': '/app/car/business/comment',

  // 更新订单评论详情
  '/order/comment/update': '/app/car/business/comment/update',

  // 获取订单评论详情
  '/order/comment/detail': '/app/car/get/comment',

  // 提交订单退款请求
  '/order/beauty/refund': '/app/car/wash/order/refund',
  '/order/mt/refund': '/app/car/mt/order/refund',
  '/order/inspection/refund': '/app/car/inspection/order/refund',

  // 获取订单退款理由列表
  '/order/refund/reasons': '/app/car/refund/reason/get',

  // 获取订单退款详情
  '/order/refund/detail': '/app/car/order/refund/reason/get',

  // 删除订单
  '/order/delete': '/app/car/wash/order/delete',

  // 创建美容订单
  '/order/beauty/create': '/app/car/wash/order/create',

  // 获取订单统计数据
  '/orders/count/unused': '/app/car/wash/order/unserv',

  // 获取订单流水号-云闪付小程序
  // '/orders/unionpay': '/Radio/pay/charge?orderId={orderId}&channel=union_pay_xcx',
  '/orders/unionpay': '/Radio/pay/charge',

  // 获取订单信息-针对H5支付
  '/orders/h5/pay': '/Radio/pay/charge',

  // 获取所有订单列表
  '/all/orders': '/Radio/api/order/list/for/app',
  // 获取所有订单列表
  '/type/orders': '/Radio/api/order/list',
  // 获取所有售后订单列表
  '/type/refund/orders': '/app/goods/afterSale/paging/list/search',
  // 车机-支付二维码
  '/scan/qrcode/pay': '/Radio/scan/qrcode/pay/charge',
});

/**
 * 获取用户所有订单列表
 * @param {object} params 查询参数
 */
export function getAllOrderList(params = {}) {
  const url = '/all/orders';
  const query = Object.assign({ rows: 10, page: 1 }, params);
  return api.doGet(url, query);
}

/**
 * 获取用户所有订单列表
 * @param {object} params 查询参数
 */
export function getTypeOrderList(params = {}) {
  const url = '/type/orders';
  const query = Object.assign({ rows: 10, page: 1 }, params);
  return api.doGet(url, query);
}
/**
 * 获取用户所有售后订单列表
 * @param {object} params 查询参数
 */
export function getAfterSalesOrderList(params = {}) {
  const url = '/type/refund/orders';
  const query = Object.assign({ rows: 10, page: 1 }, params);
  return api.postJSON(url, query);
}
/**
 * 获取订单详情页面数据
 * @returns {Promise.<*>}
 */
export async function getOrderDetail(oid) {
  const order = await api.doGet('/order/detail', { oid });
  const shop = await getShopData(order.bid);
  order.shop = shop;

  // 服务器未实现，暂时注掉
  if (order.orderStatus == OrderStatus.COMMENTED) {
    const comment = await getOrderComment(oid);
    order.comment = comment;
  }
  return Promise.resolve(order);
}

/**
 * 获取报名活动相关信息
 * @param {*} id
 */
export async function getActivityOrderDetail(id) {
  return api.doGet('/radio/activity/order/info', { id });
}

/**
 * 报名活动提交付款订单
 * @param {object} params
 * @param {string} params.applyId 报名活动报名id
 * @param {object} extra
 */
export async function createActivityOrder(params, extra) {
  const data = {
    ...params,
    ...extra,
  };
  return api.doPost('/radio/activity/order/create', data);
}

/**
 * 查询订单退款详情
 * @param {string} orderId 订单号
 */
export function getOrderRefundDetail(orderId) {
  return api.doGet('/order/refund/detail', { orderId });
}

/**
 * 获取订单评论详情
 * @param {string} oid 订单号
 */
export function getOrderComment(oid) {
  return api.doPost('/order/comment/detail', { oid });
}

/**
 * 支付前更新订单信息
 * @param {*} data
 */
export function updateOrderInfo(data) {
  return api.doGet('/order/info/update', data);
}

/**
 * 获取订单统计信息，无权限时静默失败
 */
export function getUnUsedOrderCount() {
  return api.doGet(
    '/orders/count/unused',
    {},
    {
      onAuthorityFail() {},
    }
  );
}

/**
 * 获取用户订单列表
 * @param {object} params 查询参数
 */
export function getOrderList(params = {}) {
  const url = '/orders';
  const query = Object.assign({ rows: 10, page: 1, type: 'all' }, params);
  return api.doGet(url, query);
}

/**
 * 获取退款原因
 */
export function getOrderRefundReasons() {
  const url = '/order/refund/reasons';
  return api.doGet(url);
}

/**
 * 删除用户订单
 * @param {string} oid 订单id
 */
export function deleteOrder(oid) {
  const url = '/order/delete';
  return api.doPost(url, { oid });
}

/**
 * 提交洗车订单
 * @param {string} id 商品id
 */
export function submitCarWashOrder(bid, serviceLabel) {
  const url = '/order/submit/carwash';
  const params = {
    bid,
    serviceLabel,
  };
  return api.doPost(url, params);
}

/**
 * 获取收银台页面所需数据
 * @param {*} oid 业务订单号
 * @param {*} category 订单类型
 */
export async function getCheckoutCounterViewData(oid, category) {
  const c = OrderCategory.getEnum(category);
  let order = null;
  if (c == OrderCategory.MALL) {
    order = await getMallOrderDetail(oid);
  } else if (c == OrderCategory.FUELCARD) {
    order = await getFuelcardOrderDetail(oid);
  } else if (c == OrderCategory.FUELCARDRECHARGE) {
    order = await getRechargeInfo(oid);
  } else {
    order = await api.doGet('/order/detail', { oid });
  }
  // const account = await api.doGet('/account/info'));
  return Promise.resolve({ order });
}

/**
 * 订单退款
 * @param {*} params
 */
export function refundOrder(orderType, data) {
  if (orderType == OrderType.WASH) {
    return api.doPost('/order/beauty/refund', data);
  }
  if (orderType == OrderType.MAINTAIN) {
    return api.doPost('/order/mt/refund', data);
  }
  if (orderType == OrderType.VEHICLE_INSPECTION) {
    return api.doPost('/order/inspection/refund', data);
  }
  return Promise.reject('此订单不支持退款！');
}

/**
 * 订单提交评论
 * @param {*} params
 */
export function postOrderComment(params) {
  return api.doPost('/order/comment/post', params);
}

/**
 * 订单更新评论
 * @param {*} params
 */
export function updateOrderComment(params) {
  return api.doPost('/order/comment/update', params);
}

/**
 * 将订单通知标记为已读
 * @param nid
 * @returns { Promise }
 */
export function readOrderNotice(nid) {
  return api.doPost('/notice/read', { nid });
}

/**
 * 支付流水号
 */
export function getTn(orderId) {
  return api.doPost('/orders/unionpay', {
    orderId: orderId,
    channel: 'union_pay_xcx',
  });
}

/**
 * 支付流水号
 */
export function getH5PayInfo(params) {
  return api.doPost('/orders/h5/pay', {
    orderId: params.orderId,
    channel: params.channel,
    returnBusinessFrontUrl: params.redirectUrl,
  });
}

/**
 * @description: 支付二维码
 * @param {string} orderId 订单id
 * @param {string} channel 支付渠道
 * @return {*}
 */
export function getPayQrcode(params) {
  return api.doPost('/scan/qrcode/pay', {
    orderId: params.orderId,
    channel: params.channel,
  });
}
