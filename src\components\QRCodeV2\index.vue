<template>
  <vue-qr v-bind="$attrs" v-on="$listeners"></vue-qr>
</template>

<script>
import VueQr from 'vue-qr';

export default {
  name: 'QRCodeV2',
  components: { VueQr },
  props: {
    value: String,
    size: {
      type: Number,
      default: 80,
    },
    level: {
      type: String,
      default: 'L',
    },
  },
  mounted() {},
  computed: {
    bgColor() {
      return this.$attrs.bgColor || '#FFFFFF';
    },
    fgColor() {
      return this.$attrs.fgColor || '#000000';
    },
  },
  watch: {},
  methods: {},
};
</script>
