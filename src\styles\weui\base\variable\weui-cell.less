@weuiCellBg:#FFFFFF;
@weuiCellBorderColor:#D9D9D9;
@weuiCellGapV:10px;
@weuiCellGapH:15px;
@weuiCellInnerGapH:.35em;
@weuiCellHeight: 44px;
@weuiCellFontSize:15px;
@weuiCellTipsFontSize:14px;
@weuiCellLabelWidth:105px;

@weuiCellLineHeight: unit(((@weuiCellHeight - 2 * @weuiCellGapV) / @weuiCellFontSize)); // 高度为44px，减去上下padding的行高
@weuiCellsMarginTop:unit((20 / @weuiCellFontSize), em);

// weui switch
@weuiSwitchHeight: 32px;

// weui uploader
@weuiUploaderBorderColor:#D9D9D9;
@weuiUploaderActiveBorderColor:#999999;
@weuiUploaderFileSpacing: 9px;
@weuiUploaderSize: 79px;
@weuiUploaderBorderWidth: 1px;
