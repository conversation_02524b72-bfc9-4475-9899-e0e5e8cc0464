<template>
  <div
    class="tab-container"
    :class="{
      scrollable: scrollable,
      'un-scrollable': !scrollable,
      'tab-sticky': sticky
    }"
  >
    <div class="tab-items" ref="items">
      <div class="tab-items-inner">
        <div
          v-for="(item, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ 'tab-item-active': value === item.value }"
          @click="setActive(item, index)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <!-- <div class="tab-item-slider" :style="sliderStyle"></div> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
$active-color: #398eff;
$tab-item-padding: 8px;

$tab-active-bar-height: 2px;

.tab-container {
  width: 100%;
}
.tab-sticky {
  position: sticky;
  top: -1px; /* px */
  z-index: 10;
}
.un-scrollable {
  .tab-items-inner {
    display: flex;
    flex-direction: row;
  }
  .tab-item {
    flex: 1;
  }
}
.tab-items {
  // display:flex;
  // flex-direction: row;
  position: relative;
  border-bottom: 1px solid #e9e9e9;
}
.scrollable {
  .tab-items {
    overflow-x: scroll;
  }
  .tab-items-inner {
    width: 1000px;
  }
}
.tab-item {
  // flex:1;
  text-align: center;
  float: left;
  position: relative;
  padding: $tab-item-padding;
  overflow: hidden;
  white-space: nowrap;
  &::after {
    content: '';
    height: $tab-active-bar-height; /* px */
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    background: transparent;
    width: 70px;
  }
}

.tab-item-active {
  color: $active-color;
  &::after {
    bottom: 0;
    background: $active-color;
  }
}

.tab-item-slider {
  content: '';
  height: $tab-active-bar-height;
  background: $active-color;
  flex: 1;
  position: absolute;
  width: 20%;
  left: 0;
  bottom: 0;
  transform: translateX(0);
  transition: transform 250ms;
}
</style>

<script>
import { animate } from '@/lib/animation';
export default {
  name: 'tabs',
  props: {
    tabs: Array,
    value: {
      type: [Number, String],
      default() {
        const defaultTab = this.tabs[0];
        return defaultTab && defaultTab.value;
      }
    },
    scrollable: {
      type: Boolean,
      default: false
    },
    sticky: {
      type: Boolean,
      default: false
    },
    itemMinWidth: 0
  },
  data() {
    let initIndex = 0;
    this.tabs.some((item, index) => {
      if (item.value == this.value) {
        initIndex = index;
        return true;
      }
    });
    return {
      currentIndex: initIndex
    };
  },
  computed: {
    sliderStyle() {
      return {
        width: `${(1 / this.tabs.length) * 100}%`,
        transform: `translateX(${this.currentIndex * 100}%)`,
        '-webkit-transform': `translateX(${this.currentIndex * 100}%)`
      };
    },
    values() {
      return this.tabs.map(item => item.value);
    }
  },
  methods: {
    setActive(item, index) {
      this.$emit('input', item.value);
      this.$emit('click', index);
    },
    onValueChange(value) {
      // this.$emit("input", item.value, index);
      const index = this.values.indexOf(value);
      if (index > -1) {
        this.$emit('change', { value, index });
        // this.$emit("change", item.value, index);
        this.currentIndex = index;
        this.$nextTick(this.scrollItemIntoView);
      }
    },
    scrollItemIntoView() {
      if (!this.scrollable) return;
      const $items = this.$refs.items;
      const $active = $items.querySelector('.tab-item-active');
      if ($active) {
        const rect = $active.getBoundingClientRect();
        const boundary = {
          left: 50,
          right: window.innerWidth - 50
        };
        const step = window.innerWidth / 2;
        // console.log(rect);
        if (rect.left < boundary.left) {
          animate(
            $items.scrollLeft,
            $items.scrollLeft - Math.abs(rect.left) - step,
            300,
            'Quart.easeOut',
            (value, end) => {
              $items.scrollLeft = value;
            }
          );
        } else if (rect.right > boundary.right) {
          animate(
            $items.scrollLeft,
            $items.scrollLeft + (rect.right - window.innerWidth) + step,
            300,
            'Quart.easeOut',
            (value, end) => {
              $items.scrollLeft = value;
            }
          );
        }
      }
    }
  },
  watch: {
    value(val, oldVal) {
      if (val != oldVal) {
        this.onValueChange(val);
        // console.info('active change: ', val, oldVal);
        // this.$emit("change", val);
        // this.$nextTick(this.scrollItemIntoView)
      }
    }
  }
};
</script>
