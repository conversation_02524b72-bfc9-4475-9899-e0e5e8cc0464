/**
 * 异步加载一个图片，返回图片地址和宽高信息
 * @param {*} src 
 */
export function loadImage(src) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    img.crossOrigin = "anonymous";
    img.onload = function onload() {
      resolve({
        img,
        src,
        width: img.width,
        height: img.height,
      });
    };
    img.onerror = function onerror(e) {
      reject(src);
    };
    if (img.width > 0) {
      resolve({
        img,
        src,
        width: img.width,
        height: img.height,
      });
    }
  });
}