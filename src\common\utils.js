function detectType(type, o) {
  return new RegExp(type).test(Object.prototype.toString.apply(o))
}

export function isPlainObject(o) {
  return typeof o == 'object' && Object.getPrototypeOf(o) === Object.prototype;
}

export function isString(o) {
  return detectType('String', o)
}

export function isArray(o) {
  return detectType('Array', o)
}

export function isFunction(o) {
  return detectType('Function', o)
}

export function isNumber(o) {
  return /^\d+(\.\d+)?$/.test(o)
}

export function getDuration(t) {
  const total = t / 1000;
  const [DAY, HOUR, MINUTE] = [60 * 60 * 24, 60 * 60, 60];

  let remain = 0;

  const days = parseInt(total / DAY, 10);
  remain = total - days * DAY;

  const hours = parseInt(remain / HOUR, 10);
  remain = remain - hours * HOUR;

  const minutes = parseInt(remain / MINUTE);
  remain = remain - minutes * MINUTE;

  const seconds = parseInt(remain, 10);

  return {
    hours: () => hours,
    minutes: () => minutes,
    seconds: () => seconds,
    days: () => days,
  }
}

export function formatDate(t, fmt = 'YYYY-MM-DD') {
  const date = new Date(Number(t));
  let o = {
    'M+': date.getMonth() + 1,
    '[Dd]+': date.getDate(),
    '[hH]+': date.getHours(),
    '[m]+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    'S': date.getMilliseconds()
  };
  if (/([Yy]+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
    }
  }
  return fmt
}

export function formatDistance(distance) {
  const MIN = 100;
  const m = parseInt(distance);
  if (m <= MIN) return `<${MIN}m`;
  if (m < 1000) return `${m}m`;
  if (m > 9999 * 1000) return '>9999km';
  return (m / 1000).toFixed(m > 99 * 1000 ? 0 : 1) + 'km';
}

export function formatShopHours(hour) {
  const LENGTH = 4;
  let result = String(hour);
  const padLength = LENGTH - result.length;
  if (padLength > 0) {
    result = Array.from(Array(padLength)).map(item => 0).join('') + result;
  }
  return result.replace(/(\d{2})$/, ':$1')
}

export function formatPrice(value) {
  if (!value) return 0;
  return Number(value.toFixed(2));
}

export function parseAppointmentTime(value) {
  const regex = /^(\d{4})(\d{2})(\d{2})(\d)$/;
  const result = regex.exec(value);
  if (result && result.length == 5) {
    let [all, year, month, day, meridian] = result;
    return `${year}-${month}-${day} ${meridian == 1 ? '下午' : '上午'}`;
  }
  return value;
}

export function fixRichHtmlImageSize(str, padding = 5) {
  const pattern = /data-size="(\d+):(\d+)"/g;
  const containerWidth = window.innerWidth - padding * 2;
  if (str) {
    str = str.replace(pattern, (term, w, h, all) => {
      const width = Number(w);
      const height = Number(h);
      if (width && height) {
        if (width < containerWidth) {
          return `width=${width} height=${height}`;
        }
        return `width=${containerWidth} height=${(containerWidth * height) / width}`;
      }
      return term;
    });
  }
  return filterRichHtml(str);
}

export function filterRichHtml(str) {
  const pattern = /href/;
  if (str) {
    return str.replace(/href/g, '').replace(/on\w+=/g, '');
  }
  return str;
}

/**
 * 页面加载速度统计
 */
export function tracePageSpeed() {
  // 页面加载速度统计
  // Gets the number of milliseconds since page load
  // (and rounds the result since the value must be an integer).
  const timeSincePageLoad = window.performance ? Math.round(window.performance.now()) : 0;
  setTimeout(() => {
    // Feature detects Navigation Timing API support.
    if (timeSincePageLoad && window.ga) {
      // Sends the timing hit to Google Analytics.
      console.log('ga send...');
      window.ga('send', 'timing', 'appReady', 'load', timeSincePageLoad);
    }
  }, 0)
}

/**
 * 劫持统计
 */
function gaTraceHijack() {
  const whiteList = [
    /www\.google-analytics\.com/i
  ]
  const ignorePattern = new RegExp(`${location.hostname}`, 'i');
  Array.from(document.querySelectorAll('script')).forEach(script => {
    const src = script.src;
    if (src && !ignorePattern.test(src)) {
      const isSafe = whiteList.some(item => {
        return item.test(src);
      });
      console.log('src:', src, isSafe);
      if (window.ga && !isSafe) {
        const name = src.replace(/\?.*/, '');
        console.log('ga safe');
        window.ga('send', 'event', 'Safe', `Hijack_${location.protocol}`, src);
      }
    }
  })
}

export function traceHijack() {
  setTimeout(() => {
    gaTraceHijack();
  }, 0)
}

// 获取vue router绝对路径
export function getAbsolutePath(path, options = {
  search: ''
}) {
  const location = window.location;
  const search = options.search ? options.search : location.search;
  return `${location.origin}${location.pathname}${search}#${path}`;
}

export function zeroFill(number, fix = 2) {
  const chars = String(number).split('');
  while (chars.length < fix) {
    chars.unshift(0);
  }
  return chars.join('');
}

console.error('此模块已弃用，请从 `@/utils` 引入所需模块或方法');
