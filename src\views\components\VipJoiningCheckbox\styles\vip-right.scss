$vip-color: #cc963c;

.rights{
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  line-height: 1;
  li{
    width: 25%;
    box-sizing: border-box;
    // overflow: hidden;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    &:nth-child(n+5){
      margin-top: 20px;
    }
    .right-badge{
      position: absolute;
      left: 50%;
      top: -8px;
      z-index: 1;
      padding: 3px 4px;
      background: linear-gradient(70deg, #FE270A, #FF4A02);
      border-radius: 8px 8px 8px 0px;
      font-size: 10px;
      color: #FFFFFF;
      word-break: keep-all;
    }
    .right-icon{
      width: 40px;
      height: 40px;
      border-radius: 50%;
      position: relative;
      margin-bottom: 12px;
    }
    p{
      font-size: 12px;
      color: #FFFFFF;
      margin-bottom: 5px;
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    span{
      font-size: 10px;
      color: #676786;
      display: block;
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}
.mall-section, .car-section ::v-deep {
  .coupons{
    display: flex;
    align-items: stretch;
    // justify-content: space-between;
    flex-wrap: wrap;
    line-height: 1;
    width: 100%;
    li{
      position: relative;
      // flex: 1;
      width: 32%;
      // max-width: 33.3%;
      // min-width: 33.0%;
      min-height: 114px;
      padding: 10px 0;
      box-sizing: border-box;
      overflow: hidden;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      background: url(../images/coupon-bg.jpg) no-repeat left top;
      background-size: 100% 100%;
      box-sizing: border-box;
      border-radius: 10px;
      overflow: hidden;
      &::before{
        content: '';
        width: 12px;
        height: 12px;
        background: #363647;
        border-radius: 6px;
        position: absolute;
        left: -6px;
        top: 50%;
        transform: translateY(-50%);
      }
      &::after{
        content: '';
        width: 12px;
        height: 12px;
        background: #363647;
        border-radius: 6px;
        position: absolute;
        right: -6px;
        top: 50%;
        transform: translateY(-50%);
      }
      &:nth-child(n+4){
        margin-top: 10px;
      }
      &:nth-child(3n-1){
        margin-left: 2%;
        margin-right: 2%;
      }
      .coupon-price{
        font-size: 12px;
        font-weight: bold;
        color: #FD4925;
        margin-bottom: 8px;
        i{
          font-style: normal;
        }
        span{
          font-size: 24px;
        }
      }
      .coupon-name{
        padding: 0 10px 10px;
        flex: 1;
        line-height: 1.2;
        width: 100%;
        box-sizing: border-box;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        span{
          display: inline-block;
          font-size: 10px;
          color: #AFB1B8;
          margin-bottom: 6px;
        }
        p{
          max-width: 100%;
          font-size: 12px;
          color: #242435;
          margin: 0 auto;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
      .renew{
        padding: 0 10px;
        font-size: 10px;
      }
    }
  }
  .right-img{
    display: block;
    width: 100%;
    // height: 60px;
    border-radius: 10px;
    overflow: hidden;
  }
}
