import { handleError } from '../error-handler';

export default [
/*  {
    path: '/account/pay',
    name: '账户支付',
    component: resolve => {
      import('@/views/packages/account/AccountPay.vue').then(e => {
        resolve(e);
      }).catch(e => {
        handleError(e);
      })
    },
    meta: { requiresAuth: true }
  },
  {
    path: '/account/paypass',
    name: '支付密码',
    component: resolve => {
      import('@/views/packages/account/AccountPayPassword.vue').then(e => {
        resolve(e);
      }).catch(e => {
        handleError(e);
      })
    },
    meta: { requiresAuth: true }
  }, */
  {
    path: '/account/home',
    name: '账户首页',
    redirect: '/account/redpocket',
    component: resolve => {
      import(/* webpackChunkName: "uaccount" */ '@/views/packages/account/Account.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/redpocket',
    name: '红包余额',
    component: resolve => {
      import(/* webpackChunkName: "account-redpocket" */ '@/views/packages/account/AccountRedPocket.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/redpocket/qa',
    name: 'AccountRedPocketQA',
    component: resolve => {
      import(/* webpackChunkName: "account-redpocket-qa" */ '@/views/packages/account/AccountRedPocketQA.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/orders/:type?',
    name: 'AccountOrderList',
    props: true,
    component: resolve => {
      import(/* webpackChunkName: "account-orders" */ '@/views/packages/account/OrdersNew.vue').then(resolve).catch(handleError);
      // import(/* webpackChunkName: "account-orders" */ '@/views/packages/account/Orders.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/orders_new/:type?',
    name: 'AccountOrderList-new',
    props: true,
    component: resolve => {
      import(/* webpackChunkName: "account-orders" */ '@/views/packages/account/OrdersNew.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/tickets',
    name: 'AccountTickets',
    meta: {
      title: '我的卡券',
    },
    component: resolve => {
      import(/* webpackChunkName: "account-tickets" */ '@/views/packages/ticket/TicketList.vue').then(resolve).catch(handleError);
    },
  },
  // {
  //   path: '/account/cards',
  //   name: 'AccountCards',
  //   meta: {
  //     title: '我的卡包',
  //   },
  //   component: resolve => {
  //     import(/* webpackChunkName: "account-cards" */ '@/views/packages/ticket/Cards.vue').then(resolve).catch(handleError);
  //   },
  // },
  {
    path: '/account/car',
    name: '爱车档案',
    component: resolve => {
      import(/* webpackChunkName: "mycar" */ '@/views/packages/account/AccountMyCar.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/car/:id/edit',
    name: '爱车编辑',
    component: resolve => {
      import(/* webpackChunkName: "car-edit" */ '@/views/packages/account/AccountMyCarEdit.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/car/:model/add',
    name: '爱车添加',
    component: resolve => {
      import(/* webpackChunkName: "car-edit" */ '@/views/packages/account/AccountMyCarEdit.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/cars/:id?',
    name: '爱车',
    component: resolve => {
      import(/* webpackChunkName: "mycars" */ '@/views/packages/account/AccountMyCars.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/car/brands',
    name: '汽车品牌',
    component: resolve => {
      import(/* webpackChunkName: "car-brand-select" */ '@/views/packages/account/CarSelect_Brand.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/car/brand/:brand/series',
    name: '选择车系',
    component: resolve => {
      import(/* webpackChunkName: "car-series-select" */ '@/views/packages/account/CarSelect_Series.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/car/series/:series/models/:length?',
    name: '选择车型',
    component: resolve => {
      import(/* webpackChunkName: "car-model-select" */ '@/views/packages/account/CarSelect_Model.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/car/mt',
    name: '汽车保养',
    component: resolve => {
      import(/* webpackChunkName: "car-mt" */ '@/views/packages/maintain/CarMT.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/charge',
    name: '账户充值',
    component: resolve => {
      import(/* webpackChunkName: "uaccount-charge" */ '@/views/packages/account/AccountCharge.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/charge/licence',
    name: '充值协议',
    component: resolve => {
      import(/* webpackChunkName: "charge-licence" */ '@/views/packages/account/AccountChargeLicence.vue').then(resolve).catch(handleError);
    },
  },
]
