<template>
    <!-- 支付完成 营销弹窗 -->
    <div class="mask" v-if="showMarketing">
      <div class="mask-content-m">
        <img :src="image" @click="pushWebview" />
        <span class="icon_jglh icon-a-guanbiquxiao" @click="closeMarketing"></span>
      </div>
    </div>
</template>
<script>
import { mixinAuthRouter } from '@/mixins';
import { getImageURL } from '@/common/image';
import { getFeedBackSetting } from '@/api/modules/marketing';
export default {
  name: 'PayResultPopup',
  mixins: [mixinAuthRouter],
  props: {
    category: {
      type: Number,
      default: 3, // 业务类型[0:洗车,1:保养,2:审车,3:商城团购,4:会员,5:商城会员,6:车主会员 ]
    },
    amount: {
      type: Number,
      default: 0, // 实付金额 ，为0时不显示
    },
  },
  data () {
    return {
      showMarketing: false,
      setting: {},
      pageUrl: ''
    }
  },
  computed: {
    image() {
      return getImageURL(this.setting.image);
    },
  },
  mounted () {
    this.init()
  },
  methods: {
    init() {
      if ([0, 1, 2, 3, 4, 5, 6].indexOf(+this.category) > -1 && this.category != null && this.amount > 0) {
      // if ([0, 1, 2, 3, 4].indexOf(+this.category) > -1 && this.category != null) {
        getFeedBackSetting(this.category)
          .then((res) => {
            this.setting = res
            // 状态值 1：启用 0：禁用
            if (res.status == 1) {
              setTimeout(() => {
                this.showMarketing = true
              }, 1000);
            }
          })
          .catch((err) => {
            console.error(err)
          })
      }
    },
    pushWebview(url) {
      this.closeMarketing()
      this.pageUrl = this.setting.url;
      try {
        const webviewURL = new URL(this.setting.url);
        // debugger
        !webviewURL.searchParams.has('share') && webviewURL.searchParams.append('share', this.setting.canShare ? 1 : 0);
        this.pageUrl = webviewURL.href;
      } catch (e) {
        console.error(e)
      }
      this.$_router_pageTo(this.pageUrl, {
        // replace: true,
        shareButton: this.setting.canShare,
        titleBar: true,
        progressBar: true,
        autoUpdateTitle: true,
        requireSignIn: true
      });
    },
    closeMarketing() {
      this.showMarketing = false
    },
  }
}
</script>

<style lang="scss" scoped>
.mask{
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  position: fixed;
  left: 0;
  top: 0;
  background: rgba(0,0,0,.5);
  z-index: 888;
  .mask-content-m{
    width: 260px;
    box-sizing: border-box;
    background: transparent;
    text-align: center;
    border-radius: 5px;
    overflow: hidden;
    img{
      display: block;
      margin: 0 auto;
      width: 100%;
      height: auto;
      border-radius: 5px;
    }
    span{
      margin-top: 15px;
      font-size: 34px;
      font-weight: normal;
      color: #ffffff;
    }
  }
}
</style>
