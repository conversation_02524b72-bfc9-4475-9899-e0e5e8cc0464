/**
 * 充电站工具函数测试
 */

import {
  formatDistance,
  getStatusText,
  getTagType,
  getServiceIcon,
  calculateDistance,
  isValidCoordinate,
  formatPrice,
  formatPileCount,
  getStationAvailability,
} from '../utils';

describe('充电站工具函数测试', () => {
  describe('formatDistance', () => {
    test('应该正确格式化小于1000米的距离', () => {
      expect(formatDistance(500)).toBe('500m');
      expect(formatDistance(999)).toBe('999m');
    });

    test('应该正确格式化1000米到10000米的距离', () => {
      expect(formatDistance(1500)).toBe('1.5km');
      expect(formatDistance(2300)).toBe('2.3km');
    });

    test('应该正确格式化大于10000米的距离', () => {
      expect(formatDistance(15000)).toBe('15km');
      expect(formatDistance(25600)).toBe('26km');
    });
  });

  describe('getStatusText', () => {
    test('应该返回正确的状态文本', () => {
      expect(getStatusText('open')).toBe('营业中');
      expect(getStatusText('closed')).toBe('停业中');
      expect(getStatusText('maintenance')).toBe('维护中');
      expect(getStatusText('unknown')).toBe('未知');
    });
  });

  describe('getTagType', () => {
    test('应该返回正确的标签类型', () => {
      expect(getTagType('24小时')).toBe('success');
      expect(getTagType('快充')).toBe('primary');
      expect(getTagType('慢充')).toBe('warning');
      expect(getTagType('超充')).toBe('danger');
      expect(getTagType('未知标签')).toBe('default');
    });
  });

  describe('getServiceIcon', () => {
    test('应该返回正确的服务图标', () => {
      expect(getServiceIcon('休息室')).toBe('home-o');
      expect(getServiceIcon('便利店')).toBe('shop-o');
      expect(getServiceIcon('洗车')).toBe('brush-o');
      expect(getServiceIcon('未知服务')).toBe('star-o');
    });
  });

  describe('calculateDistance', () => {
    test('应该正确计算两点间距离', () => {
      // 北京天安门到故宫的距离约1000米
      const distance = calculateDistance(39.9042, 116.4074, 39.9163, 116.3972);
      expect(distance).toBeGreaterThan(800);
      expect(distance).toBeLessThan(1200);
    });

    test('相同坐标的距离应该为0', () => {
      const distance = calculateDistance(39.9042, 116.4074, 39.9042, 116.4074);
      expect(distance).toBe(0);
    });
  });

  describe('isValidCoordinate', () => {
    test('应该验证有效的坐标', () => {
      expect(isValidCoordinate(39.9042, 116.4074)).toBe(true);
      expect(isValidCoordinate(0, 0)).toBe(true);
      expect(isValidCoordinate(-90, -180)).toBe(true);
      expect(isValidCoordinate(90, 180)).toBe(true);
    });

    test('应该拒绝无效的坐标', () => {
      expect(isValidCoordinate(91, 116.4074)).toBe(false);
      expect(isValidCoordinate(39.9042, 181)).toBe(false);
      expect(isValidCoordinate('invalid', 116.4074)).toBe(false);
      expect(isValidCoordinate(39.9042, 'invalid')).toBe(false);
    });
  });

  describe('formatPrice', () => {
    test('应该正确格式化价格', () => {
      expect(formatPrice(1.5)).toBe('1.5元/度');
      expect(formatPrice(2.0)).toBe('2.0元/度');
      expect(formatPrice(1.23)).toBe('1.2元/度');
    });

    test('应该处理无效价格', () => {
      expect(formatPrice(null)).toBe('价格待定');
      expect(formatPrice(undefined)).toBe('价格待定');
      expect(formatPrice('invalid')).toBe('价格待定');
    });
  });

  describe('formatPileCount', () => {
    test('应该正确格式化充电桩数量', () => {
      expect(formatPileCount(5, 8)).toBe('5/8个');
      expect(formatPileCount(0, 10)).toBe('0/10个');
      expect(formatPileCount(10, 10)).toBe('10/10个');
    });
  });

  describe('getStationAvailability', () => {
    test('应该正确计算充电站可用性', () => {
      const station1 = {
        fastCharging: { total: 8, available: 6 },
        slowCharging: { total: 4, available: 2 },
      };
      const availability1 = getStationAvailability(station1);
      expect(availability1.status).toBe('available');
      expect(availability1.text).toBe('充足');

      const station2 = {
        fastCharging: { total: 10, available: 2 },
        slowCharging: { total: 5, available: 1 },
      };
      const availability2 = getStationAvailability(station2);
      expect(availability2.status).toBe('limited');
      expect(availability2.text).toBe('紧张');

      const station3 = {
        fastCharging: { total: 10, available: 0 },
        slowCharging: { total: 5, available: 0 },
      };
      const availability3 = getStationAvailability(station3);
      expect(availability3.status).toBe('unavailable');
      expect(availability3.text).toBe('暂无可用');
    });
  });
});

describe('存储工具测试', () => {
  // 模拟localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  global.localStorage = localStorageMock;

  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  test('应该正确设置和获取存储', () => {
    const { storage } = require('../utils');
    
    // 测试设置
    storage.set('test_key', { value: 'test' });
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'test_key',
      JSON.stringify({ value: 'test' })
    );

    // 测试获取
    localStorageMock.getItem.mockReturnValue(JSON.stringify({ value: 'test' }));
    const result = storage.get('test_key');
    expect(result).toEqual({ value: 'test' });

    // 测试删除
    storage.remove('test_key');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('test_key');
  });

  test('应该处理存储错误', () => {
    const { storage } = require('../utils');
    
    // 模拟存储错误
    localStorageMock.setItem.mockImplementation(() => {
      throw new Error('Storage error');
    });
    
    // 应该不抛出错误
    expect(() => storage.set('test_key', 'test_value')).not.toThrow();
    
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('Storage error');
    });
    
    // 应该返回默认值
    const result = storage.get('test_key', 'default');
    expect(result).toBe('default');
  });
});
