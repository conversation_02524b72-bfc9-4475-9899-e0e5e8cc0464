import Header from './Header';
import HeaderButton from './HeaderButton';
import Container from './Container';
import View from './View';
import Picture from './Picture/index.vue';
import BizImage from './Picture/BizImage.vue';
import Panel from './Panel';
import Tabs from './Tabs';
import Loader from './Loader';
import Tip from './Tip';
import TabPanel from './TabPanel';
import ContentView from './ContentView';
import Rater from './Rater';
import Toast from './Toast';
import Alert from './Dialog/Alert';
import Confirm from './Dialog/Confirm';
import Payment from './Payment';
import Counter from './Counter';
import PageLoader from './Page/PageLoader.vue';
import Loading from './Loading/Loading.vue';
import ListLoader from './ListLoader';
import ListPlaceholder from './ListPlaceholder';
import CarPlateNumberInput from './CarPlateNumberInput';


// 为了减小打包后的公共js大小，以下两个组件不打包到公共js中
// import Swiper from './Swiper/index';
// import QRCode from './QRCode';

export {
  Header,
  HeaderButton,
  Container,
  View,
  PageLoader,
  Loader,
  TabPanel,
  Tip,
  Rater,
  Panel,
  Tabs,
  ContentView,
  // Picture,
  BizImage,
  Toast,
  Alert,
  Confirm,
  Payment,
  Counter,
  Loading,
  ListLoader,
  ListPlaceholder,
  CarPlateNumberInput,
}
