import { mapActions, mapGetters } from 'vuex';
import { isInWeixin, isInJglh } from '@/common/env';
import { dialog } from '@/bus/index';
import { getAppURL } from '@/utils';

/**
 * $_share_info 数据对象结构
 * title, desc, imgUrl
 * link,
 * path: 若有link，会优先使用link，若有此属性，会转成link
 */

export const mixinShare = {
  computed: {
    ...mapGetters({
      $_shareInfo: 'shareInfo', // 获取分享信息
    }),
  },
  mounted() {
    // 自动配置分享信息
    // data中 以 _ 或 $ 开头的属性 不会 被 Vue 实例代理，需要通过$data.访问
    // https://cn.vuejs.org/v2/api/index.html#data
    if (this.$data.$_share_info) {
      const info = this.$data.$_share_info;
      const shareInfo = {};
      if (info.link) {
        shareInfo.link = info.link;
      } else if (info.path) {
        const link = getAppURL(info.path, {
          search: isInWeixin
            ? '?utm_source=WEIXIN_PUB&utm_medium=share'
            : '?jglh',
        });
        shareInfo.link = link;
      }
      if (info.title) shareInfo.title = info.title;
      if (info.desc) shareInfo.desc = info.desc;
      if (info.imgUrl) shareInfo.imgUrl = info.imgUrl;
      this.$_share_update(shareInfo);
    }
  },
  watch: {
    $_share_info(val) {
      console.log('$_share_info change', val);
    },
  },
  methods: {
    ...mapActions(['setShareInfo']),
    // 根据vue官方风格指导，将函数名修改为$_share_update
    ...mapActions({
      $_share_update: 'setShareInfo',
      $_share_showShareView: 'share',
    }),
    // 调起分享界面
    $_share(options) {
      if (isInWeixin) {
        dialog().alert('请点击微信右上角的按钮分享页面', {
          title: '提示',
        });
      } else {
        this.$_share_showShareView(options);
      }
    },
  },
};
