import { handleError } from '@/router/error-handler';

export default [
  {
    path: '/blindDate',
    name: 'BlindDate',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/apply',
    name: 'BlindDateApplyView',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/apply.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/update-id-card',
    name: 'UpdateIdCardView',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/update-id-card.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/user/:id',
    name: 'BlindDateUserDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/user-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/agreement',
    name: 'BlindDateAgreement',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/agreement.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/payment',
    name: 'BlindDatePayment',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/payment-reminder.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/heartbeat',
    name: 'BlindDateHeartbeat',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/heartbeat.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/likeMe',
    name: 'LikeMe',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/like-me.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/couple/:id',
    name: 'BlindDateCouplesDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/couples-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/cp',
    name: 'CouplesMine',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/couples-mine.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/blindDate/cp/:id',
    name: 'CouplesMineDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "blind-date" */ '@/views/blind-date/couples-mine-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
