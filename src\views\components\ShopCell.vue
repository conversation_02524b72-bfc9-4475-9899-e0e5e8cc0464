<template>
  <div class="weui-cell shop-item" @click="click">
    <h4 class="shop-title">
      <span>{{shop.title}}</span>
      <span class="shop-distance">{{formatDistance(shop.distance)}}</span>
    </h4>
    <div class="shop-body">
      <p class="shop-address">{{shop.address}}</p>
      <span class="shop-tip">离我最近</span>
    </div>
  </div>
</template>
<style lang="scss" scoped>
  $highlight-color:#F29E5C;
  .shop-title{
    font-weight: 400;
  }
  .shop-item {
    display:block;
  }
  .shop-body{
    margin-top:3px;
    font-size:0.9em;
    position:relative;
  }
  .shop-address {
    color: gray;
    min-width: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width:80%;
  }
  .shop-distance {
    float: right;
  }
  .shop-tip {
    color:$highlight-color;
    text-align: right;
    position: absolute;
    right: 0;
    top: 0;
  }
</style>
<script>
import { formatDistance } from '@/utils';

export default {
  name: 'shop-cell',
  props: {
    shop: {
      type: Object,
    },
  },
  computed: {
  },
  methods: {
    formatDistance(...args) {
      return formatDistance(...args);
    },
    click() {
      this.$emit('click');
    },
  },
  data() {
    return {};
  }
};
</script>
