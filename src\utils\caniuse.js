
/**
 * @description 判断当前浏览器环境是否支持 WebP 格式
 * @param {string} [feature='lossless'] WebP 功能
 * @return {Promise<boolean>} 描述是否支持 WebP 格式的 Promise 对象
 */

export function isWebPSupported (feature = 'lossless') {
  // 参考网站：https://developers.google.com/speed/webp/faq?hl=zh-cn#how_can_i_detect_browser_support_for_webp
  const testImages = {
    lossy: 'UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA', // 有损压缩
    lossless: 'UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==', // 无损压缩
    alpha: 'UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==',
    animation: 'UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA',
  };

  return new Promise(resolve => {
    const _image = new Image();
    _image.onload = () => {
      const result = _image.width > 0 && _image.height > 0;
      resolve(result);
    };
    _image.onerror = () => {
      resolve(false);
    };
    _image.src = `data:image/webp;base64,${testImages[feature]}`;
  });
};

/**
 * @description: 检测浏览器是否支持 Grid 布局
 * @return {boolean} 描述是否支持布尔值
 */
function isGridSupported() {
  return 'grid' in document.body.style || 'msGrid' in document.body.style;
}
