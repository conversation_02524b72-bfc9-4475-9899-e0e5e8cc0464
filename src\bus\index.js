import { isPlainObject } from '@/utils';
import Events from './events';
import bus from './bus';
export * from './biz';

export function toast(position = 'default') {
  let counter = Date.now();
  return {
    success(text) {
      if (typeof text !== 'string') {
        console.warn('参数必须为string类型');
      }
      bus.$emit(Events.TOAST, { type: 'success', text: String(text), counter });
    },
    tip(text) {
      if (typeof text !== 'string') {
        console.warn('参数必须为string类型');
      }
      bus.$emit(Events.TOAST, { type: 'text', position, text: String(text), counter });
    },
  }
}

// 显示论坛举报弹窗
export function showForumDialog(data) {
  bus.$emit(Events.FORUMREPORT, data);
}

// 设置返回键状态
export function setBackButton(flag) {
  bus.$emit(flag ? Events.ENABLE_BACK : Events.DISABLE_BACK);
}

export function dialog(name) {
  let counter = Date.now();
  return {
    alert(content, options = {}) {
      if (!String(content)) return;
      if (typeof content !== 'string') {
        console.warn('content参数必须为string类型');
      }
      if (options.title && typeof options.title !== 'string') {
        console.warn('options.title参数必须为string类型');
      }
      setBackButton(false);
      bus.$emit(Events.ALERT, {
        content: String(content),
        counter,
        ok: options.ok || '确定',
        title: options.title || name,
        then() {
          options.then && options.then();
          setBackButton(true);
        }
      });
    },
    confirm(content, options = {}) {
      setBackButton(false);
      bus.$emit(Events.CONFIRM, {
        counter,
        reversePrimary: !!options.reversePrimary,
        content: String(content),
        title: options.title || name,
        cancel() {
          console.log('cancel button clicked...')
          options.cancel && options.cancel();
          setBackButton(true);
        },
        cancelText: options.cancelText,
        ok() {
          options.ok && options.ok();
          setBackButton(true);
        },
        okText: options.okText,
      });
    },
  }
}

export function actionSheet(options) {
  setBackButton(false);
  bus.$emit(Events.ACTIONSHEET, {
    // counter,
    title: options.title,
    menus: options.menus,
    onCancel() {
      console.log('cancel button clicked...')
      options.onCancel && options.onCancel(...arguments);
      setBackButton(true);
    },
    onClick() {
      options.onClick && options.onClick(...arguments);
      setBackButton(true);
    },
  });
}

export function loading(show, content) {
  bus.$emit(Events.LOADING, {
    show, content
  });
}

export function back(e) {
  bus.$emit(Events.VIEW_BACK_START, e);
}

/**
 * 2018年9月30日11:52:20
 * 目前作用仅仅是通知SPA，页面即将开始进行push页面操作，SPA会对此做一些准备工作
 * @param {*} e 
 */
export function push(e) {
  bus.$emit(Events.VIEW_PUSH_START, e);
}

export function emit(e, data, type) {
  // console.log(e, data, type);
  bus.$emit(e, data, type);
}

export function bind(e, fn) {
  bus.$off(e, fn);
  bus.$on(e, fn);
}

/**
 * EventBus事件代理，解决事件污染问题
 * @param {Events} e
 * @param {string} sourceId 来源标识
 * @param {Function} fn 事件回调
 */
export function on(e, sourceId, fn) {
  // console.log('on ', e, sourceId);
  // bus.$off(e);
  // 重复调用会重复绑定事件
  bus.$on(e, function(data, from) {
    // console.log('receive:', data, from);
    if (sourceId && sourceId !== from) return;
    // console.log('call bus event:', sourceId, data, from);
    fn(...arguments);
  });
}

export { on as listen };

export function unbind(e, fn) {
  bus.$off(e, fn);
}
// export { bus, Events, toast, dialog, loading, back, setBackButton };

export { Events, bus };

export function createBusEvents(events) {
  if (!isPlainObject(events)) throw new Error('请指定events');
  for (let event in events) {
    if (event in Events) throw new Error(`${event}已定义，请修改event名称！`);
  }
  Object.assign(Events, events);
  return events;
}
