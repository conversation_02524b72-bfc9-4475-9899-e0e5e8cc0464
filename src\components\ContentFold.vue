<template>
  <div class="text-content" :class="{ 'fold': status == DisplayStatus.FOLD }" >
    <div ref="content" class="text-content-inner"  :style="contentStyle" v-html="content"></div>
    <div v-if="enable" class="fold-btn" @click="toggle" >{{btn.text}}</div>
  </div>
</template>
<style lang="scss" scoped>
  $btn-height: 25px;

  .fold-btn{
    text-align: center;
    position: relative;
    z-index: 1;
    padding: 2px;
    color: #2196F3;
    &::after{
      content: '\e606';
      font-family:iconfont;
      margin-left: 5px;
    }
  }
  .text-content-inner {
    position:relative;
    overflow-y: hidden;
    max-height: 100px; /*px*/
    white-space: pre-line;
    .content-all {
      max-height: none;
    }
  }
  .fold{
    .fold-btn{
      box-shadow: 0 -10px 20px 8px rgba(255, 255, 255, 0.7);
      height: $btn-height;
      background: rgba(255, 255, 255, 0.96);
      &::after{
        content: '\e607';
      }
    }
  }
</style>
<script>
  const DisplayStatus = {
    ALL: -1,
    NONE: 0,
    FOLD: 1,
    UNFOLD: 2,
  }

  export default {
    name: 'content-fold',
    props: {
      value: {
        type: String
      },
      limit: {
        type: Number,
        default: 200,
      },
      changer: {
        type: [Number, String],
      },
      rows: {
        type: Number,
        default: 4,
      },
      placeholder: {
        type: String,
        default: '暂无内容'
      }
    },
    data() {
      return {
        DisplayStatus,
        status: DisplayStatus.NONE,
        contentHeight: 0,
        height: 0,
      };
    },
    watch: {
      changer(val, oldVal) {
        if (this.status != DisplayStatus.FOLD) {
          this.init();
        }
      }
    },
    mounted() {
      this.init();
    },
    computed: {
      contentStyle() {
        const styles = {};
        if (this.status == DisplayStatus.FOLD) {
          styles.maxHeight =  `${this.height}px`;
        } else if (this.status == DisplayStatus.UNFOLD || this.status == DisplayStatus.ALL) {
          styles.maxHeight =  `none`;
        }
        return styles;
      },
      enable() {
        return this.value && this.contentHeight > this.height;
      },
      btn() {
        return {
          text: this.status === DisplayStatus.FOLD ? '展开全部' : '收起'
        }
      },
      content() {
        return this.value || this.placeholder;
      },
      content2() {
        const value = this.value || this.placeholder;
        if (!value.length) return this.placeholder;
        if (value.length <= this.limit || this.status == DisplayStatus.UNFOLD) {
          return value;
        } else {
          return value.substr(0, this.limit);
        }
      },
    },
    methods: {
      init() {
        // console.log('this:', this.$el);
        // console.log('this.$refs.content:', this.$refs.content.scrollHeight);
        const that = this;
        /*
        setTimeout(() => {
          console.log('contentHeight:', this.$refs.content.scrollHeight);
        }, 0)
        */
        that.contentHeight = that.$refs.content.scrollHeight;
        this.updateFold();
      },
      updateFold() {
        const height = this.rows * parseInt(this.$refs.content && parseInt(getComputedStyle(this.$refs.content).lineHeight));
        this.height = height;
        const needFold = this.value && this.contentHeight > height;
        if (needFold) {
          this.status = DisplayStatus.FOLD; 
        } else {
          this.status = DisplayStatus.ALL;
        }
      },
      toggle() {
        // console.log(this.$refs);
        this.status = (this.status === DisplayStatus.FOLD ? DisplayStatus.UNFOLD : DisplayStatus.FOLD);
      },
    },
  };
</script>
