<template>
  <div>
    <template v-if="status == AppStatus.READY">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          class="vant-list"
          v-model="loading"
          :error.sync="error"
          error-text="请求失败，点击重新加载"
          :offset="100"
          :finished="finished"
          :finished-text="finishedText"
          @load="init"
          :immediate-check="false"
        >
          <ActivityItem
            class="bargain-item"
            :value="item"
            :itemInfoData="childInfoData"
            v-for="item in pageData"
            :key="item.id"
          ></ActivityItem>
          <!-- v-for="(item,index) in pageData"
            :key="index" -->
          <template v-if="!pageData.length && !loading&&!error">
            <list-placeholder
              icon="~@pkg/actions/assets/images/list-empty.png"
            ></list-placeholder>
          </template>
        </van-list>
      </van-pull-refresh>
    </template>
  </div>
</template>

<script>
import { formatDate, formatMoney } from '@/utils';
import { AppStatus } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
import { getGoodsList } from '@pkg/mall/api';
import { Button, Toast, List, PullRefresh } from 'vant';
import ActivityItem from './ActivityItem.vue';

export default {
  name: 'ActivityShopList',
  props: {
    id: {
      type: [String, Number],
    },
    type: {
      type: String,
      default: 'all'
    },
    recommendStatus: {
      type: [String, Number],
    },
    childInfoData: {
      type: Object
    }
  },
  mixins: [mixinAuthRouter],
  components: {
    ActivityItem,
    [Button.name]: Button,
    [Toast.name]: Toast,
    [PullRefresh.name]: PullRefresh,
    [List.name]: List,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.READY,
      pageData: [],
      loading: false,
      error: false,
      finished: false,
      refreshing: false,
      tempFinished: false, // 切换tab时，临时储存finished的值，防止在其他tab下触发本页van-list的load方法(vant-list组件bug，issues：#3430)
      paging: {
        page: 1,
        pageSize: 10,
        total: null
      }
    };
  },
  computed: {
    finishedText() {
      return this.pageData.length ? '没有更多数据了' : '';
    },
    filterParams() {
      return {
        sort: 'normal',
        categoryId: this.type != 'category' ? this.id : '', // type为'all'或者'label' 类目类型时
        topicId: this.type == 'category' ? this.id : '', // type为专区类型时
        page: this.paging.page,
        pageSize: this.paging.pageSize,
        recommendStatus: this.recommendStatus ? this.recommendStatus : ''
      }
    },
  },
  watch: {
  },
  mounted () {
    this.init()
  },
  methods: {
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      this.pageData = [];
      this.paging.page = 1;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.init();
    },
    init () {
      this.loading = true;
      this.getPageData();
    },
    getPageData () {
      getGoodsList(this.filterParams)
        .then((res) => {
          if (this.refreshing) {
            this.pageData = [];
            this.refreshing = false;
          }
          if (res.list.length < 10) {
            this.finished = true;
            this.tempFinished = this.finished;
          }
          this.paging.page += 1;
          this.pageData = this.pageData.concat(res.list);
          // this.pageData = []
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          this.error = true;
          console.log(err)
        });
    }
  }
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
::v-deep .list-empty{
  margin-top: 10vh;
}
::v-deep .van-list__finished-text, ::v-deep .van-loading__text, ::v-deep .van-loading{
  color: #feddd4;
}
.vant-list {
  padding-top: 10px;
  .bargain-item {
    margin-bottom: 10px;
  }
}
</style>
