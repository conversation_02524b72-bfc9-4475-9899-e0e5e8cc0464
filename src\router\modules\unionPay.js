import { handleError } from '../error-handler';

export default [
  {
    path: '/unionpay',
    name: '云闪付小程序首页',
    component: resolve => {
      import(/* webpackChunkName: "wechat" */ '@/views/packages/unionpay/UnionPay.vue').then(resolve).catch(handleError);
    },
  }
  // {
  //   path: '/wechat/me',
  //   name: '交广领航微信-我的',
  //   component: resolve => {
  //     import(/* webpackChunkName: "wechat-me" */ '@/views/packages/wechat/WeChatMe.vue').then(resolve).catch(handleError);
  //   },
  // }
];
