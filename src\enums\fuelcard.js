import { createEnums } from './utils';

export const FuelcardRechargeStatus = createEnums({
  RECHAEGING: ['充值中', 0, '充值中'],
  SUCCES: ['充值成功', 1, '充值成功'],
  FAIL: ['充值失败', 2, '充值失败'],
});

export const FuelcardPayStatus = createEnums({
  
  REFUNDED: ['已退款', -1, '申请退款'],
  UNPAID: ['待付款', 1, '待付款'],
  SUCCES: ['支付成功', 2, '支付成功'],
});

export const FuelcardApplyStatus = createEnums({
  INVALID: ['已失效', -1, '已失效'],
  PENDING: ['待审核', 0, '正在审核中，请耐心等待'],
  REJECT: ['审核驳回', 1, '查看驳回原因，修改后重新提交'],
  PASS: ['审核通过', 2, '油卡正在办理中，办理后将邮寄到您手中'],
  MAILED: ['邮寄中', 3, '油卡正在飞回您手中'],
  MAILING: ['邮寄中', 4, '油卡正在飞回您手中'],
  COMMENTED: ['已完成', 5, '油卡办理已完成'],
  REFUNDING: ['退款中', 6, '您的退款正在处理中，请稍后'],
  REFUNDED: ['已退款', 7, '您的钱已退回到原支付账户'],
});

export const InvoiceStatus = createEnums({
  PERSON: ['个人/非企业', 0, '个人'],
  COMPANY: ['企业', 1, '企业'],
});

// VIP状态
export const VIPSTATUS = createEnums({
  NOVIP: ['非会员', 0, '不是会员或已过期'],
  FUELCARD: ['加油卡会员', 1, '是会员可以是用加油卡功能'],
  NOFUELCARD: ['非加油卡会员', 2, '是会员加油卡不能用']
});
