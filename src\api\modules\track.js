import { postJSON, serialize } from '../request/';

/**
 * 事件追踪
 * @param {string} event 事件名
 * @param {array} data 事件数据
 */
export function trackEvent(event, data = []) {
  // const token = serialize(data);
  const token = data.join(',')
  return postJSON('/Radio/platform-data/save/homePageClick?sid=&v=394', [
    {
      'time': Date.now(),
      'token': token,
      'type': event,
      // 'uid': 'jgrm.net',
      'url': location.href
    },
  ])
};
