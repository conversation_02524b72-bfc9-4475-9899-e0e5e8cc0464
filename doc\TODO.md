
# TODO

## 0. 解决iOS端WebView假死问题

复现方式（非100%）
1. 打开iOS端交广领航App
2. 打开一个加载本项目页面的WebView如汽车美容
3. 进行正常的购买流程
4. 购买中途切换到其他App，或回到桌面，或锁屏十几秒到几分钟不等
5. 切换到交广领航App，此时webview可能已经假死，即使数据发生变化或通过调试工具删除DOM，页面的UI也不会发生变化，但疼可产生有限的交互操作

引发此问题的原因目前推测可能是
1. iOS端代码实现有问题
2. 页面在iOS端的WKWebView中占用内存过大，触发iOS内存回收机制

## 1. 部分依赖库升级如Swipper，部分依赖库替换如hammer.js替换为FingerJS

目前使用Swipper3，最新版本为Swipper4(有ES6代码，有利于webpack优化代码体积)

Hammer.js体积大，且已几乎停止维护，而FingerJS体积小，依然在维护

## 2. 部分组件在iOS端交广领航内触发点击事件与预期不符

以洗车美容商家列表为例：x-header，tip，x-select组件，用手指触摸这些组件，滑动手指到屏幕其他位置，手指离开屏幕，触发了点击事件。看起来似乎是touchstart触发的。

## 3. 解决路由跳转A1=>B=>C=>A2的问题

整个SPA应用使用keep-alive实现页面压栈出栈，但若某个操作路径形成闭环就可能导致组件状态混乱。

如操作路径：A1=>B=>C=>A2

C=>A2 时，组件A会被keep-alive复用，导致A1数据被替换为A2刷新，后续若后退到A1，A1需要重新加载。目前通过调用native端的pushWebView打开新的WebView来规避

## 4. 研究使用DLLPlugin后，修改代码导致所有编译文件发生变更的原因

使用DllPlugin本意是想提升Webpack编译速度，减少编译时间

但使用时发现，代码仅做微小改动，编译后几乎所有js文件的hash值都发生了变化

而不使用DllPlugin时，则仅有部分文件有变动

## 5. Router梳理，优化设计，避免router潜在冲突

新增的功能越来越多，项目越来越臃肿，不同功能模块声明路由时越来越容易起冲突

尤其是设计REST风格的动态路由时，容易导致不同模块的路由产生交集,如
```
/mall/order/list
/:category/order/:type
```

## 6. 优化代码，解决内存泄漏问题
为了使页面尽量接近原生应用的操作习惯，项目采用了很多手段来弥补web项目的不足

为了追求实现某个功能，不知不觉间写出了导致内存泄漏的代码。

内存泄漏在传统网页中一般不是大问题，因为刷新页面内存就会全部释放

但SPA应用跳转不刷新页面带来较好用户体验的同时，也让内存泄漏问题成为影响程序性能的一个关键因素。

Windows10平台下，使用Chrome开发工具测试

本项目初始化后占用内存约15MB

进入汽车美容列表，选商家，下单，选券，付款，查看订单详情操作后占用内存约21MB

总体来看占用内存不大

但iOS端WebView有时会出现假死现象，而普通的网页几乎没有出现过此问题

猜测页面在iOS的WebView中占用内存情况和PC端Chrome有所差别，需要抽时间专门在iOS上测试实际占用内存情况

也可能和iOS端WebView的设置或功能实现有关

## 7. SourceMap 问题

本项目的报错信息会通过Sentry统一收集起来。

生产环境中项目代码是混淆压缩过的，这导致报错信息中代码定位不够准确

通过Sentry上传SourceMap可以在报错信息中直观看到报错代码的位置

但使用时发现编译时若保留sourceMap，即使配置为将sourceMap与JS分离，vue文件被编译后的JS文件中依然包含SourceMap信息，增加了文件体积，故目前暂时屏蔽了上传sourceMap到Sentry

## 8. 长列表加载

长列表页面加载过多数据，DOM元素过多会导致页面占用内存增加和卡顿。

未来需要优化为使用虚拟列表实现，效果可参考Twitter移动端Web页面

## 9. TypeScript

Vue3对TS进行了全面支持，但旧项目基于Vue2，升级改造测试工作量较大，

目前可行的方案是对项目进行微前端化改造，新模块逐步替换升级到Vue3（工作量依然较大）

或使用 @vue/compat，逐步升级