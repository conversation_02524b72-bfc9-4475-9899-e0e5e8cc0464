<template>
  <container
    class="member-experience"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="share">
        <i class="icon_jglh icon-fenxiang1" @click="share('show')"></i>
      </div>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="content-wrap">
        <div class="banner"></div>
        <div class="content">
          <div
            class="coupon-card"
            v-for="(item, index) in memberCards"
            :key="index"
          >
            <div class="card-left">
              <div class="card-title">
                <i class="card-icon"></i>
                {{ item.title }}
              </div>
              <div class="card-desc">{{ item.desc }}</div>
            </div>
            <div class="card-right">
              <van-button
                class="receive-btn"
                :loading="item.loading"
                :loading-text="loadingText"
                :disabled="disabled"
                @click="receiveCoupon(item.category)"
                >立即领取</van-button
              >
            </div>
          </div>
          <div class="activity-info">
            <p class="activity-date">
              活动有效期：{{ formatDate(pageData.sdate) }}至{{
                formatDate(pageData.edate)
              }}
            </p>
          </div>
        </div>
        <div class="activity-rules">
          <h3>活动规则</h3>

          <biz-rich-text
            v-if="pageData.description"
            class="intros-desc"
            :value="pageData.description"
          ></biz-rich-text>
        </div>
      </div>
      <PrizePopMember
        v-if="showPrize"
        :show="showPrize"
        :prize="prizeInfo"
        @cancel="handleTipsClose"
        @confirm="handleTipsConfirm"
      >
      </PrizePopMember>
    </content-view>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { formatDate, getAppURL } from '@/utils';
import { getImageURL } from '@/common/image';
import { toast } from '@/bus';
import { Button } from 'vant';
import BizRichText from '@/views/components/BizRichText.vue';
import { getGrantMemberShipActionDetail, receiveMemberShip } from './api';
import PrizePopMember from './components/PrizePopMember.vue';
export default {
  name: 'MemberExperience',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    PrizePopMember,
    BizRichText,
  },
  data() {
    let actionId = this.$route.query.id;
    return {
      AppStatus,
      status: AppStatus.LOADING,
      pageTitle: '会员体验中心',
      keepAlive: false,
      pageData: {},
      loadingText: '领取中',
      disabled: false,
      memberCards: [],
      showPrize: false,
      prizeInfo: {
        prizeName: '车主会员券',
      },
      actionId,
    };
  },
  methods: {
    formatDate(date) {
      return formatDate(date, 'YYYY-MM-DD');
    },
    init() {
      // 初始化数据
      getGrantMemberShipActionDetail(this.actionId)
        .then(res => {
          this.memberCards = [];
          this.pageTitle = res.title || '会员体验中心';
          this.pageData = res;
          if (res.carGrantMemberCategoryInfo) {
            this.memberCards.push({
              category: res.carGrantMemberCategoryInfo.category,
              title: res.carGrantMemberCategoryInfo.categoryName,
              desc: `${res.carGrantMemberCategoryInfo.grantDays}天免费体验`,
              loading: false,
            });
          }
          if (res.mallGrantMemberCategoryInfo) {
            this.memberCards.push({
              category: res.mallGrantMemberCategoryInfo.category,
              title: res.mallGrantMemberCategoryInfo.categoryName,
              desc: `${res.mallGrantMemberCategoryInfo.grantDays}天免费体验`,
              loading: false,
            });
          }
          // 判断活动是否结束
          if (
            new Date(res.edate).getTime() < new Date().getTime() ||
            res.status == -1
          ) {
            this.disabled = true;
            toast().tip('活动已下线');
          } else if (res.status == 0) {
            this.disabled = true;
            toast().tip('活动未开始');
          } else {
            this.disabled = false;
          }

          this.status = AppStatus.READY;
          this.share();
        })
        .catch(err => {
          this.status = AppStatus.ERROR;
          toast().tip(err.message);
        });
    },
    receiveCoupon(category) {
      const card = this.memberCards.find(item => item.category === category);
      card.loading = true;
      receiveMemberShip({
        id: this.actionId,
        memberCategory: category,
      })
        .then(res => {
          card.loading = false;
          this.disabled = true;
          this.showPrize = true;
          this.prizeInfo = {
            prizeName: card.title,
            category: category,
          };
        })
        .catch(err => {
          card.loading = false;
          toast().tip(err.message);
        });
    },
    share(action = 'config') {
      const title = this.pageData.shareTitle;
      const logo = getImageURL(this.pageData.shareImage);
      const desc = this.pageData.shareContent;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    handleTipsClose() {
      this.showPrize = false;
    },
    handleTipsConfirm() {
      this.showPrize = false;
      if (this.prizeInfo.category === 'car') {
        this.$_route_vipPage('car');
      } else {
        this.$_route_vipPage('mall');
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      // 页面重新激活时在重新获取数据
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/animate.scss';
@import '~styles/mixin/index.scss';

.member-experience {
  min-height: 100vh;
  background: #f3f3f3;
  .more {
    width: 45px;
    height: 45px;
    text-align: center;
    line-height: 46px;
    .icon_jglh {
      font-size: 24px;
    }
  }
  .content-wrap {
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }
  .banner {
    width: 100%;
    height: 125px;
    background: url('./assets/images/member.png') no-repeat left top;
    background-size: 100% auto;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;

    &-content {
      color: #fff;
      h1 {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      p {
        font-size: 16px;
        opacity: 0.8;
      }
    }
  }

  .content {
    padding: 20px 15px;
    border-radius: 12px 12px 0px 0px;
    background: #ffffff;
    overflow: hidden;
    margin-top: -10px;
    position: relative;

    .coupon-card {
      background: #fff2f2;
      border-radius: 8px;
      padding: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      .card-left {
        .card-title {
          font-size: 15px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
          display: flex;
          align-items: center;

          .card-icon {
            flex-shrink: 0;
            word-break: keep-all;
            width: 15px;
            height: 15px;
            margin-right: 5px;
            background: url('./assets/images/member_coupon.png') no-repeat
              center center;
            background-size: 100% 100%;
          }
        }

        .card-desc {
          font-size: 12px;
          color: #fd4925;
        }
      }

      .receive-btn {
        flex-shrink: 0;
        word-break: keep-all;
        height: 28px;
        padding: 0 12px;
        background: #fd4925;
        border: none;
        border-radius: 16px;
        color: #fff;

        &--disabled {
          background: #ccc;
        }
      }
    }

    .activity-info {
      margin: 15px 0 0;
      text-align: center;

      .activity-date {
        font-size: 13px;
        color: #999999;
      }
    }
  }
  .activity-rules {
    flex: 1;
    margin-top: 12px;
    padding: 20px 15px;
    background: #ffffff;

    h3 {
      font-weight: bold;
      font-size: 15px;
      color: #333333;
      margin-bottom: 10px;
    }

    p {
      font-size: 13px;
      color: #333333;
      line-height: 16px;
    }
  }
  .intros-desc {
    padding: 0;
  }
  .share {
    width: 45px;
    height: 45px;
    text-align: center;
    line-height: 46px;
    .icon_jglh {
      font-size: 24px;
    }
  }
}
</style>
