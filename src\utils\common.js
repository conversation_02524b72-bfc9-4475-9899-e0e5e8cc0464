import * as ENV from '@/common/env';
import { getSessionThirdAppParams } from '@/store/storage';

export function generateUUID() {
  let d = new Date().getTime();
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    function (c) {
      let r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c == 'x' ? r : (r & 0x7) | 0x8).toString(16);
    }
  );
  return uuid;
}

export function formatSensitiveText(text = '', hiddenPercent = 0.6) {
  if (!text) return text;
  const textLength = text.length;
  const hiddenLength = Math.floor(textLength * hiddenPercent);
  const startBorder = Math.floor((textLength - hiddenLength) / 2);
  const endBorder = startBorder + hiddenLength - 1;
  return text.replace(/./g, (item, index) => {
    if (index >= startBorder && index <= endBorder) return '*';
    return item;
  });
}
// console.log(formatSensitiveText('18203679025', 0.6));

/**
 * 根据一个相对路径返回其绝对路径
 * @param {*} path
 */
export function getAbsoluteURL(path, options) {
  let a = document.createElement('A');
  a.href = path;
  return a.href;
}

/**
 * 依赖于 https://github.com/amfe/lib-flexible/blob/master/src/flexible.js
 * 获取iPhone6下的像素值在当前设备下的像素值
 * @param {number} value iPhone6设备下像素值
 * @return {number} result 当前设备下像素值
 */
export function getCurrentDevicePixelValue(value) {
  // 所有尺寸基于iPhone6尺寸，其他分辨率都需要通过此进行转换
  const BASE = 375 / 10;
  if (window.lib && window.lib.flexible) {
    return (value / BASE) * window.lib.flexible.rem;
  } else {
    return value;
  }
}

/**
 * 解析url中hash值后面的参数
 * @param {*} localhash
 */
export function getSearchParams(search) {
  let paramPart = search.split('?');
  let all = {};
  if (paramPart[1]) {
    let tmp = paramPart[1].split('&');
    tmp.forEach(item => {
      let aa = item.split('=');
      all[aa[0]] = aa[1];
    });
  }

  return all;
}

/**
 * 获取第三方app加载时传递的参数
 * @param {*} localhash
 */
export function getThirdAppParams() {
  let hash = window.location.hash;
  const params = getSearchParams(hash);
  return params;
}

/**
 * 从 URL 中提取参数
 * @param {string} url
 */
export function getQueryParams(url) {
  if (!url) {
    url = window.location.href;
  }
  const urlObj = new URL(url);
  const params = new URLSearchParams(urlObj.search);

  const result = {};
  for (const [key, value] of params.entries()) {
    result[key] = value;
  }

  return result;
}

/**
 * 加密姓名
 * @param {string} url
 */
export function encryptName(name) {
  if (typeof name !== 'string' || name.length === 0) {
    return ''; // 处理空字符串或非字符串输入
  }

  if (name.length === 1) {
    return name[0] + '*'; // 只有一个字符时，加密后为"字符*"
  }

  if (name.length === 2) {
    return name[0] + '*'; // 两个字符时，加密后为"字符****"
  }

  // 对于三个或以上字符的名称，替换中间内容
  let newStr = name.replace(/^([\s\S])(.*)([\s\S])$/, '$1*$3');
  return newStr;
}
/**
 * 判断是否是在第三方app中加载,从sessionStorage中获取链接参数
 */
export function isThirdApp() {
  let params = getSessionThirdAppParams();
  if (params.platform) {
    return true;
  }
  return false;
}

/* *
 * 计算两地点之间距离
 * @param {Number} lat1
 * @param {Number} lng1
 * @param {Number} lat2
 * @param {Number} lng2
 */
export function getDistance(lat1, lng1, lat2, lng2) {
  let radLat1 = (lat1 * Math.PI) / 180.0;
  let radLat2 = (lat2 * Math.PI) / 180.0;
  let a = radLat1 - radLat2;
  let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  let s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
      )
    );
  s = s * 6378.137;
  s = Math.round(s * 10000) / 10000;
  return s;
}

function fallbackCopyTextToClipboard(text) {
  let textArea = document.createElement('textarea');
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  return new Promise((resolve, reject) => {
    try {
      let successful = document.execCommand('copy');
      let msg = successful ? 'successful' : 'unsuccessful';
      // console.log('Fallback: Copying text command was ' + msg);
      resolve();
    } catch (err) {
      reject(err);
      console.error('Fallback: Oops, unable to copy', err);
    }
    document.body.removeChild(textArea);
  });
}

/**
 * 复制文本到剪贴板
 */
export function copyTextToClipboard(text) {
  if (!navigator.clipboard) {
    return fallbackCopyTextToClipboard(text);
  }
  return navigator.clipboard.writeText(text).then(
    function () {
      console.log('Async: Copying to clipboard was successful!');
    },
    function (err) {
      console.error('Async: Could not copy text: ', err);
      return Promise.reject(err);
    }
  );
}

export function copyToClipboard(text) {
  return new Promise((resolve, reject) => {
    try {
      // 创建一个临时的 textarea 元素
      const textarea = document.createElement('textarea');
      textarea.value = text; // 将要复制的文本赋值给 textarea
      textarea.style.position = 'fixed'; // 防止页面滚动
      textarea.style.opacity = '0'; // 让 textarea 隐藏
      document.body.appendChild(textarea); // 将 textarea 添加到页面

      textarea.select(); // 选择文本
      const successful = document.execCommand('copy'); // 执行复制命令
      document.body.removeChild(textarea); // 移除 textarea

      if (successful) {
        resolve();
      } else {
        reject('复制失败');
      }
    } catch (err) {
      reject(err);
      console.error('Oops, unable to copy', err);
    }
  });
}
/**
 * @description: 匿名用户名
 * @param {*} username
 * @return {*}
 */
export function anonymizeUsername(username) {
  const arr = Array.from(username.trim());
  let num = 0;
  const result = arr.map((char, index) => {
    if (index === 0 || index === arr.length - 1) {
      // 如果是第一个或最后一个字符，则不做处理
      return char;
    } else {
      // 其他字符用 * 替代
      num++;
      if (num > 4) {
        return '';
      }
      return '*';
    }
  });
  if (result.length < 5) {
    const start = result.slice(0, 1);
    const end = result.slice(result.length - 1);
    return [...start, '***', ...end].join('');
  }
  return result.join('');
}
/**
 * @description: 去除对象中所有字符串值的首位空格 支持嵌套对象
 * @param {*} obj
 * @return {*}
 */
export function trimObjectValues(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return typeof obj === 'string' ? obj.trim() : obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => trimObjectValues(item));
  }

  let result = {};
  for (let key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result[key] = trimObjectValues(obj[key]);
    }
  }

  return result;
}
