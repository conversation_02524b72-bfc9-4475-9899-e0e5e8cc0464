<template>
  <container class="charging-station-search">
    <!-- {{ AURA-X: Modify - 移除搜索框，改为简单标题头部. Confirmed via 寸止 }} -->
    <x-header title="搜索充电站">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status">
      <!-- {{ AURA-X: Add - 将搜索框移动到内容区域顶部. Confirmed via 寸止 }} -->
      <template slot="head" v-if="status == AppStatus.READY">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <van-search
            ref="searchInput"
            v-model="keyword"
            show-action
            autofocus
            placeholder="搜索充电站名称或地址"
            @search="doSearch"
            @input="onInput"
            @focus="onFocus"
          >
            <template #action>
              <div @click="doSearch">搜索</div>
            </template>
          </van-search>
        </div>
      </template>
      <!-- 搜索建议 -->
      <div v-if="showSuggestions && suggestions.length" class="suggestions">
        <div class="suggestions-title">搜索建议</div>
        <div
          v-for="suggestion in suggestions"
          :key="suggestion.id"
          class="suggestion-item"
          @click="selectSuggestion(suggestion)"
        >
          <van-icon name="location-o" />
          <div class="suggestion-content">
            <div class="suggestion-name">{{ suggestion.name }}</div>
            <div class="suggestion-address">{{ suggestion.address }}</div>
          </div>
        </div>
      </div>

      <!-- 搜索历史 -->
      <div v-if="showHistory && searchHistory.length" class="search-history">
        <div class="history-header">
          <span class="history-title">搜索历史</span>
          <van-icon name="delete" @click="clearHistory" />
        </div>
        <div class="history-list">
          <van-tag
            v-for="(item, index) in searchHistory"
            :key="index"
            class="history-tag"
            @click="selectHistory(item)"
          >
            {{ item }}
          </van-tag>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="showResults" class="search-results">
        <template v-if="searchStatus === AppStatus.LOADING">
          <div class="search-loading">正在搜索...</div>
        </template>
        <template v-else-if="searchStatus === AppStatus.ERROR">
          <div class="search-error">搜索失败，请重试</div>
        </template>
        <template v-else-if="searchStatus === AppStatus.READY">
          <div v-if="searchResults.length" class="results-list">
            <charging-station-item
              v-for="station in searchResults"
              :key="station.id"
              :station="station"
              @click.native="goDetail(station)"
              @navigate="navigateToStation(station)"
            />
            <list-loader
              v-if="searchResults.length"
              :options="$_loader_options"
              @load="loadMore"
            />
          </div>
          <div v-else class="no-results">
            <van-empty description="未找到相关充电站" />
          </div>
        </template>
      </div>
    </content-view>
  </container>
</template>

<script>
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import { AppStatus } from '@/enums';
import { getLocation } from '@/bridge';
import { toast } from '@/bus';
import { getChargingStationList } from '@/api/modules/charging-station';
import ChargingStationItem from './components/ChargingStationItem.vue';
import { Search, Icon, Tag, Empty } from 'vant';

// 导入静态数据用于搜索建议
const mockStations = [
  {
    id: '1',
    name: '万达广场充电站',
    address: '北京市朝阳区建国路93号万达广场B1层',
  },
  {
    id: '2',
    name: '国贸中心充电站',
    address: '北京市朝阳区建国门外大街1号国贸中心地下停车场',
  },
  {
    id: '3',
    name: '首都机场T3充电站',
    address: '北京市顺义区首都机场T3航站楼停车场',
  },
  {
    id: '4',
    name: '西单大悦城充电站',
    address: '北京市西城区西单北大街131号大悦城地下停车场',
  },
  {
    id: '5',
    name: '奥林匹克公园充电站',
    address: '北京市朝阳区奥林匹克公园中心区',
  },
];

const SEARCH_HISTORY_KEY = 'charging_station_search_history';
const MAX_HISTORY_COUNT = 10;

export default {
  name: 'ChargingStationSearch',
  components: {
    ChargingStationItem,
    [Search.name]: Search,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [Empty.name]: Empty,
  },
  mixins: [mixinLoader, mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.READY,
      searchStatus: AppStatus.READY,

      // 搜索相关
      keyword: '',
      searchResults: [],
      suggestions: [],
      searchHistory: [],

      // 地理位置
      geo: {
        latitude: null,
        longitude: null,
      },

      // 搜索参数 - 根据接口文档调整参数格式
      searchParams: {
        // 分页参数
        page: 1,
        size: 10,

        // 位置参数
        latitude: null,
        longitude: null,

        // 搜索关键词
        keyword: '',

        // 排序参数
        sortField: 'distance',
        sortDirection: 'asc',

        // 其他参数设置为默认值
        province: '',
        city: '',
        district: '',
        stationType: '',
        status: '',
        operationType: '',
        businessHoursType: '',
        highwayMode: '',
        minPower: null,
        maxPower: null,
        minVoltage: null,
        maxVoltage: null,
        parkingFeeType: '',
        parkingTypes: [],
        chargingMethods: [],
        benefits: [],
        services: [],
      },

      // 显示状态
      showSuggestions: false,
      showHistory: true,
      showResults: false,

      // 防抖定时器
      suggestionTimer: null,
    };
  },
  created() {
    this.init();
  },

  beforeDestroy() {
    // 清理定时器
    if (this.suggestionTimer) {
      clearTimeout(this.suggestionTimer);
    }
  },
  methods: {
    init() {
      this.loadSearchHistory();
      this.getLocation();
    },

    // 获取地理位置
    getLocation() {
      getLocation()
        .then(location => {
          this.geo = {
            latitude: location.latitude,
            longitude: location.longitude,
          };
          this.searchParams.latitude = location.latitude;
          this.searchParams.longitude = location.longitude;
        })
        .catch(e => {
          console.error('获取位置失败:', e);
        });
    },

    // 加载搜索历史
    loadSearchHistory() {
      try {
        const history = localStorage.getItem(SEARCH_HISTORY_KEY);
        if (history) {
          this.searchHistory = JSON.parse(history);
        }
      } catch (e) {
        console.error('加载搜索历史失败:', e);
      }
    },

    // 保存搜索历史
    saveSearchHistory(keyword) {
      if (!keyword.trim()) return;

      // 移除重复项
      const index = this.searchHistory.indexOf(keyword);
      if (index > -1) {
        this.searchHistory.splice(index, 1);
      }

      // 添加到开头
      this.searchHistory.unshift(keyword);

      // 限制数量
      if (this.searchHistory.length > MAX_HISTORY_COUNT) {
        this.searchHistory = this.searchHistory.slice(0, MAX_HISTORY_COUNT);
      }

      // 保存到本地存储
      try {
        localStorage.setItem(
          SEARCH_HISTORY_KEY,
          JSON.stringify(this.searchHistory)
        );
      } catch (e) {
        console.error('保存搜索历史失败:', e);
      }
    },

    // 清除搜索历史
    clearHistory() {
      this.searchHistory = [];
      localStorage.removeItem(SEARCH_HISTORY_KEY);
    },

    // 输入事件
    onInput(value) {
      if (value.trim()) {
        this.showSuggestions = true;
        this.showHistory = false;
        this.showResults = false;
        // 生成搜索建议
        this.getSuggestions(value);
      } else {
        this.showSuggestions = false;
        this.showHistory = true;
        this.showResults = false;
        this.suggestions = [];
      }
    },

    // 获取搜索建议（防抖处理）
    getSuggestions(keyword) {
      // 清除之前的定时器
      if (this.suggestionTimer) {
        clearTimeout(this.suggestionTimer);
      }

      // 设置新的定时器
      this.suggestionTimer = setTimeout(() => {
        // 简单的搜索建议逻辑，基于静态数据
        const suggestions = [];
        const searchKeyword = keyword.toLowerCase();

        // 从静态数据中筛选匹配的充电站
        mockStations.forEach(station => {
          if (
            station.name.toLowerCase().includes(searchKeyword) ||
            station.address.toLowerCase().includes(searchKeyword)
          ) {
            suggestions.push({
              id: station.id,
              name: station.name,
              address: station.address,
            });
          }
        });

        // 限制建议数量
        this.suggestions = suggestions.slice(0, 5);
      }, 300); // 300ms防抖
    },

    // 聚焦事件
    onFocus() {
      if (!this.keyword.trim()) {
        this.showHistory = true;
        this.showSuggestions = false;
        this.showResults = false;
      }
    },

    // 执行搜索
    doSearch() {
      const keyword = this.keyword.trim();
      if (!keyword) {
        toast().tip('请输入搜索关键词');
        return;
      }

      this.saveSearchHistory(keyword);
      this.performSearch(keyword);
    },

    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.keyword = suggestion.name;
      this.doSearch();
    },

    // 选择搜索历史
    selectHistory(keyword) {
      this.keyword = keyword;
      this.doSearch();
    },

    // 执行搜索请求
    performSearch(keyword) {
      this.showSuggestions = false;
      this.showHistory = false;
      this.showResults = true;
      this.searchStatus = AppStatus.LOADING;

      const params = {
        ...this.searchParams,
        keyword,
        page: 1,
      };

      this.$_loader_bind(getChargingStationList, res => {
        this.searchResults = res.list || res;
        this.searchStatus = AppStatus.READY;
        return res;
      })
        .load(params)
        .catch(e => {
          this.searchStatus = AppStatus.ERROR;
          toast().tip(e);
          console.error(e);
        });
    },

    // 加载更多搜索结果
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;

      const nextPage = this.searchParams.page + 1;
      this.searchParams.page = nextPage; // 更新页码
      const params = {
        ...this.searchParams,
        keyword: this.keyword,
        page: nextPage,
      };

      this.$_loader_bind(getChargingStationList, res => {
        this.searchResults = this.searchResults.concat(res.list || res);
        return res;
      })
        .load(params)
        .catch(e => {
          toast().tip(e);
          console.error(e);
        });
    },

    // 跳转详情页
    goDetail(station) {
      this.$_router_push(`/charging-station/${station.id}`);
    },

    // 导航到充电站
    navigateToStation(station) {
      try {
        // 调用地图导航功能
        if (window.jsBridge && window.jsBridge.openMap) {
          window.jsBridge.openMap({
            latitude: station.latitude,
            longitude: station.longitude,
            name: station.name,
            address: station.address,
          });
        } else {
          // 备用方案：打开高德地图网页版
          const url = `https://uri.amap.com/navigation?to=${station.longitude},${station.latitude},${station.name}&mode=car&policy=1&src=myapp&coordinate=gaode&callnative=0`;
          window.open(url);
        }
      } catch (error) {
        console.error('导航失败:', error);
        toast().tip('导航功能暂时不可用');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.charging-station-search {
  // {{ AURA-X: Add - 添加搜索栏样式，参考ChargingStationList.vue. Confirmed via 寸止 }}
  .search-bar {
    padding: 12px 16px 8px;
    background: #fff;

    .van-search {
      padding: 0;

      .van-search__content {
        background: #f7f8fa;
        border-radius: 18px;

        .van-field__control {
          font-size: 14px;
          color: #323233;
        }

        .van-search__action {
          color: #1989fa;
        }
      }
    }
  }

  .suggestions {
    padding: 16px;

    .suggestions-title {
      font-size: 14px;
      color: #999;
      margin-bottom: 12px;
    }

    .suggestion-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
      }

      .van-icon {
        margin-right: 12px;
        color: #999;
        font-size: 16px;
      }

      .suggestion-content {
        flex: 1;

        .suggestion-name {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
        }

        .suggestion-address {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  .search-history {
    padding: 16px;

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .history-title {
        font-size: 14px;
        color: #999;
      }

      .van-icon {
        color: #999;
        font-size: 16px;
        cursor: pointer;
      }
    }

    .history-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .history-tag {
        cursor: pointer;
      }
    }
  }

  .search-results {
    padding-top: 16px;
    .search-loading,
    .search-error {
      padding: 40px 0;
      text-align: center;
      color: #999;
    }

    .results-list {
      padding: 0 16px;
    }

    .no-results {
      padding: 40px 0;
      text-align: center;
    }
  }
}
</style>
