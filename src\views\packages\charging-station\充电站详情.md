## 根据ID查询充电站详情

**接口地址**:`/api/chargingstations/{id}`

**请求方式**:`GET`

**请求数据类型**:`*`

**响应数据类型**:`*/*`

**接口描述**:获取指定ID的充电站详细信息

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | in   | 是否必须 | 数据类型       | schema |
| -------- | -------- | ---- | -------- | -------------- | ------ |
| id       | 充电站ID | path | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明         | schema              |
| ------ | ------------ | ------------------- |
| 200    | OK           | Result«StationVO» |
| 401    | Unauthorized |                     |
| 403    | Forbidden    |                     |
| 404    | Not Found    |                     |

**响应参数**:

| 参数名称                          | 参数说明                                | 类型           | schema         |
| --------------------------------- | --------------------------------------- | -------------- | -------------- |
| code                              |                                         | integer(int32) | integer(int32) |
| data                              |                                         | StationVO      | StationVO      |
| &emsp;&emsp;acCount               | 交流充电桩数量                          | integer(int32) |                |
| &emsp;&emsp;address               | 地址                                    | string         |                |
| &emsp;&emsp;availableChargerCount | 可用充电桩数量                          | integer(int32) |                |
| &emsp;&emsp;benefits              | 权益                                    | array          | string         |
| &emsp;&emsp;businessHoursDesc     | 具体营业时间描述                        | string         |                |
| &emsp;&emsp;businessHoursType     | 营业时间类型                            | string         |                |
| &emsp;&emsp;chargerCount          | 充电桩数量                              | integer(int32) |                |
| &emsp;&emsp;chargingMethods       | 充电方式                                | array          | string         |
| &emsp;&emsp;city                  | 城市                                    | string         |                |
| &emsp;&emsp;dcCount               | 直流充电桩数量                          | integer(int32) |                |
| &emsp;&emsp;delStatus             | 删除状态                                | integer(int32) |                |
| &emsp;&emsp;distance              | 距离（公里）                            | number(double) |                |
| &emsp;&emsp;district              | 区/县                                   | string         |                |
| &emsp;&emsp;highwayMode           | 高速模式                                | string         |                |
| &emsp;&emsp;id                    | 主键ID                                  | integer(int64) |                |
| &emsp;&emsp;images                | 充电站图片URL列表（多张图片用逗号分隔） | string         |                |
| &emsp;&emsp;latitude              | 纬度                                    | number         |                |
| &emsp;&emsp;longitude             | 经度                                    | number         |                |
| &emsp;&emsp;maxPower              | 最大功率（千瓦）                        | integer(int32) |                |
| &emsp;&emsp;maxVoltage            | 最大电压（伏特）                        | integer(int32) |                |
| &emsp;&emsp;minPower              | 最小功率（千瓦）                        | integer(int32) |                |
| &emsp;&emsp;minVoltage            | 最小电压（伏特）                        | integer(int32) |                |
| &emsp;&emsp;name                  | 充电站名称                              | string         |                |
| &emsp;&emsp;operationType         | 运营类型                                | string         |                |
| &emsp;&emsp;parkingFeeType        | 停车费类型                              | string         |                |
| &emsp;&emsp;parkingFeeTypeDesc    | 停车费类型详情描述                      | string         |                |
| &emsp;&emsp;parkingTypes          | 停车场类型                              | array          | string         |
| &emsp;&emsp;pricePerKwh           | 充电单价(元/度)                         | number         |                |
| &emsp;&emsp;province              | 省份                                    | string         |                |
| &emsp;&emsp;rating                | 评分                                    | number         |                |
| &emsp;&emsp;services              | 电站服务                                | array          | string         |
| &emsp;&emsp;stationType           | 电站类型                                | string         |                |
| &emsp;&emsp;status                | 电站状态                                | string         |                |
| msg                               |                                         | string         |                |
| success                           |                                         | boolean        |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"acCount": 0,
		"address": "",
		"availableChargerCount": 0,
		"benefits": [],
		"businessHoursDesc": "",
		"businessHoursType": "",
		"chargerCount": 0,
		"chargingMethods": [],
		"city": "",
		"dcCount": 0,
		"delStatus": 0,
		"distance": 0,
		"district": "",
		"highwayMode": "",
		"id": 0,
		"images": "",
		"latitude": 0,
		"longitude": 0,
		"maxPower": 0,
		"maxVoltage": 0,
		"minPower": 0,
		"minVoltage": 0,
		"name": "",
		"operationType": "",
		"parkingFeeType": "",
		"parkingFeeTypeDesc": "",
		"parkingTypes": [],
		"pricePerKwh": 0,
		"province": "",
		"rating": 0,
		"services": [],
		"stationType": "",
		"status": ""
	},
	"msg": "",
	"success": true
}
```
