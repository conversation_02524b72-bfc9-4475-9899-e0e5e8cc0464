import { isInJ<PERSON><PERSON><PERSON>, isInWeApp, isInUnionPayMP, isInAliApp } from '@/common/env';

export function fixRichHtmlImageSize(str, padding = 5) {
  const pattern = /data-size="(\d+):(\d+)"/g;
  const containerWidth = window.innerWidth - padding * 2;
  if (str) {
    str = str.replace(pattern, (term, w, h, all) => {
      const width = Number(w);
      const height = Number(h);
      if (width && height) {
        if (width < containerWidth) {
          return `width=${width} height=${height}`;
        }
        return `width=${containerWidth} height=${
          (containerWidth * height) / width
        }`;
      }
      return term;
    });
  }
  return filterRichHtml(str);
}

export function filterRichHtml(str) {
  const pattern = /href/;
  if (str) {
    return str.replace(/href/g, '').replace(/on\w+=/g, '');
  }
  return str;
}

// 动态加载js

function loadScript(url, scriptId, callback) {
  callback = typeof callback === 'function' ? callback : function () {};
  var head = document.getElementsByTagName('head')[0];
  var script = document.createElement('script');
  script.type = 'text/javascript';
  scriptId && (script.id = scriptId);
  script.src = url;
  // 对于旧的 IE 浏览器
  script.onreadystatechange = function () {
    if (this.readyState == 'loaded' || this.readyState == 'complete') {
      script.onreadystatechange = null; // 防止重复调用
      callback();
    }
  };

  // 对于现代浏览器
  script.onload = function () {
    callback();
  };
  head.appendChild(script);
}
/**
 * 初始化顶部广告条展示
 * @param { boolean } strategy : 广告条展示策略
 */
export function initDltJs(strategy) {
  var ADStrategy = {
    AUTO: 1, // 自动，交广领航内不显示，其他环境显示
    HIDE: 0, // 隐藏
    SHOW: 3, // 显示
  };
  var shouldShowADBar;
  if (strategy === ADStrategy.AUTO) {
    shouldShowADBar = !isInJGLH && !isInWeApp && !isInUnionPayMP && !isInAliApp;
  } else if (strategy === ADStrategy.HIDE) {
    shouldShowADBar = false;
  } else if (strategy === ADStrategy.SHOW) {
    shouldShowADBar = true;
  }
  return new Promise(function (resolve, reject) {
    if (shouldShowADBar) {
      loadScript(
        '/actions/static/js/libs/jglh/dlt.js?v=2020',
        'dlt-script',
        function () {
          resolve('success');
        }
      );
    }
  });
}
