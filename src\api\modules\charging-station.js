import APIModel from '../APIModel';

const api = new APIModel({
  // 获取充电站列表
  '/charging-station/list': '/Radio/api/chargingstations/query',

  // 获取充电站详情
  '/charging-station/detail': '/Radio/api/chargingstations/{id}',

  // 搜索充电站
  '/charging-station/search': '/app/charging-station/search',

  // 获取筛选条件配置
  '/charging-station/filter/config': '/app/charging-station/filter/config',

  // 获取充电站分类
  '/charging-station/categories': '/app/charging-station/categories',

  // 获取充电站标签
  '/charging-station/tags': '/app/charging-station/tags',
});

// 静态数据
const mockStations = [
  {
    id: '1',
    name: '万达广场充电站',
    address: '北京市朝阳区建国路93号万达广场B1层',
    lat: 39.9042,
    lng: 116.4074,
    distance: 1200,
    status: 'open',
    totalPiles: 12,
    availablePiles: 8,
    fastCharging: { total: 8, available: 5 },
    slowCharging: { total: 4, available: 3 },
    powerRange: '60KW-120KW',
    priceRange: '1.2-1.8元/度',
    currentPrice: 1.5,
    rating: 4.5,
    commentCount: 128,
    tags: ['24小时', '快充', '免费停车'],
    services: ['休息室', '便利店', '免费WIFI', '洗手间'],
    businessHours: '24小时营业',
    phone: '************',
  },
  {
    id: '2',
    name: '国贸中心充电站',
    address: '北京市朝阳区建国门外大街1号国贸中心地下停车场',
    lat: 39.9088,
    lng: 116.4317,
    distance: 2100,
    status: 'open',
    totalPiles: 20,
    availablePiles: 15,
    fastCharging: { total: 15, available: 12 },
    slowCharging: { total: 5, available: 3 },
    powerRange: '120KW-300KW',
    priceRange: '1.5-2.2元/度',
    currentPrice: 1.8,
    rating: 4.8,
    commentCount: 256,
    tags: ['超充', '地下停车', '收费停车'],
    services: ['休息室', '便利店', '洗车', '简餐', '免费WIFI'],
    businessHours: '06:00-24:00',
    phone: '************',
  },
  {
    id: '3',
    name: '首都机场T3充电站',
    address: '北京市顺义区首都机场T3航站楼停车场',
    lat: 40.0799,
    lng: 116.6031,
    distance: 35000,
    status: 'open',
    totalPiles: 30,
    availablePiles: 22,
    fastCharging: { total: 20, available: 15 },
    slowCharging: { total: 10, available: 7 },
    powerRange: '60KW-500KW',
    priceRange: '1.8-2.5元/度',
    currentPrice: 2.2,
    rating: 4.2,
    commentCount: 89,
    tags: ['24小时', '超充', '机场专用'],
    services: ['休息室', '便利店', '场站照明', '免费WIFI', '洗手间'],
    businessHours: '24小时营业',
    phone: '************',
  },
  {
    id: '4',
    name: '西单大悦城充电站',
    address: '北京市西城区西单北大街131号大悦城地下停车场',
    lat: 39.9097,
    lng: 116.3738,
    distance: 4500,
    status: 'closed',
    totalPiles: 8,
    availablePiles: 0,
    fastCharging: { total: 4, available: 0 },
    slowCharging: { total: 4, available: 0 },
    powerRange: '30KW-60KW',
    priceRange: '1.0-1.5元/度',
    currentPrice: 1.2,
    rating: 3.8,
    commentCount: 45,
    tags: ['慢充', '地下停车', '限时免费'],
    services: ['便利店', '免费WIFI'],
    businessHours: '08:00-22:00',
    phone: '************',
  },
  {
    id: '5',
    name: '奥林匹克公园充电站',
    address: '北京市朝阳区奥林匹克公园中心区',
    lat: 39.9928,
    lng: 116.3979,
    distance: 8900,
    status: 'open',
    totalPiles: 16,
    availablePiles: 12,
    fastCharging: { total: 10, available: 8 },
    slowCharging: { total: 6, available: 4 },
    powerRange: '120KW-240KW',
    priceRange: '1.3-1.9元/度',
    currentPrice: 1.6,
    rating: 4.6,
    commentCount: 167,
    tags: ['快充', '地上停车', '景区'],
    services: ['休息室', '场站照明', '免费WIFI', '洗手间'],
    businessHours: '06:00-23:00',
    phone: '************',
  },
];

/**
 * 获取充电站列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，默认1
 * @param {number} params.size 每页数量，默认10
 * @param {number} params.lat 纬度
 * @param {number} params.lng 经度
 * @param {string} params.keyword 搜索关键词
 * @param {string} params.sortBy 排序方式：distance(距离)、rating(评分)、price(价格)
 * @param {Array} params.filters 筛选条件数组
 */
export function getChargingStationList(params) {
  const url = api.render('/charging-station/list');
  const data = Object.assign(
    // {
    //   pageNum: 1,
    //   pageSize: 10,
    //   sortBy: 'distance',
    // },
    params,

    {
      pageNum: params.page,
      pageSize: params.size,
    }
  );

  // 先尝试调用真实API，失败时使用静态数据
  return api.postJSON(url, data).catch(() => {
    // API失败时使用静态数据
    return new Promise(resolve => {
      setTimeout(() => {
        let filteredStations = [...mockStations];

        // 应用筛选条件
        if (params.filters && params.filters.length > 0) {
          // 这里可以根据筛选条件过滤数据
          // 简单示例：如果有状态筛选，只显示营业中的
          const hasStatusFilter = params.filters.some(
            f => f.type === 'stationStatus'
          );
          if (hasStatusFilter) {
            filteredStations = filteredStations.filter(
              s => s.status === 'open'
            );
          }
        }

        // 排序
        if (params.sortBy === 'distance') {
          filteredStations.sort((a, b) => a.distance - b.distance);
        } else if (params.sortBy === 'rating') {
          filteredStations.sort((a, b) => b.rating - a.rating);
        }

        // 分页
        const page = params.page || 1;
        const size = params.size || 10;
        const start = (page - 1) * size;
        const end = start + size;
        const list = filteredStations.slice(start, end);

        resolve({
          list,
          total: filteredStations.length,
          page,
          size,
          hasMore: end < filteredStations.length,
        });
      }, 500); // 模拟网络延迟
    });
  });
}

/**
 * 获取充电站详情
 * @param {string} stationId 充电站ID
 */
export function getChargingStationDetail(stationId) {
  const url = api.render('/charging-station/detail', { id: stationId });

  // 先尝试调用真实API，失败时使用静态数据
  return api.doGet(url, { stationId }).catch(() => {
    // API失败时使用静态数据
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const station = mockStations.find(s => s.id === stationId);
        if (station) {
          resolve(station);
        } else {
          reject(new Error('充电站不存在'));
        }
      }, 300);
    });
  });
}

/**
 * 搜索充电站
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 搜索关键词
 * @param {number} params.lat 纬度
 * @param {number} params.lng 经度
 * @param {number} params.page 页码
 * @param {number} params.size 每页数量
 */
export function searchChargingStation(params) {
  const url = api.render('/charging-station/search');
  const data = Object.assign(
    {
      page: 1,
      size: 10,
    },
    params
  );

  // 先尝试调用真实API，失败时使用静态数据
  return api.doGet(url, data).catch(() => {
    // API失败时使用静态数据模拟搜索
    return new Promise(resolve => {
      setTimeout(() => {
        let results = [...mockStations];

        // 关键词搜索
        if (params.keyword) {
          const keyword = params.keyword.toLowerCase();
          results = results.filter(
            station =>
              station.name.toLowerCase().includes(keyword) ||
              station.address.toLowerCase().includes(keyword)
          );
        }

        // 分页
        const page = params.page || 1;
        const size = params.size || 10;
        const start = (page - 1) * size;
        const end = start + size;
        const list = results.slice(start, end);

        resolve({
          list,
          total: results.length,
          page,
          size,
          hasMore: end < results.length,
        });
      }, 400);
    });
  });
}

/**
 * 获取筛选条件配置
 */
export function getFilterConfig() {
  const url = api.render('/charging-station/filter/config');

  // 先尝试调用真实API，失败时使用静态数据
  return api.doGet(url).catch(() => {
    // API失败时返回筛选配置的静态数据
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          voltage: ['200V-500V', '700V以上'],
          power: ['15KW-50KW', '50KW-120KW', '120KW-300KW', '300KW-500KW'],
          businessHours: ['24小时', '营业中', '不确定'],
          highway: ['高速路充电桩', '靠近高速路充电桩'],
          parking: ['箱货', '大巴', '重卡', '地上', '地下'],
          parkingFee: ['限时免费', '收费'],
          operationType: ['自营', '非自营', '互联'],
          stationStatus: ['营业中', '停业中'],
          chargingType: [
            '直流快充',
            '直流慢充',
            '超级快充',
            '交流快充',
            '交流慢充',
          ],
          stationType: ['对外开放', '不对外开放'],
          services: [
            '休息室',
            '便利店',
            '洗车',
            '场站照明',
            '免费WIFI',
            '简餐',
            '洗手间',
          ],
          benefits: ['特来电', '即插即充', 'V2G'],
        });
      }, 200);
    });
  });
}

/**
 * 获取充电站分类
 */
export function getChargingStationCategories() {
  const url = api.render('/charging-station/categories');

  // 先尝试调用真实API，失败时使用静态数据
  return api.doGet(url).catch(() => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve([
          { id: 1, name: '快充站', count: 120 },
          { id: 2, name: '慢充站', count: 80 },
          { id: 3, name: '超充站', count: 45 },
          { id: 4, name: '综合站', count: 200 },
        ]);
      }, 200);
    });
  });
}

/**
 * 获取充电站标签
 */
export function getChargingStationTags() {
  const url = api.render('/charging-station/tags');

  // 先尝试调用真实API，失败时使用静态数据
  return api.doGet(url).catch(() => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve([
          '24小时',
          '快充',
          '慢充',
          '超充',
          '免费停车',
          '收费停车',
          '地上停车',
          '地下停车',
          '机场专用',
          '景区',
          '高速路',
        ]);
      }, 200);
    });
  });
}
