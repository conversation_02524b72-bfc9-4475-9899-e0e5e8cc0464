<template>
  <div class="page-loader">
    <div class="page-loader_inner">
      <i>-</i>
      <span></span>
      <i>-</i>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'page-loader',
  }
</script>

<style lang="scss" scoped>
  .page-loader{
    text-align: center;
    padding: 10px;
    margin-top: 30%;
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    .page-loader_inner{
      position: relative;
      display:inline-block;
      >span{
        display:block;
      }
      >span::before {
        content: "\e615";
        font-size:40px;
        font-family:iconfont;
        display:block;
        animation-direction: alternate;
      }
      /* 将animation使用在伪元素上, 在android4.4.4的webview中会导致webview崩溃 ,故此处改用标签*/
      >i {
        content: "-";
        display: block;
        font-size: 16px;
        line-height: 1;
        height: 10px;
        text-align: center;
        position:absolute;
      }
      >i:first-child{
        animation: road-left 300ms infinite ;
        left:-20%;
        bottom:10px;
      }
      >i:last-child{
        animation: road-right 300ms infinite ;
        right:-20%;
        bottom:10px;
      }
    }
  }

  @keyframes shake{
    0%{
      transform: translateX(2px);
    }
    100%{
      transform: translateX(-2px);
    }
  }

  @keyframes road-left{
    from{
      opacity:1;
    }
    to{
      bottom:30px;
      left:35%;
      opacity:0;
    }
  }

  @keyframes road-right{
    from{
      opacity:1;
    }
    to{
      bottom:30px;
      opacity:0;
      right:35%;
    }
  }
  .loading{
    margin-top:30%;
  }
</style>
