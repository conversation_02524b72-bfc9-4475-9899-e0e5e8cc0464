<template>
  <van-popup
    v-model="showPopup"
    overlay
    closeable
    round
    safe-area-inset-bottom
    position="bottom"
    get-container="body"
    class="rights-pop"
    @close="handleClose"
  >
    <div class="pop-title">
      <h2 class="panel-title">{{ title }}</h2>
    </div>
    <div class="pop-content">
      <div class="receive-desc">
        <van-form validate-first ref="form">
          <van-field
            v-model="form.phone"
            name="phone"
            label=""
            type="tel"
            maxlength="11"
            clearable
            :border="false"
            autocomplete="off"
            :formatter="formatterTel"
            placeholder="请输入手机号"
            :rules="[
              {
                required: true,
                pattern: patternTel,
                message: '请输入正确的手机号',
              },
            ]"
          />
          <van-field
            v-model="form.code"
            center
            clearable
            label=""
            type="digit"
            maxlength="6"
            :border="false"
            autocomplete="off"
            placeholder="请输入短信验证码"
          >
            <template #button>
              <!-- <van-button size="small" type="primary">发送验证码</van-button> -->

              <div class="countdown">
                <div v-show="!countDownFinished" class="count-down-wrap">
                  <van-count-down
                    ref="countDown"
                    :time="time"
                    format="ss"
                    @finish="countDownFinish"
                  />S后重新发送
                </div>
                <!-- <span v-show="countDownFinished" @click="getCode">发送验证码</span> -->
                <van-button
                  v-show="countDownFinished"
                  @click="getCode"
                  color="#FD4925"
                  size="mini"
                  plain
                  type="info"
                  >发送验证码</van-button
                >
              </div>
            </template>
          </van-field>
        </van-form>
        <div class="agreement">
          <van-checkbox
            v-model="checked"
            shape="square"
            checked-color="#FD4925"
            icon-size="16px"
            >已阅读并同意</van-checkbox
          >
          <span @click="toAgreement">《交广领航用户协议》</span>
        </div>
      </div>
    </div>
    <div class="pop-bottom-btn">
      <van-button
        class=""
        round
        block
        type="danger"
        loading-text="登录中"
        :loading="submitLoading"
        @click="submit"
        >登录</van-button
      >
    </div>
  </van-popup>
</template>
<script>
import { formatDate } from '@/utils';
import { mixinAuthRouter } from '@/mixins';
import { dialog, loading, dialogLoginSuccess } from '@/bus';
import { mixinForm } from '@pkg/finance/mixins/form.js';
import { getAuthPhoneCode, loginByPhoneCode } from '@/api/modules/account';
import { saveTokenToStorage } from '@/store/storage';

import {
  Checkbox,
  Button,
  Form,
  Field,
  Cell,
  CountDown,
  Toast,
  Popup,
} from 'vant';

export default {
  name: 'LoginDialog',
  props: {
    title: {
      type: String,
      default: '',
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [mixinAuthRouter, mixinForm],
  components: {
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Checkbox.name]: Checkbox,
    [Form.name]: Form,
    [Field.name]: Field,
    [Cell.name]: Cell,
    [CountDown.name]: CountDown,
    [Toast.name]: Toast,
  },
  data() {
    return {
      showPopup: this.show,
      form: {
        phone: '',
        code: '',
      },
      time: 0,
      countDownFinished: false,
      checked: true,
      submitLoading: false,
    };
  },
  computed: {},
  watch: {
    show(val) {
      this.showPopup = val;
    },
  },
  mounted() {},
  methods: {
    ...{ formatDate },
    countDownFinish() {
      this.time = 0;
      this.countDownFinished = true;
    },
    startCountDown() {
      this.time = 60000;
      this.countDownFinished = false;
      this.$nextTick(() => {
        this.$refs.countDown && this.$refs.countDown.start();
      });
    },
    getCode() {
      let isPhoneOk = this.patternTel.test(this.form.phone);
      if (!isPhoneOk) {
        this.$toast('请输入正确的手机号');
        return;
      }
      this.getSmsCode();
    },
    getSmsCode() {
      if (!this.form.phone) {
        this.$toast('请输入手机号');
        return;
      }

      // setTimeout(() => {
      //   this.startCountDown();
      // }, 1000);
      return getAuthPhoneCode({ value: this.form.phone })
        .then(res => {
          if (res === 0) {
            this.startCountDown();
          } else {
            this.countDownFinished = true;
          }
        })
        .catch(e => {
          e && dialog().alert(e);
        });
    },
    submit(params) {
      // if (!this.checked) {
      //   this.$toast('请勾选服务协议');
      //   return
      // }
      this.$refs.form
        .validate()
        .then(res => {
          this.submitLoading = true;
          let formatParams = {
            phone: this.form.phone,
            code: this.form.code,
          };
          // 登录
          loginByPhoneCode(formatParams)
            .then(res => {
              this.submitLoading = false;

              // 更新sessionStorage中session
              saveTokenToStorage(res.sessionid);

              this.syncAppUserState()
                .then(res => {
                  this.handleClose();
                  this.$toast('登录成功');
                  dialogLoginSuccess();
                })
                .catch(err => {
                  console.error(err);
                });
            })
            .catch(err => {
              this.submitLoading = false;
              dialog('提示').alert(err);
            });
        })
        .catch(e => {
          this.submitLoading = false;
          this.$toast(e[0].message);
        });
    },
    syncAppUserState() {
      return this.$_auth_checkSession(true).then(res => {
        // 若已登录，同步用户信息
        if (res.ok) {
          return this.$_auth_syncUserInfo();
        }
      });
    },
    toAgreement() {
      let url = 'https://radio.jgrm.net/actions/app/agreement/index.html';
      this.$_router_pageTo(url, {
        title: '用户协议',
        titleBar: true,
        progressBar: true,
        autoUpdateTitle: true,
        shareButton: false,
      });
    },
    toPage(url) {
      this.handleClose();
      this.$_auth_push(url);
    },
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.rights-pop ::v-deep {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow-y: visible;
  .pop-title {
    height: 54px;
    box-sizing: border-box;
    text-align: center;
  }
  .panel-title {
    height: 100%;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
  }
  .pop-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
    line-height: 1;
    background: #f3f3f3;
    &::-webkit-scrollbar {
      width: 0 !important;
    }
  }
  .receive-status {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    color: #333333;
    margin-bottom: 20px;
  }
  .receive-desc {
    font-size: 14px;
    color: #333333;
    text-align: left;
    line-height: 1.5;
  }
  .receive-flag {
    padding: 20px;
    background: #fef4ee;
    border-radius: 5px;
    line-height: 1;
    text-align: center;
    .receive-flag-title {
      font-size: 15px;
      color: #242435;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .receive-flag-content {
      font-size: 12px;
      color: #242435;
      line-height: 18px;
    }
  }
  .pop-bottom-btn {
    padding: 5px 15px 20px;
    background: #f3f3f3;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    .van-button {
      flex: 1;
      width: 100%;
      font-size: 15px;
      line-height: 40px;
      height: 40px;
      &:not(:first-child) {
        margin-left: 15px;
      }
    }
    .close-btn {
      background: #f3f3f3;
      border-color: #f3f3f3;
      color: #333333;
    }
  }
  .van-popup--center.van-popup--round {
    border-radius: 10px;
  }
  .van-icon-cross {
    top: 18px;
    font-size: 16px;
    line-height: 1;
  }
  .close-icon {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    margin: 0 auto;
  }
}
.van-cell {
  border-radius: 10px;
  &:not(:last-child) {
    margin-bottom: 15px;
  }
  ::v-deep .van-field__error-message {
    display: none;
  }
  ::v-deep .van-field__button {
    font-size: 12px;
    padding-left: 15px;
    .van-button {
      border: none;
      background: transparent;
    }
    .van-button::before {
      display: none;
    }
  }
}
.countdown {
  font-size: 12px;
  color: $lh-2022-primary-color;
  line-height: 18px;
  text-align: right;
}
.count-down-wrap {
  color: $lh-2022-primary-color;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.van-count-down {
  color: $lh-2022-primary-color;
  font-size: 12px;
  line-height: 1;
}
.agreement {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 1;
  padding-top: 6px;
  margin-top: 10px;
  .van-checkbox {
    display: inline-flex;
  }
  &::v-deep .van-checkbox__label {
    font-weight: 400;
    color: #333333;
    line-height: 1;
  }
  > span {
    color: $lh-2022-primary-color;
  }
}
</style>
