<template>
  <container
    class="account-my-car-edit"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
  >
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div
          v-if="carModel && carModel.id"
          class="flex-row card car-top"
          @click="goSelectNewCarModel"
        >
          <c-picture class="card-logo" :src="carModel.brandLogo"></c-picture>
          <div class="card-content">
            <h4 class="card-content__title">{{ carModel.brand }}</h4>
            <p class="card-content__desc">{{ carModel.carSysName }}</p>
          </div>
        </div>
        <div class="weui-cells weui-cells_form wei-box">
          <IdentifyVehicleLicense @afterRead="handAfterRead" />
          <div class="weui-cell weui-cell_select car-model-select">
            <div class="weui-cell__hd">
              <label for="" class="weui-label">车型</label>
            </div>
            <div class="weui-cell__bd" @click="goSelectCarModel">
              <span
                class="car-model-name"
                v-html="carModel.name || '请选择'"
              ></span>
            </div>
          </div>
          <div class="weui-cell">
            <div class="weui-cell__hd">
              <label for="" class="weui-label">车牌号码</label>
            </div>
            <div class="weui-cell__bd carNumber">
              <!-- <input
                class="weui-input"
                type="number"
                v-model="form.number"
                placeholder="请输入车牌号"
                readonly
                @click="plateShow = true"
              /> -->
              <van-field
                v-model="form.number"
                readonly
                placeholder="请输入车牌号码"
                @click="plateShow = true"
              />
              <!-- <car-plate-number-input v-model="form.number"></car-plate-number-input> -->
            </div>
          </div>
          <template>
            <div class="weui-cell weui-cell_select">
              <div class="weui-cell__hd">
                <label for="" class="weui-label">注册时间</label>
              </div>
              <div class="weui-cell__bd">
                <!--<span>{{formatDate(form.date)}}</span>-->
                <!-- <date-picker
                  v-model="form.date"
                  :options="{ id: Date.now(), container: '#date-picker-container' }"
                ></date-picker>
                <div v-if="status == AppStatus.READY" id="date-picker-container"></div> -->

                <date-picker
                  v-model="form.registDate"
                  :options="{
                    id: Date.now(),
                    container: '#date-picker-container',
                  }"
                ></date-picker>
                <div
                  v-if="status == AppStatus.READY"
                  id="date-picker-container"
                ></div>
              </div>
            </div>

            <div class="weui-cell weui-cell_select">
              <div class="weui-cell__hd">
                <label for="" class="weui-label">检验有效期</label>
              </div>
              <div class="weui-cell__bd">
                <!--<span>{{formatDate(form.date)}}</span>-->
                <!-- <date-picker
                  v-model="form.date"
                  :options="{ id: Date.now(), container: '#date-picker-container' }"
                ></date-picker>
                <div v-if="status == AppStatus.READY" id="date-picker-container"></div> -->

                <!-- <date-picker
                  v-model="form.registDate"
                  :options="{ id: Date.now(), container: '#date-picker-container' }"
                ></date-picker>
                <div v-if="status == AppStatus.READY" id="date-picker-container"></div> -->

                <!-- <date-picker
                  v-model="form.effectiveDate"
                  format="YYYY-MM"
                  :options="{
                    id: Date.now() + 1,
                    container: '#date-picker-container',
                    start: minDate,
                    end: maxDate,
                    className: 'date-picker-effectiveDate',
                  }"
                ></date-picker> -->
                <date-picker
                  v-model="form.effectiveDate"
                  format="yyyy-MM"
                  :options="{
                    id: Date.now(),
                    start: minDate,
                    end: maxDate,
                    container: '#date-picker-container',
                    className: 'date-picker-effectiveDate',
                  }"
                ></date-picker>
                <div
                  v-if="status == AppStatus.READY"
                  id="date-picker-container"
                ></div>
              </div>
            </div>
            <!-- <div class="weui-cell">
              <div class="weui-cell__hd"><label class="weui-label">行驶里程</label></div>
              <div class="weui-cell__bd w-line-box">
                <input
                  class="weui-input"
                  type="number"
                  v-model="form.distance"
                  pattern="[0-9]*"
                  placeholder="选填"
                />
                <span class="w-unit">km</span>
              </div>
            </div> -->
            <!-- <div class="weui-cell">
              <div class="weui-cell__hd">
                <label class="weui-label">车辆识别码</label>
              </div>
              <div class="weui-cell__bd w-border w-line-box">
                <input
                  class="weui-input"
                  type="number"
                  v-model="form.id6"
                  pattern="[0-9]*"
                  placeholder="选填"
                />
                <van-icon name="info-o" class="w-unit" @click="handleIcon" />
              </div>
            </div> -->
          </template>
          <!-- <div class="weui-cell">
            <div class="weui-cell__hd">
              <label class="weui-label">设为默认车辆</label>
            </div>
            <div class="weui-cell__bd w-border w-right">
              <van-switch
                v-model="checked"
                size="18px"
                active-color="#FD4925"
                inactive-color="#DDDDDD"
              />
            </div>
          </div> -->
        </div>

        <div class="button-sp-area">
          <div class="btn" href="javascript:;" @click="submit">保 存</div>
        </div>
      </template>
      <plate-number-input
        @typeChange="typeChange"
        v-if="plateShow"
        :plate="plateNo"
        :isEnergy="isEnergy"
        @export="setPlate"
        @close="plateShow = false & close()"
      ></plate-number-input>
      <!-- <div class="popup-box">
        <van-action-sheet v-model="showSheet" title="车辆识别代号指示">
          <div class="popup-content">
            <img src="@/assets/images/account/bg_identification.png" />
          </div>
        </van-action-sheet>
      </div> -->
    </content-view>
  </container>
</template>
<style>
.date-picker-effectiveDate .weui-picker__group:nth-child(3) {
  display: none !important;
}
</style>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.content-wrapper {
  background: #f3f3f3;
}
.weui-cell_select-before .weui-select {
  padding-left: 0;
  width: 100px;
}

.weui-select {
  height: auto;
  line-height: auto;
}
.car-top {
  margin-top: 16px;
}
.wei-box {
  margin-top: 10px;
}
.weui-cell {
  line-height: 16px;
  padding: 16px 15px 0 18px;
  border-top: none;
  .weui-label {
    width: 90px;
    // font-weight: bold;
    font-size: 15px;
    line-height: 15px;
    color: #333333;
  }
}
.weui-cell__hd {
  padding-bottom: 15px;
}
.weui-cell__bd {
  padding-bottom: 15px;
  border-bottom: 1px solid #eeeeee;
}
.w-line-box {
  display: flex;
  align-items: center;
  .w-unit {
    flex-shrink: 0;
  }
}
.w-right {
  display: flex;
  justify-content: flex-end;
}
.popup-box {
  .van-action-sheet__header {
    border-bottom: 1px solid #eee;
  }
  .van-action-sheet__close {
    line-height: inherit;
  }
  .popup-content {
    padding: 15px 50px 45px 50px;
    img {
      width: 100%;
    }
  }
}

.w-border {
  border-bottom: 0;
}
.weui-cell_select .weui-cell__bd:after {
  border-color: #333;
}

.carNumber .van-field {
  padding: 0;
  line-height: 1;
}
.card {
  align-items: center;
  padding: 20px 16px 21px 15px;
  background: white;
  .card-logo {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }
  .card-content {
    flex: 1;
    position: relative;
    .card-content__title {
      font-size: 18px;
      // font-weight: bold;
      color: #333333;
      line-height: 18px;
    }
    &::after {
      content: '\e605';
      font-family: iconfont;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      color: #333;
      font-weight: bold;
    }
    .card-content__desc {
      color: gray;
      font-size: 13px;
      color: #666666;
      line-height: 13px;
      margin-top: 13px;
    }
  }
}
.car-model-name {
  font-size: 16px;
  // font-weight: bold;
  color: #333333;
  line-height: 16px;
  // margin-right: 10px;
}
.car-model-select {
  .weui-cell__bd {
    padding-right: 10px;
  }
}
.select-inline {
  display: inline-block;
  padding-right: 15px;
  position: relative;
  select {
    -webkit-appearance: none;
    border: 0;
    font-size: 1em;
    height: 26px;
    line-height: 26px;
    min-width: 20px;
  }
  &::after {
    content: ' ';
    display: inline-block;
    border-style: solid;
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px 5px 0;
    border-color: black transparent transparent;
  }
}

.button-sp-area {
  height: 44px;
  position: absolute;
  bottom: 36px;
  width: 100%;
  .btn {
    background: #fd4925;
    border-radius: 22px;
    line-height: 44px;
    color: #ffffff;
    font-size: 18px;
    text-align: center;
    margin: 0 15px;
  }
}
.field-row {
  padding: 15px;
}
</style>
<script>
import { formatDate } from '@/utils';
import Runner from '@/lib/runner';
import { AppStatus, CarType } from '@/enums';
import FormValidater from '@/model/FormValidater';
import { blobToBase64 } from '@/utils/image';
import {
  getMyCar,
  addMyCarV2,
  addMyCarNewEnergy,
  updateMyCar,
  removeMyCar,
  getCarList,
  getCarModelInfo,
  getNewEnergyCarModelInfo,
} from '@/api';
import { identifyVehicleLicense } from '@pkg/vehicle-business/api';
import dayjs from 'dayjs';
import { listen, Events, loading, dialog, toast, back } from '@/bus';

// 组件
import { Field, Icon, ActionSheet, Switch, Uploader } from 'vant';
import DatePicker from '@/components/DatePicker';
// import CarPlateNumberInput from "@/components/CarPlateNumberInput";
import PlateNumberInput from '@/components/PlateNumberInput';
import IdentifyVehicleLicense from '@pkg/vehicle-business/components/IdentifyVehicleLicense';

const runner = new Runner();

// 页面模式：添加模式和编辑模式
const PageMode = {
  ADD: 'add',
  EDIT: 'edit',
};

export default {
  name: 'account-my-car-edit',
  components: {
    DatePicker,
    PlateNumberInput,
    [Field.name]: Field,
    [Icon.name]: Icon,
    [ActionSheet.name]: ActionSheet,
    [Switch.name]: Switch,
    [Uploader.name]: Uploader,
    IdentifyVehicleLicense,
  },
  data() {
    const pageMode = this.$route.params.model ? PageMode.ADD : PageMode.EDIT;
    const pageTitle = pageMode === PageMode.ADD ? '添加爱车' : '修改爱车';
    const year = new Date().getFullYear();
    const month = new Date().getMonth() + 1;
    const minDate = year - 1 + '-' + month + '-' + '01';
    const maxDate = year + 6 + '-' + month + '-' + '01';
    console.log(year, month);
    return {
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 1,
      CAR_ADD_LIMIT: 5,
      PageMode,
      pageMode: pageMode,
      plateNo: '豫',
      PageCur: 1,
      plateShow: false,
      showSheet: false,
      // checked: false,
      form: {
        id: null,
        type: null,
        number: null,
        id6: null,
        // date: null,
        registDate: null,
        distance: null,
        effectiveDate: null,
        ownerName: null,
      },
      minDate: minDate,
      maxDate: maxDate,
      carModel: {},
      page: {
        car: null,
      },
      pageTitle: pageTitle,
      loading: false,
    };
  },
  computed: {
    carModelName() {
      const name = this.carModel.name || '';
      const systemName = this.carModel.carSysName;
      return name.replace(systemName, '');
    },
    formAddCar() {
      return {
        ownerName: this.form.ownerName,
        identificationCode: this.form.id6,
        number: this.form.number,
        effectiveDate: this.form.effectiveDate,
        registDate: this.form.registDate,
        model: this.carModel.id,
      };
    },
  },
  mounted() {
    const sourceId = this.$options.name;
    listen(Events.AFTER_CAR_MODEL_SELECTED, sourceId, carModel => {
      runner.set(this, () => {
        if (this.status != AppStatus.READY) return;
        this.$nextTick(() => {
          this.carModel = carModel;
        });
      });
    });
  },
  watch: {
    carModel(carModel, oldVal) {
      if (carModel) {
        this.form.type = carModel.id;
      }
    },
  },
  methods: {
    // onConfirm(result) {
    //   // let date = new Date(result[0].value, result[1].value).getTime();
    //   // this.form.effectiveDate = date;
    // },
    // onChange(result) {
    //   let date = new Date(result[0].value, result[1].value).getTime();
    //   this.form.effectiveDate = date;
    // },
    initPageData() {
      if (this.pageMode === PageMode.ADD) {
        const id = this.$route.params.model;
        let fetchFunc =
          this.$route.query.carType == CarType.NEW_ENERGY
            ? getNewEnergyCarModelInfo
            : getCarModelInfo;
        return fetchFunc(id).then(
          res => {
            this.carModel = res;
            this.isEnergy = this.$route.query.carType == CarType.NEW_ENERGY;
            this.status = AppStatus.READY;
          },
          err => {
            console.error(err);
            this.status = AppStatus.ERROR;
          }
        );
      } else if (this.pageMode === PageMode.EDIT) {
        const id = this.$route.params.id;
        return getMyCar(id).then(
          res => {
            this.page.car = res;
            const car = res;
            this.carModel = car.carModelInfo || {};
            this.form = {
              id: id,
              type: car.modelId,
              distance: car.mileage,
              number: car.carRegistNumber,
              date: car.buyCarTime,
              id6: car.identificationCode,
              registDate: Number(car.registDate),
              effectiveDate: Number(car.effectiveDate),
              ownerName: car.ownerName,
            };
            this.isEnergy = res.newEnergy;
            this.plateNo = car.carRegistNumber;
            this.status = AppStatus.READY;
          },
          err => {
            console.error(err);
            this.status = AppStatus.ERROR;
          }
        );
      }
    },
    init() {
      return this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      // this.initPageData();
      runner.run();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    // validateForm(form) {
    //   const rules = {
    //     number: {
    //       name: "车牌号码",
    //       required: true,
    //       emptyTip: "请输入车牌号码",
    //     },
    //     effectiveDate:{
    //       name: "车牌号码",
    //       required: true,
    //       emptyTip: "请输入车牌号码",
    //     },
    //     registDate: this.form.registDate,
    //   };
    //   return new FormValidater(rules).validate(form);
    // },
    setPlate(plate) {
      if (plate.length >= 7) this.form.number = plate;
      this.plateShow = false;
    },
    typeChange(e) {
      this.PageCur = e;
      this.plateNo = '';
    },
    close() {
      this.PageCur = 1;
    },
    handleIcon() {
      this.showSheet = true;
    },
    submit() {
      if (this.loading) return;
      this.loading = true;
      if (this.pageMode === PageMode.ADD) {
        this.addCar();
      } else if (this.pageMode === PageMode.EDIT) {
        this.updateCar();
      }
    },
    updateCar() {
      const carId = this.$route.params.id;
      const params = {
        ...this.form,
        id: carId,
      };

      if (!params.number) {
        toast().tip('请输入车牌号码');
        return false;
      }
      if (!params.registDate) {
        toast().tip('请选择注册时间');
        return false;
      }
      if (!params.effectiveDate) {
        toast().tip('请选择校验有效期');
        return false;
      }

      loading(true, '正在保存...');
      updateMyCar(params).then(
        res => {
          loading(false);
          if (res && res.lhxNum) {
            toast().tip(`奖励金币${res.lhxNum}`);
            setTimeout(() => {
              back();
            }, 2000);
            return;
          }
          this.loading = false;
          back();
        },
        err => {
          loading(false);
          this.loading = false;
          err &&
            dialog().alert(err, {
              title: '',
            });
        }
      );
    },
    addCar() {
      if (!this.formAddCar.number) {
        toast().tip('请输入车牌号码');
        return false;
      }
      if (!this.formAddCar.registDate) {
        toast().tip('请选择注册时间');
        return false;
      }
      if (!this.formAddCar.effectiveDate) {
        toast().tip('请选择校验有效期');
        return false;
      }
      loading(true, '正在添加...');
      let fetchFunc =
        this.$route.query.carType == CarType.NEW_ENERGY
          ? addMyCarNewEnergy
          : addMyCarV2;
      fetchFunc(this.formAddCar)
        .then(res => {
          loading(false);
          if (res && res.lhxNum) {
            toast().tip(`奖励金币${res.lhxNum}`);
            setTimeout(() => {
              back();
            }, 2000);
            return;
          }
          this.loading = false;
          back();
        })
        .catch(e => {
          loading(false);
          this.loading = false;
          toast().tip('添加失败！');
        });
    },
    setCarType(item) {
      console.log('item', item);
      this.form.type = item[0];
    },
    go(url) {
      this.$router.push(url);
    },
    goSelectNewCarModel() {
      const from = this.$options.name;
      const carType =
        (this.page.car && this.page.car.newEnergy && CarType.NEW_ENERGY) ||
        this.$route.query.carType ||
        '';
      this.go(`/car/brands?from=${from}&carType=${carType}`);
    },
    goSelectCarModel() {
      if (!this.carModel.id) return this.goSelectNewCarModel();
      const from = this.$options.name;
      const carType =
        (this.page.car && this.page.car.newEnergy && CarType.NEW_ENERGY) ||
        this.$route.query.carType ||
        '';
      this.go(
        `/car/series/${this.carModel.carSystemId}/models/-1?from=${from}&carType=${carType}`
      );
    },
    formatDistance(value) {
      const result = parseInt(value / 1000);
      return `${result}km`;
    },
    formatDate(...args) {
      return formatDate(...args);
    },
    handAfterRead(file) {
      this.form = {
        ...this.form,
        ownerName: file.owner,
        number: file.carNo,
        registDate: file.registDate,
        id6: file.vin,
      };
    },
  },
};
</script>
