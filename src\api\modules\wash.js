import { doGet, doPost } from '../request/';
import { getCity } from '@/store/storage';
import { getCityAreas } from './share';
import { getGeoData } from '@/bridge';
import { isInWeApp } from '@/common/env';
import APIs from '../apis';

APIs.extend({
  // 获取平台洗车介绍
  '/wash/info': '/app/car/wash/introduce',

  // 获取汽车美容类别
  '/beauty/category': '/app/car/beauty/category/list',

  // 获取洗车服务介绍信息
  '/about/wash': '/app/car/wash/service/info',

  // 获取商家洗车套餐列表
  '/wash/packages': '/app/car/wash/servs',

  // 获取商家美容业务列表
  '/shop/beauty': '/app/car/business/beauty/categoty',

  // 获取洗车门店列表
  '/shops/carwash': '/app/car/wash/business/list',

  // 获取洗车门店搜索建议
  '/shops/wash/suggestion': '/app/car/wash/business/like/list',

  // 提交洗车订单
  '/order/submit/carwash': '/app/car/wash/order/create',

  // 提交1元洗车订单
  '/order/1yuan/submit': '/app/car/onedollar/wash/order/create',
})

/**
 * 获取洗车门店页面数据
 * 1.待服务订单数？;
 * 2.洗车介绍数据？;
 * 3.洗车门店可筛选的地区列表;
 * 4.首屏洗车门店列表？;
 */
export async function getCarWashViewData(center) {
  // const counts = await doGet(APIs.get('/orders/count/unused'));
  const city = getCity().id;
  const params = {
    ...center,
    city
  }
  // let areaList = await doGet(APIs.get('/city/areas'), params);
  const areas = await getCityAreas(params);
  const categories = await getBeautyCategories();
  const shops = await getShopsOfCarWash(params);
  return Promise.resolve({
    counts: {},
    areas,
    shops,
    categories,
  });
}

/**
 * 查询洗车门店列表
 * @param {object} options 查询参数
 */
export async function getShopsOfCarWash(options = {}) {
  const city = getCity().id;
  let defaults = {
    /* lng: geo.longitude,
    lat: geo.latitude, */
    city,
    area: 0,
    sort: 'default', // default | sales | cmt | near
    rows: 10,
    page: 1
  };
  const params = Object.assign(defaults, options);
  const shops = await doGet(APIs.get('/shops/carwash'), params);
  return shops;
}

export async function getShopMapViewData(bid) {
  const center = await getGeoData();
  const { longitude: lng, latitude: lat } = center;
  const shop = await doGet(APIs.get('/shop'), { bid, lng, lat });
  return Promise.resolve({ shop, center });
}

/**
 * 获取商家美容业务列表
 */
export function getShopBeautyServices(bid, categoryId) {
  return doGet(APIs.get('/shop/beauty'), { bid, categoryId });
}

/**
 * 获取汽车美容业务类别列表
 */
export function getBeautyCategories(params) {
  return doGet(APIs.get('/beauty/category'), params);
}

/**
 * 获取汽车洗车服务介绍
 */
export function getCMIData() {
  return doGet(APIs.get('/about/wash'));
}

/**
 * 提交1元洗车订单
 * 由于小程序域名限制，1元洗车仅限银联支付不支持在小程序环境中使用
 * @param {*} data 
 */
export function submitOneYuanOrder(data) {
  if (isInWeApp) {
    return Promise.reject('小程序暂不支持此项业务，请在交广领航APP下单支付');
    // return Promise.reject('因小程序限制，此业务不支持在小程序中使用，请在交广领航App中下单购买！');
  }
  return doPost(APIs.get('/order/1yuan/submit'), data);
}

/**
 * 提交美容订单
 * @param {object} data 订单数据
 * @param {string} data.id 商品id
 * @param {string} data.bid 商家id
 * @param {object} extra 附加信息
 */
export function submitCarBeautyOrder(data, extra = {}) {
  const url = APIs.get('/order/submit/carwash');
  const params = Object.assign({}, data, {
    // bid, id, 
    createChannel: 0, 
    ...extra,
  });
  return doPost(url, params);
}

/**
 * 获取洗车搜索建议
 * @param {string} type
 */
export function getSuggestions(data) {
  const city = getCity().id;
  const params = {
    city,
    ...data,
  }
  return doGet(APIs.get('/shops/wash/suggestion'), params);
}
