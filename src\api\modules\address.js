import { doGet, doPost } from '../request/';
import APIs from '../apis';

const storage = localStorage;
const ADDRESS_KEY = '_ls_address_list';

function responseJSON(code = 200, data = {}, msg = '') {
  return new Promise((resolve, reject) => {
    code === 200 ? resolve(data) : reject(`${msg}(${code})`);
  });
}

function createUUID() {
  return String(`${Date.now()}_${Math.random() * 100000000}`);
}

// 获取列表
function getAddressListFromStorage() {
  const list = JSON.parse(storage.getItem(ADDRESS_KEY)) || [];
  return list.filter(item => {
    return !!item.id;
  });
}

// 保存列表
function saveAddressListToStorage(list) {
  storage.setItem(ADDRESS_KEY, JSON.stringify(list));
}

const Commonds = {
  getAddressList() {
    return getAddressListFromStorage();
  },
  // 添加地址
  addAddressIntoStorage(data) {
    const list = getAddressListFromStorage();
    const saveData = Object.assign(
      {
        id: createUUID(),
        t: Date.now(),
        lastUpdate: Date.now(),
      },
      data
    );
    list.push(saveData);
    saveAddressListToStorage(list);
  },
  // 更新地址详情
  updateAddressIntoStorage(id, data) {
    const list = getAddressListFromStorage();
    const list2 = list.map(item => {
      if (item.id == id) {
        data.lastUpdate = Date.now();
        return data;
      }
      return item;
    });
    saveAddressListToStorage(list2);
  },
  // 删除地址
  removeAddressFromStorage(id) {
    const list = getAddressListFromStorage();
    const list2 = list.filter(item => item.id !== id);
    saveAddressListToStorage(list2);
  },
  // 获取地址详情
  getAddressFromStorage(id) {
    const list = getAddressListFromStorage();
    const address = list.filter(item => item.id === id);
    return address.length ? address[0] : null;
  },
};

function proxyCommond(cmd, ...args) {
  try {
    const result = Commonds[cmd](...args);
    return responseJSON(200, result, '');
  } catch (e) {
    console.error(e);
    return responseJSON(500, null, '操作失败');
  }
}

APIs.extend({
  '/account/address/list': '/app/goods/mine/address/list',
  '/account/address/info': '/app/goods/mine/address/detail',
  '/account/address/setdefault': '/app/goods/mine/address/set/defaulted',
  '/account/address/add': '/app/goods/mine/address/add',
  '/account/address/update': '/app/goods/mine/address/update',
  '/account/address/remove': '/app/goods/mine/address/delete',
  '/selfPickUp/reciveAddress/list': '/app/goods/selfPickUp/reciveAddress/list',
  '/selfPickUp/detail': '/app/goods/selfPickUp/detail',
});

/**
 * 获取地址详情
 */
export function getAddress(id) {
  const url = APIs.get('/account/address/info');
  // return proxyCommond('getAddressFromStorage', id);
  return doGet(url, { id }).then(res => {
    return {
      ...res,
      name: res.recvName,
      area: res.county,
    };
  });
}

/**
 * 更新收货地址
 * @param {object} address
 */
export function updateAddress(address) {
  const url = APIs.get('/account/address/update');
  // return proxyCommond('updateAddressIntoStorage', data.id, data);
  const info = {
    id: address.id,
    recvName: address.name,
    telephone: address.phone,
    address: address.address,
    postareaProv: address.province,
    postareaCity: address.city,
    postareaCountry: address.area,
    setDefault: address.setDefault,
    flag: address.flag,
  };
  return doPost(url, info);
}

/**
 * 添加收货地址
 * @param {object} address
 */
export function addAddress(address) {
  const url = APIs.get('/account/address/add');
  // return proxyCommond('addAddressIntoStorage', data);
  const info = {
    recvName: address.name,
    telephone: address.phone,
    address: address.address,
    postareaProv: address.province,
    postareaCity: address.city,
    postareaCountry: address.area,
    setDefault: address.setDefault,
    flag: address.flag,
  };
  return doPost(url, info);
}

/**
 * 删除收货地址
 * @param {string} id
 */
export function removeAddress(id) {
  const url = APIs.get('/account/address/remove');
  // return proxyCommond('removeAddressFromStorage', id);
  return doPost(url, { id });
}

/**
 * 设置默认收货地址
 * @param {string} id
 */
export function setAddressAsDefault(id) {
  const url = APIs.get('/account/address/setdefault');
  // return proxyCommond('removeAddressFromStorage', id);
  return doPost(url, { id });
}

/**
 * 获取地址列表
 * @param { object } params
 */
export function getAddressList(params) {
  const url = APIs.get('/account/address/list');
  // return proxyCommond('getAddressList');
  const data = Object.assign(
    {
      rows: 20,
      page: 1,
    },
    params
  );
  return doGet(url, data).then(res => {
    return res.map(item => {
      return {
        ...item,
        name: item.recvName,
        area: item.county,
      };
    });
  });
}

/**
 * 获取自提地址列表
 * @param { number } goodsId
 * @param { number } lat 维度
 * @param { number } lng 经度
 */
export function getSelfPickUpAddressList(params) {
  const url = APIs.get('/selfPickUp/reciveAddress/list');
  const data = Object.assign(
    // {
    //   rows: 20,
    //   page: 1,
    // },
    params
  );
  return doGet(url, data);
}

/**
 * 获取地址详情
 */
export function getSelfPickUpAddress(id) {
  const url = APIs.get('/selfPickUp/detail');
  // return proxyCommond('getAddressFromStorage', id);
  return doGet(url, { id });
}
