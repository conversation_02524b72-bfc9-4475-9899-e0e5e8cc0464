<template>
  <div class="cell-box">
    <p class="title">订单：{{ order.id }}</p>
    <goods-item
      v-if="order.orderRefGoodsSpec"
      :showTitle="false"
      class="goods-info"
      :goods="order.orderRefGoodsSpec"
      @click.native="go(`/mall/goods/${order.goodsId}`)"
    ></goods-item>
    <template v-if="!hidePrice">
      <van-divider :style="{ margin: '15px 0' }" />
      <template
        v-if="order.discountInfoList && order.discountInfoList.length > 0"
      >
        <template v-for="(item, index) in order.discountInfoList">
          <div :key="index" v-if="Math.abs(item.amount) > 0" class="dis-box">
            <label class="dis-label">{{ item.name }}</label>
            <div class="dis-amount">
              -<span class="rmb">{{ Math.abs(item.amount) }}</span>
            </div>
          </div>
        </template>
        <template v-if="order.memberShipTitle">
          <div class="dis-box">
            <label class="dis-label">{{ order.memberShipTitle }}</label>
            <div class="dis-amount">
              +<span class="rmb">{{ order.memberShipPrice }}</span>
            </div>
          </div>
        </template>
        <van-divider v-if="discountNumber > 0" :style="{ margin: '15px 0' }" />
      </template>

      <div class="dis-total">
        <span v-if="discountNumber > 0"
          >已优惠&nbsp;&nbsp;<span class="rmb-c"
            >¥{{ discountNumber }}</span
          ></span
        >
        <span class="dis-count"
          >合计：<span class="rmb-b">¥{{ orderPayAmount }}</span></span
        >
      </div>
    </template>
  </div>
</template>
<style lang="scss" scoped>
.cell-box {
  background: #ffffff;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 16px 15px;
  margin: 10px 0;
  .title {
    font-size: 12px;
    font-weight: bold;
    color: #999999;
  }
}
.dis-box {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  line-height: 20px;
  margin-bottom: 15px;
  .dis-label {
    color: #333;
  }
  .dis-amount {
    color: #fd4925;
  }
}
.dis-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 13px;
  color: #333333;
  line-height: 20px;
  font-weight: bold;
  .dis-count {
    margin-left: 10px;
  }
  .rmb-c {
    color: #fd4925;
  }
  .rmb-b {
    font-size: 16px;
  }
}
.goods-info {
  margin: 14px 0;
}
</style>
<script>
import GoodsItem from '../packages/mall/after-sales/_GoodsItem.vue';
import { Divider } from 'vant';
import { mixinShare, mixinAuthRouter } from '@/mixins';
export default {
  mixins: [mixinAuthRouter, mixinShare],
  name: 'OrderDetailItems',
  props: {
    value: {
      type: Object,
    },
    hidePrice: {
      type: Boolean,
    },
  },
  computed: {
    order() {
      return this.value;
    },
    orderPayAmount() {
      // 团购订单详情金额字段为needPay，其他订单中为needPayAmount
      if (this.order.needPayAmount != null) return this.order.needPayAmount;
      return this.order.needPay || 0;
    },
    discountNumber() {
      let amount = 0;
      if (
        this.value.discountInfoList &&
        this.value.discountInfoList.length > 0
      ) {
        amount = this.value.discountInfoList.reduce((prev, current) => {
          return prev + current.amount;
        }, 0);
      } else {
        amount = 0;
      }
      amount = amount.toFixed(2);
      return amount > 0 ? amount : 0;
    },
  },
  components: {
    GoodsItem,
    [Divider.name]: Divider,
  },
  methods: {
    go(url) {
      this.$_router_push(url);
    },
  },
};
</script>
