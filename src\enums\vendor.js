import { createEnums } from './utils';

export const OrderCategory = createEnums({
  CAR_LIFE: ['车生活订单', 'cl', '车生活订单'],
  MALL: ['团购订单', 'mall', '团购订单'],
  INSPECTION: ['审车订单', 'inspection', '审车订单'],
  FUELCARD: ['加油卡预存', 'fuelcard', '我的加油卡预存'],
  FUELCARDRECHARGE: ['加油卡预存', 'fuelcardrecharge', '我的加油卡预存'],
  VIP: ['交广领航VIP会员订单', 'vip', '交广领航VIP会员订单'],
  CAR_WASH_CARD: ['洗车卡', 'washcard', '交广领航洗车卡'],
});

/* export const OrderBizType = createEnums({
  CAR_BEAUTY: ['汽车美容', 'car_beauty', ''],
  CAR_MAINTENANCE: ['汽车保养', 'car_maintenance', ''],
  GROUP_BUY: ['团购', 'mall', ''],
  VEHICLE_INSPECTION: ['审车', 'inspection', ''],
  CARD_REPLACEMENT: ['证牌补换', 'card_replacement', ''],
  VIP_BUYING: ['购买VIP会员', 'vip', ''],
}); */

export const OrderBizType = createEnums({
  Activity: ['报名活动', 'activity', ''],
  CarBeauty: ['汽车美容', 'car_beauty', ''],
  CarMaintenance: ['汽车保养', 'car_maintenance', ''],
  GroupBuy: ['团购', 'mall', ''],

  CardReplacement: ['证牌补换', 'card_replacement', ''],
  NoExamInspection: ['免检审车', 'no-exam-inspection', ''],

  VehicleInspection: ['上线审车', 'inspection', '1'],
  VehicleInspectionForSpecial: ['上线审车(专场)', 'inspection_special', ''],
  VehicleInspectionReservation: [
    '上线审车(先审后付)',
    'inspection_reservation',
    '',
  ],
  AgentVehicleInspection: ['代办审车', 'agent_inspection', '6'],
  AgentVehicleInspectionReservation: [
    '代办审车(先审后付)',
    'agent_inspection_reservation',
    '',
  ],

  CarRegister: ['新车上牌', 'car-register', ''],
  CarTransfer: ['二手车过户', 'car-transfer', ''],
  CarTransferToZz: ['外地车转入郑州', 'car-transfer-to-zz', ''],
  CarWashCard: ['洗车卡', 'car-washcard', ''],

  VipBuying: ['购买VIP会员', 'vip-buying', ''],
  MallG2: ['餐饮商品', 'mall-g2', ''],
  MobileRecharge: ['手机充值', 'mobrecharge', ''],
});
