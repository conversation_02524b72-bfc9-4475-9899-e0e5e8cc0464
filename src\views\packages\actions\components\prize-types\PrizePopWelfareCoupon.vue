<template>
  <prize-pop-base :prize="prize" :prizeType="prizeType" :prizeTip="prizeTipText" v-on="$listeners">
    <template #content>
      <div class="prize-name">
        <span class="name">{{ prize.prizeName }}</span>
      </div>
    </template>
  </prize-pop-base>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';
import PrizePopBase from '../PrizePopBase.vue';

export default {
  name: 'PrizePopWelfareCoupon',
  components: {
    PrizePopBase
  },
  props: {
    prize: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prizeType: PrizeTypeEnum.WELFARE_COUPON.valueOf()
    };
  },
  computed: {
    prizeTipText() {
      return '福利券已放入【我的-优惠券】';
    }
  }
};
</script>

<style lang="scss" scoped>
.prize-name {
  font-size: 20px;
  color: #ff273b;
  text-align: center;
  margin-bottom: 12px;
}
</style>
