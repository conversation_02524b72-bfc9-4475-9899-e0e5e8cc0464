<template>
  <div class="play-list">
    <h2 class="head">
      <!-- <van-button type="primary" size="small" round>
        <template slot="icon">
          <svg class="icon-svg">
            <use xlink:href="#icon-xf-bofang"></use>
          </svg>
        </template>
        全部播放
      </van-button> -->
      <div class="update-status">
        已更新{{ audioTotal }}集
      </div>
      <div class="flex"></div>
      <div class="play-order" @click="togglePlayOrder">
          <svg v-if="playListSequence == 'asc'" class="icon-svg">
            <use xlink:href="#icon-xf-zhengxu"></use>
          </svg>
          <svg v-else class="icon-svg">
            <use xlink:href="#icon-xf-daoxu"></use>
          </svg>
          <span>{{ playListSequence == 'asc'? '升序': '降序' }}</span>
      </div>
    </h2>
    <div class="list-wrap" ref="list">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onReachBottom"
        offset="20"
        :immediate-check="false"
      >
        <div v-for="(item, index) in playList" :key="item.id" @click="selectItem(item, index)" class="song-item">
          <div class="audio-no">
            <div v-if="currentSong.id == item.id" class="equilizer">
              <span class="bar"></span>
              <span class="bar"></span>
              <span class="bar"></span>
            </div>
            <span v-else>{{ item.index }}</span>
          </div>
          <div class="audio-info">
            <p>{{ item.name }}</p>
            <div class="audio-duration">
              <svg class="icon-svg">
                <use xlink:href="#icon-xf-shijian"></use>
              </svg>
              <span>{{formatTime(item.duration)}}</span>
            </div>
          </div>
        </div>
      </van-list>
    </div>
    <!-- <ul class="list-wrap">
      <li v-for="(item, index) in playList" :key="index" @click="selectItem(index)">
        <div class="audio-no">
          <div v-if="currentSong.id == item.id" class="equilizer">
            <span class="bar"></span>
            <span class="bar"></span>
            <span class="bar"></span>
          </div>
          <span v-else>{{index+1}}</span>
        </div>
        <div class="audio-info">
          <p>{{ item.name }}</p>
          <div class="audio-duration">
            <svg class="icon-svg">
              <use xlink:href="#icon-xf-shijian"></use>
            </svg>
            <span>{{formatTime(item.duration)}}</span>
          </div>
        </div>
      </li>
    </ul> -->
    <!-- <div class="empty">
      <div class="empty-icon"></div>
      <div class="empty-text">暂无</div>
    </div> -->
  </div>
</template>
<script>
import { Slider, Button, List } from 'vant';
import { toast } from '@/bus'
import { mapGetters, mapMutations, mapActions } from 'vuex';
import { durationTrans, createSong } from '@/common/audio';
import { getAudioList } from '../api';
export default {
  name: 'PlayList',
  components: {
    [Slider.name]: Slider,
    [Button.name]: Button,
    [List.name]: List,
  },
  data() {
    return {
      total: 0,
      loading: false,
      paging: {
        page: 1,
        pageSize: 10,
        programId: 1,
        total: 0,
      }
    };
  },
  computed: {
    ...mapGetters([
      'currentSong',
      'currentIndex',
      'playList',
      'radioInfo',
      'playListPage',
      'audioTotal',
      'playListSequence',
      'currentSong',
    ]),
    parameter() {
      return {
        page: this.playListPage,
        pageSize: 10,
        programId: 1,
        orderType: this.playListSequence
      }
    },
    finished() {
      return (this.playListPage * this.paging.pageSize) > this.audioTotal
    }
  },
  created() {
    this.init();
    // console.log('加载列表');
  },
  methods: {
    ...mapMutations([
      'SET_PLAYING_STATUS',
      'SET_FULLSCREEN',
      'SET_CURRENTINDEX',
      'SET_PLAYLIST_PAGE',
      'SET_AUDIO_TOTAL',
      'SET_PLAYLIST_SEQUENCE',
      'SET_CURRENT_SONG',
    ]),
    ...mapActions(['select_play']),
    init() {
      if (this.playList.length) return // 已从简介页面获取列表初始数据
      let params = {
        page: 1,
        pageSize: 10,
        programId: 1,
      }
      this.handleGetAudioList(params)
        .then((audio) => {
          this.select_play({
            playlist: this._createSong(audio.list),
            index: -1
          })
          this.SET_AUDIO_TOTAL(audio.paging.total)
        })
    },
    // 获取音频列表
    handleGetAudioList(params) {
      return getAudioList(params)
        .then((audio) => {
          return audio
        })
        .catch((err) => {
          toast().tip(err)
          // return Promise.reject(err)
        });
    },
    _createSong(list) {
      if (list instanceof Array) {
        return list.map(item => {
          return createSong({
            id: item.id,
            index: item.seqNo,
            cover: this.radioInfo.image,
            duration: item.duration,
            // singer: item.singer,
            name: item.title,
            audioId: item.radioResource
          });
        });
      }
    },
    // 滚动到底部加载更多
    onReachBottom() {
      if (this.audioTotal && ((this.playListPage * this.paging.pageSize) > this.audioTotal)) {
        // 加载状态结束
        this.loading = false;
      }
      if (this.finished) return
      let params = Object.assign(this.parameter, {
        page: this.playListPage + 1,
      })
      this.handleGetAudioList(params)
        .then((audio) => {
          let currentPlaylist = this.playList
          let oldCurrentSongId = this.currentSong.id
          let newPlaylist = [...currentPlaylist, ...this._createSong(audio.list)]
          this.select_play({
            playlist: newPlaylist,
            index: -1
          })
          this.SET_PLAYLIST_PAGE(params.page)
          this.SET_AUDIO_TOTAL(audio.paging.total)
          this.changeCurrentIndex(oldCurrentSongId)
          // 加载状态结束
          this.loading = false;
        })
    },
    // 修改播放序号
    changeCurrentIndex(oldId) {
      let newCurrentIndex = this.playList.findIndex((item) => {
        return item.id == oldId
      })
      this.SET_CURRENTINDEX(newCurrentIndex);
    },
    // 修改播放顺序
    togglePlayOrder() {
      let playOrder = this.playListSequence == 'asc' ? 'desc' : 'asc';
      this.SET_PLAYLIST_SEQUENCE(playOrder)
      let params = Object.assign(this.parameter, {
        page: 1,
        orderType: playOrder,
      })
      this.handleGetAudioList(params)
        .then((audio) => {
          let oldCurrentSongId = this.currentSong.id
          this.$refs.list.scrollTop = 0
          this.select_play({
            playlist: this._createSong(audio.list),
            index: -1
          })
          this.SET_PLAYLIST_PAGE(1)
          this.SET_AUDIO_TOTAL(audio.paging.total)
          this.changeCurrentIndex(oldCurrentSongId)
        })
    },
    // 选中播放
    selectItem(item, index) {
      this.SET_PLAYING_STATUS(true);
      this.SET_CURRENTINDEX(index);
      this.SET_CURRENT_SONG(item);
      this.SET_FULLSCREEN(true);
      this.$emit('select')
    },
    formatTime(time) {
      if (!time) {
        return '00:00'
      } else {
        return durationTrans(time)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~../assets/style/player-list.scss";

</style>
