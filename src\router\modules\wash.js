import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';

export default [
  {
    name: '洗车首页',
    path: '/car/wash',
    redirect: '/shops/wash',
  },
  {
    name: '汽车美容',
    path: '/car/beauty',
    redirect: '/shops/wash',
  },
  {
    path: '/about/beauty',
    name: '洗车服务介绍',
    component: resolve => {
      import(/* webpackChunkName: "car-wash-about" */ '@/views/packages/beauty/CarBeautyAbout').then(resolve).catch(handleError);
    }
  },
  {
    path: '/shop/:shop/beauty/:type/about',
    name: '商家美容业务介绍',
    component: resolve => {
      import(/* webpackChunkName: "car-wash-about" */ '@/views/packages/beauty/ShopCarBeautyStandard').then(resolve).catch(handleError);
    }
  },
  {
    path: '/cities',
    name: '切换城市',
    component: resolve => {
      import(/* webpackChunkName: "changecity" */ '@/views/CityChange').then(resolve).catch(handleError);
    }
  },
  {
    name: '商家洗车服务',
    path: '/shop/:id',
    redirect: '/shop/:id/wash',
  },
  {
    path: '/shop/:id/wash/:category?',
    alias: '/shop/:id',
    name: '商家美容服务',
    component: resolve => {
      import(/* webpackChunkName: "shop-car-wash" */ '@/views/packages/beauty/ShopCarWash').then(resolve).catch(handleError);
    },
    children: [
      {
        name: 'wash/contacts',
        path: 'contacts',
        component: Contacts
      }
    ]
  },
  {
    path: '/shops/wash',
    name: '洗车门店',
    component: resolve => {
      import(/* webpackChunkName: "car-wash" */ '@/views/packages/beauty/CarWash').then(resolve).catch(handleError);
    },
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/shops/wash/search',
    name: '洗车门店搜索',
    component: resolve => {
      import(/* webpackChunkName: "car-wash-search" */ '@/views/packages/beauty/ShopsOfCarWashSearch').then(resolve).catch(handleError);
    },
    meta: {
      keepAlive: true,
    },
  },
]
