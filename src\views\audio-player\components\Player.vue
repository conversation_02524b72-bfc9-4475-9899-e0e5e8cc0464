<template>
  <div v-show="playList.length > 0" class="net-player">
    <transition name="van-slide-up">
      <div v-show="fullScreen" class="normal-player">
        <div class="player-header">
          <svg @click="hidePlayer" class="icon-xf-zhankai-xiala">
            <use xlink:href="#icon-xf-zhankai-xiala"></use>
          </svg>
          <svg v-if="!isInWeApp" class="icon-xf-fenxiang" @click="share('show')">
            <use xlink:href="#icon-xf-fenxiang"></use>
          </svg>
        </div>
        <div class="player-cover">
          <transition name="van-fade">
            <div v-show="!isFlag" class="cd">
              <p v-if="currentSong.name" class="current-text">{{ currentSong.name }}</p>
              <!-- <div :class="poleIcon" class="music-pole"></div> -->
              <div class="inner-circle">
                <div class="album-img" :class="albumIcon">
                  <img
                    class="image"
                    :src="currentSong.cover || album"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </transition>
        </div>
        <div class="player-list-icon">
            <div class="list-icon" @click.stop="handleShowPlaylist">
              <svg class="icon-svg">
                <use xlink:href="#icon-xf-bofangliebiao"></use>
              </svg>
              <span>播放列表</span>
            </div>
        </div>
        <div class="player-control">
          <progress-bar
            @onPercentChange="onPercentChange"
            :percent="percent"
            :time="currentTime"
            :current-song="currentSong"
          ></progress-bar>
          <div class="operator_2">
            <svg class="icon-Loop">
              <!-- <use xlink:href="#icon-Loop"></use> -->
            </svg>
            <svg @click="prev" class="icon-svg">
              <use xlink:href="#icon-xf-shangyige"></use>
            </svg>
            <div class="play-icon">
              <svg v-if="playing" @click="togglePlaying" class="icon-svg">
                <use xlink:href="#icon-xf-zanting"></use>
              </svg>
              <svg v-if="!playing" @click="togglePlaying" class="icon-svg">
                <use xlink:href="#icon-xf-bofang"></use>
              </svg>
            </div>
            <svg @click="next" class="icon-svg">
              <use xlink:href="#icon-xf-xiayige"></use>
            </svg>
            <svg class="icon-svg">
              <!-- <use xlink:href="#icon-xf-bofangliebiao"></use> -->
            </svg>
          </div>
        </div>
        <div class="player-recommend">
          <div class="card" @click="hidePlayer">
            <biz-image
              class="avatar"
              :src="radioInfo.image"
              :lazy="true"
            >
            </biz-image>
            <div class="info">
              <p>{{ radioInfo.title }}</p>
              <span v-if="moderator">主持人：{{ radioInfo.dj }}</span>
            </div>
            <svg class="icon-svg">
              <use xlink:href="#icon-xf-shouqi-wangshang"></use>
            </svg>
          </div>
        </div>
        <!-- 播放列表 -->
        <van-popup v-model="showPlayList" overlay round close-on-click-overlay position="bottom" class="playlist-pop" :style="{ height: '80%' }">
          <play-list @select="select"></play-list>
        </van-popup>
      </div>
    </transition>
    <transition name="van-slide-up">
      <div @click="showPlayer" v-show="!fullScreen && currentSong.id" class="mini-player">
        <div :class="albumIcon" class="mini-icon">
          <img class="image" :src="currentSong.cover || album" alt="" />
        </div>
        <div class="text">
          <span
            >{{ currentSong.name
            }}
          </span>
          <span class="duration">{{ formatTime(currentTime) }}/{{ formatTime(currentSong.duration) }}</span>
        </div>
        <div class="play-icon">
          <svg v-if="playing" @click.stop="togglePlaying" class="icon-svg">
            <use xlink:href="#icon-xf-zanting"></use>
          </svg>
          <svg v-if="!playing" @click.stop="togglePlaying" class="icon-svg">
            <use xlink:href="#icon-xf-bofang"></use>
          </svg>
        </div>
      </div>
    </transition>
    <audio
      @error="error"
      @canplay="getDuration"
      @playing="handlePlay"
      @timeupdate="timeupdate"
      ref="audio"
      @ended="end"
      :src="currentSong.url"
    ></audio>
    <!-- <play-list
      @select="select"
      @show="hidePlaylist"
      :show="showPlayList"
      :list="playList"
    ></play-list> -->
  </div>
</template>

<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { isInWeixin, isInWeApp } from '@/common/env';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { mapGetters, mapMutations, mapActions } from 'vuex';
// import { saveHistory } from "@/common/js/cache";
import { getAudioURL, durationTrans } from '@/common/audio';
import ProgressBar from './ProgressBar';
import PlayList from './PlayList';
import { Popup } from 'vant';
// import { getSongComment } from "@/api/index";
import album from '../assets/images/album.png';
const ANONYMOUS_AVATAR = require('@/assets/images/anonymous.png');
const DEFAULT_AVATAR = require('@/assets/images/account/avatar.png');
export default {
  name: 'Player',
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    return {
      isInWeApp,
      currentTime: 0,
      percent: 0,
      isFlag: false,
      title: '小疯的简史生活',
      txt: '明星代言背后的另一套商业逻辑',
      moderator: '谢小疯 张弛',
      nolyric: false,
      showPlayList: false,
      album: album,
      currentSongTest: {},
    };
  },
  components: {
    [Popup.name]: Popup,
    ProgressBar,
    PlayList,
  },
  computed: {
    playIcon() {
      return this.playing
        ? 'icon-bofang1'
        : 'icon-bofang rotate_pause';
    },
    albumIcon() {
      return this.playing ? 'rotate' : 'rotate rotate_pause';
    },
    poleIcon() {
      return this.playing ? 'rateCls' : 'pase_rateCls';
    },
    ...mapGetters([
      'currentSong',
      'currentIndex',
      'playList',
      'playing',
      'fullScreen',
      'radioInfo',
      'currentSong',
    ]),
  },
  methods: {
    ...mapMutations([
      'SET_PLAYING_STATUS',
      'SET_FULLSCREEN',
      'SET_CURRENTINDEX',
      'SET_CURRENT_SONG',
    ]),
    ...mapActions(['select_play']),
    formatAudioURL(url) {
      return getAudioURL(url)
    },
    hidePlayer() {
      this.SET_FULLSCREEN(false);
      // 更新为简介分享信息
      const path = '/audio/share/album';
      const link = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share' : '?jglh'
      });
      const shareInfo = {
        link: link,
        title: `我正在收听《${this.radioInfo.title}》~`,
        desc: this.radioInfo.introduction,
        imgUrl: getImageURL(this.radioInfo.image)
      };
      this.$_share_update(shareInfo);
    },
    select(index) {
      this.showPlayList = false
    },
    hidePlaylist() {
      this.showPlayList = false;
    },
    handleShowPlaylist() {
      this.showPlayList = true;
    },
    onselectSong() {
      console.log('asdsd');
    },
    showPlayer() {
      this.SET_FULLSCREEN(true);
    },
    onPercentChange(percent) {
      console.log(percent);
      this.$refs.audio.currentTime = percent * this.currentSong.duration;
    },
    timeupdate(e) {
      // this.SET_PLAYING_STATUS(true);
      this.currentTime = e.target.currentTime;
      this.percent = e.target.currentTime / this.currentSong.duration;
    },
    end() {
      this.next();
    },
    _play() {
      this.$refs.audio.play();
    },
    handlePlay() {
      this.SET_PLAYING_STATUS(true);
      // alert('222')
    },
    next() {
      if (this.currentIndex == -1) {
        // 有歌曲正在播放时，点击排序，歌曲列表中没有正在播放的歌曲时，特殊处理
        this.SET_CURRENTINDEX(0);
        this.SET_CURRENT_SONG(this.playList[0]);
        return;
      }
      let index = this.currentIndex + 1;
      if (index === this.playList.length) {
        index = 0;
        this.SET_CURRENTINDEX(index);
        this.SET_CURRENT_SONG(this.playList[index]);
        return;
      }
      this.SET_CURRENTINDEX(index);
      this.SET_CURRENT_SONG(this.playList[index]);
      this.SET_PLAYING_STATUS(true);
    },
    prev() {
      if (this.currentIndex == -1) {
        // 有歌曲正在播放时，点击排序，歌曲列表中没有正在播放的歌曲时，特殊处理
        this.SET_CURRENTINDEX(0);
        this.SET_CURRENT_SONG(this.playList[0]);
        return;
      }
      let index = this.currentIndex - 1;
      if (index === -1) {
        index = this.playList.length - 1;
        this.SET_CURRENTINDEX(index);
        this.SET_CURRENT_SONG(this.playList[index]);
        return;
      }
      this.SET_CURRENTINDEX(index);
      this.SET_CURRENT_SONG(this.playList[index]);
      this.SET_PLAYING_STATUS(true);
    },
    getDuration() {
      // console.log('可以播放了');
      console.log('时长++++', this.$refs.audio.duration);
      // this.currentSongTest = Object.assign({
      //   duration: this.$refs.audio.duration
      // }, this.currentSong)
    },
    /*
     * 事件 error 会在因为一些错误（如网络连接错误）导致无法加载资源的时候触发。
     * */
    error() {
      console.log('加载音频资源出错');
    },
    togglePlaying() {
      this.SET_PLAYING_STATUS(!this.playing);
    },
    share(action = 'update') {
      // 设置邀请链接分享信息
      const path = `/audio/share/player?id=${this.currentSong.id}`;
      const link = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share' : '?jglh',
      });
      const shareInfo = {
        link: link,
        title: `《${this.currentSong.name}》,快来一起收听吧~`,
        desc: `为您推荐《${this.radioInfo.title}》选章，快来一起来收听吧~`,
        imgUrl: this.currentSong.cover,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    getUserAvatar(item) {
      // console.log(DEFAULT_AVATAR, ANONYMOUS_AVATAR)
      if (item.anonymous) return ANONYMOUS_AVATAR;
      return item.avatar || DEFAULT_AVATAR;
    },
    formatTime(time) {
      if (!time) {
        return '00:00'
      } else {
        return durationTrans(time)
      }
    },
  },
  watch: {
    currentSong(newSong, oldSong) {
      if (!newSong.id) {
        return;
      }
      if (newSong.id === oldSong.id) {
        return;
      }
      /*
       * 一般是等dom的数据更新后 然后才会播放歌曲的
       * */
      setTimeout(() => {
        this.currentTime = 0;
        this.percent = 0;
        this._play();
      }, 30);
    },
    playing(newPlaying, oldValue) {
      this.$nextTick(() => {
        newPlaying ? this.$refs.audio.play() : this.$refs.audio.pause();
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "~../assets/style/player.less";
.playlist-pop{
  max-height: 80%;
  box-sizing: border-box;
  &.show-mini-player{
    padding-bottom: 61px;
  }
}

</style>
