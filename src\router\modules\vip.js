import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/v1/vip',
    name: '交广领航VIP',
    component: resolve => {
      import(/* webpackChunkName: "vip-interests" */ '@/views/packages/vip/VIP-Rights/VIP-Rights.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/share',
    name: '现金红包',
    component: resolve => {
      import(/* webpackChunkName: "vip-interests" */ '@/views/packages/vip/VIP-Share/VipShare.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/share/rules',
    name: '分享红包规则',
    component: resolve => {
      import(/* webpackChunkName: "vip-interests" */ '@/views/packages/vip/VIP-Share/VipShareRules.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/share/invite',
    name: '分享邀请',
    component: resolve => {
      import(/* webpackChunkName: "vip-interests" */ '@/views/packages/vip/VIP-ShareInvite/VIP-ShareInvite.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/rights/detail',
    name: '交广领航VIP-权益详情',
    component: resolve => {
      import(/* webpackChunkName: "vip-interest" */ '@/views/packages/vip/VIP-Rights/VIP-RightsDetail.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/buy',
    name: '购买交广领航VIP',
    component: resolve => {
      import(/* webpackChunkName: "vip-buy" */ '@/views/packages/vip/VIP-Buy/VIP-Buy.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/buy/fill-icode',
    name: '输入邀请码',
    component: resolve => {
      import(/* webpackChunkName: "vip-fill-icode" */ '@/views/packages/vip/VIP-Buy_FillInviteCode.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/exchange',
    name: '兑换会员',
    component: resolve => {
      import(/* webpackChunkName: "vip-exchange" */ '@/views/packages/vip/VIP-Exchange.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/exchange/jgyh',
    name: '兑换交广银合VIP会员',
    component: resolve => {
      import(/* webpackChunkName: "vip-exchange-jgyh" */ '@/views/packages/vip/VIP-ExchangeOfJgyh.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/qa',
    name: 'vip-qa',
    component: resolve => {
      import(/* webpackChunkName: "vip-qa" */ '@/views/packages/vip/VipQa.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/agreement',
    name: 'VIP会员-用户协议',
    component: resolve => {
      import(/* webpackChunkName: "vip-exchange" */ '@/views/packages/vip/VipAgreement.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/result',
    name: 'VIP-提示',
    component: resolve => {
      import(/* webpackChunkName: "vip-result" */ '@/views/packages/vip/VIP-Result.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/invite',
    name: '我的邀请',
    component: resolve => {
      import(/* webpackChunkName: "my-invite" */ '@/views/packages/vip/MyInvite.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/v1/vip/invite/records',
    name: '我的邀请记录',
    component: resolve => {
      import(/* webpackChunkName: "my-invite-records" */ '@/views/packages/vip/MyInviteRecords.vue').then(resolve).catch(handleError);
    },
  },
];
