<template>
  <div class="charge-card">
    <!-- 标题和距离 -->
    <div class="card-header">
      <div class="title">
        <van-icon name="star" color="#ff7e00" />
        <span class="name">{{ station.name }}</span>
      </div>
      <div class="distance">{{ distance ? distance + 'km' : '--' }}</div>
    </div>

    <!-- 评分和功率 -->
    <div class="card-subtitle" v-if="station.rating || stationDesc">
      <span v-if="station.rating" class="score">{{
        station.rating || '--'
      }}</span>
      <span v-if="stationDesc" class="desc">{{ stationDesc }}</span>
    </div>

    <!-- 价格信息 -->
    <div class="card-price" v-if="station.pricePerKwh">
      <span class="now">¥{{ station.pricePerKwh.toFixed(4) }}</span>
      <span class="unit">/度</span>
    </div>

    <!-- 标签信息 -->
    <!-- <div class="card-tags" v-if="stationTags.length">
      <van-tag
        v-for="tag in stationTags"
        :key="tag.text"
        :type="tag.type"
        size="mini"
      >
        {{ tag.text }}
      </van-tag>
    </div> -->

    <!-- 停车和充电桩信息 -->
    <div class="card-footer">
      <div class="parking" v-if="parkingInfo">
        <van-icon name="passed" color="#1989fa" />
        <span>{{ parkingInfo }}</span>
      </div>
      <div class="charger-info" v-if="chargerTypes.length">
        <div
          v-for="charger in chargerTypes"
          class="charger-item"
          :key="charger.type"
        >
          <van-tag
            :color="charger.type === 'dc' ? '#ff7e00' : '#1989fa'"
            text-color="#fff"
            size="mini"
          >
            {{ charger.name }}
          </van-tag>
          <span class="count">{{
            formatChargerCount(charger.available, charger.total)
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, getDistance } from '@/utils';
import {
  isValidCoordinate,
  getOperationTypeText,
  getParkingFeeTypeText,
  getStationTags,
  formatChargerCount,
  getChargerTypes,
} from '../utils';
import { Icon, Tag, Popup, Button } from 'vant';
export default {
  name: 'ChargeCard',
  components: {
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [Popup.name]: Popup,
    [Button.name]: Button,
  },
  props: {
    station: {
      type: Object,
      required: true,
    },
    geo: {
      type: Object,
      required: true,
    },
  },
  computed: {
    distance() {
      // 如果station已有distance属性，直接返回
      if (this.station.distance) {
        return this.station.distance;
      }

      // 验证坐标有效性
      const stationLat = this.station.latitude;
      const stationLng = this.station.longitude;
      const geoLat = this.geo.latitude;
      const geoLng = this.geo.longitude;

      // 使用项目现有的坐标验证函数
      if (
        !isValidCoordinate(stationLat, stationLng) ||
        !isValidCoordinate(geoLat, geoLng)
      ) {
        return '--'; // 坐标无效时返回默认显示值
      }

      let _distance = getDistance(stationLat, stationLng, geoLat, geoLng);
      return _distance.toFixed(0); // 直线距离
    },

    // 充电站描述信息
    stationDesc() {
      const parts = [];

      // 停车场类型
      if (this.station.parkingTypes && this.station.parkingTypes.length) {
        const parkingText = getParkingFeeTypeText(this.station.parkingTypes[0]);
        if (parkingText !== '未知') {
          parts.push(parkingText);
        }
      }

      if (this.station.maxPower) {
        parts.push(`最快${this.station.maxPower}kW`);
      }

      // 运营类型
      const operationType = getOperationTypeText(this.station.operationType);
      if (operationType !== '未知') {
        parts.push(operationType);
      }

      return parts.join(' | ') || '';
    },

    // 充电站标签
    // stationTags() {
    //   return getStationTags(this.station);
    // },

    // 停车信息
    parkingInfo() {
      const parkingFeeText = getParkingFeeTypeText(this.station.parkingFeeType);
      if (parkingFeeText === '未知') return null;

      if (this.station.parkingFeeTypeDesc) {
        return this.station.parkingFeeTypeDesc;
      }
      return parkingFeeText;
    },

    // 充电桩类型信息
    chargerTypes() {
      return getChargerTypes(this.station);
    },
  },

  methods: {
    formatChargerCount,
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.charge-card {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 0px 0 12px;
  font-size: 14px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;

      .name {
        margin-left: 6px;
        font-weight: 600;
      }
    }

    .distance {
      color: #666;
      font-size: 12px;
    }
  }

  .card-subtitle {
    margin-top: 6px;
    font-size: 12px;
    color: #999;

    .score {
      color: #ff7e00;
      font-weight: bold;
      margin-right: 6px;
    }
  }

  .card-price {
    margin: 8px 0;

    .now {
      font-size: 16px;
      font-weight: bold;
      color: #000;
      margin-right: 8px;
    }

    .vip {
      background: #fff2e5;
      color: #ff7e00;
      padding: 2px 6px;
      border-radius: 4px;
      margin-right: 8px;
    }

    .old {
      font-size: 12px;
      color: #999;
      text-decoration: line-through;
    }
  }

  .card-tags {
    .van-tag {
      margin-right: 5px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .card-coupon {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .coupon-text {
      margin-left: 4px;
      color: #ff4d4f;
      font-size: 13px;
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;

    .parking {
      display: flex;
      align-items: center;
      width: 0;
      flex: 1;
      max-width: 200px;
      @include singleline-ov;

      span {
        margin-left: 4px;
      }
    }

    .charger-info {
      display: flex;
      justify-content: flex-end;
      flex-basis: 100px;
      flex-shrink: 0;
      .charger-item {
        display: flex;
        align-items: center;
        margin-right: 10px;
        line-height: 1;

        .van-tag {
          margin-right: 5px;
          font-style: italic;
          border-radius: 4px;
          padding: 4px;
          line-height: 1;
        }
        .count {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .fast {
      display: flex;
      align-items: center;

      .count {
        margin-left: 4px;
        color: #333;
      }
    }
  }
}
</style>
