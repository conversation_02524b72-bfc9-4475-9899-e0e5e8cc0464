
<style lang="scss" scoped>
 @import '~styles/mixin/index.scss';
  $border-color: #EBEBEB;
  $menu-border-color: #dadada;
  .no-header{
    .page-content{
      margin-top: 0;
    }
  }
  .orders ::v-deep {
    .header {
      display: flex!important;
    }
    .swiper-container {
      display: flex;
      flex: 1;
    }
    .swiper-wrapper {
      flex: 1;
      height: auto;
    }
    .swiper-slide {
      display: flex;
      height: inherit;
      .content-wrapper, .page-content {
        margin-top: 0 !important;
      }
    }
  }
  .my-tabs {
    background: white;
    box-shadow: 0 0 0 0 #dcdcdc; /* px */
    z-index: 1;
    ::v-deep .tab-item {
      flex: auto;
      color: #333333;
    }
    ::v-deep .tab-item-active{
      color: $lh-2022-primary-color;
      &::after{
        display: none;
      }
    }
    ::v-deep .tab-items-inner {
      justify-content: space-evenly;
    }
  }
  .error {
    text-align: center;
    padding: 10px;
    margin: 20px;
    &::before {
      content: '\E64f';
      font-family: iconfont;
      display: block;
      font-size: 38px;
      color: #3a3a3a;
    }
  }
  .error {
    margin-top:30%;
  }
</style>
<template>
    <page class="orders" @ready="init" @resume="onResume" :class="!isShowHeader ? '':'no-header'">
      <x-header title="我的订单" class="white-header" v-if="!isShowHeader">
        <x-button  slot="left" type="back"></x-button>
        <x-button v-if="shouldDisplayAfterSaleBtn"  slot="right" type="text" @click="$_router_pageTo('/mall/order/list?mode=aftersale')">退款/售后</x-button>
      </x-header>
      <div class="page-content">
        <template v-if="status == AppStatus.READY">
          <tabs
            class="my-tabs"
            :index="currentTypeIndex"
            :scrollable="true"
            :tabs="tabList"
            @change="switchType"
          >
          </tabs>
          <swiper
            ref="swiper"
            class="my-swiper"
            @change="onSlide"
            :options="{
              autoplay: false,
              initialSlide: currentTypeIndex,
            }"
          >
            <swiper-item v-fix:height>
              <order-list :ref="OrdersType.ALL"></order-list>
            </swiper-item>
            <swiper-item v-fix:height>
              <order-list :category="1" :ref="OrdersType.UNPAY"></order-list>
            </swiper-item>
            <swiper-item v-fix:height>
              <order-list :category="2" :ref="OrdersType.UNUSED"></order-list>
            </swiper-item>
            <swiper-item v-fix:height>
              <order-list :category="3" :ref="OrdersType.UNDELIVERED"></order-list>
            </swiper-item>
            <swiper-item v-fix:height>
              <order-list :category="4" :ref="OrdersType.UNRECEIVED"></order-list>
            </swiper-item>
            <swiper-item v-fix:height>
              <order-list :category="5" :ref="OrdersType.REFUND"></order-list>
              <!-- <order-list-refund :category="5" :ref="OrdersType.REFUND"></order-list-refund> -->
            </swiper-item>
            <swiper-item v-fix:height>
              <order-list :category="6" :ref="OrdersType.UNCOMMENT"></order-list>
            </swiper-item>
            <swiper-item v-fix:height>
              <order-list :category="7" :ref="OrdersType.FINISHED"></order-list>
            </swiper-item>
          </swiper>
          <!-- <van-tabs v-model="currentTypeIndex" swipeable animated>
            <van-tab title="全部">
              <order-list :ref="OrdersType.ALL"></order-list>
            </van-tab>
            <van-tab title="待支付">
              <order-list :category="1" :ref="OrdersType.UNPAY"></order-list>
            </van-tab>
            <van-tab title="待使用">
              <order-list :category="2" :ref="OrdersType.UNUSED"></order-list>
            </van-tab>
            <van-tab title="待发货">
              <order-list :category="3" :ref="OrdersType.UNDELIVERED"></order-list>
            </van-tab>
            <van-tab title="待收货">
              <order-list :category="4" :ref="OrdersType.UNRECEIVED"></order-list>
            </van-tab>
            <van-tab title="售后/退款">
              <order-list :category="5" :ref="OrdersType.REFUND"></order-list>
            </van-tab>
            <van-tab title="待评价">
              <order-list :category="6" :ref="OrdersType.UNCOMMENT"></order-list>
            </van-tab>
            <van-tab title="已完成">
              <order-list :category="7" :ref="OrdersType.FINISHED"></order-list>
            </van-tab>
          </van-tabs> -->
        </template>
      </div>
    </page>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
// import { Tabs } from '@/components';
import Tabs from '@/components/Tabs2.vue';
import { Swiper, SwiperItem } from '@/components/Swiper';
import OrderList from '@/views/order/OrderList.vue';
import OrderListRefund from '@/views/order/OrderListRefund.vue';
import { dialog } from '@/bus';
import { isInWeApp, isInWeixin } from '@/common/env';
import { NewOrdersStatus } from '@/views/order/enums';
// import { Tab, Tabs } from 'vant';

const OrdersType = {
  ALL: 'all', // 全部订单
  UNPAY: 'unpay', // 待支付
  UNUSED: 'unused', // 待使用
  UNDELIVERED: 'undelivered', // 待发货
  UNRECEIVED: 'unreceived', // 待收货
  REFUND: 'refund', // 售后/退款
  UNCOMMENT: 'uncomment', // 待评价
  FINISHED: 'finished', // 已完成
}

const OrderCategory = [
  {
    value: OrdersType.ALL,
    name: '全部',
  },
  {
    value: OrdersType.UNPAY,
    name: '待支付',
  },
  {
    value: OrdersType.UNUSED,
    name: '待使用',
  },
  {
    value: OrdersType.UNDELIVERED,
    name: '待发货',
  },
  {
    value: OrdersType.UNRECEIVED,
    name: '待收货',
  },
  {
    value: OrdersType.REFUND,
    name: '售后/退款',
  },
  {
    value: OrdersType.UNCOMMENT,
    name: '待评价',
  },
  {
    value: OrdersType.FINISHED,
    name: '已完成',
  },
]

export default {
  name: 'AccountOrderListNew',
  props: {
    type: {
      type: String,
      default: OrdersType.ALL,
    }
  },
  mixins: [mixinAuthRouter],
  components: {
    Tabs,
    Swiper,
    SwiperItem,
    'order-list': OrderList,
    OrderListRefund,
    // [Tabs.name]: Tabs,
    // [Tab.name]: Tab,
  },
  mounted() {
    // this.init();
  },
  data() {
    const defaultType = Object.values(OrdersType).indexOf(this.type);
    return {
      AppStatus,
      NewOrdersStatus,
      status: AppStatus.LOADING,
      OrdersType,
      offsetTime: Date.now(),
      currentTypeIndex: defaultType,
      // currentTypeIndex: OrderCategory[0].value,
      refreshAction: 1,
      scrollable: true,
    };
  },
  computed: {
    isShowHeader() {
      // return isInWeixin;
      return isInWeApp;
    },
    // 小程序自定义交易组件审核需要售后能力，即小程序内需要展示此按钮表明小程序有售后能力
    // 2022-01-19新版本订单，订单分类中含有售后项，此处可以暂时屏蔽
    shouldDisplayAfterSaleBtn() {
      // return isInWeApp;
      return false;
    },
    defaultSwiperIndex() {
      return Object.values(OrdersType).indexOf(this.type);
    },
    tabList() {
      return OrderCategory.map(item => {
        return {
          id: item.value,
          name: item.name,
          value: item.value,
        }
      });
    },
  },
  methods: {
    init() {
      this.status = AppStatus.READY;
      console.log('init ...');
      this.$nextTick(() => {
        // this.$refs.swiper.slideTo(this.defaultSwiperIndex);
        // this.$refs[this.currentTypeIndex].init();
        this.showPage(this.currentTypeIndex);
      })
      const tip = `
        <div style="text-align:left;color: #f72626;">
        亲~春节期间各业务有调整，请看清楚说明后再进店哦~ <br>
        1、洗车服务：2月2日--2月27日停止营业(前期已下单客户需在1月17日前进店消费），2月28日开始接单。<br>
        2、保养服务：因春节假期，进店前请先与对应门店联系确认是否营业。<br>
        3、上线审车、上门代审、二手车过户、新车上牌：1月23日-1月31日停止下单，2月1日开始接单。 <br>
        4、免检审车、证牌补换：1月21日-1月31日停止办理，2月1日开始接单。
        </div>
      `
      if (Date.now() > new Date(2020, 1 - 1, 13, 0, 0, 0) && Date.now() < new Date(2020, 2 - 1, 9, 0, 0, 0)) {
        dialog().alert(tip, {
          title: '业务提醒',
          ok: '知道了',
        })
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    switchType(index) {
      // console.log('onswithc...', arg)
      // this.currentTypeIndex = arg.value;
      this.$refs.swiper.slideTo(index);
    },
    tabChange(index) {
      // name：标识符，title：标题  name默认标签的索引值
      this.onSlide(index);
    },
    showPage(index) {
      console.log('showPage:', index)
      const page = this.$refs[OrderCategory[index].value];
      if (page.status != AppStatus.READY) {
        page.init();
      } else {
        console.log('refresh...')
        page.onResume();
      }
    },
    onResume() {
      console.log('home resume...');
      this.showPage(this.currentTypeIndex);
    },
    onSlide(index) {
      this.currentTypeIndex = index;
      console.log('onSlide showPage:');
      this.showPage(index);
    },
  }
};
</script>
