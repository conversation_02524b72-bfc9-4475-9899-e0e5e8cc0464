<template>
  <div class="question-wrap">
    <!-- 对战双方信息 -->
    <div class="players-container">
      <div class="player-info left-player">
        <div class="avatar-container">
          <biz-image class="avatar" :src="leftPlayer.portrait" immediate>
          </biz-image>
        </div>
        <div class="player-name">{{ leftPlayer.name }}</div>
        <div class="player-score">{{ myScore }}</div>
      </div>

      <div class="vs-logo">
        <img
          src="@/views/live-battle/assets/images/vs.png"
          alt="VS"
          class="vs-image"
        />
      </div>

      <div v-if="rightPlayer" class="player-info right-player">
        <div class="avatar-container">
          <biz-image class="avatar" :src="rightPlayer.portrait" immediate>
          </biz-image>
        </div>
        <div class="player-name">{{ rightPlayer.name }}</div>
        <div class="player-score">{{ opponentScore }}</div>
      </div>
    </div>
    <!-- 完成答题，等待提示 -->
    <div v-if="showWaiting" class="waiting">请等待对手完成答题~</div>
    <!-- 题目内容 -->
    <div class="question-card">
      <!-- 题目内容 -->
      <div class="question-container">
        <!-- 倒计时区域 -->
        <div class="countdown-container" ref="countdownContainer">
          <CircleProgress
            :progress="circleProgress"
            :radius="circleRadius"
            :stroke-width="circleWidth"
            stroke-color="#7150FD"
            background-color="#EEF2FE"
          />
          <div class="countdown-circle">
            <div class="countdown-label">剩余时间</div>
            <div class="countdown-value">{{ countdown }}</div>
          </div>
        </div>

        <!-- 题目进度 -->
        <div class="question-progress">
          <div class="progress-text">
            问题 {{ currentQuestionIndex }}
            <span class="total-questions">/{{ totalQuestions }}</span>
          </div>
          <van-progress
            :percentage="progress"
            color="#7150FD"
            :stroke-width="8"
            :show-pivot="false"
          />
        </div>
        <template v-if="currentQuestion">
          <!-- <transition name="fade"> -->
          <div class="question-text">
            <span class="question-type" v-if="isMultipleChoice">[多选]</span>
            <span class="question-type" v-else>[单选]</span>
            <span v-html="currentQuestion.qstContent"></span>
          </div>
          <!-- </transition>
              :key="currentQuestionIndex" -->

          <!-- 选项 -->
          <div class="options-container">
            <div
              v-for="(option, index) in currentQuestion.options"
              :key="`${currentQuestionIndex}-${index}`"
              class="option-item"
              :class="{
                'multiple-choice': isMultipleChoice,
                selected: isOptionSelected(option.optOrder),
                correct: isMultipleChoice
                  ? showingResult && isCorrectMultipleOption(option.optOrder)
                  : showingResult && option.optOrder === currentQuestion.isAsr,
                incorrect: isMultipleChoice
                  ? showingResult && isIncorrectMultipleOption(option.optOrder)
                  : showingResult &&
                    selectedOption === option.optOrder &&
                    option.optOrder !== currentQuestion.isAsr,
              }"
              @click="selectOption(option.optOrder)"
            >
              {{ option.optContent }}
              <van-icon
                v-if="
                  !isMultipleChoice &&
                  showingResult &&
                  option.optOrder === currentQuestion.isAsr
                "
                name="success"
                class="result-icon"
              />
              <van-icon
                v-if="
                  !isMultipleChoice &&
                  showingResult &&
                  selectedOption === option.optOrder &&
                  option.optOrder !== currentQuestion.isAsr
                "
                name="cross"
                class="result-icon"
              />
              <van-icon
                v-if="
                  isMultipleChoice &&
                  showingResult &&
                  isCorrectMultipleOption(option.optOrder)
                "
                name="success"
                class="result-icon"
              />
              <van-icon
                v-if="
                  isMultipleChoice &&
                  showingResult &&
                  isIncorrectMultipleOption(option.optOrder)
                "
                name="cross"
                class="result-icon"
              />
            </div>
          </div>

          <!-- 多选题提交按钮 -->
          <div
            class="submit-button-container"
            v-if="isMultipleChoice && !showingResult"
          >
            <van-button
              type="primary"
              :disabled="!canSubmitMultiple"
              @click="submitMultipleAnswer"
              round
              block
            >
              提交答案
            </van-button>
          </div>

          <!-- 多选题提示 -->
          <div
            class="multiple-choice-hint"
            v-if="isMultipleChoice && !showingResult"
          >
            请选择所有正确选项
          </div>
        </template>
      </div>
    </div>
    <div class="tips">关闭/退出页面将会退出房间哦~</div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { mixinAuth } from '@/mixins/auth';
import { Progress, Icon, Button } from 'vant';
import CircleProgress from './CircleProgress';

export default {
  name: 'QuizQuestion',
  mixins: [mixinAuth],
  components: {
    [Progress.name]: Progress,
    [Icon.name]: Icon,
    [Button.name]: Button,
    CircleProgress,
  },
  data() {
    return {
      selectedOption: null,
      selectedOptions: [], // 多选题已选项存储
      timer: null,
      circleRadius: 0,
      circleWidth: 0,
    };
  },
  computed: {
    ...mapState('quiz', {
      connectionStatus: state => state.connection,
      quizResults: state => state.quiz.results,
      quizStatus: state => state.quiz.status,
      userId: state => state.user.userId,
      quizUser: state => state.user,
      quizPlayers: state => state.quiz.players,
      countdown: state => state.quiz.currentQuestionRemainingSeconds,
      currentQuestionIndex: state => state.quiz.currentIndex,
      totalQuestions: state => state.quiz.questions.length,
      showingResult: state => state.quiz.showingResult,
    }),
    ...mapGetters('quiz', ['currentQuestion', 'progress', 'progressTime']), //
    progressPercentage() {
      return (this.currentQuestionIndex / this.totalQuestions) * 100;
    },
    circleProgress() {
      return this.progressTime;
    },
    myScore() {
      if (this.quizResults) {
        return this.quizUser.isHouseOwner
          ? this.quizResults.promoter.score
          : this.quizResults.opponent.score;
      }
      return 0;
    },
    opponentScore() {
      if (this.quizResults) {
        return this.quizUser.isHouseOwner
          ? this.quizResults.opponent.score
          : this.quizResults.promoter.score;
      }
      return 0;
    },
    leftPlayer() {
      let player = {
        portrait: this.$_auth_userInfo.portrait,
        name: this.$_auth_userInfo.name,
      };
      return player;
    },
    rightPlayer() {
      if (!this.quizPlayers.opponent) {
        return null;
      }
      return this.quizUser.isHouseOwner
        ? this.quizPlayers.opponent.userDetail
        : this.quizPlayers.promoter.userDetail;
    },
    showWaiting() {
      return (
        this.showingResult &&
        this.currentQuestionIndex == this.totalQuestions &&
        this.quizStatus != 'ended'
      );
    },
    // 判断当前题目是否为多选题
    isMultipleChoice() {
      return this.currentQuestion && this.currentQuestion.type === 2;
    },
    // 判断是否可以提交多选题答案
    canSubmitMultiple() {
      return this.selectedOptions.length > 0;
    },
  },
  watch: {
    currentQuestion: {
      handler(val) {
        if (val) {
          // 重置状态
          this.selectedOption = null;
          this.selectedOptions = [];
        }
      },
      deep: true,
    },
  },
  mounted() {
    // 获取ref，countdownCircle的宽高，用于计算倒计时圆形的大小
    this.$nextTick(() => {
      const { width, height } =
        this.$refs.countdownContainer.getBoundingClientRect();
      this.circleRadius = width / 2;
      this.circleWidth = 6;
    });
  },
  beforeDestroy() {},
  methods: {
    ...mapActions('quiz', ['submitAnswer']),
    selectOption(value) {
      if (this.showingResult) return;

      // 多选题处理逻辑
      if (this.isMultipleChoice) {
        const index = this.selectedOptions.indexOf(value);
        if (index === -1) {
          // 选中该选项
          this.selectedOptions.push(value);
        } else {
          // 取消选中
          this.selectedOptions.splice(index, 1);
        }
      } else {
        // 单选题逻辑保持不变
        this.selectedOption = value;
        this.submitAnswer({ value });
      }
    },
    // 提交多选题答案
    submitMultipleAnswer() {
      if (!this.canSubmitMultiple || this.showingResult) return;

      // 按照选项顺序排序
      const sortedOptions = [...this.selectedOptions].sort();
      // 转换为逗号分隔的字符串
      const value = sortedOptions.join(',');

      this.submitAnswer({ value });
    },
    // 检查选项是否已选中（多选题使用）
    isOptionSelected(value) {
      return this.isMultipleChoice
        ? this.selectedOptions.includes(value)
        : this.selectedOption === value;
    },
    // 判断多选题选项是否正确
    isCorrectMultipleOption(optOrder) {
      if (!this.showingResult || !this.currentQuestion) return false;

      // 获取正确答案（假设正确答案是逗号分隔的字符串）
      const correctAnswers = this.currentQuestion.isAsr.split(',');
      return correctAnswers.includes(optOrder);
    },
    // 判断用户选择的多选项是否错误
    isIncorrectMultipleOption(optOrder) {
      if (!this.showingResult || !this.currentQuestion) return false;

      // 用户选择了此选项但它不在正确答案中
      const correctAnswers = this.currentQuestion.isAsr.split(',');
      return (
        this.selectedOptions.includes(optOrder) &&
        !correctAnswers.includes(optOrder)
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.question-wrap {
  width: 100%;
  min-height: 100%;
  background: #5944ff;
  padding-top: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  position: relative;

  .players-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }

  .player-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
    line-height: 1;
    padding: 18px 0;

    .avatar-container {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 6px;

      .avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .player-name {
      font-size: 14px;
      margin-bottom: 12px;
      color: #fff;
    }

    .player-score {
      font-size: 26px;
      font-weight: bold;
    }
    &.left-player {
      color: #ff7e9d;
      background: linear-gradient(90deg, #b22bed 0%, rgba(89, 68, 255, 0) 100%);
    }

    &.right-player {
      color: #43ffd2;
      background: linear-gradient(
        270deg,
        #4d94ff 0%,
        rgba(89, 68, 255, 0) 100%
      );
    }
  }

  .left-player .player-score {
    color: #ff7e9d;
  }

  .right-player .player-score {
    color: #43ffd2;
  }

  .vs-logo {
    width: 80px;
    height: 80px;
    flex-basis: 80px;
    flex-shrink: 0;
    .vs-image {
      width: 80px;
      height: auto;
    }
  }

  .waiting {
    width: 100%;
    margin-top: 12px;
    background: linear-gradient(90deg, #5944ff 0%, #3e2ad8 49%, #5944ff 100%);
    text-align: center;
    font-weight: bold;
    line-height: 28px;
    font-size: 14px;
    color: #ffffff;
  }
  .question-card {
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;
    margin-top: 46px;
  }
  .countdown-container {
    width: 75px;
    height: 75px;
    box-sizing: border-box;
    position: absolute;
    left: 50%;
    top: 0;
    z-index: 2;
    transform: translate(-50%, -50%);

    .countdown-circle {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      line-height: 1;

      .countdown-label {
        font-size: 9px;
        color: #7150fd;
        margin-bottom: 4px;
      }

      .countdown-value {
        font-weight: bold;
        font-size: 28px;
        color: #7150fd;
      }
    }
  }
  .circle-progress {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .question-progress {
    width: 100%;
    margin-bottom: 20px;

    .progress-text {
      margin-bottom: 8px;

      font-weight: bold;
      font-size: 16px;
      color: #7150fd;
      .total-questions {
        color: #a58fff;
        font-size: 12px;
      }
    }
  }

  .question-container {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    padding: 46px 18px 18px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 20px;

    .question-text {
      font-weight: bold;
      font-size: 14px;
      color: #111111;
      margin-bottom: 20px;
      line-height: 1.5;
      & > span {
        float: left;
      }
      // 清除浮动
      &::after {
        content: '';
        display: block;
        clear: both;
      }
    }

    .options-container {
      display: flex;
      flex-direction: column;
      gap: 14px;

      .option-item {
        background-color: #f5f6fa;
        border-radius: 60px;
        padding: 16px;
        color: #333;
        font-size: 14px;
        position: relative;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        line-height: 1;
        animation: fadeInUp 0.5s ease both;

        // 多选题样式 - 移除方形复选框样式
        &.multiple-choice {
          border-radius: 8px;
        }

        // 为每个选项添加延迟，实现依次出现的效果
        @for $i from 1 through 4 {
          &:nth-child(#{$i}) {
            animation-delay: #{$i * 0.1}s;
          }
        }

        &.selected {
          background-color: #6956e5;
          color: #fff;
        }

        &.correct {
          background-color: #7150fd;
          color: #fff;
        }

        &.incorrect {
          background-color: #fe324d;
          color: #fff;
        }
        .result-icon {
          position: absolute;
          right: 18px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 20px;
        }
      }
    }
  }

  // 多选题题型标识
  .question-type {
    display: inline-block;
    margin-right: 5px;
    padding: 2px 5px;
    background-color: #7150fd;
    color: #fff;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
  }

  // 多选题提交按钮
  .submit-button-container {
    margin-top: 20px;

    .van-button {
      background-color: #7150fd;
      border-color: #7150fd;
      height: 44px;
      font-size: 16px;

      &--disabled {
        opacity: 0.5;
      }
    }
  }

  // 多选题提示文字
  .multiple-choice-hint {
    margin-top: 10px;
    font-size: 12px;
    color: #999;
    text-align: center;
  }

  .countdown-progress {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 74px;
    height: 74px;
    z-index: 3;

    .circle-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 6px solid rgba(255, 255, 255, 0.3);
      box-sizing: border-box;
    }

    .circle-progress {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 6px solid transparent;
      border-top: 6px solid #fff;
      border-right: 6px solid #fff;
      box-sizing: border-box;
      transform-origin: center;
      transition: transform 0.1s linear;
    }

    .circle-inner {
      position: absolute;
      top: 6px;
      left: 6px;
      width: calc(100% - 12px);
      height: calc(100% - 12px);
      border-radius: 50%;
      background-color: transparent;
    }
  }
  .tips {
    margin-top: 18px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.4);
    line-height: 16px;
    text-align: center;
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>
