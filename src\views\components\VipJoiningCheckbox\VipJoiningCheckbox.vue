<style lang="scss" scoped>
  .vip-msg {
    background: white;
    padding-left: 10px;
    margin-top: 10px;
    font-weight: 500;
    display: flex;
    justify-content: center;
    .vip-msg__content {
      flex: 1;
      padding: 3px;
    }
    .vip-msg__title {
      font-weight: 500;
      font-size: 14px;
    }
    .vip-msg__desc {
      color: #9A6C2E;
      font-size: 13px;
    }
    .rmb {
      font-size: 16px;
      font-weight: 700;
      color: #FF5E1B;
      &::before {
        font-size: 14px;
      }
    }
    .vip-msg__btn {
      color: white;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 10px;
      width: 30px;
      text-align: right;
      position: relative;
    }
  }
  .weui-check__label:active {
    background-color: transparent;
  }
  .vip-icon {
    background: url(./images/vip.png) center center no-repeat;
    background-size: cover;
    width: 42px; /* px */
    height: 16px; /* px */
    display: inline-block;
    vertical-align: -3px; /* px */
    margin-right: 5px;
  }
  .vip-checkbox {
    background: url(./images/label-unchecked.png) center center no-repeat;
    background-size: cover;
    position: absolute;
    width: 55px;
    height: 23px;
    top: -12px;
    left: -22px;
    &.vip-checkbox-checked {
      left: -60px;
      width: 93px;
      background-image: url(./images/label-checked.png);
    }

  }
  .weui-cells_radio .weui-check:checked + .weui-icon-checked{
    background-color: #fd4925;
    border-color: #fd4925;
  }
</style>

<template>
  <div class="vip-msg">
    <div class="vip-msg__content">
      <div class="vip-msg__title">
        <slot name="title"><span class="vip-icon"></span>勾选开通VIP会员，本单可减 <span class="rmb">{{formatMoney(discountAmount)}}</span></slot>
      </div>
      <div class="vip-msg__desc"><slot name="desc">开通VIP会员，专享11项特权</slot></div>
    </div>
    <div class="vip-msg__btn">
      <!-- <input type="checkbox" v-model="joinVip"> -->
      <label
        class="weui-cells_radio weui-check__label"
      >
        <input type="checkbox" class="weui-check" v-model="joinVip">
        <span class="weui-icon-checked" @click.stop.prevent="toggle"></span>
      </label>
      <span class="vip-checkbox" :class="{ 'vip-checkbox-checked': joinVip }"></span>
    </div>
  </div>
</template>
<script>
/**
 * 是否开通vip会员的checkbox
 */
import { formatMoney } from '@/utils';

export default {
  name: 'VipJoiningCheckbox',
  props: {
    // 开通vip后可优惠金额
    discountAmount: {
      type: Number,
      default() {
        return 0;
      },
    },
    value: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    return {
      // joinVip: false,
    };
  },
  computed: {
    joinVip: {
      get() {
        return this.value;
      },
      set(newValue) {
        // 使用 $emit 触发事件，将新的值传递给父组件
        this.$emit('input', newValue);
      },
    },
  },
  watch: {
    // joinVip(val) {
    //   this.$emit('input', val);
    // },
  },
  methods: {
    ...{ formatMoney },
    toggle(e) {
      // console.log(e);
      this.joinVip = !this.joinVip;
    },
  },
}
</script>
