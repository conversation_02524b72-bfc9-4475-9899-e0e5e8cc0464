
/**
 * 滚动相关
 */
export const mixinScroll = {
  directives: {
    // 滚动同步
    'scroll-sync': {
      inserted(el, binding, vnode) {
        console.info(binding.value);
        el.style.overflowY = 'scroll';
        const targetEl = binding.value;
        el.addEventListener('scroll', e => {
          if (e.target.lastScrollTop) {
            const distance = e.target.scrollTop - e.target.lastScrollTop;
            if (distance > 0) {
              requestAnimationFrame(() => {
                targetEl.scrollTop = targetEl.scrollTop + distance;
              })
            }
          }
          e.target.lastScrollTop = e.target.scrollTop;
        }, { passive: true });
      }
    },
    /**
     * 导航tab内容滚动同步
     * @param {HtmlElement} el 使用指令的容器
     * @param {HtmlElement} binding.value 要同步的滚动容器
     */
    'scroll-view': {
      inserted(el, binding, vnode) {
        console.info(binding.value);
        const $scrollHead = el.querySelector('[data-role="scroll-head"]');
        const $scrollBody = el.querySelector('[data-role="scroll-body"]');
        const $targetScroller = binding.value;
        if ($scrollHead && $scrollBody) {
          const bodyHeight = $targetScroller.getBoundingClientRect().height - $scrollHead.getBoundingClientRect().height;
          // 滚动边界，当 $targetScroller.scrollTop为containerBorder时
          const containerBorder = $targetScroller.scrollHeight - $targetScroller.clientHeight;
          Array.from($scrollBody.children).forEach($child => {
            if ($child.dataset.sticky) {
              $child.style.minHeight = `${bodyHeight}px`;
            } else {
              $child.style.height = `${bodyHeight}px`;
              $child.style.overflowY = 'auto';
            }
          })
        }
      }
    },
    /**
      滚动同步指令
      滚动嵌套：一个滚动容器中包含另一个可滚动的容器
      使用场景如外卖商品列表，
      手指在子滚动容器向上滑动时，子容器不滚动，父容器滚动，
      当子容器顶端触及父容器在屏幕中的顶部时，手指继续往上滑动，父容器不滚动，子容器滚动
      由于原生dom滚动不可取消，在不适用模拟滚动的情况下，只能通过同步滚动来满足需求
     */
    'scroll-view2': {
      inserted(el, binding, vnode) {
        console.warn('scroll-view2', binding.value);
        const $scrollHead = el.querySelector('[data-role="scroll-view_head"]');
        const $scrollBody = el.querySelector('[data-role="scroll-view_body"]');
        const $targetScroller = binding.value;
        if ($scrollHead && $scrollBody) {
          setTimeout(() => {
            // const bodyHeight = $targetScroller.getBoundingClientRect().height - $scrollHead.getBoundingClientRect().height;
            // 滚动边界，当 $targetScroller.scrollTop为containerBorder时，无需再进行滚动同步
            const containerBorder = $targetScroller.scrollHeight - $targetScroller.clientHeight;
            console.log($targetScroller.scrollHeight, $targetScroller.clientHeight);
            Array.from($scrollBody.children).forEach($child => {
              // $child.style.minHeight = `${bodyHeight}px`;
              // $child.style.overflowY = `auto`;
              // 子滚动条滚动同步
              const $childScroller = $child.querySelector('[data-role="scroll-view_schild"]');
              // console.log($childScroller);
              $childScroller && $childScroller.addEventListener('scroll', e => {
                // console.log('e.target.lastScrollTop:', e.target.lastScrollTop);
                if (e.target.lastScrollTop) {
                  const distance = e.target.scrollTop - e.target.lastScrollTop;
                  // distance >= 0: 仅当向上滑动时才同步滚动
                  /* console.log('view-sync', {
                    distance: distance,
                    'e.target.scrollTop': e.target.scrollTop,
                    containerBorder: containerBorder,
                    'distance >= 0 && e.target.scrollTop <= containerBorder': distance >= 0 && e.target.scrollTop <= containerBorder,
                  }); */
                  /**
                   * 最佳的滚动同步效果是手指触摸在子滚动容器向上滚动时，子容器不滚动父容器滚动，直到滚到顶部再滚动子容器
                   * 但此效果在web端实现起来有些麻烦
                   * 目前滚动同步效果借鉴饿了么web端，
                   * 一开始其采用同步滚动，子容器向上滚动时，父容器也往上以与子容器同样的速率滚动，
                   * 这种滚动效果看似更友好，实际体验并不佳，且iOS端滚动事件不是连续触发的，只在滚动结束时才触发
                   * 近期其改为了往上滚动立即将父容器滚动到顶部的方式，不知道是有意而为之还是设计与实现有所偏差
                   * 当用户
                   * 经过研究，暂改为立即滚动到顶部的方式
                   */
                  if (distance >= 0 && $targetScroller.scrollTop <= containerBorder) {
                    console.log(e, e.target.lastScrollTop, distance, containerBorder);
                    // 立即滚到位
                    $targetScroller.scrollTop = containerBorder;
                    
                    // 慢慢滚到位
                    /* requestAnimationFrame(() => {
                      if ($targetScroller.scrollTop == e.target.scrollTop) {
                        $targetScroller.scrollTop = e.target.scrollTop;
                      } else {
                        $targetScroller.scrollTop = $targetScroller.scrollTop + distance;
                      }
                    }) */
                  }
                }
                e.target.lastScrollTop = e.target.scrollTop;
              }, { passive: true });
            })
          }, 0)
        }
      },
    },
  },
}
