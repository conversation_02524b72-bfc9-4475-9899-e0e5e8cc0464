import { createEnums } from './utils';

export const MallOrderStatus = createEnums({
  UNPAID: ['待付款', 1, '待付款'],
  PAID: ['已付款', 2, '待发货'],
  POSTED: ['已发货', 3, '待收货'],
  RECEIPTED: ['已收货', 4, '待评价'],
  COMMENTED: ['已评价', 5, '已评价'],
  REFUNDING: ['退款中', 8, '退款中'],
  REFUNDED: ['已退款', -2, '已退款'],
  REFUNDED_PART: ['部分退款', -3, '部分退款'],
  REFUND_REJECTED: ['退款驳回', 9, '退款驳回'],
  CLOSED: ['已关闭', -1, '已关闭(未付款)'],
});

export const ProcessStatus = createEnums({
  WAIT_PROCESS: ['待处理', 1, '待处理'],
  BUSINESS_REJECT: ['商家拒绝', 2, '商家拒绝'],
  PLATFORM_REJECT: ['平台拒绝', 32, '平台拒绝'],
  AWAITING_REFUND: ['待用户退回', 4, '待用户退回'],
  WAIT_BUSINESS_SIGN: ['等待商家签收', 6, '等待商家签收'],
  WAIT_REFUND: ['待退款', 8, '待退款'],
  // business_agreen_refund: ['商家同意退款', 3, '商家同意退款'],
  BUSINESS_REJECT_REFUND: ['商家拒绝退款', 16, '商家拒绝退款'],
  PLATFORM_REJECT_REFUND: ['平台拒绝退款', 26, '平台拒绝退款'],
  BUSINESS_REFUND_PEDING: ['商家退款中', 17, '商家退款中'],
  PLATFORM_REFUND_PEDING: ['平台退款中', 27, '平台退款中'],
  BUSINESS_REFUND_SUCCESS: ['商家退款成功', 15, '商家退款成功'],
  PLATFORM_REFUND_SUCCESS: ['平台退款成功', 25, '平台退款成功'],
  CHANGE_GOODS_SUCCESS: ['换货成功', 33, '换货成功'],
  CLOSED: ['用户取消申请', -1, '用户取消申请'],
  AUTO_CLOSED: ['售后申请自动关闭', -2, '售后申请自动关闭'],
});

export const MallDeliveryType = createEnums({
  EXPRESS: ['快递', 1, '物流发货'],
  SELF_PICK_UP: ['自提', 2, '发货点自提'],
});

export const MallGoodsType = createEnums(
  {
    NORMAL: ['自营货物', 1, '平台维护商品'],
    OUTLINK: ['外链', 2, '外链商品'],
  },
  true
);

export const MallGoodsStatus = createEnums(
  {
    ONLINE: ['上线', 1, '上线'],
    OFFLINE: ['下线', -1, '下线'],
  },
  true
);
// 砍价活动状态
export const BargainActionStatus = createEnums({
  UNOPEN: ['未开启', 0, '未开启'],
  ONLINE: ['生效中', 1, '生效中'],
  OVER: ['结束', 2, '结束'],
  INVALID: ['已失效', 3, '已失效'],
  DELETE: ['已删除', 4, '已删除'],
});

// 砍价任务状态
export const BargainStatus = createEnums({
  PROGRESS: ['进行中', 1, '进行中'],
  FINISH: ['完成', 2, '已完成'],
  OVER: ['失败', 3, '已结束'],
  TIMEOUT: ['超时', 4, '超时'],
  STOP: ['失败', 5, '已终止'],
  SUCCESS: ['成功', 6, '已成功'],
});

// 拼团活动状态
export const BargainGroupActionStatus = createEnums({
  UNOPEN: ['未开启', 0, '未开启'],
  ONLINE: ['生效中', 1, '生效中'],
  OVER: ['结束', 2, '结束'],
  INVALID: ['已失效', 3, '已失效'],
  DELETE: ['已删除', 4, '已删除'],
});

// 拼团任务状态
export const BargainGroupStatus = createEnums({
  INIT: ['未支付', 0, '未支付'],
  PROGRESS: ['进行中', 1, '进行中'],
  FINISH: ['完成', 2, '已完成'],
  OVER: ['失败', 3, '已结束'],
  TIMEOUT: ['超时', 4, '超时'],
  STOP: ['失败', 5, '已终止'],
});

// 秒杀状态
export const secKillStatus = createEnums({
  UNPAY: ['未支付', 1, '未支付'],
  FINISH: ['完成', 2, '已完成'],
  REFUNDING: ['退款中', 3, '退款中'],
  TIMEOUT: ['超时未支付', -1, '超时未支付'],
  REFUNDED: ['已退款', -2, '已退款'],
});
