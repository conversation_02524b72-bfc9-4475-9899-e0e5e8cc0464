<template>
  <container
    class="vehicle-inspection-online"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header title="交广领航&中原科创服务专区">
      <x-button slot="left" type="back"></x-button>
      <!-- <x-button
        slot="right"
        type="share"
        @click="popupShare"
      ></x-button> -->
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="container_box">
        <div class="card-wrap">
          <FeatureCardTitle title="车生活服务平台简介" />
          <div class="card-content2">
            <div class="card-text">河南交通广播（FM104.1）在河南地区平均收听率、市场占有率、品牌影响力均排名第一，同时还是河南省人民政府的应急广播，覆盖全省2000万以上的有车群体，为车载广播的top1。</div>
            <div class="card-text">交广领航是河南交通广播的官方客户端，具有实时分享路况、查看路况，在线收听广播，主持人互动，查看新闻资讯，参与电台活动、汽车保养、审车、保险、自营商城、旅游等诸多本地生活服务为一体的生活服务平台。</div>
            <div  class="card-btn-text">
              <p class="text">可提供的车生活服务如下</p>
              <img class="img" src="./assets/images/btn-bottom.png" alt="" />
            </div>
            <!-- <div class="vehicle-nav">
              <div
                class="nav"
                @click="pageTo('/vehicle-business/online-inspection/shops')"
              >
                <img src="./assets/images/zj.png" alt="" />
              </div>
              <div
                class="nav"
                @click="pageTo('/vehicle-business/agent-inspection')"
              >
                <img src="./assets/images/dj.png" alt="" />
              </div>
            </div>
            <div class="card-text-wrap">
              <h3>服务优势</h3>
              <p>
                商家范围遍布郑州各个城区，专属小秘书服务，最快60分钟审结，通过率高，审验过程中遇到问题交广领航帮助解决。<br>
                上门代审，专业工作人员全程负责审好再把车送回，不占用工作时间。
              </p>
            </div>
            <div
              class="card-btn"
              style="margin-top: 16px"
              @click="pageTo('/vehicle-business-v2')"
            >
              点击进入审车专区
            </div> -->
          </div>
        </div>
        <div class="card-wrap">
          <FeatureCardTitle title="审车服务" color="blue" />
          <div class="card-table-box">
            <table class="card-table">
              <tr>
                <th style="width: 20%;">审车服务</th>
                <th class="bg-th" style="width: 40%;">内购价</th>
                <th style="width: 40%;">市场价</th>
              </tr>
              <tr>
                <td>自驾审车</td>
                <td class="bg-td">139元/次/车</td>
                <td>170-200元/次/车</td>
              </tr>
              <tr>
                <td>上门审车</td>
                <td class="bg-td">219元/次/车</td>
                <td>300元/次/车</td>
              </tr>
              <tr>
                <td>优势对比</td>
                <td class="bg-td text"><span>包过！低于市场价20%左右</span>，覆盖郑州全程各个区域26个站点，交广领航直接智能推荐距离最近门店，每个门店配备专属审车小秘书，提前预约基本不用排队，60分钟内审完，可根据员工的集体需求，灵活为职工做审车专场，审车过程遇到问题，交广领航直接出面解决，确保省时省心省钱省力。</td>
                <td class="text"><span>不包过！</span>检测站服务水平参差不齐、收费乱，存在故意设卡不通过现象，遇到问题，扯皮推诿，综合体验差。遇到审车排队，基本上需要花费1天的时间。</td>
              </tr>
              <tr>
                <td colspan="3" class="td-1">备注：审车不过的话需自行维修，尤其是尾气，需另出费用。</td>
              </tr>
            </table>
            <div
              class="card-btn blue"
              style="margin-top: 12px"
              @click="pageTo('/vehicle-business-v2')"
            >
              点击进入审车专区
            </div>
            <!-- <div
              class="card-btn blue"
              style="margin-top: 12px"
              @click="pageTo('/cm/shops')"
            >
              点击进入保养专区
            </div> -->
          </div>
        </div>
        <div class="card-wrap">
          <FeatureCardTitle title="汽车保养服务" />
          <div class="card-table-box">
            <table class="card-table yellow-table">
              <tr>
                <th style="width: 20%;">保养套餐</th>
                <th class="yellow-th" style="width: 30%;">内购价</th>
                <th style="width: 30%;">市场价</th>
                <th style="width: 20%;">适用车辆</th>
              </tr>
              <tr>
                <td class="text">佰优半合成保养</td>
                <td class="yellow-td">104.1元/次/车</td>
                <td>168元/次/车</td>
                <td class="text">适用20万内非涡轮增压车型</td>
              </tr>
              <tr>
                <td class="text">佰优全合成保养</td>
                <td class="yellow-td">130元/次/车</td>
                <td>198元/次/车</td>
                <td class="text">适用涡轮增压车型</td>
              </tr>
              <tr>
                <td class="text">嘉实多金嘉护半合成保养</td>
                <td class="yellow-td">189元/次/车</td>
                <td>248元/次/车</td>
                <td class="text">适用20万以内非涡轮增压车型</td>
              </tr>
              <tr>
                <td class="text">嘉实多磁护全合成保养</td>
                <td class="yellow-td">279元/次/车</td>
                <td>398元/次/车</td>
                <td class="text">适用50万以内涡轮增压车型</td>
              </tr>
              <tr>
                <td class="text">嘉实多极护全合成5w-30/5w-40</td>
                <td class="yellow-td">369元/次/车</td>
                <td>498元/次/车</td>
                <td class="text">适用50万以内涡轮增压车型</td>
              </tr>
              <tr>
                <td class="text">嘉实多极护顶级全合成0w-20</td>
                <td class="yellow-td">469元/次/车</td>
                <td>580元/次/车</td>
                <td class="text">适用50万以内涡轮增压车型</td>
              </tr>
              <tr>
                <td class="text">优势对比</td>
                <td class="text yellow-td"><span>低于市场价30%左右</span>，覆盖郑州全城各个区域36家门店，保养方便。为员工保养车辆全部使用正品小桶机油，专车专用，非廉价大桶机油，服务水平精细，交广平台全程质量监督。</td>
                <td class="text">使用廉价大桶机油，价格虚高，整个服务过程不透明，师傅施工水平参差不齐，胡乱推销，不易辨别好坏。</td>
                <td></td>
              </tr>
            </table>
            <div
              class="card-btn"
              style="margin-top: 20px"
              @click="pageTo('/cm/shops')"
            >
              点击进入保养专区
            </div>
            <!-- <div
              class="card-btn"
              style="margin-top: 20px"
              @click="toActionsPage('/actions/plugins/app/insurance', true)"
            >
              点击进入保险专区
            </div> -->
          </div>
        </div>
        <div class="card-wrap">
          <FeatureCardTitle title="交广VIP服务" color="blue" />
          <div class="card-table-box">
            <table class="card-table">
              <tr>
                <th style="width: 30%;">会员权益</th>
                <th class="bg-th" style="width: 20%;">内购价</th>
                <th style="width: 20%;">市场价</th>
                <th style="width: 30%;">优势对比</th>
              </tr>
              <tr>
                <td class="text">免费车辆真皮方向盘套1个</td>
                <td rowspan="14" class="bg-td text">50元/年即可享受市场价2000元的车主+商城共14项双重VIP权益</td>
                <td>99元/个</td>
                <td rowspan="14" class="text td-rowspan">交广平台全程监督，合作商家均为经过筛选的优质商家，服务水平有保证，郑州全城商家合作门店多，使用方便，遇到问题由交广工作人员直接解决处理。</td>
              </tr>
              <tr>
                <td class="text">免费更换车辆后档或者四门中任意一门的太阳膜</td>
                <td>100元/门</td>
              </tr>
              <tr>
                <td class="text">免费享受车辆补胎一次</td>
                <td>30元/次</td>
              </tr>
              <tr>
                <td class="text">免费防冻液全年不限次数加满使用</td>
                <td>100元</td>
              </tr>
              <tr>
                <td class="text">免费汽车前档玻璃破损修复</td>
                <td>150元</td>
              </tr>
              <tr>
                <td class="text">免费享受车辆喷漆一块，需支付66元工时费</td>
                <td>300元/面</td>
              </tr>
              <tr>
                <td class="text">免费享受郑州市区三环内3公里不限次数搭电救援</td>
                <td>30元/次</td>
              </tr>
              <tr>
                <td class="text">免费车辆玻璃水全年不限次数加满使用</td>
                <td>-</td>
              </tr>
              <tr>
                <td class="text">免费车辆四轮充气全年不限次数使用</td>
                <td>-</td>
              </tr>
              <tr>
                <td class="text">免费车辆全车检查全年不限次数使用</td>
                <td>-</td>
              </tr>
              <tr>
                <td class="text">专属车驾管问题服务客服</td>
                <td>-</td>
              </tr>
              <tr>
                <td class="text">交广领航商城价值<span>1200元消费代金券</span>。付款时直接抵扣现金使用。</td>
                <td>1200元/年</td>
              </tr>
              <tr>
                <td class="text">7天无理由<span>免费</span>退换货，由交广直接安排快递上门取货，全年不限次数。</td>
                <td>10元/次</td>
              </tr>
              <tr>
                <td class="td-2 text">商城涵盖日常生活消费全品类1000多种商品，均可享受专属<span>会员价+优惠券双重补贴，最高可低于市场价50%，</span>支持比价。</td>
                <td class="td-2">视产品而定</td>
              </tr>
            </table>
            <div
              class="card-btn blue"
              style="margin-top: 20px"
              @click="pageTo('/vip/rights?type=car')"
            >
              点击进入会员专区
            </div>
          </div>
        </div>
        <div class="card-wrap">
          <FeatureCardTitle title="房屋装修服务" />
          <div class="card-table-box">
            <table class="card-table yellow-table">
              <tr>
                <th style="width: 20%;">装修服务</th>
                <th class="bg-th yellow-th" style="width: 40%;">内购价</th>
                <th style="width: 40%;">市场价</th>
              </tr>
              <tr>
                <td>100平米</td>
                <td class="yellow-td">12.98万元/100平</td>
                <td>20万元左右/100平</td>
              </tr>
              <tr>
                <td class="td-3">优势对比</td>
                <td class="text td-3 yellow-td"><span>低于市场价35%左右，</span>整个装修过程由河南交通广播交广领航全程监督、南方、谢小疯、海洋等知名主持人亲自签订质量监督协议，记者时巡检工地，从毛坯到拎包入住，一线大牌，一价全含。还送一系列装修大礼包。</td>
                <td class="text td-3">价格较高，服务质量参差不齐，乱加价现象较多，遇到问题维权很难，装修材料不易辨别。</td>
              </tr>
            </table>
            <div
              class="card-btn"
              style="margin-top: 20px"
              @click="toActionsPage('/actions/plugins/template/signup/?id=1554&actionId=1554')"
            >
              点击进入服务专区
            </div>
          </div>
        </div>
      </div>
    </content-view>
  </container>
</template>

<script>
import { isInJglh } from '@/common/env';
import { getVersion, parseJglhURL } from '@/bridge';
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { dialog, loading } from '@/bus';
import { getBoxConfig, getBoxOrder, getBoxDetail } from './api';
import BizRichText from '@/views/components/BizRichText.vue';
import FeatureCardTitle from '@pkg/actions/components/FeatureCardTitle.vue';

const jglhVersion = getVersion();
export default {
  name: 'FeatureShowcase',
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      keepAlive: true,
      pageData: {},
      maintains: [
        { path: '/cm/shops', category: 101 },
        { path: '/cm/shops', category: 101 },
        { path: '/cm/shops', category: 25 },
        { path: '/cm/shops', category: 25 },
        { path: '/cm/shops', category: 25 },
        { path: '/cm/shops', category: 25 },
      ],
      titleImgs: [
        require('@pkg/actions/assets/images/insurance.png'),
        require('@pkg/actions/assets/images/feature-vip.png'),
        require('@pkg/actions/assets/images/feature-furnish.png'),
      ],
      contentImgs: [
        require('@pkg/actions/assets/images/maintain.png'),
        require('@pkg/actions/assets/images/feature-interests.png'),
      ],
    };
  },
  components: {
    BizRichText,
    FeatureCardTitle,
  },
  computed: {
    // 计算属性
  },
  mounted() {
    // 元素挂载结束
  },
  methods: {
    pageTo(url) {
      const options = parseJglhURL(url);
      // this.$_router_pageTo(options.url, {
      //   ...options,
      //   titleBar: false,
      //   progressBar: false,
      // });
      let params = (/\?/.test(options.url) ? '&' : '?') + ['cooperationChannel', 'zykc'].join('=');
      this.$_router_push(options.url + params);
    },
    authPageTo(url) {
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      // this.$_router_push(url);
      this.pageTo(url);
    },
    toActionsPage(url, login) {
      if (!this.$_auth_isLoggedIn && login) {
        this.$_auth_login();
        return;
      }
      let params = (/\?/.test(url) ? '&' : '?') + ['cooperationChannel', 'zykc'].join('=');
      this.$_router_pageTo(`${location.origin}${url}${params}`, {
        titleBar: true,
        shareButton: true,
      });
    },
    requireAuth() {
      return new Promise((resolve, reject) => {
        if (!this.$_auth_isLoggedIn) {
          this.$_auth_login();
          return;
        }
        resolve();
      });
    },
    initPageData: function () {
      // 获取页面加载的数据
    },
    init() {
      // 初始化数据
      this.status = AppStatus.READY;
      // this.status = AppStatus.ERROR;
    },
    reload() {
      this.status = AppStatus.LOADING;
      // 重新加载页面
      this.init();
    },
    onResume() {
      // 页面重新激活时在重新获取数据
      // this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.container_box {
  padding: 260px 20px 20px;
  background: #f0f0f0 url(./assets/images/feature-bg.png) no-repeat center top;
  background-size: 100% auto;
}
.card-wrap {
  margin-bottom: 20px;
  position: relative;
  margin-bottom: 56px;
  > .card-content,.card-content2,.card-table-box {
    position: relative;
    z-index: 2;
  }
  .card-content {
    padding: 12px 12px 24px;
    background: #fff;
    box-shadow: 2px 3px 3px 0px #f4f8fe;
    border-radius: 13px;
  }
  .card-content2{
    padding: 12px 12px 0;
    background: #fff;
    box-shadow: 2px 3px 3px 0px #f4f8fe;
    border-radius: 13px;
  }
  .card-table-box{
    padding: 5px 5px 22px;
    background: #fff;
    box-shadow: 2px 3px 3px 0px #f4f8fe;
    border-radius: 13px;
  }
}
.card-table{
  border-collapse: separate;
  border-spacing: 0;
  border: none;         
  font-size: 10px;
  text-align: center;
  color:#0D1A3E;
  border-radius: 10px;
  border-right: 1px solid #EFEFEF;
  tr td,tr th{
   border: 1px solid #EFEFEF;
  }
  tr th{
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    background: #EFEFEF;
    height: 37px;
    border-bottom: 0;
    border-right: 0;
    &:nth-child(1){
      border-radius: 10px 0 0 0;
    }
    &:last-child{
      border-right: 1px solid #EFEFEF;
      border-radius: 0 10px 0 0;
    }
  }
  tr td{
    height: 30px;
    border-bottom: 0;
    border-right: 0;
    span{
      color:#E7211C
    }
    // &:last-child{
    //   // border-right: 1px solid #EFEFEF;
    // }
  }
  tr th.bg-th{
    background: #1D57F3;
    color:white
  }
  tr td.bg-td{
    background: #F4F4FE;
    color:#0325AF
  }
  tr td.text{
    text-align: justify;
    padding: 4px;
  }
  tr td.td-rowspan{
    border-bottom:1px solid #EFEFEF; 
    border-radius: 0 0 10px 0;
  }
  tr:last-child{
    td.td-1{
      border-bottom:1px solid #EFEFEF; 
      border-radius: 0 0 10px 10px;
    }
    td.td-3{
      &:nth-child(1){
        border-radius: 0 0 0 10px;
      }
      &:last-child{
        border-radius: 0 0 10px 0;
      }
      border-bottom:1px solid #EFEFEF;
    }
    td.td-2{
      border-bottom:1px solid #EFEFEF; 
      &:nth-child(1){
        border-radius: 0 0 0 10px;
      }
    }
  }
}
.card-table.yellow-table{
  tr th.yellow-th{
    background: #FE6119;
    color:white
  }
  tr td.yellow-td{
    background: #FEF4E8;
    color: #EB4F07;
  }
  tr:last-child td{
    border-radius:0;
    border-bottom: 1px solid #EFEFEF;
    &:nth-child(1){
      border-radius: 0 0 0 10px !important;
    }
    &:last-child{
      border-radius: 0 0 10px!important;
    }
  }
}
.card-text{
  font-size: 9px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #0D1A3E;
  text-indent: 15px;
  text-align: justify;
  &:nth-child(2){
    margin-top: 14px;
  }
}
.card-btn-text{
  text-align: center;
  margin-top: 10px;
  padding-bottom: 10px;
  .text{
    font-size: 16px;
    font-weight: bold;
    color: #FE551F;
    line-height: 16px;
  }
  .img{
    width: 8px;
    height: 8px;
  }
}
.card-text-wrap {
  line-height: 1;
  > h3 {
    padding-left: 10px;
    font-size: 12px;
    font-weight: bold;
    color: #e7211c;
    position: relative;
    margin-bottom: 4px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 3px;
      height: 12px;
      background: linear-gradient(90deg, #e7211c, #ff7810);
    }
  }
  > p {
    font-size: 10px;
    color: #0d1a3e;
    line-height: 16px;
  }
  &.card-text-list {
    > p {
      &::before {
        content: '•';
        display: inline-block;
        width: 1em;
      }
    }
  }
}
.card-img-title {
  margin-bottom: 12px;
  img {
    display: block;
    width: 100%;
    height: auto;
    user-select: none;
    pointer-events: none;
  }
}
.card-btn {
  width: 200px;
  height: 33px;
  background: linear-gradient(0deg, #e7211c, #ffa34a);
  box-shadow: 0px 2px 3px 0px rgba(231, 33, 28, 0.5),
    inset 0px 2px 3px 0px #fff8a4;
  border-radius: 17px;
  text-align: center;
  line-height: 33px;
  margin: 0 auto;
  font-size: 16px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0px 2px 3px #e7211c;
  &.blue {
    background: linear-gradient(0deg, #0c47e7, #2761fa);
    box-shadow: 0px 2px 3px 0px #8aaaff, inset 0px 2px 3px 0px #a4bdff;
    text-shadow: 0px 2px 3px #0e49e9;
  }
}
.vehicle-nav {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  width: 100%;
  .nav {
    flex: 1;
  }
  img {
    display: block;
    width: 100%;
    height: auto;
    &:not(:last-child) {
      margin-right: 4px;
    }
  }
}
.maintain {
  width: 100%;
  line-height: 1;
  position: relative;
  margin-bottom: 6px;
  > img {
    display: block;
    width: 100%;
    height: auto;
  }
  .service-wrap {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .maintain-item {
    flex: 1;
    width: 100%;
  }
}
.content-img,
.title-img {
  width: 100%;
}
</style>
