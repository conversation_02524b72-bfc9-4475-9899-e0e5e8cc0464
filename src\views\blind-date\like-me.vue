<template>
  <!-- 页面包裹组件，处理初始化、前后台切换等状态 -->
  <container @ready="onReady" @leave="onLeave" @resume="onResume" @init="init">
    <!-- 头部导航 -->
    <x-header ref="header" :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <!-- 页面内容包裹组件 -->
    <content-view ref="content" :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div class="header-tabs">
          <div
            class="tab-item"
            :class="{ active: activeTab === 1 }"
            @click="switchTab(1)"
          >
            收到的心动
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 2 }"
            @click="switchTab(2)"
          >
            未读的心动
          </div>
        </div>
        <div v-if="loading" class="loading">加载中...</div>
        <div v-else-if="userList.length > 0" class="rank-list">
          <div
            class="rank-list-item"
            v-for="item in userList"
            :key="item.id"
            @click="goUserDetail(item.id)"
          >
            <div class="rank-item-left">
              <div class="avatar-small">
                <img :src="getImageUrl(item.profileImages)" alt="头像" />
              </div>
              <div class="user-info">
                <div class="user-name">{{ item.nickName }}</div>
                <div class="user-id">
                  {{ item.age }}岁 · {{ item.occupation }} ·
                  {{ item.location }}
                </div>
              </div>
            </div>
            <div class="rank-item-right">
              <div class="score">
                <van-icon name="fire" color="#ff5858" size="16" />{{
                  formatNumber(item.popularityCount)
                }}
              </div>
              <!-- <div class="label">人气值</div> -->
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <page-placeholder
            :icon="require('@/components/Page/images/placeholder.png')"
          >
            <div class="empty-state-text">缘分正在赶来的路上</div>
          </page-placeholder>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
// 工具
import { AppStatus } from '@/enums';
import { toast, loading } from '@/bus';
import { mapState } from 'vuex';
import { getImageURL } from '@/common/image';
// import { getActivityList } from '@/api'; // 假设有这个API
import {
  readSendHeartbeat,
  getReceivedHeartbeatsUnreadList,
  getReceivedHeartbeats,
} from '@/views/blind-date/api';

// 组件
import { Button, Icon } from 'vant';
import PagePlaceholder from '@/components/Page/PagePlaceholder.vue';

export default {
  name: 'LikeMe',
  components: {
    [Button.name]: Button,
    [Icon.name]: Icon,
    PagePlaceholder,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      userList: [],
      activeTab: 1,
      loading: false,
    };
  },
  computed: {
    ...mapState(['supportWebP']),
    pageTitle() {
      return '对我心动';
    },
  },
  methods: {
    init() {
      this.loading = true;
      let fetchFn = getReceivedHeartbeats;
      if (this.activeTab === 2) {
        fetchFn = getReceivedHeartbeatsUnreadList;
      }
      fetchFn()
        .then(res => {
          this.userList = res || [];
          this.status = AppStatus.READY;
        })
        .catch(err => {
          toast().tip(err || '加载失败');
          this.status = AppStatus.ERROR;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    switchTab(tab) {
      this.activeTab = tab;
      this.init();
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image.startsWith('http')
        ? image
        : getImageURL(
            image,
            `?imageView2/1/w/150/h/150/format/${
              this.supportWebP ? 'webp' : 'jpg'
            }/q/90`
          );
    },
    formatNumber(num) {
      // 格式化数字，添加千位分隔符
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
    onReady() {
      this.init();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    goUserDetail(id) {
      readSendHeartbeat(id)
        .then()
        .finally(() => {
          this.$router.push({
            path: '/blindDate/user/' + id,
          });
        });
      // if (this.activeTab === 2) {
      // } else {
      //   this.$router.push({
      //     path: '/blindDate/user/' + id,
      //   });
      // }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/variable/global.scss';

.header-tabs {
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #fff;

  .tab-item {
    position: relative;
    font-size: 16px;
    color: #999;
    padding: 0 20px;

    &.active {
      color: #ff4e78;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: #ff4e78;
        border-radius: 1px;
      }
    }
  }
}

.loading {
  text-align: center;
  padding: 10px 0;
}

.rank-list {
  box-sizing: border-box;
  padding: 15px;

  .rank-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;

    &:first-child {
      border-top-left-radius: 7px;
      border-top-right-radius: 7px;
    }

    &:last-child {
      border-bottom-left-radius: 7px;
      border-bottom-right-radius: 7px;
    }

    .rank-item-left {
      display: flex;
      align-items: center;

      .avatar-small {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 12px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .user-info {
        .user-name {
          font-size: 16px;
          color: #333;
          margin-bottom: 4px;
        }

        .user-id {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .rank-item-right {
      display: flex;
      align-items: center;
      flex-direction: column;

      .score {
        font-size: 16px;
        font-weight: 500;
        color: #333;

        .van-icon {
          margin-right: 5px;
        }
      }

      .label {
        font-size: 10px;
        color: #999;
      }
    }
  }
}

.empty-state {
  padding: 0 20px;

  .empty-state-text {
    font-size: 14px;
    color: #999;
    margin-bottom: 15px;
  }

  .empty-state-btn {
    .van-button {
      padding: 0 25px;
      height: 35px;
      line-height: 33px;
      background-color: #ff5858;
      border-color: #ff5858;

      &:active {
        background-color: darken(#ff5858, 5%);
        border-color: darken(#ff5858, 5%);
      }
    }
  }
}
</style>
