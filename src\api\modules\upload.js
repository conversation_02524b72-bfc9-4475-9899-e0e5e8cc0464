import APIs from '../apis';
import { fetch_timeout } from '../request/';
import { saveTokenToStorage } from '@/store/storage';
import { getImageUploadToken, setImageUploadToken } from '@/common/image';
import APIModel from '@/api/APIModel';

const api = new APIModel({
  // 获取七牛上传token
  '/upload/token': '/Radio/resource/cloud/bulk/gettoken',
});

/**
 * 获取上传token
 * @param {number} type 上传类型 1：图片，2视频
 */
export function getQiniuUploadToken(type) {
  const url = api.render('/upload/token');
  return api.doGet(url, { type }, { headers: {} }).then(res => {
    return res;
  });
}

/**
 * 获取图片上传token，先取本地缓存中可用token，本地没有则从接口获取
 * @returns {*}
 */
export function getUploadToken() {
  const token = getImageUploadToken();
  if (token) return Promise.resolve(token);
  return getQiniuUploadToken(1).then(res => {
    setImageUploadToken(res.token);
    return res.token;
  });
}

export function initQiniuUploadToken(rcallback) {
  // token刷新周期：30分钟
  const FETCH_INTERVAL = 1000 * 60 * 30;
  let params = {};
  let failCount = 0;
  // let rcallback;
  const getTokenFromServer = () => {
    return getQiniuUploadToken(1).then(res => {
      params = res;
    });
  };

  const refreshToken = () => {
    getTokenFromServer()
      .then(res => {
        failCount = 0;
        rcallback && rcallback(params);
        setTimeout(() => {
          refreshToken();
        }, FETCH_INTERVAL);
      })
      .catch(() => {
        // 获取失败一定时间后重试
        setTimeout(() => {
          refreshToken();
        }, 1000 * (20 + failCount * 10));
        failCount++;
      });
  };
  refreshToken();
}

/**
 * 上传图片文件到七牛
 * @param {string} file
 * @param {string} token
 */
export function uploadToQiniu(params) {
  return fetch_timeout({
    url: 'https://upload.qiniup.com/',
    method: 'POST',
    body: params,

    mode: 'cors',
    timeout: 15000,
  });
}
