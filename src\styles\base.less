// 由于weui使用了less，为了引入less，此基础样式也使用less

@import './weui/weui.less';
@import './iconfont.css';
@import './theme/white.less';
@import './theme/gray.less';
@import './theme/fullscreen.less';

// app styles
@header-height: 45px;
@max-index: 3;
@lhui-color: rgba(56, 142, 253, 1);
// @lhui-color: #fe5418;
@lhui-color-dark: rgba(56, 142, 253, 1);
@lhui-color-light: rgba(56, 142, 253, 0.8);
// 2021-12-31改版
@lhui-header-bg-color: rgba(255, 255, 255, 1);
@lhui-header-font-color: rgba(51, 51, 51, 1);
@lh-2022-primary-color: rgb(253, 73, 37, 1);

html,
body {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Wenquanyi Micro Hei, Arial, sans-serif;
  // font-family: 'Helvetica Neue',<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>ang<PERSON>-Regular,'Hiragino Sans GB','Microsoft Yahei',sans-serif;;
  -webkit-touch-callout: none; /* 禁用 wkwebview长按链接出现菜单 */
  -webkit-tap-highlight-color: rgba(
    0,
    0,
    0,
    0
  ); /* 将ios点击元素出现的虚影设为透明*/
  position: relative;
  /* prettier-ignore */
  max-width: 750PX;
  margin: 0 auto;
}
input,
textarea {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Wenquanyi Micro Hei, Arial, sans-serif;
}
// weui custom style
.weui-select {
  height: auto;
  line-height: 1;
}

.weui-cell__bd {
  word-break: break-word;
}
.weui-btn {
  border-radius: 3px;
}
.weui-btn-reactive {
  width: 100%;
  border-radius: 0;
}
input,
textarea {
  &::placeholder {
    color: rgb(178, 178, 178);
  }
}
.weui-btn_default {
  color: #000000;
  background-color: #f8f8f8;
  border: 1px solid #dadada;
}
.weui-cells__title {
  padding: 5px 15px;
  background: white;
  margin-bottom: 0;
  font-size: 16px;
  color: #4f4f4f;
}
.weui-cell {
  padding: 10px;
  &:before {
    display: none;
  }
  border-top: 1px solid #efefef;
  &:first-child {
    border-top: 0;
  }
}
.weui-agree__checkbox:checked:before {
  color: @lhui-color;
}
.weui-dialog {
  user-select: none;
}
.weui-dialog__bd {
  font-size: 14px;
}
.weui-dialog__bd:first-child {
  max-height: 70vh;
  overflow: hidden;
  overflow-y: scroll;
}

/* weui picker style start*/
/*
  weuijs.picker 未适配rem单位，此处需将其单位固定为px不转换为rem
  px2rem 注释容易被构建工具移除，此处统一使用大写PX来保持px单位不被转换
  rem vs vw: rem可控制rem最大基准值，在屏幕宽度过大时也可保证页面的正常展示，故此处使用rem
*/
.weui-picker {
  font-size: 16px;
}
.weui-picker__indicator {
  height: 34px;
  top: 102px;
}
.weui-picker__action {
  color: #2196f3;
}
.weui-picker__bd {
  height: 238px;
}
.weui-picker__mask {
  background-size: 100% 102px;
}
.weui-picker__item {
  padding: 0;
  height: 34px;
  line-height: 34px;
}
/* weui picker style end*/

/* 按钮loading */
.btn-loading:after {
  content: '\e60c';
  font-family: iconfont;
  animation: loading 800ms infinite linear;
  display: inline-block;
  margin-left: 5px;
}

@keyframes loading {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

/*weui check-radio style*/
@icon-size: 20px;
@icon-font-size: 15px;
.weui-icon-checked {
  border-radius: 50%;
  border: 1px solid #dadada;
  width: @icon-size; /*px*/
  height: @icon-size; /*px*/
  line-height: @icon-size; /*px*/
  &::before {
    font-size: 15px;
  }
}
.weui-cells_radio .weui-check:checked + .weui-icon-checked {
  background: @lhui-color;
  color: white;
  border-color: @lhui-color;
}
.weui-cells_radio .weui-check:checked + .weui-icon-checked:before {
  color: white;
}
.weui-switch:checked,
.weui-switch-cp__input:checked ~ .weui-switch-cp__box {
  background: @lhui-color;
  color: white;
  border-color: @lhui-color;
}
.weui-cells_overlay,
.weui-cells_overlaying {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: none;
  .weui-cells_disabled & {
    display: block;
  }
}
.weui-cells_overlaying-transparent {
  display: block;
}
.weui-cells_overlaying {
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
}
/*@icon-size: 20px;
.weui-icon-checked {
  border-radius: 50%;
  border: 1px solid #DADADA;
  width: @icon-size;
  height: @icon-size;
  &::before{
    display:none;
  }
}
.weui-cells_radio .weui-check:checked+.weui-icon-checked {
  background:url(./images/radio-checked.png) center center no-repeat;
  background-size: 100%;
  background-size: cover;
  border:0;
}*/

/*rich text image class*/
.rt-image {
  background-color: #d8d7d7;
  vertical-align: bottom;
}

.flex {
  display: flex;
}
.flex-row {
  .flex;
  flex-direction: row;
}
.flex-col {
  .flex;
  flex-direction: column;
}
.flex-item {
  flex: 1;
}
.flex-center {
  justify-content: center;
  align-items: center;
}

// will deprecate
.center {
  justify-content: center;
  align-items: center;
}

/*html,body{
  font-family:STHeiti,'Microsoft YaHei',Helvetica,Arial,sans-serif;
}*/

.container {
  background: #eeeff3;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.26);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  .flex-col;
  &.theme-white {
    .theme-white;
  }
  &.theme-gray {
    .theme-gray;
  }
  &.container-fullscreen {
    .fullscreen-page;
  }
}

.header {
  display: flex;
  background: @lhui-header-bg-color;
  color: @lhui-header-font-color;
  // border-bottom:1px solid @lhui-color-dark;
  height: @header-height;
  line-height: @header-height;
  position: absolute;
  user-select: none;
  /*position:fixed;*/
  top: 0;
  left: 0;
  width: 100%;
  z-index: @max-index;
  .jglh.old-version & {
    border-radius: 0 0 3px 0;
  }
  // .container-fullscreen & {
  //   background: rgba(@lhui-color, 0);
  // }
}
.white-header.header {
  background: @lhui-header-bg-color;
  color: @lhui-header-font-color;
}
.fixed-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  line-height: @header-height;
  height: @header-height;
  background: white;
  border-top: 1px solid #efefef;
  z-index: @max-index;
}

.content-wrapper,
.page-content {
  margin-top: @header-height;
  user-select: none;
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0;
  // .container.container-fullscreen & {
  //   margin-top: 0!important;
  // }
}
.scroller {
  flex: 1;
  /*overflow: auto;*/
  height: 100%;
  -webkit-overflow-scrolling: touch;
  user-select: none;
}
.scroll-loading {
  text-align: center;
  color: #bfbfbf;
  padding: 5px;
  font-size: 0.9em;
  padding: 10px;
  /*  visibility: hidden;
  &.scroll-loading-show{
    visibility: visible;
  }*/
}

// 该设置会导致元素闪烁
.overlaying .scroller {
  -webkit-overflow-scrolling: auto;
  overflow-y: hidden !important;
}
.align-right {
  text-align: right;
}
.align-left {
  text-align: left;
}
.align-center {
  text-align: center;
}
.fn-left {
  float: left;
}
.fn-right {
  float: right;
}
.rmb {
  // margin:5px;
}
.rmb-normal {
  font-weight: 400;
}
.rmb:before {
  font-family: sans-serif, 'Helvetica Neue', Helvetica, 'PingFang SC';
  margin-right: 1px;
  content: '\A5';
  font-size: 0.9em;
}

/* button  style */
@lh-blue: #51b4fe;
.btn-area {
  padding: 10px;
}

.btn-blue {
  background: #53b5ff;
  border-color: #4eaaed;
  border-radius: 2px;
  &:not(.weui-btn_disabled):active {
    background: #4eaaed;
  }
}

.img-container {
  background: center center no-repeat transparent;
  background-size: cover;
  display: inline-block;
}

.panel-title {
  margin: 0;
  padding: 5px 10px;
  font-size: 16px;
  border-bottom: 1px solid #f1f1f1;
}
.panel-content {
  padding: 5px 10px;
}

.panel {
  margin: 5px 0;
}

.top2head {
  top: @header-height!important;
  .ios.jglh & {
    top: (@header-height + @ios-statusbar-height + 1px) !important;
  }
  .iphonex.ios.jglh & {
    top: (@header-height + @iphonex-statusbar-height + 1px) !important;
  }
}

.pad2top {
  padding-top: 0;
  .ios.jglh & {
    padding-top: @ios-statusbar-height + 1px;
  }
  .iphonex & {
    padding-top: @iphonex-statusbar-height + 1px;
  }
}
.pad2head {
  padding-top: @header-height;
  .ios.jglh & {
    padding-top: @header-height + @ios-statusbar-height + 1px;
  }
  .iphonex & {
    padding-top: @header-height + @iphonex-statusbar-height + 1px;
  }
}

/*svg class*/
svg[class^='icon-'] {
  /* 通过设置 font-size 来改变图标大小 */
  width: 1em;
  height: 1em;
  /* 图标和文字相邻时，垂直对齐 */
  vertical-align: -0.15em;
  /* 通过设置 color 来改变 SVG 的颜色/fill */
  fill: currentColor;
  /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
     normalize.css 中也包含这行 */
  overflow: hidden;
}

/*ios 软键盘弹起后将输入框区域定位到顶部，避免页面标题栏被顶出视界以外*/
/*android收起键盘后输入框焦点不消失，增加一个关闭按钮，引导用户*/
/*android webview未对输入框做适配*/
.panel-input {
  .textarea {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
  &.panel-inputting {
    .top2head;
    position: fixed;
    margin-top: 0px;
    width: 100%;
    height: 100%;
    z-index: 5;
    overflow-y: scroll;
    .textarea::after {
      display: none;
    }
    .panel-title {
      position: relative;
      &::after {
        content: '\d7';
        content: '完成';
        color: #4e85fb;
        position: absolute;
        right: 10px;
        /*transform:scale(1.5);*/
      }
    }
  }
}

// 适配ios6+，交广领航iOS端顶部留出一个状态栏高度
// 注：安全区域不是刘海屏机型特有的，普通机型中，状态栏的高度也是安全区域，交广领航内，当webview全屏展示时，env(safe-area-inset-top)为20px
@ios-statusbar-height: 18px;
.ios.jglh {
  .header {
    padding-top: @ios-statusbar-height;
    // padding-top: calc(constant(safe-area-inset-top) - 2PX);
    // padding-top: calc(env(safe-area-inset-top) - 2PX);
  }
  .content-wrapper,
  .page-content,
  .news-page-content {
    margin-top: @header-height + @ios-statusbar-height;
    // margin-top: calc(@header-height + constant(safe-area-inset-top) - 2PX);
    // margin-top: calc(@header-height + env(safe-area-inset-top) - 2PX);
  }
  @supports (height: constant(safe-area-inset-top)) {
    .header {
      padding-top: calc(constant(safe-area-inset-top) - 2px);
    }
    .content-wrapper,
    .page-content,
    .news-page-content {
      margin-top: calc(@header-height + constant(safe-area-inset-top) - 2px);
    }
  }
  @supports (height: env(safe-area-inset-top)) {
    .header {
      padding-top: calc(env(safe-area-inset-top) - 2px);
    }
    .content-wrapper,
    .page-content,
    .news-page-content {
      margin-top: calc(@header-height + env(safe-area-inset-top) - 2px);
    }
  }
  /*.menu ul.menus{
    top: @header-height + @ios-statusbar-height + 1px;
  }*/
  /*.panel-inputting{
    top: @header-height + @ios-statusbar-height;
  }*/
}

// iPhoneX顶部需要留出一个刘海高度（废弃）
@iphonex-statusbar-height: 38px;
// @iphonex-statusbar-height : 44px;
.iphonex.jglh {
  .header {
    padding-top: @iphonex-statusbar-height; /* px */
  }
  .content-wrapper,
  .page-content,
  .news-page-content {
    margin-top: @header-height + @iphonex-statusbar-height; /* px */
  }
}

.no-header {
  .header {
    display: none !important;
  }
  .content-wrapper {
    margin-top: 0 !important;
  }
}

/*
.app{
  overflow:inherit!important;
  .header{
    position:fixed;
  }
  .scroller{
    overflow:inherit;
    overflow-y:inherit!important;
  }
  .tab-container-sticky, .tab-panel__items{
    position: sticky;
    top: 1.2rem;
    top: calc(@header-height - 1px);
    z-index: 2;
    &.ios & {
      top: calc(@header-height + @ios-statusbar-height - 1px);
    }
  }
}*/

/* 富文本区域样式 */
.rich-text-content {
  margin: 0;
  padding: 5px;
  background: white;
  overflow: hidden;
  word-wrap: break-word;
  width: 100%;
  box-sizing: border-box;
  line-height: 1.8;
  white-space: pre-line;
  user-select: text;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  img {
    vertical-align: bottom;
    max-width: 100%;
  }
  h3,
  h4 {
    margin-top: 8px;
  }
}

/* 可选择复制文本的区域 */
.selectable {
  user-select: text;
}

/* 列表为空时 */
.list-empty {
  text-align: center;
  justify-content: center;
  background: transparent;
  color: gray;
  margin: 20px;
}

/* 带分割线样式的标题 */
.title-line {
  text-align: center;
  color: #b3b3b3;
  background: transparent;
  position: relative;
  padding: 10px;
  margin: 0 10px;
  // font-size: 14px;
  > span {
    position: relative;
    background: #eeeff3;
    padding: 0 10px;
    z-index: 1;
  }
  &::before {
    content: '';
    position: absolute;
    width: 70%;
    background: #d4d4d4;
    top: 50%;
    height: 1px; /* px */
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/**
android端输入时页面高度会被虚拟键盘挤占，某些布局下需要在此时隐藏一些元素
*/
.inputing.android {
  .android-inputing-hidden {
    display: none !important;
  }
}

/*
 iOS端，某些布局下，z-index无效，无法遮盖某些元素，此时采用隐藏元素的方式达到兼容
*/
.ios-overlaying-hidden {
  transition: all 100ms;
  opacity: 1;
  .overlaying .ios & {
    opacity: 0;
    // display: none !important;
    // visibility: hidden;
  }
}

/* beta: iOS支持0.5px边框 */
.ios {
  .weui-cell {
    border-top-width: 0.5px; /* px */
  }
}
.hairlines {
  .border-1s {
    border-width: 0.5px !important;
  }
  .border-1s-top {
    border-top-width: 0.5px !important;
  }
  .border-1s-left {
    border-left-width: 0.5px !important;
  }
  .border-1s-bottom {
    border-bottom-width: 0.5px !important;
  }
  .border-1s-right {
    border-right-width: 0.5px !important;
  }
}
/*
 文本自动显示为大写
*/
.input-bold {
  font-weight: 700;
  text-transform: uppercase;
  &::placeholder {
    font-weight: 400;
  }
}
