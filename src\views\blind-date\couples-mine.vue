<template>
  <!-- 页面包裹组件，处理初始化、前后台切换等状态 -->
  <container @ready="onReady" @leave="onLeave" @resume="onResume" @init="init">
    <!-- 头部导航 -->
    <x-header ref="header" title="我的CP">
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <!-- 页面内容包裹组件 -->
    <content-view ref="content" :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">

        <div class="couples-view-mine">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="couples-list-container">
            <van-list v-model="loading" :finished="finished" :finished-text="couplesList.length > 10 ? '没有更多了' : ''"
              @load="onLoad">
              <div class="couples-list">
                <!-- CP卡片 -->
                <couple-card v-for="(couple, index) in couplesList" :key="index" :couple="couple" :show-details="true"
                  @click="viewCoupleDetail" />

                <!-- 无数据提示 -->
                <div v-if="couplesList.length === 0 && !loading" class="empty-state">
                  <van-empty description="缘分正在路上" />
                </div>
              </div>
            </van-list>
          </van-pull-refresh>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
// 工具
import { AppStatus } from '@/enums';
import { toast, loading } from '@/bus';
import { getImageURL } from '@/common/image';
// import { getActivityList } from '@/api'; // 假设有这个API
import { getMyCpList } from '@/views/blind-date/api';

// 组件
import { List, PullRefresh, Empty, Icon } from 'vant';
import CoupleCard from './components/CoupleCard.vue';

export default {
  name: 'CouplesMine',
  components: {
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Empty.name]: Empty,
    [Icon.name]: Icon,
    CoupleCard,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      couplesList: [],
      loading: false,
      finished: false,
      refreshing: false,
      page: 1,
      pageSize: 10,
    };
  },
  mounted() {
    this.loadCouplesList();
  },
  methods: {
    init() {
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image.startsWith('http') ? image : getImageURL(image);
    },
    // 加载CP列表
    loadCouplesList() {
      getMyCpList({ status: 1, page: this.page, pageSize: this.pageSize }).then(response => {
        if (this.refreshing) {
          this.couplesList = response;
          this.refreshing = false;
        } else {
          this.couplesList = [...this.couplesList, ...response];
        }

        this.loading = false;
        this.status = AppStatus.READY;
        // 模拟数据加载完成
        if (this.pageSize >= response.length) {
          this.finished = true;
        }
        this.page++;
      });
    },
    viewCoupleDetail(couple) {
      if (!couple.id) return;
      this.$router.push({
        path: `/blindDate/cp/${couple.id}`,
      });
    },
    onLoad() {
      if (this.refreshing) {
        this.loading = false;
        return;
      };
      this.loadCouplesList();
    },
    onRefresh() {
      // 重置状态
      this.finished = false;
      this.loading = false;
      this.page = 1;
      // 重新加载数据
      this.loadCouplesList();
    },
    reload() {
      // this.status = AppStatus.LOADING;
      this.onRefresh();
      // this.status = AppStatus.READY;
    },
    onLeave() {
      // 页面离开的处理
    },
    onReady() {
    },
    onResume() {
      // 页面恢复时无感刷新数据
      this.fetchLatestData();
    },
    // 无感刷新第一页数据
    fetchLatestData() {
      // 不显示loading状态，静默获取第一页最新数据
      // let _pageSize = this.couplesList.length || this.pageSize;
      getMyCpList({ status: 1, page: 1, pageSize: this.pageSize }).then(response => {
        if (response) {
          // 直接使用新数据
          this.couplesList = response;
        }
      }).catch(error => {
        console.error('无感刷新数据失败', error);
        // 失败时不做处理，保持当前数据状态
      });
    }
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/variable/global.scss';

.couples-view-mine {
  padding: 15px;
  background-color: #f5f6fa;
  height: 100%;
  box-sizing: border-box;

  .couples-list-container {
    min-height: 100%;
    box-sizing: border-box;
  }

  .couples-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .couple-card {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    }
  }

  .couple-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .person-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .photo-wrapper {
    width: 120px;
    height: 160px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .person-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .person-info {
    text-align: center;
    width: 100%;
    line-height: 1;
  }

  .person-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
  }

  .person-details {
    font-size: 14px;
    color: #666;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .dot-divider {
    margin: 0 3px;
    color: #ccc;
  }

  .couple-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
  }

  .heart-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 71, 133, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .empty-state {
    padding: 40px 0;
  }
}
</style>
