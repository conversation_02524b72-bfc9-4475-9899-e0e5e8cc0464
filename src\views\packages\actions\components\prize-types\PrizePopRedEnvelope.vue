<template>
  <prize-pop-base :prize="prize" :prizeType="prizeType" :prizeTip="prizeTipText" v-on="$listeners">
    <template #content>
      <div class="prize-count">
        <span class="count">{{ prize.lotteryAmount || prize.amount }}</span>
        <span>元</span>
      </div>
      <div class="receive-time">
        {{ prize.applyEndTime }}过期
      </div>
    </template>
  </prize-pop-base>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';
import PrizePopBase from '../PrizePopBase.vue';

export default {
  name: 'PrizePopRedEnvelope',
  components: {
    PrizePopBase
  },
  props: {
    prize: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prizeType: PrizeTypeEnum.REDENVELOPE.valueOf()
    };
  },
  computed: {
    receiveDesc() {
      return '付款时可当现金用';
    },
    prizeTipText() {
      return '可去【我的-我的红包】查看';
    }
  }
};
</script>

<style lang="scss" scoped>
.prize-count {
  font-size: 12px;
  color: #ff273b;
  text-align: center;
  margin-bottom: 12px;

  .count {
    font-style: italic;
    font-weight: 400;
    font-size: 48px;
    color: #ff273b;
  }

  span:last-child {
    margin-left: 8px;
  }
}

.receive-time {
  font-size: 11px;
  color: #666666;
  line-height: 13px;
  text-align: center;
}
</style>
