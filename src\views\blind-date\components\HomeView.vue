<template>
  <content-view :status="status" @reload="reload" @scroll-bottom="onScrollBottom">
    <div class="blind-date-home">
      <!-- 搜索栏 -->
      <search-bar @search="handleSearch" @click-filter="showFilter = true" />

      <!-- 筛选选项卡 -->
      <!-- <filter-tabs @filter-change="handleFilterChange" /> -->

      <!-- 活动轮播图 -->
      <div class="section">
        <activity-banner @click="goToActivity" :activities="activities" />
      </div>

      <!-- CP推荐 -->
      <div class="section">
        <CPRecommendation :recommendations="cpRecommendations" @more="goToCPMore" />
      </div>

      <!-- 用户列表 -->
      <div class="section">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" :error.sync="error"
          error-text="请求失败，点击重新加载" @load="onLoad">
          <div class="user-list" v-if="filteredUsers.length > 0">
            <user-card v-for="user in filteredUsers" :key="user.id" :user="user" @click="goToUserDetail" />
          </div>
          <!-- 空状态展示 -->
          <div v-if="!loading && filteredUsers.length === 0 && !error" class="empty-state">
            <van-empty description="暂无相关用户" image="search" />
            <p class="empty-tip">您可以尝试调整筛选条件</p>
          </div>
          <div class="finished-text" slot="finished">
            {{ filteredUsers.length > 4 ? '没有更多了' : '' }}
          </div>
        </van-list>
      </div>

      <!-- 搜索过滤器弹出框 -->
      <search-filter-popup :show.sync="showFilter" :default-query="filterQuery" @filter="handleFilterSearch" />
    </div>
  </content-view>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { AppStatus } from '@/enums';
import {
  getUserList,
  getOngoingDatingActivitiesList,
  getCpList,
} from '../api';
import ContentView from '@/components/ContentView.vue';

// 导入组件
import SearchBar from './SearchBar.vue';
import FilterTabs from './FilterTabs.vue';
import ActivityBanner from './ActivityBanner.vue';
import UserCard from './UserCard.vue';
import SearchFilterPopup from './SearchFilterPopup.vue';
import { List, Empty } from 'vant';
import CPRecommendation from './CPRecommendation.vue';

export default {
  name: 'HomeView',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    SearchBar,
    FilterTabs,
    ActivityBanner,
    UserCard,
    SearchFilterPopup,
    ContentView,
    [List.name]: List,
    [Empty.name]: Empty,
    CPRecommendation,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING, // 内部状态，用于控制content-view
      showFilter: false, // 控制筛选弹窗显示
      users: [],
      filter: {
        type: 'latest', // 默认按最新筛选
        keyword: '',
      },
      query: {
        education: '',
        fieldName: '',
        gender: '',
        maxAge: null,
        minAge: null,
        orderType: '',
        page: 1,
        pageSize: 10,
        // status: 0,
      },
      filterQuery: {},
      loading: false,
      finished: false,
      error: false,
      totalCount: 0,
      activities: [],
      cpRecommendations: [],
    };
  },
  computed: {
    ...mapGetters({
      userInfo: 'auth/userInfo',
    }),
    filteredUsers() {
      let result = [...this.users];

      return result;
    },
  },
  created() {
    this.initData();
    this.initActivity();
    this.initCPRecommendations();
  },
  watch: {
    // 监听 error 状态，处理点击错误提示重新加载的逻辑
    error(newVal, oldVal) {
      // 当 error 从 true 变为 false 时，说明用户点击了错误提示
      if (oldVal === true && newVal === false) {
        // 重新加载当前页数据，不增加页码
        this.fetchUserList();
      }
    },
  },
  methods: {
    refresh() {
      // 设置加载状态
      this.loading = true;

      // 创建临时参数，保持page为1，pageSize设为当前列表长度
      const params = {
        ...this.query,
        page: 1,
        pageSize: this.query.page * this.query.pageSize || 10, // 如果当前没有数据，默认加载10条
      };

      // 直接调用API而不是fetchUserList，避免数据重复
      getUserList(params)
        .then(response => {
          const newUsers = response;
          if (newUsers && Array.isArray(newUsers.list)) {
            // 替换现有列表而不是追加
            this.users = [...newUsers.list];
            this.totalCount = newUsers.paging.total || 0;

            // 更新finished状态
            this.finished =
              this.users.length >= this.totalCount ||
              newUsers.list.length < params.pageSize;
          } else {
            // 后端返回格式不是预期的格式时
            console.warn('后端返回数据格式异常:', newUsers);
            this.finished = true;
          }

          this.status = AppStatus.READY;
          this.loading = false;
          this.error = false;
        })
        .catch(error => {
          console.error('刷新用户列表失败:', error);
          this.status = AppStatus.ERROR;
          this.loading = false;
          this.error = true;
        });
    },
    initActivity() {
      getOngoingDatingActivitiesList().then(response => {
        this.activities = response;
      });
    },
    initCPRecommendations() {
      // 这里应该调用获取CP推荐的API
      // 以下为模拟数据，实际项目中应该替换为真实的API调用
      getCpList({ status: 1, page: 1, pageSize: 10 }).then(response => {
        if (response && response.list && response.list.length > 0) {
          this.cpRecommendations = response.list[0];
        }
      });
      // this.cpRecommendations = [
      //   {
      //     femaleId: '1001',
      //     femaleName: '陈小姐',
      //     femaleAge: 26,
      //     femaleLocation: '上海',
      //     femaleOccupation: '金融行业',
      //     femaleAvatar: 'https://picsum.photos/200/300?random=1',
      //     maleId: '1002',
      //     maleName: '李先生',
      //     maleAge: 28,
      //     maleLocation: '上海',
      //     maleOccupation: 'IT工程师',
      //     maleAvatar: 'https://picsum.photos/200/300?random=2'
      //   }
      // ];
    },
    initData() {
      // 重置分页参数
      this.query.page = 1;
      this.users = [];
      this.finished = false;
      this.error = false;
      this.fetchUserList();
    },
    fetchUserList() {
      // 设置加载状态
      this.loading = true;

      // 如果已经完成，不再请求
      if (this.finished) {
        this.loading = false;
        return;
      }

      getUserList(this.query)
        .then(response => {
          const newUsers = response;
          if (newUsers && Array.isArray(newUsers.list)) {
            // 处理有分页结构的情况：{ list: [], paging: { total: 100 } }
            this.users = [...this.users, ...newUsers.list];
            this.totalCount = newUsers.paging.total || 0;
            this.finished =
              this.users.length >= this.totalCount ||
              newUsers.list.length < this.query.pageSize;
          } else {
            // 后端返回格式不是预期的格式时
            console.warn('后端返回数据格式异常:', newUsers);
            this.finished = true;
          }

          this.status = AppStatus.READY;
          this.loading = false;
          this.error = false;
        })
        .catch(error => {
          console.error('获取用户列表失败:', error);
          this.status = AppStatus.ERROR;
          this.loading = false;
          this.error = true;
        });
    },
    onLoad() {
      // 防止重复请求
      if (this.finished) return;

      // 如果是错误状态，不要增加页码，只重新加载当前页
      if (!this.error) {
        // 只有在非错误状态下才增加页码
        this.query.page += 1;
      }

      this.fetchUserList();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.initData();
    },
    handleSearch(keyword) {
      this.filter.keyword = keyword;
      // 搜索时重置列表
      this.initData();
    },
    handleFilterChange(type) {
      this.filter.type = type;
      // 过滤条件变化时重置列表
      this.initData();
    },
    handleFilterSearch(filter) {
      // 更新查询参数
      this.query.gender = filter.gender || '';
      this.query.minAge = filter.minAge || null;
      this.query.maxAge = filter.maxAge || null;
      this.query.education = filter.education || '';
      this.filterQuery = filter;
      // 重置分页和列表
      this.initData();
    },
    goToUserDetail(userId) {
      if (!userId) return;
      this.$router.push({
        path: `/blindDate/user/${userId}`,
      });
    },
    goToActivity() {
      this.$emit('switch-tab', 'ActivityView'); // 跳转到活动tab
    },
    goToCPMore() {
      this.$emit('switch-tab', 'CouplesView'); // 跳转到CP列表
    },
    // 添加处理滚动到底部的事件
    onScrollBottom() {
      // this.$emit('scroll-bottom');
    },
  },
};
</script>

<style lang="scss" scoped>
.blind-date-home {
  padding-bottom: 66px;
}

.section {
  padding: 0 15px;
}

.user-list {
  margin-top: 15px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.empty-tip {
  margin-top: 10px;
  text-align: center;
  color: #4b5563;
  font-size: 14px;
}
</style>
