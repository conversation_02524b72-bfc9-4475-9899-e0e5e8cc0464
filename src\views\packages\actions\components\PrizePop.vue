<template>
  <van-popup v-model="showPopup" overlay round :close-on-click-overlay="false" position="center" get-container="body"
    class="rights-pop" :class="{
      'prize-goods-pop': this.prizeType == this.PrizeTypeEnum.PROMOTIONAL_GOODS,
    }" @close="handleClose">
    <div class="pop-title">
      <div v-if="this.prizeType != this.PrizeTypeEnum.PROMOTIONAL_GOODS" class="text">
        恭喜你！获得了{{ titleStr }}
      </div>
      <img v-if="this.prizeType != this.PrizeTypeEnum.PROMOTIONAL_GOODS" :src="popIcon" alt="" srcset="" />
    </div>
    <div class="pop-content">
      <slot></slot>
      <component :is="currentComponent" :prize="prize" @confirm="handleConfirm" @cancel="handleClose"
        @goodsDetail="toGoodsDetail"></component>
    </div>
    <van-icon class="close-icon" @click="emit('cancel')" name="close" size="40" color="#ffffff" />
  </van-popup>
</template>
<script>
import { formatDate } from '@/utils';
import { mixinAuthRouter } from '@/mixins';
import { PrizeType as PrizeTypeEnum } from '@/enums';
import { PrizeTypeComponentMap } from './prize-types';

import { Icon, Button, Popup } from 'vant';

// 奖品标题映射
const PRIZE_TITLE_MAP = {
  [PrizeTypeEnum.GOLD]: '奖励',
  [PrizeTypeEnum.COUPON]: '消费券',
  [PrizeTypeEnum.WELFARE_COUPON]: '福利券',
  [PrizeTypeEnum.PROMOTIONAL_GOODS]: '爆款好物',
  [PrizeTypeEnum.NORMAL]: '精美礼品',
  [PrizeTypeEnum.COUPONCODE]: '兑换码',
  [PrizeTypeEnum.REDENVELOPE]: '消费红包',
  [PrizeTypeEnum.CARMEMBER]: '车主会员体验券',
  [PrizeTypeEnum.MALLMEMBER]: '商城会员体验券'
};

// 奖品图标映射
const PRIZE_ICON_MAP = {
  [PrizeTypeEnum.GOLD]: require('@pkg/actions/assets/images/gasha_prize02.png'),
  [PrizeTypeEnum.COUPON]: require('@pkg/actions/assets/images/gasha_prize03.png'),
  [PrizeTypeEnum.WELFARE_COUPON]: require('@pkg/actions/assets/images/gasha_prize03.png'),
  [PrizeTypeEnum.NORMAL]: require('@pkg/actions/assets/images/gasha_prize04.png'),
  [PrizeTypeEnum.COUPONCODE]: require('@pkg/actions/assets/images/gasha_prize05.png'),
  [PrizeTypeEnum.REDENVELOPE]: require('@pkg/actions/assets/images/gasha_prize01.png'),
  [PrizeTypeEnum.CARMEMBER]: require('@pkg/actions/assets/images/gasha_prize07.png'),
  [PrizeTypeEnum.MALLMEMBER]: require('@pkg/actions/assets/images/gasha_prize06.png'),
};

export default {
  name: 'PrizePop',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    prize: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  mixins: [mixinAuthRouter],
  components: {
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Icon.name]: Icon,
  },
  data() {
    return {
      PrizeTypeEnum,
      prizeType: PrizeTypeEnum.GOLD.valueOf(),
      showPopup: this.show,
    };
  },
  computed: {
    // 根据奖品类型返回对应的组件
    currentComponent() {
      return PrizeTypeComponentMap[this.prizeType] || null;
    },
    // 使用映射表获取奖品标题
    titleStr() {
      return PRIZE_TITLE_MAP[this.prizeType] || '';
    },
    // 使用映射表获取奖品图标
    popIcon() {
      return PRIZE_ICON_MAP[this.prizeType] || '';
    },
  },
  watch: {
    show(val) {
      this.showPopup = val;
      if (val) {
        this.prizeType = this.prize.prizeType;
      }
    },
  },
  mounted() {
    this.prizeType = this.prize.prizeType;
  },
  methods: {
    ...{ formatDate },
    toPage(url) {
      this.$_auth_push(url);
    },
    toGoodsDetail() {
      this.$_router_pageTo(this.prize.url, {
        theme: 'light',
      });
    },
    emit(event) {
      this.$emit(event);
    },
    handleConfirm() {
      this.$emit('confirm');
    },
    toVip() {
      this.handleClose();
      this.$_route_vipPage('mall');
    },
    handleClose() {
      this.$emit('cancel');
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/variable/global.scss';

.rights-pop ::v-deep {
  display: flex;
  flex-direction: column;
  background: transparent;
  width: 284px;
  max-height: 80%;
  overflow-y: visible;
  padding-top: 158px;
  box-sizing: border-box;
  top: 45%;

  .pop-title {
    box-sizing: border-box;
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    position: absolute;
    left: 0;
    top: 36px;
    z-index: 1;

    .text {
      font-size: 18px;
      color: #fef8db;
      margin-bottom: 20px;
    }

    img {
      display: block;
      width: 155px;
      height: 110px;
    }
  }

  .pop-content {
    flex: 1;
    overflow: auto;
    padding: 48px 15px 22px;
    line-height: 1;
    background: linear-gradient(0deg, #fef6f4 70%, #ffa6a6 100%);
    border-radius: 20px;

    &::-webkit-scrollbar {
      width: 0 !important;
    }
  }

  .close-icon {
    width: 40px;
    height: 40px;
    position: absolute;
    top: calc(100% + 15px);
    left: 50%;
    transform: translateX(-50%);
  }
}

.prize-goods-pop {
  padding-top: 48px;

  .pop-title {
    top: 0;
  }

  .pop-content {
    background: #fff;
    padding: 15px;
  }
}
</style>
