<template>
  <!--
    2018年7月31日17:16:32
    用途：新店铺首页顶部区域，显示店铺名称，营业时间，地址，logo的通用业务组件
  -->
  <div class="">
    <div class="header-box">
      <div class="h-content">
        <biz-image
          class="shop-logo"
          :src="shop.images[0]"
          @click="playShopPhotos(0)"
        >
          <span class="img-count">{{ shop.images.length }}</span>
        </biz-image>
        <div class="right">
          <h4 class="shop-name">{{ shop.name }}</h4>
          <div class="rater-box">
            <template v-if="shop.commentCount != 0 && shop.commentScore != 0">
              <van-icon name="star" color="#fd4925" size="13px" />
              <span class="c-score">{{ shop.commentScore.toFixed(1) }}</span>
            </template>
            <span v-else>暂无评分</span>
          </div>
        </div>
      </div>
    </div>
    <div class="b-content">
      <div class="shop-info-box">
        <div class="shop-time">
          <i class="icon_jglh icon-a-sc-dengdaishijian icon-common"></i>
          <span>营业时段：</span>
          <span>{{ serviceTime.start }} ~ {{ serviceTime.end }}</span>
        </div>
        <div class="shop-address">
          <div class="box">
            <span class="address"><i class="icon_jglh icon-a-sc-weizhidizhidingwei icon-common"></i>{{ shop.address }}</span>
            <div class="btn">
              <div class="navigation" @click="goNavigate">
                <i class="icon_jglh icon-sc-daohang icon"></i>
                <!-- <van-icon name="guide-o" class="icon" color="#000" /> -->
                <span>导航</span>
              </div>
              <div class="phone" @click="showContacts">
                <!-- <van-icon name="phone-o" class="icon" color="#000" /> -->
                <i class="icon_jglh icon-sc-dianhua icon"></i>
                <span>电话</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 
    <div class="shop-head flex-row">
      <div class="shop-head__content">
          <h4 class="shop-name">{{shop.name}}</h4>
          <div class="shop-time">
            <span>营业时段：</span>
            <span>{{serviceTime.start}} ~ {{serviceTime.end}}</span>
          </div>
      </div>
      <c-picture class="shop-logo" :src="shop.images[0]" type="?imageView2/1/w/300/h/240/format/jpg/q/100" @click="playShopPhotos(0)">
        <span class="img-count">{{shop.images.length}}</span>
      </c-picture>
    </div>
    <div class="shop-info">
      <div class="shop-about flex-row center">
        <div class="shop-address" @click="goNavigate">{{shop.address}}</div>
        <a class="shop-phone" @click="showContacts"></a>
      </div>
    </div> -->
  </div>
</template>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$border-color-gray: #e8e8e8;
// .shop-head {
//   background: white;
//   padding: 10px 10px 3px;
//   font-size: 14px;
//   .shop-name {
//     font-weight: 700;
//     font-size: 18px;
//     margin-bottom: 8px;
//     margin-right: 5px;
//     height: 54px;
//     -webkit-line-clamp: 2;
//     -webkit-box-orient: vertical;
//     display: -webkit-box;
//     overflow: hidden;
//   }
//   .shop-time {
//     color: rgb(66, 66, 66);
//     &::before {
//       content: '\e609';
//       font-family: iconfont;
//       margin-right: 8px;
//       vertical-align: top;
//     }
//   }
//   .shop-head__content {
//     flex: 1;
//   }
//   .shop-logo {
//     width: 90px;
//     height: 70px;
//     margin-right: 5px;
//     border-radius: 1px;
//     position: relative;

//     .img-count {
//       position: absolute;
//       right: 5px;
//       bottom: 5px;
//       display: inline-block;
//       width: 16px;
//       height: 16px;
//       background: rgba(255, 255, 255, 0.84);
//       text-align: center;
//       border-radius: 2px;
//       line-height: 16px;
//       color: rgb(56, 142, 253);
//     }
//   }
// }

// .shop-address {
//   flex: 1;
//   color: #424242;
//   line-height: 1.2;
//   /*border-right: 1px solid #e4e4e4;*/
//   @include border-right('#e4e4e4', 'after');
//   font-size: 0.92em;
//   &:active {
//     background: rgba(208, 208, 208, 0.11);
//   }
// }
// .shop-nav {
//   padding-left:15px;
//   &::before {
//     content: "\e679";
//     font-family: iconfont;font-size: 18px;padding-right: 5px;
//     display:block;
//     color:#4E85FB;
//   }
// }
// .shop-phone {
//   padding: 0 15px 0 17px;
//   font-size: 24px;
//   color: #4e85fb;
//   &:active {
//     background: rgba(128, 128, 128, 0.1);
//   }
//   &:before {content: "\e618";font-family: iconfont;font-size: 22px;}
// }

// .shop-info {
//   background: white;
//   border-bottom: 1px solid $border-color-gray;
//   margin-bottom: 5px;
//   font-size: 14px;
//   .shop-about {
//     padding: 0 0 8px 8px;
//     /*.icon-ditu{
//       font-size:18px;
//     }*/
//     &::before{
//       /*display:none;*/
//       content: "\e647";font-family: iconfont;font-size: 1.2em;
//     }
//   }
// }

.b-content {
  // padding: 0 15px;
}
.header-box {
  position: relative;
  .h-content {
    display: flex;
    padding: 15px 15px 0 15px;
  }

  // padding: 15px;
  // margin: 0 15px;
  .shop-logo {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    margin-right: 14px;
    position: relative;
    flex-shrink: 0;
    .img-count {
      position: absolute;
      right: 5px;
      bottom: 5px;
      display: inline-block;
      width: 15px;
      height: 15px;
      background: rgba(0, 0, 0, 0.6);
      text-align: center;
      border-radius: 3px;
      line-height: 15px;
      font-size: 11px;
      color: #fff;
    }
  }
  .right {
    flex: 1;
    .shop-name {
      font-size: 16px;
      font-weight: bold;
      color: #111111;
      @include multiline-ov(3);
    }
    .rater-box {
      color: #fd4925;
      font-size: 12px;
      .c-score {
        margin-left: 5px;
      }
    }
  }
}
.shop-info-box {
  background: #fff;
  border-radius: 10px;
  padding: 44px 15px 15px 15px;
  margin-top: -24px;
  margin-bottom: 10px;
  .shop-time {
    color: #111;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 10px;
    .icon-common{
      font-size: 14px;
      font-weight: normal;
      margin-right: 6px;
    }
    // &::before {
    //   content: '\e609';
    //   font-family: iconfont;
    //   margin-right: 6px;
    //   vertical-align: top;
    //   color: #999999;
    // }
  }
  .shop-address {
    display: flex;
    font-size: 13px;
    font-weight: bold;
    .box {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }
    .icon-common{
      font-size: 14px;
      font-weight: normal;
      margin-right: 6px;
    }
    // &::before {
    //   /*display:none;*/
    //   content: '\e647';
    //   font-family: iconfont;
    //   margin-right: 6px;
    //   color: #999999;
    // }
    .btn {
      flex-shrink: 0;
      display: flex;
      .navigation,
      .phone {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #666 !important;
        font-weight: initial;
        font-size: 10px;
      }
      .phone {
        margin-left: 16px;
      }
      .icon {
        width: 20px;
        height: 20px;
        font-size: 12px;
        font-weight: bold;
        background: #f3f3f3;
        border-radius: 50%;
        line-height: 20px;
        text-align: center;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
<script>
import { fixRichHtmlImageSize, formatShopHours } from '@/utils';
import { playPhotos, navigate } from '@/bridge';
import { ImageType } from '@/enums';
import { getImageURL } from '@/common/image';
import { Icon } from 'vant';
const Events = {
  SHOW_CONTACTS: 'show-contacts'
};
export default {
  name: 'x-shop-head',
  props: {
    shop: {
      type: Object
    }
  },
  components: {
    [Icon.name]: Icon
  },
  data() {
    return {};
  },
  computed: {
    // 商家营业时间，字段值为特殊格式，需要转换
    serviceTime() {
      const shop = this.shop;
      const start = formatShopHours(shop.serviceStartTime);
      const end = formatShopHours(shop.serviceEndTime);
      return {
        start,
        end
      };
    },
    imageCount() {
      return 1;
    }
  },
  methods: {
    goNavigate() {
      const { address, lat, lng, name } = this.shop;
      navigate({
        address,
        name: name,
        longitude: lng,
        latitude: lat,
        callback() {}
      });
    },
    playShopPhotos(index) {
      this.playPhotos(this.shop.images, index);
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function(item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, ImageType.MEDIUM)
        };
      });
      const option = {
        download: true,
        initIndex,
        photos
      };
      playPhotos(option);
    },
    showContacts() {
      this.$emit('event', Events.SHOW_CONTACTS);
    }
  }
};
</script>
