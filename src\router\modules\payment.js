import { handleError } from '../error-handler';

export default [
  // v1版本收银台地址，将逐渐废弃
  {
    path: '/order/pay/:id',
    name: '收银台1',
    redirect: '/cl/order/pay/:id',
    component: resolve => {
      import('@/views/CheckoutCounter').then(resolve).catch(handleError);
    },
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: 'payment',
        component: resolve => {
          import('@/views/Child_Payment').then(resolve).catch(handleError);
        },
      },
    ],
  },
  // v2，将逐渐废弃
  {
    path: '/:category/order/pay/:id',
    name: '收银台2',
    component: resolve => {
      import(
        /* webpackChunkName: "checkout-counter" */ '@/views/CheckoutCounter'
      )
        .then(resolve)
        .catch(handleError);
    },
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: 'payment',
        component: resolve => {
          import('@/views/Child_Payment').then(resolve).catch(handleError);
        },
      },
    ],
  },
  // v3，请统一通过mixinRouter中的特定方法跳转到本页面
  // 收银台有子路由，使用query传参会导致路由错误，所以统一使用params方式传参
  {
    path: '/cashier/:category/checkout/:id/next/:nextPath',
    name: 'CashierCheckout',
    component: resolve => {
      import(
        /* webpackChunkName: "cashier-checkout" */ '@/views/CashierCheckout'
      )
        .then(resolve)
        .catch(handleError);
    },
    children: [
      {
        path: 'payment',
        component: resolve => {
          import('@/views/Child_Payment').then(resolve).catch(handleError);
        },
      },
    ],
  },
  {
    path: '/cashier/bankcard/enter',
    name: '输入银行卡',
    component: resolve => {
      import(
        /* webpackChunkName: "checkout-enter-bankcard" */ '@/views/packages/account/CashierCheckout_EnterBankCard.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/car/cashier/pay',
    name: 'CarCashierPay',
    component: resolve => {
      import(
        /* webpackChunkName: "checkout-enter-bankcard" */ '@/views/PayCar.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
