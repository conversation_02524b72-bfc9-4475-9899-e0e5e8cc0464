
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <link rel="stylesheet" href="iconfont.css">
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                <i class="icon iconfont icon-tupian"></i>
                    <div class="name">图片</div>
                    <div class="fontclass">.icon-tupian</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-jiajian02"></i>
                    <div class="name">加</div>
                    <div class="fontclass">.icon-jiajian02</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-jinggao"></i>
                    <div class="name">警告</div>
                    <div class="fontclass">.icon-jinggao</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-guanbi"></i>
                    <div class="name">关闭</div>
                    <div class="fontclass">.icon-guanbi</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-daohang"></i>
                    <div class="name">导航</div>
                    <div class="fontclass">.icon-daohang</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-dianhua"></i>
                    <div class="name">电话</div>
                    <div class="fontclass">.icon-dianhua</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-gonggao"></i>
                    <div class="name">公告</div>
                    <div class="fontclass">.icon-gonggao</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-zuojiantou"></i>
                    <div class="name">左箭头</div>
                    <div class="fontclass">.icon-zuojiantou</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-vip"></i>
                    <div class="name">vip</div>
                    <div class="fontclass">.icon-vip</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-youjiantou"></i>
                    <div class="name">右箭头</div>
                    <div class="fontclass">.icon-youjiantou</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-shangjiantou"></i>
                    <div class="name">上箭头</div>
                    <div class="fontclass">.icon-shangjiantou</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-xiajiantou"></i>
                    <div class="name">下箭头</div>
                    <div class="fontclass">.icon-xiajiantou</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-dingdan"></i>
                    <div class="name">订单</div>
                    <div class="fontclass">.icon-dingdan</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-shoucang"></i>
                    <div class="name">收藏</div>
                    <div class="fontclass">.icon-shoucang</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-time"></i>
                    <div class="name">时间</div>
                    <div class="fontclass">.icon-time</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-weihuguanli"></i>
                    <div class="name">维护管理</div>
                    <div class="fontclass">.icon-weihuguanli</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-dianhua1"></i>
                    <div class="name">电话</div>
                    <div class="fontclass">.icon-dianhua1</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-loading"></i>
                    <div class="name">Loading</div>
                    <div class="fontclass">.icon-loading</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-ditu"></i>
                    <div class="name">地图</div>
                    <div class="fontclass">.icon-ditu</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-shoucang1"></i>
                    <div class="name">收藏</div>
                    <div class="fontclass">.icon-shoucang1</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-phone"></i>
                    <div class="name">电话</div>
                    <div class="fontclass">.icon-phone</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-p-reply-comment"></i>
                    <div class="name">回复评论</div>
                    <div class="fontclass">.icon-p-reply-comment</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-zhengque"></i>
                    <div class="name">正确</div>
                    <div class="fontclass">.icon-zhengque</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-weibiaoti105"></i>
                    <div class="name">通知</div>
                    <div class="fontclass">.icon-weibiaoti105</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-huifu"></i>
                    <div class="name">回复</div>
                    <div class="fontclass">.icon-huifu</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-map"></i>
                    <div class="name">地址</div>
                    <div class="fontclass">.icon-map</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-editing"></i>
                    <div class="name">编辑</div>
                    <div class="fontclass">.icon-editing</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-che"></i>
                    <div class="name">车</div>
                    <div class="fontclass">.icon-che</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-share"></i>
                    <div class="name">分享</div>
                    <div class="fontclass">.icon-share</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-guzhangjiance"></i>
                    <div class="name">故障检测</div>
                    <div class="fontclass">.icon-guzhangjiance</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-touxiang1"></i>
                    <div class="name">头像</div>
                    <div class="fontclass">.icon-touxiang1</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-daohang1"></i>
                    <div class="name">导航</div>
                    <div class="fontclass">.icon-daohang1</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-tongzhi"></i>
                    <div class="name">通知</div>
                    <div class="fontclass">.icon-tongzhi</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-service"></i>
                    <div class="name">客服</div>
                    <div class="fontclass">.icon-service</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-nav"></i>
                    <div class="name">导航</div>
                    <div class="fontclass">.icon-nav</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-search"></i>
                    <div class="name">搜索-搜索</div>
                    <div class="fontclass">.icon-search</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-refresh"></i>
                    <div class="name">刷新</div>
                    <div class="fontclass">.icon-refresh</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-shop"></i>
                    <div class="name">店铺</div>
                    <div class="fontclass">.icon-shop</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-delete"></i>
                    <div class="name">删除</div>
                    <div class="fontclass">.icon-delete</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-qa"></i>
                    <div class="name">常见问题</div>
                    <div class="fontclass">.icon-qa</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-inspection-mian"></i>
                    <div class="name">免检审车</div>
                    <div class="fontclass">.icon-inspection-mian</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-inspection-shen"></i>
                    <div class="name">上线审车图标</div>
                    <div class="fontclass">.icon-inspection-shen</div>
                </li>
            
                <li>
                <i class="icon iconfont icon-service-cn"></i>
                    <div class="name">客服</div>
                    <div class="fontclass">.icon-service-cn</div>
                </li>
            
        </ul>

        <h2 id="font-class-">font-class引用</h2>
        <hr>

        <p>font-class是unicode使用方式的一种变种，主要是解决unicode书写不直观，语意不明确的问题。</p>
        <p>与unicode使用方式相比，具有如下特点：</p>
        <ul>
        <li>兼容性良好，支持ie8+，及所有现代浏览器。</li>
        <li>相比于unicode语意明确，书写更直观。可以很容易分辨这个icon是什么。</li>
        <li>因为使用class来定义图标，所以当要替换图标时，只需要修改class里面的unicode引用。</li>
        <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的fontclass代码：</h3>


        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;link rel="stylesheet" type="text/css" href="./iconfont.css"&gt;</span></code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-css hljs">&lt;<span class="hljs-selector-tag">i</span> <span class="hljs-selector-tag">class</span>="<span class="hljs-selector-tag">iconfont</span> <span class="hljs-selector-tag">icon-xxx</span>"&gt;&lt;/<span class="hljs-selector-tag">i</span>&gt;</code></pre>
        <blockquote>
        <p>"iconfont"是你项目下的font-family。可以通过编辑项目查看，默认是"iconfont"。</p>
        </blockquote>
    </div>
</body>
</html>
