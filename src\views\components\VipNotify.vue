<template>
  <van-popup
    v-if="showPopup"
    v-model="showPopup"
    overlay
    round
    :close-on-click-overlay="false"
    safe-area-inset-bottom
    position="center"
    get-container="body"
    class="rights-pop"
    @close="handleClose"
  >
    <div class="pop-content">
      <div class="pop-title">
        <img :src="popIcon" alt="" srcset="">
      </div>
      <div class="pop-desc">{{ expireTime }}过期</div>
      <div class="pop-desc-extra">限时特价</div>
    </div>
    <div class="pop-bottom-btn">
      <!-- <van-button
        class="close-btn"
        round
        block
        @click="cancel"
      >{{ cancelButtonText }}</van-button> -->
      <van-button
        class="confirm-btn"
        round
        block
        type="danger"
        @click="confirm"
      >{{ confirmButtonText }}</van-button>
    </div>
    <div class="agree-checkbox">
      <van-checkbox v-model="checked" icon-size="22px">下次不再提醒</van-checkbox>
    </div>
    <van-icon class="close-icon" @click="cancel" name="close" size="36" color="#ffffff" />
  </van-popup>
</template>
<script>
import { formatDate } from '@/utils';
import { mixinAuthRouter } from '@/mixins';
import {
  setVipExpireTip,
  getVipExpireTip,
} from '@/store/storage';

import { Icon, Button, Popup, Checkbox } from 'vant';

export default {
  name: 'VipNotify',
  props: {
    vipType: {
      type: String,
      default: 'mall'
    },
    confirmButtonText: {
      type: String,
      default: ''
    },
  },
  mixins: [mixinAuthRouter],
  components: {
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Icon.name]: Icon,
    [Checkbox.name]: Checkbox,
  },
  data() {
    return {
      showPopup: false,
      popIcon: require('@/assets/images/vip-expire-text.png'),
      checked: false,
    };
  },
  computed: {
    expireTime() {
      if (this.vipType === 'mall') {
        return this.formatDate(this.$_auth_userInfo.mallMemberVipExpireTime, 'yyyy-MM-dd')
      }
      return this.formatDate(this.$_auth_userInfo.carMemberVipExpireTime, 'yyyy-MM-dd')
    }
  },
  watch: {
    // 监听路由
    $route(to, from) {
      // debugger
    }
  },
  mounted() {
    setTimeout(() => {
      // 2个条件：1.用户没有勾选不再提醒 2.用户是会员且会员即将到期
      if (getVipExpireTip() && this.vipWillExpire()) {
        this.showPopup = true;
      }
    }, 1500);
  },
  methods: {
    ...{ formatDate },
    vipWillExpire() {
      // 到期时间据今天<=15天
      if (this.vipType === 'mall' && (this.$_auth_userInfo.memberCategory == 'mall' || this.$_auth_userInfo.memberCategory == 'all')) {
        return this.$_auth_userInfo.mallMemberVipExpireTime && ((this.$_auth_userInfo.mallMemberVipExpireTime - new Date().getTime()) <= 1000 * 60 * 60 * 24 * 15)
      } else if (this.vipType === 'car' && (this.$_auth_userInfo.memberCategory == 'car' || this.$_auth_userInfo.memberCategory == 'all')) {
        return this.$_auth_userInfo.carMemberVipExpireTime && ((this.$_auth_userInfo.carMemberVipExpireTime - new Date().getTime()) <= 1000 * 60 * 60 * 24 * 15)
      }
      return false
    },
    toPage(url) {
      // this.handleClose()
      this.$_auth_push(url);
    },
    toVip() {
      this.$_router_pageTo(`/vip/share/invite?i=1003440&type=${this.vipType}`, {
        requireSignIn: false,
        sbarColor: 'black',
        sbarBgColor: '#f2f2f2'
      });
    },
    handleClose() {
      this.showPopup = false;
      if (this.checked) {
        setVipExpireTip(false)
      }
    },
    confirm() {
      this.handleClose()
      this.toVip()
    },
    cancel() {
      this.handleClose()
    },
  }
}
</script>
<style lang="scss" scoped>
@import '~styles/variable/global.scss';
.rights-pop ::v-deep {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  width: 300px;
  // max-height: 80%;
  padding-top: 172px;
  overflow-y: visible;
  background: url('~@/assets/images/vip-expire.png') no-repeat center top;
  background-size: 100% 100%;
  border-radius: 0;
  top: 40%;
  .pop-title {
    text-align: center;
    margin-bottom: 18px;
    img{
      display: inline-block;
      width: 195px;
      height: auto;
      pointer-events: none;
      -webkit-user-select: none; /* Safari 3.1+ */
      -khtml-user-select: none; /* Konqueror 3.5-4.1 */
      -moz-user-select: none; /* Firefox 2+ */
      -ms-user-select: none; /* IE 10+ */
      user-select: none; /* 标准语法 */
    }
  }
  .pop-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
    line-height: 1;
    &::-webkit-scrollbar {
      width: 0 !important;
    }
  }
  .pop-desc{
    font-weight: 500;
    font-size: 14px;
    color: #FD4925;
    margin-bottom: 12px;
    text-align: center;
  }
  .pop-desc-extra{
    font-weight: bold;
    font-size: 16px;
    color: #FD4925;
    text-align: center;
  }
  .agree-checkbox {
    padding: 0 15px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.6);
    transform-origin: center top;
    .van-checkbox__label{
      line-height: 1;
      font-size: 20px;
    }
  }
  .pop-bottom-btn {
    padding: 5px 15px 15px;
    // background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    .van-button{
      // flex: 1;
      width: 220px;
      font-size: 15px;
      line-height: 40px;
      height: 40px;
      border: 0;
      &:not(:first-child){
        margin-left: 15px;
      }
    }
    .confirm-btn {
      background: linear-gradient(90deg, #FF8E34, #FD4925);
    }
    .close-btn {
      background: #F3F3F3;
      border-color: #F3F3F3;
      color: #333333;
    }
  }
  .van-popup--center.van-popup--round{
    border-radius: 10px;
  }
  .van-icon-cross{
    top: 19px;
    font-size: 12px;
    line-height: 1;
  }
  .close-icon{
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    margin: 0 auto;
  }
}
</style>
