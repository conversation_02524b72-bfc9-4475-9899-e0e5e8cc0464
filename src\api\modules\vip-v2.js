import { fetch_timeout } from '../request/';
// import APIs from '../apis';
import APIModel from '../APIModel';

/**
 * 接口文档地址
 * http://jgrm.net:10230/swagger-ui.html#!/membership45new45controller/exchangeMembersJGYHUsingPOST
 */
const api = new APIModel({
  '/vip/interests': '/Membership-new-app/list/member/interests', // 会员权益信息
  '/vip/packages': '/Radio/membership/v3/memberCategory/info/by/category/forapp', // 会员套餐信息
  // '/vip/buy': '/Membership-new-app/save/member/order', // 提交购买或续期会员订单（不支持红包，优惠券
  '/vip/buy': '/Membership-new-app/member/order/create', // 提交购买或续期会员订单(支持红包，优惠券
  '/vip/invite/config': '/Membership-new-app/get/member/invitationCode', // 获取邀请配置信息
  '/vip/invite/records': '/Membership-new-app/list/returnamount/uid', // 获取邀请返现记录
  '/vip/invite/income': '/Membership-new-app/count/returnamount', // 邀请返现收入总额
  '/vip/latest/buyers': '/Membership-new-app/list/member/carousel', // 最近购买的会员列表
  '/vip/exchange': '/Membership-new-app/exchange/members', // 兑换会员接口
  '/vip/exchange/check': '/Membership-new-app/check/conversionCode/type', // 查询兑换码兑换类型
  '/vip/exchange/jgyh': '/Membership-new-app/exchange/members/jgyh', // 交广银河兑换码兑换

  '/vip/buy/v3': '/Radio/membership/v3/member/order/create', // 提交购买或续期会员订单(支持红包，优惠券
  '/saveMoneyRecord/list': '/Radio/membership/v3/saveMoneyRecord/paging/list/for/app', // 分页查询用户的省钱记录
  '/saveMoneyRecord/rules': '/Radio/membership/v3/memberConfig/info', // 获取配置信息[会员协议配置 省钱计算说明]

  '/vip/gift/action': '/Radio/membership/v3/memberAction/info/by/category', // 根据会员类型category查询会员活动详情
  '/vip/gift/action/by/id': '/Radio/membership/v3/memberAction/info/for/app', // 根据主键查询会员活动详情
  '/vip/gift/recieve/list': '/Radio/membership/v3/memberActionJoinRecord/paging/list', // 分页查询领取记录
  '/vip/gift/get/list': '/Radio/membership/v3/memberAction/receive/join', // 领取会员活动开卡礼-列表形式
  '/vip/gift/get/blindbox': '/Radio/membership/v3/memberAction/lottery/join', // 领取会员活动开卡礼-盲盒形式
  '/vip/gift/recieve/address': '/Radio/membership/v3/memberActionJoinRecord/set/user/revieveinfo', // 提交收货地址
  '/vip/gift/recieve/detail': '/Radio/membership/v3/memberActionJoinRecord/info/by/id', // 通过主键id查询领取记录详情
  '/vip/coupon/recieve': '/Radio/membership/v3/memberCategory/recieve/coupon', // 根据会员类型手动领取优惠券
  '/vip/buy/records': '/Radio/membership/open/member/history', // 会员开通记录
  '/vip/goods/detail': '/Radio/membership/v3/memberCategory/info/by/category/with/goods/detail', // 获取会员权益优惠券

  '/vip/save/top20': '/Radio/membership/v3/saveMoneyRecord/top20', // 省钱达人Top20
  '/vip/save/user': '/Radio/membership/v3/saveMoneyRecord/sumAmount/by/uid', // 根据会员类型查询 省钱总金额

  '/vip/routineRemind': '/Radio/membership/routineRemind', // 车主会员 一键联系客服
})

/**
 * 车主会员 一键联系客服
 */
export function getCalling(params) {
  const url = api.render('/vip/routineRemind');
  return api.doGet(url, params);
}

/**
 * 会员开通记录
 */
export function getBuyVipRecords(params) {
  const url = api.render('/vip/buy/records');
  return api.doGet(url, params);
}

/**
 * 省钱达人Top20
 * @param {string} category  // 会员类型 mall-商城版 car-车主版
 */
export function getTop20(category) {
  const url = api.render('/vip/save/top20');
  return api.doGet(url, { category });
}

/**
 * 根据会员类型查询 省钱总金额
 * @param {string} category  // 会员类型 mall-商城版 car-车主版
 */
export function getUserSaved(category) {
  const url = api.render('/vip/save/user');
  return api.doGet(url, { category });
}

/**
 * 获取会员权益优惠券
 * @param {string} category  // 会员类型 mall-商城版 car-车主版
 */
export function getVipPkgDetail (category = 'mall') {
  const url = api.render('/vip/goods/detail');
  return api.doGet(url, { category });
}

/**
 * 获取vip会员可享受的权益信息
 */
export function getVipInterests () {
  const url = api.render('/vip/interests');
  return api.doGet(url);
}

/**
 * 获取最近购买会员的用户列表
 */
export function getLatestBuyers (rows) {
  const url = api.render('/vip/latest/buyers');
  return api.doGet(url, { row: rows });
}

/**
 * 获取vip会员套餐列表
 * @param {string} category  // 会员类型 mall-商城版 car-车主版
 */
export function getVipPackages (category = 'mall') {
  const url = api.render('/vip/packages');
  return api.doGet(url, { category });
}

/**
 * 创建购买或续期VIP订单
 * @param {object} params
 * @param {string} params.id // 套餐id
 * @param {number} params.buyType // 购买类型
 * @param {string} params.buyChannel // 购买渠道
 * @param {string} params.invitationCode: // 邀请码
 * @param {object} extra 优惠券相关额外参数，详见 submitOrder
 */
// export function createVipOrder(params, extra) {
//   const url = api.render('/vip/buy');
//   const data = {
//     ...params,
//     ...extra,
//   }
//   return api.doPost(url, data);
// }

/**
 * v3版 创建购买或续期VIP订单
 * @param {object} params
 * @param {number} params.category // 会员类型
 * @param {string} params.sourceName // 访问渠道[安卓 IOS 微信小程序 微信h5]
 * @param {string} params.invitationCode: // 邀请码
 * @param {number} params.useRedEnvelope : // 是否使用余额抵扣[1:使用 0:不使用]
 * @param {number} params.reelId : // 优惠券发放记录id
 */
export function createVipOrder (params, extra) {
  const url = api.render('/vip/buy/v3');
  const data = {
    ...params,
    ...extra,
  }
  return api.doPost(url, data);
}

/**
 * 兑换vip会员
 * @param {string} code 兑换码
 */
export function exchangeVipByCode (code) {
  const url = api.render('/vip/exchange');
  return api.doPost(url, {
    conversionCode: code
  });
}

/**
 * 兑换交广银合vip会员
 * @param {string} code 兑换码
 * @param {string} carId 车牌号
 */
export function exchangeVipByCodeOfJgyh (conversionCode, carId) {
  const url = api.render('/vip/exchange/jgyh');
  return api.doPost(url, {
    conversionCode,
    carId,
  });
}

/**
 * 获取vip会员队兑换码信息
 */
export function getVipCodeType (conversionCode) {
  const url = api.render('/vip/exchange/check');
  return api.doGet(url, { conversionCode });
}

/**
 * 获取vip会员分享返现配置信息
 */
export function getVipInviteConfig () {
  const url = api.render('/vip/invite/config');
  return api.doGet(url);
}

/**
 * 获取vip会员分享邀请返现收入
 */
export function getVipInviteIncome () {
  const url = api.render('/vip/invite/income');
  return api.doGet(url);
}

/**
 * 获取vip会员分享邀请返现记录
 */
export function getVipInviteRecords (options) {
  const url = api.render('/vip/invite/records');
  return api.doGet(url, options);
}

/**
 * 获取vip会员省钱计算说明
 */
export function getSavedMoneyRules (options) {
  const url = api.render('/saveMoneyRecord/rules');
  return api.doGet(url, options);
}

/**
 * 上传图片文件到七牛
 * @param {string} file
 * @param {string} token
 */
export function uploadToQiniu (params) {
  return fetch_timeout({
    url: 'https://upload.qiniup.com/',
    method: 'POST',
    body: params,
    mode: 'cors',
    timeout: 15000,
  })
  // const url = 'http://upload.qiniup.com/';
  // return new Promise((resolve, reject) => {
  //   fetch(url, {
  //     method: 'POST',
  //     body: params
  //   }).then(res => res.json(res)).then(res => { // 必须要带res.json(res) 不然后台返回的json接收不到
  //     resolve(res)
  //   }).catch((err) => {
  //     reject(err)
  //   })
  // })
}
function upload (params) {
  return new Promise((resolve, reject) => {
    let pic = params;
    let url = 'https://upload.qiniup.com/'; // 非华东空间需要根据注意事项 1 修改上传域名
    let xhr = new XMLHttpRequest();
    // 超时设置15s设置
    xhr.timeout = 15000
    // 超时回调
    xhr.ontimeout = function () {
      reject({
        code: 504,
        message: '请求超时!',
      })
    }
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4) {
        if (xhr.status == 200) {
          try {
            // 只支持json
            resolve(JSON.parse(xhr.responseText))
          } catch (err) {
            reject({
              code: 504,
              message: '返回结果异常',
            })
          }
        } else {
          reject({
            code: xhr.status,
            message: '请求异常',
          })
        }
        // debugger
        xhr.responseText && resolve(JSON.parse(xhr.responseText))
      }
    }
    xhr.open('POST', url, true);
    // xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.send(pic);
  })
}

/**
 * 分页查询用户的省钱记录
 * @param {string} category 会员分类 mall:商城 car:车主卡
 * @param {string} page
 * @param {string} pageSize
 */
export function getSaveMoneyRecordList (options) {
  const url = api.render('/saveMoneyRecord/list');
  return api.postJSON(url, options);
}

/**
 * 根据会员类型手动领取优惠券
 * @param {string} category 会员分类 mall:商城 car:车主卡
 * @param {string} couponId 优惠券ID
 */
export function getCoupon (params) {
  const url = api.render('/vip/coupon/recieve');
  return api.doPost(url, params);
}

/**
 * 查询会员活动详情
 * @param {string} memberCategory 会员分类 mall:商城 car:车主卡
 */
export function getVipAction (category) {
  const url = api.render('/vip/gift/action');
  return api.doGet(url, { memberCategory: category });
}

/**
 * 查询会员活动详情
 * @param {string} id 活动id
 */
export function getVipActionById (id) {
  const url = api.render('/vip/gift/action/by/id');
  return api.doGet(url, { id });
}

/**
 * 领取会员活动的奖项
 * @param {string} memberCategory 会员分类 mall:商城 car:车主卡
 * @param {string} actionId 优惠券ID
 * @param {string} awardId 优惠券ID
 * @param {string} address 优惠券ID
 * @param {string} phone 优惠券ID
 * @param {string} postareaProv 优惠券ID
 * @param {string} postareaCity 优惠券ID
 * @param {string} postareaCountry 优惠券ID
 * @param {string} recvName 优惠券ID
 */
export function getListGift (params) {
  const url = api.render('/vip/gift/get/list');
  return api.postJSON(url, params);
}

/**
 * 领取会员活动的奖项
 * @param {string} memberCategory 会员分类 mall:商城 car:车主卡
 * @param {string} actionId 优惠券ID
 */
export function getBlindboxGift (params) {
  const url = api.render('/vip/gift/get/blindbox');
  return api.doGet(url, params);
}

/**
 * 查询会员活动详情
 * @param {string} id 领取记录id
 */
export function getRecieveDetail (id) {
  const url = api.render('/vip/gift/recieve/detail');
  return api.doGet(url, id);
}

/**
 * 领取会员活动的奖项
 * @param {string} actionId 会员活动id
 * @param {string} page
 * @param {string} pageSize
 */
export function getRecieveRecords (params) {
  const url = api.render('/vip/gift/recieve/list');
  return api.postJSON(url, params);
}

/**
 * 领取会员活动的奖项
 * @param {string} id 礼品领取记录id
 * @param {string} address 礼品领取记录id
 * @param {string} phone 礼品领取记录id
 * @param {string} postareaProv 礼品领取记录id
 * @param {string} postareaCity 礼品领取记录id
 * @param {string} postareaCountry 礼品领取记录id
 * @param {string} recvName 礼品领取记录id
 */
export function setRecieveAddress (params) {
  const url = api.render('/vip/gift/recieve/address');
  return api.postJSON(url, params);
}
