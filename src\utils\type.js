export function getDataType(value) {
  return Object.prototype.toString.apply(value).slice(8, -1);
}

export function isPlainObject(value) {
  return getDataType(value) == 'Object';
}

export function isArray(value) {
  return getDataType(value) == 'Array';
}

export function isFunction(value) {
  return getDataType(value) == 'Function';
}

export function isRegExp(value) {
  return getDataType(value) == 'RegExp';
}

export function isNumber(o) {
  return /^\d+(\.\d+)?$/.test(o)
}

export function isValidURL(o) {
  return /^http(s)?:\/\/.+$/.test(o)
}

export const getType = getDataType;

/**
 * 判断一个变量是否只指定的类型字符串
 * type(123).is('Number'): true
 * type(123).is('String'): false
 */
export function type(value) {
  return {
    is: type => getDataType(value) === type
  }
}
