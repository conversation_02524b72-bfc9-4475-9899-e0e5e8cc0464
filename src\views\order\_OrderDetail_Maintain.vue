<template>
  <div class="order-detail-mt">
    <tip v-if="order.orderStatus == OrderStatus.UN_CONFIRM" type="warn" :close="false">
      <div class="order-tip">您的订单已被修改，请及时确认订单</div>
    </tip>
    <!-- <tip v-else-if="order.orderStatus == OrderStatus.CONFIRMED" type="warn" :close="false">
      <div class="order-tip">订单已确认，将马上为您派工服务</div>
    </tip> -->

    <coupon :order="order"></coupon>
    
    <template v-if="order.orderStatus == OrderStatus.TO_BE_SERVE">
      <div class="user-tip">消费提醒：如对预约订单有疑问，请电话联系门店咨询</div>
    </template>

    <template v-if="order.orderStatus == OrderStatus.COMMENTED">
      <div class="order-comment flex-row center" @click="goComment">
        <span style="flex:1;">我的评分</span>
        <rater class="comment-rater" :value="order.commentScore"></rater>
      </div>
    </template>

    <!--<div v-if="order.orderStatus == OrderStatus.UN_COMMENTED" class="btn-area">
      <a href="javascript:;" class="weui-btn weui-btn_primary" @click="goComment">评价服务</a>
    </div>-->

    <panel v-if="order.orderStatus == OrderStatus.SERVING"  title="服务进度">
      <div class="weui-cell">
        <div class="weui-cell__bd">
          <div class="work-status">
            <p class="work-tip"><span>{{orderWorkStatus.workers}}</span> 已接车，正在进行保养服务作业</p>
            <p class="work-time">{{formatDate(orderWorkStatus.time, 'yyyy-MM-dd HH:mm')}}</p>
          </div>
        </div>
      </div>
    </panel>

    <panel  title="服务门店">
      <shop-card :shop="order.shop" @click.native="goNavigate">
        <div class="shop-phone" slot="right" @click.stop="callPhone(order.shop.phone)">
          <span>服务电话</span>
        </div>
      </shop-card>
    </panel>

    <panel class="order-panel order-info" v-if="order" title="订单信息">
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">订单号码</label>
          </div>
          <div class="weui-cell__bd">
                {{order.id}}
          </div>
        </div>
         <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">下单时间</label>
          </div>
          <div class="weui-cell__bd">
                {{formatDate(order.createTime, 'yyyy-MM-dd HH:mm:ss')}}
          </div>
        </div> 
        <!-- <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">预约时间</label>
          </div>
          <div class="weui-cell__bd">
              {{parseAppointmentTime(order.appointmentTime)}}
          </div>
        </div> -->
        <div class="weui-cell weui-cell-center">
          <div class="weui-cell__hd">
            <label class="weui-label">保养车辆</label>
          </div>
          <div class="weui-cell__bd">
            <div class="order-car">
              <h4 class="order-car__title">{{order.carNo}}</h4>
              <p class="order-car__note">{{order.carModelName}}</p>
            </div>
          </div>
        </div>
         <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">保养类型</label>
          </div>
          <div class="weui-cell__bd">
            {{order.orderTitle}}
          </div>
        </div>
    </panel>

    <!-- <panel class="order-panel order-content" v-if="order" title="订单项目">
        <div v-for="(item, index) in order.orderRefPartList" :key="index" class="weui-cell weui-cell__goods">
          <div class="weui-cell__bd">
            <label class="weui-label">{{item.bParts.name}}</label>
          </div>
          <div class="weui-cell__hd">
            <div class="align-right price">
              <span class="rmb">{{item.price}} </span>
              <sup class="count">x{{item.count}}</sup> 
            </div>
          </div>
        </div>
        <div v-if="order.hourDiscount" class="weui-cell">
          <div class="weui-cell__bd">
            <label class="weui-label">工时优惠金额</label>
          </div>
          <div class="weui-cell__hd">
              <div class="align-right">
                -<span class="rmb">{{formatPrice(order.hourDiscount)}}</span>
              </div>
          </div>
        </div>
        <template v-if="order.orderGoodsInfoList.length">
          <div v-for="(item, index) in order.orderGoodsInfoList" :key="index" class="weui-cell">
            <div class="weui-cell__hd">
              <label class="weui-label">{{item.name}}</label>
            </div>
            <div class="weui-cell__bd">
                <div class="align-right">
                  <div class="ticket-amount"><span class="rmb">{{item.amount}}</span></div>
                </div>
            </div>
          </div>
        </template>
        <template v-if="extra.length">
          <div v-for="(item, index) in extra" :key="index" class="weui-cell">
            <div class="weui-cell__hd">
              <label class="weui-label">{{item.name}}</label>
            </div>
            <div class="weui-cell__bd">
                <div class="align-right">
                  <div class="rmb-discount">-<span class="rmb">{{item.amount}}</span></div>
                </div>
            </div>
          </div>
        </template>
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">应付金额</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-right">
                <div class="ticket-amount"><span class="rmb">{{order.needPayAmount}}</span></div>
              </div>
          </div>
        </div>
    </panel> -->
    <order-detail-items :value="order"></order-detail-items>

    <panel class="order-panel order-pay-info" v-if="order && order.payTime" title="结算信息">
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">结算时间</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-left">{{formatDate(order.payTime, 'yyyy-MM-dd HH:mm:ss')}}</div>
          </div>
        </div>
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">结算方式</label>
          </div>
          <div class="weui-cell__bd">
            <div class="align-left">{{getPayment(order.payChannel)}}</div>
          </div>
        </div>
        <!-- <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">实付金额</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-left"><span class="rmb">{{order.amount}}</span></div>
          </div>
        </div> -->
        <div v-if="order.refundAmount" class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">退款金额</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-left">
                <span v-if="order.refundAmount" class="rmb">{{order.refundAmount}}</span>
                <span v-else>--</span>
              </div>
          </div>
        </div>
    </panel>

    <div class="btn-area">
      <a v-if="order.orderStatus == OrderStatus.UNPAID" href="javascript:;" class="weui-btn weui-btn_primary" @click="goToPay">立即支付</a>
      <a v-if="actions.couldRefund" href="javascript:;" class="weui-btn weui-btn_primary" @click="$_router_push4inputting('/order/' + order.id + '/refund/' + OrderType.MAINTAIN, { title: '申请退款' })">申请退款</a>
      <template v-else-if="order.orderStatus == OrderStatus.UN_CONFIRM" >
        <a href="javascript:;" class="weui-btn weui-btn_primary" @click="confirmReOrder">确认订单</a>
      </template>
      <a v-else-if="order.orderStatus == OrderStatus.CONFIRMING" href="javascript:;" class="weui-btn weui-btn_primary" @click="goToPay">去支付(补缴{{order.reorderPrice}}元)</a>
      <!-- <a v-else-if="order.orderStatus == OrderStatus.SERVIED" href="javascript:;" class="weui-btn weui-btn_primary" @click="confirmGetCar">确认提车</a> -->
      <a v-else-if="order.orderStatus == OrderStatus.UN_COMMENTED" href="javascript:;" class="weui-btn weui-btn_primary" @click="goComment">评价服务</a>
    </div>
    </div>
</template>
<style lang="scss">
  $highlight-color:#F29E5C;
  .order-detail-mt{
    .order-panel .weui-cell.weui-cell-center{
      align-items:center;
    }
    .weui-cell__goods {
      .weui-label {
        line-height: 1.2;
      }
      .price {
        position: relative;
      }
      .count {
        position: absolute;
        bottom: -1em;
        color: #bfbfbf;
        right: 0;
      }
    }

    .user-tip{
      color:#6F6E6F;
      font-size:0.9em;
      text-align:center;
    }
    .order-tip{
      text-align:center;
    }
    .see-more{
      text-align:center;
      display:block;
      font-size:0.9em;
      >b{
        color:$highlight-color;
        margin:2px;
      }
    }
    .work-status {
      .work-time{
        font-size:0.9em;
        color:gray;
      }
    }
    .rmb{
      margin:0;
    }
    .panel{
      padding:5px;
    }
    .order-car{
      .order-car__title{
        font-weight:400;
      }
      .order-car__note{
        font-size:0.9em;
      }
    }
    .order-panel{
      .panel-title{
        border-bottom:0;
      }
      .weui-cell{
        border-top:0;
        padding:5px 10px;
        align-items: flex-start;
        &:last-child{
          border-bottom:0;
        }
      }
    }
    .order-content {
      .weui-label{
        width:auto;
      }
      .weui-cell__hd{
        width: 80px;
      }
      .weui-cell{
        padding-top:8px;
        padding-bottom:8px;
      }
    }
    .rmb-discount {
      color:#F29E5C;
    }
    .order-pay-info{
      .weui-cell{
        padding-top:3px;
        padding-bottom:3px;
      }
      .weui-label{
        width:auto;
        margin-right:5px;
        &::after{
          content:':';
          padding:0 3px;
        }
      }
    }
    .order-status {
      color:#EF6C00;
    }
    .refund-tip{
      color:gray;
      font-size:0.8em;
    }
    .order-comment {
      background: white;
      padding: 10px;
      margin: 10px 0;
      border: 1px solid #eaeaea;
      border-width: 1px 0;
    }

    .shop-phone {
      width:65px;
      text-align: center;
      border-left:1px solid #efefef;
      padding:5px 10px;
      overflow:hidden;
      font-size:14px;
      color:gray;
      &:active {
        background: #e0e0e0;
      }
      &::before {
        font-family: iconfont;
        display:block;
        content: "\e618";
        font-size: 22px;
        color: #4E85FB;
      }
    }
  }
</style>
<script>
import { dialog, toast, loading } from '@/bus';
import { formatDate, formatPrice, formatMoney, parseAppointmentTime, getAbsolutePath } from '@/utils';
import { Panel, Rater, Tip } from '@/components';
import { mixinAuthRouter } from '@/mixins';
import { ShopCard, ShopCell } from '@/views/components';
import Coupon from './OrderCoupon';
import { OrderStatus, OrderType, ReOrderType, OrderRefundStatus, PaymentChannel, Ticket } from '@/enums';
import { getOrderRefundDetail, confirmMaintainOrder, confirmGetCar } from '@/api';
import { navigate, pushWebView } from '@/bridge';
import OrderDetailItems from '@/views/components/OrderDetailItems.vue';

function getInitialData() {
  return {
    OrderStatus,
    OrderRefundStatus,
    ReOrderType,
    OrderType,
    refund: null,
  };
}

export default {
  name: 'order-detail-maintain',
  props: {
    order: {
      type: Object
    },
  },
  components: {
    Rater,
    Panel,
    ShopCard,
    Coupon,
    ShopCell,
    Tip,
    OrderDetailItems,
  },
  mixins: [mixinAuthRouter],
  data() {
    return getInitialData();
  },
  mounted() {
    this.getOrderRefund();
  },
  computed: {
    actions() {
      const order = this.order;
      return {
        couldRefund: !order.refundRejectionReason && [OrderStatus.EXPIRED, OrderStatus.TO_BE_SERVE].some(item => item == order.orderStatus),
      }
    },
    orderStatusText() {
      const orderStatus = this.order.orderStatus;
      const status = OrderStatus.getEnum(orderStatus);
      if (!status) return orderStatus
      return status.getName();
    },
    refundStatusText() {
      if (this.order.refundRejectionReason) return '已驳回'
      const refundStatus = this.refund.status;
      const status = OrderStatus.getEnum(refundStatus);
      if (!status) return refundStatus
      return status.getName();
    },
    // 订单实付金额
    orderAmount() {
      let amount = this.order.price;
      if (this.order.orderType == OrderType.MAINTAIN) {
        const amount = (this.order.orderRefPartList).reduce((last, item, index) => {
          return last + item.count * item.price;
        }, this.order.hourPrice);
        return Number(amount.toFixed(2));
      }
      return amount;
    },
    orderWorkStatus() {
      const order = this.order;
      let workers;
      let time;
      if (order.orderStatus == OrderStatus.SERVING) {
        workers = order.orderDispatchings.map(item => item.workerName).join(', ');
        time = order.orderDispatchings[0].createTime;
      }
      return {
        workers,
        time
      };
    },
    extra() {
      const extraList = this.order.discountInfoList || []
      return extraList;
    },
  },
  methods: {
    ...{ formatPrice, formatDate, parseAppointmentTime },
    getOrderRefund() {
      const status = this.order.orderStatus;
      const haveRefundInfo = this.order.refundRejectionReason || [OrderStatus.REFUND_APPLIED, OrderStatus.REFUNDING, OrderStatus.REFUNDED].some(value => {
        return status == value;
      });
      if (haveRefundInfo) {
        getOrderRefundDetail(this.order.id).then(detail => {
          this.refund = detail;
        }).catch(e => {
          console.error(e);
        })
      }
    },
    go(url) {
      this.$router.push(url);
    },
    goToPay(item) {
      const { id, sysOrderId } = this.order;
      // 接口参数暂时不需要订单类型
      const paymentInfo = {
        soid: `${sysOrderId}-${id}`,
        nextPath: `/order/${id}`,
      }
      this.$_route_cashierCheckout(paymentInfo);
    },
    getPayment(value) {
      const payment = PaymentChannel.getEnum(value);
      if (payment) return payment.getName();
      return value || '未知';
    },
    /* goComment() {
        const oid = this.order.id;
        this.go(`/order/${oid}/comment?from=detail`);
      }, */
    callPhone(phone) {
      // setTimeout(() => {
      const url = `tel:${phone}`;
      window.open(url);
      // }, 10);
    },
    goNavigate() {
      const { address, lat, lng, title } = this.order.shop;
      navigate({
        address,
        name: title,
        longitude: lng,
        latitude: lat,
        callback() {},
      });
    },
    test() {
      dialog().confirm('test', {
        title: '测试'
      });
    },
    // 确认修改订单
    confirmReOrder() {
      const that = this;
      const order = that.order;
      let tip = `确认订单后需补差价${order.reorderPrice}元`;
      if (order.reorderType == ReOrderType.REFUND) {
        tip = `确认订单后5个工作日内退款${order.reorderPrice}元至原支付账户`;
      }
      const needPay = order.reorderType == ReOrderType.PAY && order.reorderPrice > 0;
      dialog().confirm(tip, {
        title: '提示',
        okText: '确认订单',
        ok() {
          loading(true, '正在提交...');
          confirmMaintainOrder(order.id).then(res => {
            loading(false);
            if (needPay) {
              that.goToPay();
            } else {
              that.$emit('update');
            }
          }, err => {
            loading(false);
            err && dialog().alert(err, {
              title: '',
            });
          });
        }
      });
    },
    goComment() {
      // 被申诉的评论不能再修改
      if (this.order.comment && this.order.comment.status == -2) {
        return;
      }
      const oid = this.order.id;
      this.$_router_push4inputting(`/order/${oid}/comment?from=detail`, { title: '评价服务' });
    },
    // 确认提车
    confirmGetCar() {
      loading(true, '正在提交...');
      const oid = this.order.id;
      confirmGetCar(oid).then(res => {
        loading(false);
        toast().success('确认成功');
        this.$emit('update');
      }, err => {
        loading(false);
        err && dialog().alert(err, {
          title: '',
        });
      });
    },
  }
};
</script>
