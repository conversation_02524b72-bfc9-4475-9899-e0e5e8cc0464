
import { doGet, doPost } from '../request/';
import { getCity } from '@/store/storage';
import { getCityAreas } from './share';
import { getGeoData } from '@/bridge';
import APIs from '../apis';

APIs.extend({
  // 获取门店保养信息
  '/shop/maintenance': '/app/car/maintenance/business/info',
  // '/shop/maintenance': ':81/static/json/shop_mt_services.json',
  // 汽车保养门店列表
  '/shops/mt': '/app/car/maintenance/business/list',
  // '/shops/mt': ':81/static/json/shops_mt.json',

  // 商家保养服务信息{ 套餐信息，时间排期列表}
  '/shop/mt/package': '/app/car/maintenance/package/info',
  // '/shop/mt/package': ':81/static/json/shop_mt_package.json',

  // 通用保养预约单 ，价格104.1元，提交汽车保养服务订单{ car_id, packge_id, date}
  '/order/mt/common/submit': '/app/car/maintenance/order/create',

  // 保养套餐单2017
  '/order/mt/package/submit': '/app/car/maintenance/order/create/v2',
  
  // 保养套餐单2018
  '/order/mt/submit': '/app/car/maintenance/order/create/v5',

  // 维修钣金单
  '/order/mt/reserve/submit': '/app/car/maintenance/order/create/v3',

  // 配件筛选条件
  '/mt/parts/options': '/app/car/maintenance/business/parts/param/list',

  // 商家备件类型列表
  '/mt/parts/types': '/app/car/maintenance/business/parts/class/list',

  // 商家备件列表
  '/mt/parts': '/app/car/maintenance/business/parts/list',

  // '/order/mt/submit': ':81/static/json/success.json',

  // 用户确认订单（订单被修改后）
  '/order/mt/confirm/change': '/app/car/set/order/confirm',

  // 用户确认提车（保养服务结束后）
  '/order/mt/confirm/getcar': '/app/car/set/order/served',

  // 汽车保养服务介绍
  '/about/mt': '/app/car/maintenance/type/list',

  // 汽车保养套餐列表
  '/shop/mt/v2/pkgs': '/app/car/scheme/list',
});

/**
 * 获取门店保养套餐详情
 * @param {string} id 套餐id
 * @param {string} car 车辆id
 * ?id=153&myCarId=2000&typeId=2
 */
export function getShopMTPackage(service, shop, car) {
  const params = {
    bid: shop,
    myCarId: car,
    typeId: service,
  }
  return doGet(APIs.get('/shop/mt/package'), params);
}

/**
 * 获取门店养车套餐列表
 * @param {string} shop 门店id
 */
export function getShopMTPackages(shop) {
  return doGet(APIs.get('/shop/mt/v2/pkgs'), { bid: shop });
}

/**
 * 获取养车店铺列表顶部轮播
 * @returns 
 * 
 */
export function getMaintainBannerList() {
  return doGet('/Radio/platform/loop/image/list?type=2');
}

/**
 * 获取汽车保养服务介绍信息
 */
export function getCarMaintainTypes() {
  return doGet(APIs.get('/about/mt'));
}

/**
  获取车辆保养门店列表数据
 data: {
   lat : number; //经度
   lng : number; //维度
   mt : string; // 保养类型 mainantenceTypeId
   car : string; // 车辆id myCarId
  }
 */
export async function getCarMTViewData(data) {
  const city = getCity().id;
  const params = {
    ...data,
    city,
    mainantenceTypeId: data.mt,
    myCarId: data.car,
  }
  const areas = await getCityAreas(params);
  const shops = await getShopsOfCarMT(params);
  return Promise.resolve({
    areas,
    shops,
  });
}

/**
  查询保养门店列表
 options: {
   lat : number; //经度
   lng : number; //维度
   mt : string; // 保养类型 mainantenceTypeId
   car : string; // 车辆id myCarId
   area? : string; // 区域id
   sort? : string; // 排序类型
   rows? : number; // 分页大小
   page? : number; // 页码
  }
  ?city&area&sort&lng=113&lat=34&myCarId=2&mainantenceTypeId=0
 */
export async function getShopsOfCarMT(options = {}) {
  const city = getCity().id;
  let defaults = {
    // mt: '',
    // car: '',
    /* lng: geo.longitude,
    lat: geo.latitude, */
    city,
    area: 0,
    sort: 'default', // default | sales | cmt | near
    rows: 10,
    page: 1
  };

  options.myCarId = options.car;
  options.mainantenceTypeId = options.mt;

  const params = Object.assign(defaults, options);
  const shops = await doGet(APIs.get('/shops/mt'), params);
  return shops;
}

/**
 *  2017年
 * 高级认证商家：提交保养套餐预约订单
 * { pkg, parts, car, date }
 * planId=153&myCarId=2000&serviceDate=201707060&partsJson=%5B%7B%22id%22%3A%201327%2C%20%22count%22%3A2%7D%2C%20%7B%20%22id%22%3A%201798%2C%20%22count%22%3A1%7D%5D
 * 后端（java)不能接收含有大括号的参数，所以partsJson需要先进行转义
 */
export function submitCarMaintainPackageOrder(data = {}, ticket) {
  const params = {
    planId: data.id,
    partsJson: JSON.stringify(data.parts), // [{"id": 1327, "count":2}, { "id": 1798, "count":1}]
    myCarId: data.car,
    serviceDate: data.date,
    recordId: ticket,
  };
  return doPost(APIs.get('/order/mt/package/submit'), params);
}

/**
 * 2018年，提交保养订单
 * @param {*} data 订单数据
 * @param {*} ticket 优惠券id
 */
export function submitCarMaintainPackageOrder2018(data = {}, ticket) {
  const params = {
    planId: data.id,
    myCarId: data.car,
    recordId: ticket,
  };
  return doPost(APIs.get('/order/mt/submit'), params);
}

/**
 * 提交养车套餐订单
 * @param {object} data 订单数据
 * @param {string} data.id 商品套餐id
 * @param {string} data.bid 商家id
 * @param {object} extra 附加优惠促销信息
 */
export function submitCarMaintainOrder(data, extra = {}) {
  const url = APIs.get('/order/mt/submit');
  const params = Object.assign({}, data, {
    planId: data.id,
    myCarId: data.car,
    ...extra,
  });
  return doPost(url, params);
}

/**
 * 高级认证商家：预约订单
 */
export function submitCarMaintainReserveOrder(serviceType, shopId, car, ticket) {
  // ?bid=153&typeId=4&myCarId=2000
  const params = {
    bid: shopId,
    typeId: serviceType,
    myCarId: car,
    recordId: ticket,
  };
  return doPost(APIs.get('/order/mt/reserve/submit'), params);
}

/**
 * 中级认证商家：普通保养预约订单
 * myCarId=85&bid=153
 */
export function submitCarMaintainCommonOrder(shopId, car) {
  const params = {
    bid: shopId,
    myCarId: car,
  };
  return doPost(APIs.get('/order/mt/common/submit'), params);
}

/**
 * 确认汽车保养改单
 * @param {string} id 业务订单id
 */
export function confirmMaintainOrder(oid) {
  const params = {
    oid,
  };
  return doPost(APIs.get('/order/mt/confirm/change'), params);
}

/**
确认提车
 */
export function confirmGetCar(oid) {
  const params = {
    oid,
  };
  return doPost(APIs.get('/order/mt/confirm/getcar'), params);
}

/**
 * 获取门店资料(包含保养信息)
 * @param {*} bid
 */
export async function getShopMaintenanceData(bid, car) {
  const { longitude: lng, latitude: lat } = await getGeoData();
  const url = APIs.get('/shop/maintenance');
  const params = { bid, lng, lat, myCarId: car };
  return doGet(url, params);
}

/**
 * 获取商家配件列表
 * @param {*} bid
 */
export function getPartsOfShop(bid, options) {
  const url = APIs.get('/mt/parts');
  const params = { bid, ...options };
  return doGet(url, params);
}

/**
 * 获取商家配件类型列表
 */
export function getTypesOfShopParts() {
  const url = APIs.get('/mt/parts/types');
  return doGet(url);
}

/**
 * 获取商家配件列表
 * @param {string} bid 商家id
 * @param {string} type 备件类型
 */
export function getFilterOptionsOfShopParts(bid, type) {
  const url = APIs.get('/mt/parts/options');
  const params = { bid, type };
  return doGet(url, params);
}
