// WebSocketClient.js

class Log {
  static console = process.env.NODE_ENV !== 'production';

  log(title, text) {
    if (!Log.console) return;
    const color = '#ff4d4f';
    console.log(
      `%c ${title} %c ${text} %c`,
      `background:${color};border:1px solid ${color}; padding: 1px; border-radius: 2px 0 0 2px; color: #fff;`,
      `border:1px solid ${color}; padding: 1px; border-radius: 0 2px 2px 0; color: ${color};`,
      'background:transparent'
    );
  }

  closeConsole() {
    Log.console = false;
  }
}

class EventDispatcher extends Log {
  constructor() {
    super();
    this.listeners = {};
  }

  addEventListener(type, listener) {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    if (this.listeners[type].indexOf(listener) === -1) {
      this.listeners[type].push(listener);
    }
    return this; // 支持链式调用
  }

  removeEventListener(type, listener) {
    if (!type) return;

    if (!listener) {
      // 如果没有指定监听器，则移除该类型的所有监听器
      this.listeners[type] = [];
    } else if (this.listeners[type]) {
      // 移除指定的监听器
      const index = this.listeners[type].indexOf(listener);
      if (index !== -1) {
        this.listeners[type].splice(index, 1);
      }
    }
    return this;
  }

  dispatchEvent(type, data) {
    if (!type || !this.listeners[type]) return;

    const listenerArray = [...this.listeners[type]]; // 复制数组，防止回调中修改监听器列表
    listenerArray.forEach(listener => {
      try {
        listener.call(this, data);
      } catch (error) {
        console.error(`Error in event listener for ${type}:`, error);
      }
    });
  }
}

class WebSocketClient extends EventDispatcher {
  constructor(options = {}) {
    super();

    // 配置选项
    const defaultOptions = {
      url: '',
      maxReconnectAttempts: 5,
      reconnectInterval: 10000, // 10秒
      heartbeatInterval: 30000, // 30秒
      heartbeatTimeout: 10000, // 10秒没有心跳响应视为断线
      heartbeatMessage: { content: 'heartbeat', mtype: 99 },
      pongMessage: null, // 服务器心跳响应内容，null表示不检查特定格式
      monitorOffline: true, // 是否监控浏览器网络状态
      messageConfirmTimeout: 5000, // 消息确认超时时间
      debug: process.env.NODE_ENV !== 'production',
    };

    this.options = { ...defaultOptions, ...options };

    // 实例属性
    this.url = this.options.url;
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = this.options.maxReconnectAttempts;
    this.reconnectInterval = this.options.reconnectInterval;
    this.heartbeatInterval = this.options.heartbeatInterval;
    this.heartbeatTimer = null;
    this.heartbeatCheckTimer = null;
    this.lastPongTime = Date.now(); // 最后一次收到服务器响应的时间
    this.stopWs = false;
    this.pendingMessages = new Map(); // 待确认的消息队列
    this.connecting = false; // 是否正在连接中

    // 如果禁用调试模式，则关闭控制台日志
    if (!this.options.debug) {
      this.closeConsole();
    }

    // 绑定方法，确保正确的this上下文
    this._handleOnline = this._handleOnline.bind(this);
    this._handleOffline = this._handleOffline.bind(this);

    // 监听浏览器网络变化
    if (this.options.monitorOffline && typeof window !== 'undefined') {
      this._setupNetworkMonitoring();
    }
  }

  // 生命周期钩子 - 使用自定义wrapper确保更新lastPongTime
  onopen(callback) {
    return this.addEventListener('open', event => {
      this.lastPongTime = Date.now();
      callback.call(this, event);
    });
  }

  onmessage(callback) {
    return this.addEventListener('message', event => {
      // 更新最后接收消息时间
      this.lastPongTime = Date.now();

      // 检查是否是心跳响应
      this._handlePossibleHeartbeatResponse(event.data);

      // 检查是否是消息确认
      this._handlePossibleConfirmation(event.data);

      // 调用原始回调
      callback.call(this, event);
    });
  }

  onclose(callback) {
    return this.addEventListener('close', callback);
  }

  onerror(callback) {
    return this.addEventListener('error', callback);
  }

  // 检查是否是心跳响应
  _handlePossibleHeartbeatResponse(data) {
    // 如果配置了特定的心跳响应格式，检查是否匹配
    if (this.options.pongMessage) {
      let pongMatched = false;

      if (
        typeof data === 'string' &&
        typeof this.options.pongMessage === 'string'
      ) {
        pongMatched = data === this.options.pongMessage;
      } else if (typeof data === 'object' && data !== null) {
        // 对象格式，检查是否包含指定字段
        if (typeof this.options.pongMessage === 'object') {
          pongMatched = Object.keys(this.options.pongMessage).every(
            key => data[key] === this.options.pongMessage[key]
          );
        }
      }

      if (pongMatched) {
        this.log('WebSocket', '收到心跳响应');
      }
    }
  }

  // 检查是否是消息确认
  _handlePossibleConfirmation(data) {
    let parsedData = data;

    // 尝试解析JSON数据
    if (typeof data === 'string') {
      try {
        parsedData = JSON.parse(data);
      } catch (e) {
        // 解析失败，保持原始数据
        return;
      }
    }

    // 检查是否包含id和type字段，表示可能是消息确认
    if (
      parsedData &&
      parsedData.id &&
      (parsedData.type === 'confirmation' ||
        parsedData.type === 'ack' ||
        parsedData.type === 'response')
    ) {
      this._handleConfirmation(parsedData);
    }
  }

  // 处理消息确认
  _handleConfirmation(response) {
    const messageId = response.id;

    if (this.pendingMessages.has(messageId)) {
      const pending = this.pendingMessages.get(messageId);
      clearTimeout(pending.timeoutId);
      this.pendingMessages.delete(messageId);

      const success = response.success !== false; // 默认成功，除非明确指定失败

      if (success) {
        pending.resolve(response);
      } else {
        pending.reject(new Error(response.errorMessage || '服务器拒绝消息'));
      }

      this.log(
        'WebSocket',
        `消息 ${messageId} 确认${success ? '成功' : '失败'}`
      );
    }
  }

  // 常规发送消息
  send(message) {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      this.log('WebSocket', '发送失败：未连接');
      return { success: false, error: '未连接' };
    }

    try {
      const data =
        typeof message === 'string' ? message : JSON.stringify(message);
      this.socket.send(data);
      return { success: true };
    } catch (error) {
      this.log('WebSocket', `发送失败：${error.message}`);
      return { success: false, error: error.message };
    }
  }

  // 发送需要确认的消息
  sendWithConfirmation(message) {
    const messageId = this._generateMessageId();
    const messageEnvelope = {
      id: messageId,
      type: 'request',
      timestamp: Date.now(),
      payload: message,
    };

    return new Promise((resolve, reject) => {
      // 检查连接状态
      if (!this.isConnected()) {
        return reject(new Error('WebSocket未连接'));
      }

      // 存储消息到待确认队列
      this.pendingMessages.set(messageId, {
        message: messageEnvelope,
        timestamp: Date.now(),
        resolve,
        reject,
        timeoutId: setTimeout(() => {
          // 超时处理
          if (this.pendingMessages.has(messageId)) {
            const pending = this.pendingMessages.get(messageId);
            this.pendingMessages.delete(messageId);
            this.log('WebSocket', `消息 ${messageId} 确认超时`);
            pending.reject(new Error('消息确认超时'));
            this.dispatchEvent('messageTimeout', { messageId, message });
          }
        }, this.options.messageConfirmTimeout),
      });

      // 发送消息
      const result = this.send(messageEnvelope);
      if (!result.success) {
        // 发送失败，立即拒绝Promise
        this._cleanupPendingMessage(messageId);
        reject(new Error(result.error || '消息发送失败'));
      }
    });
  }

  // 清理待确认消息
  _cleanupPendingMessage(messageId) {
    if (this.pendingMessages.has(messageId)) {
      const pending = this.pendingMessages.get(messageId);
      clearTimeout(pending.timeoutId);
      this.pendingMessages.delete(messageId);
    }
  }

  // 拒绝所有待确认消息
  _rejectAllPendingMessages(reason = '连接已关闭') {
    for (const [messageId, pending] of this.pendingMessages.entries()) {
      clearTimeout(pending.timeoutId);
      pending.reject(new Error(reason));
    }
    this.pendingMessages.clear();
    this.log('WebSocket', `已清除所有待确认消息: ${reason}`);
  }

  // 生成唯一消息ID
  _generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 初始化连接
  connect() {
    // 如果已连接，则不再重新连接
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return this;
    }

    // 如果正在连接中，也不再重新连接
    if (this.connecting) {
      return this;
    }

    this.connecting = true;

    if (this.reconnectAttempts === 0) {
      this.log('WebSocket', `初始化连接中... ${this.url}`);
    }

    try {
      this.socket = new WebSocket(this.url);

      this.socket.onopen = event => {
        this.connecting = false;
        this.stopWs = false;
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.log('WebSocket', `连接成功 [onopen]... ${this.url}`);
        this.dispatchEvent('open', event);
      };

      this.socket.onmessage = event => {
        // 尝试解析JSON数据
        let data = event.data;
        try {
          if (typeof data === 'string') {
            data = JSON.parse(data);
          }
        } catch (e) {
          // 如果解析失败，保持原始数据
        }

        this.dispatchEvent('message', {
          originalEvent: event,
          data: data,
        });
      };

      this.socket.onclose = event => {
        this.connecting = false;

        if (this.reconnectAttempts === 0) {
          this.log('WebSocket', `连接断开 [onclose]... ${this.url}`);
        }

        this.closeHeartbeat();
        this._rejectAllPendingMessages('连接已关闭');

        if (!this.stopWs) {
          this.handleReconnect();
        }

        this.dispatchEvent('close', event);
      };

      this.socket.onerror = event => {
        this.connecting = false;

        if (this.reconnectAttempts === 0) {
          this.log('WebSocket', `连接异常 [onerror]... ${this.url}`);
        }

        this.closeHeartbeat();
        this.dispatchEvent('error', event);
      };
    } catch (error) {
      this.connecting = false;
      this.log('WebSocket', `连接创建失败: ${error.message}`);
      this.handleReconnect();
    }

    return this;
  }

  // 断网重连逻辑
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      this.log(
        'WebSocket',
        `尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts}) ${this.url}`
      );

      setTimeout(() => {
        if (!this.stopWs) {
          this.connect();
        }
      }, this.reconnectInterval);
    } else {
      this.closeHeartbeat();
      this.log('WebSocket', `最大重连失败，终止重连: ${this.url}`);
      this._rejectAllPendingMessages('重连失败');
      this.dispatchEvent('reconnectFailed', {
        attempts: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
      });
    }
  }

  // 关闭连接
  close() {
    this.stopWs = true;
    this.closeHeartbeat();

    // 清理所有待确认消息
    this._rejectAllPendingMessages('连接主动关闭');

    if (this.socket) {
      // 在关闭之前先移除所有事件处理函数，避免触发onclose事件
      this.socket.onopen = null;
      this.socket.onmessage = null;
      this.socket.onclose = null;
      this.socket.onerror = null;

      if (
        this.socket.readyState === WebSocket.OPEN ||
        this.socket.readyState === WebSocket.CONNECTING
      ) {
        this.socket.close();
      }

      this.socket = null;
    }

    this.connecting = false;

    return this;
  }

  // 开始心跳检测
  startHeartbeat() {
    if (this.stopWs) return;

    this.closeHeartbeat(); // 先清除现有的心跳定时器

    // 发送心跳
    this.heartbeatTimer = setInterval(() => {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        const heartbeatMsg =
          typeof this.options.heartbeatMessage === 'string'
            ? this.options.heartbeatMessage
            : JSON.stringify(this.options.heartbeatMessage);

        this.socket.send(heartbeatMsg);
        this.log('WebSocket', '发送心跳数据...');
      }
    }, this.heartbeatInterval);

    // 检查心跳响应
    this.heartbeatCheckTimer = setInterval(() => {
      const now = Date.now();
      const timeSinceLastPong = now - this.lastPongTime;

      if (timeSinceLastPong > this.options.heartbeatTimeout) {
        this.log(
          'WebSocket',
          `心跳超时：${Math.floor(timeSinceLastPong / 1000)}秒未收到响应`
        );

        // 触发心跳超时事件
        this.dispatchEvent('heartbeatTimeout', {
          lastPongTime: this.lastPongTime,
          currentTime: now,
          timeout: this.options.heartbeatTimeout,
        });

        // 重连前先关闭当前连接
        if (this.socket) {
          // 将lastPongTime设置为当前时间，避免重复触发
          this.lastPongTime = now;

          // 强制关闭并重连
          this.socket.close();
        }
      }
    }, Math.min(this.options.heartbeatTimeout / 3, 5000)); // 至少每5秒检查一次
  }

  // 关闭心跳
  closeHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.heartbeatCheckTimer) {
      clearInterval(this.heartbeatCheckTimer);
      this.heartbeatCheckTimer = null;
    }
  }

  // 设置浏览器网络状态监控
  _setupNetworkMonitoring() {
    window.addEventListener('online', this._handleOnline);
    window.addEventListener('offline', this._handleOffline);
  }

  // 网络恢复在线
  _handleOnline() {
    this.log('WebSocket', '网络已恢复连接');
    this.dispatchEvent('networkOnline', null);

    // 如果WebSocket已断开，尝试重新连接
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      this.reconnectAttempts = 0; // 重置重连次数
      this.connect();
    }
  }

  // 网络离线
  _handleOffline() {
    this.log('WebSocket', '网络已断开连接');
    this.dispatchEvent('networkOffline', null);

    // 可以选择关闭当前连接，等待网络恢复再重连
    if (
      this.socket &&
      (this.socket.readyState === WebSocket.OPEN ||
        this.socket.readyState === WebSocket.CONNECTING)
    ) {
      this.socket.close();
    }
  }

  // 检查连接状态
  isConnected() {
    return this.socket && this.socket.readyState === WebSocket.OPEN;
  }

  // 获取当前状态
  getStatus() {
    if (!this.socket) return 'CLOSED';

    const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    return states[this.socket.readyState];
  }

  // 销毁实例，清理所有资源
  destroy() {
    this.close();

    // 移除浏览器网络状态监听
    if (this.options.monitorOffline && typeof window !== 'undefined') {
      window.removeEventListener('online', this._handleOnline);
      window.removeEventListener('offline', this._handleOffline);
    }

    // 清除所有事件监听器
    this.listeners = {};

    return null;
  }
}

export default WebSocketClient;
