<template>
    <div :class="btnClass" @click="clickHandle">
      <slot></slot>
    </div>
</template>
<style lang="scss">
  .header-btn {
    display:inline-block;
    min-width:45px;
    line-height:45px;
    text-align:center;
    position:relative;
    &:active {
      // background-color: #317de2;
      background-color: rgba(0, 0, 0, 0.1);
    }
    &::before {
      font-size: 22px;
      font-family: iconfont;
      display:block;
    }
    &.header-btn-back::before{
      content:'\e604';
      font-weight: 700;
    }
    &.header-btn-text{
      padding: 0 8px;
    }

    &.header-btn-order::before{
      content:'\e622';
    }

    &.header-btn-yes::before{
      content:'\e60f';
    }

    &.header-btn-plus::before{
      content:'\e626';
    }

    &.header-btn-phone::before{
      content:'\e636';
    }
    &.header-btn-service::before{
      content:'\e61e';
      font-size: 24px;
    }
    &.header-btn-share::before {
      content:'\e635';
      font-size: 22px;
      position: relative;
      top: 2px;
    }
    &.header-btn-car::before{
      content:'\e635';
      font-size:22px;
    }

    &.header-btn-notify::before{
      content:'';
      background:url(~@/assets/images/notify.png) center center no-repeat transparent;
      background-size: 20px;
      flex: 1;
      height: 45px;
    }
    &.header-btn-notify-unread{
      position:relative;
    }
    &.header-btn-notify-unread::before{
      content:'';
      background:url(~@/assets/images/notify.png) center center no-repeat transparent;
      background-size: 20px;
      flex: 1;
      height: 45px;
    }
    &.header-btn-unread::after {
      content: "";
      padding: 4px;
      position: absolute;
      top: 16%;
      right: 20%;
      background: url(~@/assets/images/red-circle.svg) center center no-repeat;
      background-size:90%;
    }
  }
</style>
<script>
    import { back } from '@/bus';

    export default {
      name: 'header-button',
      props: {
        type: {
          type: String,
          default() {
            return '';
          }
        },
        //  按钮动作
        action: {
          type: String,
          default() {
            return '';
          }
        },
        value: String,
      },
      computed: {
        btnClass() {
          const types = this.type.split(',').map(t => `header-btn-${t}`).join(' ');
          return `header-btn ${types}`;
        },
      },
      methods: {
        clickHandle() {
          if (this.type === 'back' && this.action !== null) {
            back();
          } else {
            this.$emit('click');
          }
        }
      },
    };
</script>
