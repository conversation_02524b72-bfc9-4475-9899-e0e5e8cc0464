<template>
  <container @ready="init" @leave="onLeave">
    <x-header title="洗车门店">
       <x-button  slot="left" type="back"></x-button>
    </x-header>
    <content-view :status="status" @reload="reload" class="flex-col">

      <div class="center-self" @click="center2me"></div>
      <div class="map" id="map"></div>

      <div slot="foot" v-if="shop" class="bottom">
        <div class="shops" @click="goShopHome">
          <shop-card :shop="shop"></shop-card>
        </div>
      </div>
    </content-view>
  </container>
</template>
<style lang="scss">
  .content {
    padding-bottom: 0;
  }
  .amap-icon img{
    width:19px;
    height:31px;
  }
  .map-container{
    position:relative;
  }
  .map {
    position: absolute;
    left:0;
    top:0;
    width: 100%;
    height: 100%;
  }
  .loc-icon{
    width:25px;
    height:25px;
    background:url(~@/assets/images/amap/loc.png) center center no-repeat transparent;
    background-size:contain;
  }
  .shops{
    background:white;
    color:#989898;
    border-top:1px solid #DBDAD5;
    padding:5px;
  }
  .center-self{
    background:url(~@/assets/images/amap/loc_gray.png) 50% 50% no-repeat #fff;
    width: 35px;
    height: 35px;
    position: absolute;
    bottom: 130px;
    right: 14px;
    z-index: 999;
    border: 1px solid #dedede;
    border-radius: 2px;
    background-size: 24px;
  }
</style>

<script>
import { mapState, mapActions } from 'vuex';
import { Header, HeaderButton, Container, ContentView } from '@/components';
import { ShopCard } from './components';
import { AppStatus } from '@/common/enums';
import { getGeoData, getAMap } from '@/bridge';

export default {
  data() {
    return {
      activeShopIndex: 0,
      shops: [],
      status: AppStatus.LOADING,
    };
  },
  computed: {
    shop() {
      return this.shops[this.activeShopIndex];
    },
    shopLogo() {
      return `background-image:url(${this.shop.logo})`;
    }
  },
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    ContentView,
    ShopCard,
  },
  methods: {
    ...mapActions(['getAMap', 'getShopsMapViewData']),
    init() {
      // console.log('init...', this.map);
      this.getShopsMapViewData().then(({ center, shops }) => {
        getAMap().then(AMap => {
          this.status = AppStatus.READY;
          setTimeout(() => {
            this.initMap(AMap, [center.longitude, center.latitude], shops);
          }, 50);
        });
      }, err => {
        this.status = AppStatus.ERROR;
      });
    },
    onLeave() {
      this.clearMap();
      this.$destroy();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    goShopHome() {
      const view = `/shop/${this.shop.id}`;
      this.$router.push(view);
    },
    center2me() {
      this.map.setFitView();
      this.$ua && this.$ua.trackEvent('Click', 'MapCenter2Me', this.$route.path);
    },
    initMap(AMap, center, shops) {
      this.shops = shops;

      let map = this.map;
      if (!map) {
        this.map = map = new AMap.Map('map', {
          resizeEnable: true,
          center
        });
      }

      AMap.plugin(['AMap.ToolBar', 'AMap.Scale'], () => {
        map.addControl(new AMap.ToolBar({ locate: false }));
        map.addControl(new AMap.Scale());
      });

      const centerMarker = new AMap.Marker({
        map,
        content: '<div class="loc-icon"></div>',
        position: center,
      });

      let currentMarker = null;
      shops.forEach((item, i) => {
        if (!item.lng || !item.lat) return;
        const marker = new AMap.Marker({
          map,
          icon: `http://webapi.amap.com/theme/v1.3/markers/n/mark_b${i + 1}.png`,
          position: [item.lng, item.lat],
          extData: {
            index: i,
          },
        });
        marker.on('click', e => {
          this.activeShopIndex = e.target.getExtData().index;
          currentMarker.setIcon(currentMarker.getIcon().replace('mark_r', 'mark_b'));
          currentMarker = marker;
          currentMarker.setIcon(currentMarker.getIcon().replace('mark_b', 'mark_r'));
        });
        if (i === 0) {
          currentMarker = marker;
          currentMarker.setIcon(currentMarker.getIcon().replace('mark_b', 'mark_r'));
        }
      });

      map.setFitView();
    },
    clearMap() {
      if (this.map) {
        this.map.clearMap();
      }
    },
  }
};
</script>
