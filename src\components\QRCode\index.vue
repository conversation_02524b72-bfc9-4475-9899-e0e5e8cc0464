<template>
  <canvas
  :height="QRSize"
  :qrsize="QRSize"
  :width="QRSize">
  </canvas>
</template>

<script>
import QRCodeImpl from 'qr.js/lib/QRCode';
import ErrorCorrectLevel from 'qr.js/lib/ErrorCorrectLevel';

function getBackingStorePixelRatio (ctx) {
  return (
    ctx.webkitBackingStorePixelRatio ||
    ctx.mozBackingStorePixelRatio ||
    ctx.msBackingStorePixelRatio ||
    ctx.oBackingStorePixelRatio ||
    ctx.backingStorePixelRatio ||
    1
  );
}

function convertPx(value) {
  if (window.lib) {
    // 设计稿基于375宽度设计，此处基于此尺寸进行转换
    return lib.flexible.rem2px(value / 37.5);
  }
  // debugger;
  return value;
}

export default {
  name: 'qrcode',
  props: {
    value: String,
    size: {
      type: Number,
      default: 80
    },
    level: {
      type: String,
      default: 'L'
    },
    bgColor: {
      type: String,
      default: '#FFFFFF'
    },
    fgColor: {
      type: String,
      default: '#000000'
    }
  },
  mounted() {
    this.render();
  },
  computed: {
    QRSize() {
      return convertPx(this.size);
    },
    sizeStyle() {
      return {
        width: `${this.QRSize}px`,
        height: `${this.QRSize}px`,
      }
    },
    qrcode() {
      return this.value + this.size + this.level + this.bgColor + this.fgColor;
    }
  },
  watch: {
    qrcode() {
      this.render();
    }
  },
  methods: {
    render() {
      const qrcode = new QRCodeImpl(-1, ErrorCorrectLevel[this.level]);
      qrcode.addData(this.value);
      qrcode.make();

      const canvas = this.$el;
      const ctx = canvas.getContext('2d');
      const cells = qrcode.modules;
      const size = this.QRSize;
      const tileW = size / cells.length;
      const tileH = size / cells.length;
      const scale = getBackingStorePixelRatio(ctx);
      canvas.height = canvas.width = size * scale;
      ctx.scale(scale, scale);

      cells.forEach((row, rdx) => {
        row.forEach((cell, cdx) => {
          ctx.fillStyle = cell ? this.fgColor : this.bgColor;
          const w = (Math.ceil((cdx + 1) * tileW) - Math.floor(cdx * tileW));
          const h = (Math.ceil((rdx + 1) * tileH) - Math.floor(rdx * tileH));
          ctx.fillRect(Math.round(cdx * tileW), Math.round(rdx * tileH), w, h);
        });
      });
    },
  }
};
</script>
