<template>
  <container class="account-my-car" @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="爱车档案">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div class="mycar">
          <div class="car-block">
            <div class="cars-wrapper">
              <slider
                ref="slider"
                class="cars bottom-line"
                :slides="cars"
                value-mode="index"
                :options="{ initialSlide: carIndex, autoplay: 0 }"
                :allow-empty="true"
                v-model="carIndex"
              >
                <template #item="{ item }">
                  <div class="swiper-slide car">
                    <div
                      v-if="item.carModelInfo"
                      class="flex-row car-card"
                      @click="editCar()"
                      :class="{ 'car-default': item.isDefault }"
                    >
                      <c-picture
                        class="car-logo"
                        :src="item.carModelInfo.brandLogo"
                      ></c-picture>
                      <div>
                        <div class="car-attr">
                          <div class="car-attr__content">
                            <p
                              class="car-attr__value"
                              v-html="item.carModelInfo.brand"
                            ></p>
                            <div class="car-attr__name">
                              <span
                                >{{ item.carModelInfo.carSysName }}
                                {{ item.carModelInfo.year }}</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      v-else
                      class="flex-row car-card"
                      @click="editCar()"
                      :class="{ 'car-default': item.isDefault }"
                    >
                      <c-picture class="car-logo"></c-picture>
                      <div>
                        <div class="car-attr">
                          <div class="car-attr__content">
                            <p class="car-attr__value">{{ item.carRegistNumber }}</p>
                            <div class="car-attr__name">
                              <span>点击完善爱车信息</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <div
                  v-if="cars && cars.length < CAR_ADD_LIMIT"
                  class="swiper-slide car"
                  slot="last"
                >
                  <div class="flex-row car-card" @click="goAddCar">
                    <div class="car-add">
                      <svg class="icon-plus">
                        <use xlink:href="#icon-plus"></use>
                      </svg>
                      <span>添加爱车</span>
                    </div>
                  </div>
                </div>
              </slider>
            </div>
            <div v-if="car" class="flex-row car-main">
              <div
                class="car-attr"
                :class="{ 'car-attr-empty': !car.mileage }"
                @click="editCar('distance')"
              >
                <div class="car-attr__content">
                  <p class="car-attr__name">行驶里程</p>
                  <div class="car-attr__value">
                    {{ car.mileage ? car.mileage + "km" : "点击输入" }}
                  </div>
                </div>
              </div>
              <div
                class="car-attr"
                :class="{ 'car-attr-empty': !car.buyCarTime }"
                @click="editCar('date')"
              >
                <div class="car-attr__content">
                  <p class="car-attr__name">上路时间</p>
                  <div class="car-attr__value">
                    {{
                      car.buyCarTime
                        ? formatDate(car.buyCarTime, "yyyy年MM月")
                        : "请选择上路时间"
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="car && car.carModelInfo" class="car-block">
            <div class="flex-row bottom-line">
              <div class="car-attr" @click="editCar('displacement')">
                <div class="car-attr__content">
                  <div class="car-attr__value">
                    {{ car.carModelInfo.engineDisplacement }}
                  </div>
                  <p class="car-attr__name">发动机排量</p>
                </div>
              </div>
              <div class="car-attr" @click="editCar('model')">
                <div class="car-attr__content">
                  <div class="car-attr__value">{{ car.carModelInfo.name }}</div>
                  <p class="car-attr__name">车型</p>
                </div>
              </div>
            </div>

            <div class="flex-row">
              <div
                class="car-attr"
                :class="{ 'car-attr-empty': !car.carRegistNumber }"
                @click="editCar('number')"
              >
                <div class="car-attr__content">
                  <div class="car-attr__value">
                    {{ car.carRegistNumber || "点击输入" }}
                  </div>
                  <p class="car-attr__name">车牌号</p>
                </div>
              </div>
              <div
                class="car-attr"
                :class="{ 'car-attr-empty': !car.identificationCode }"
                @click="editCar('id6')"
              >
                <div class="car-attr__content">
                  <div class="car-attr__value">
                    {{ car.identificationCode ? car.identificationCode : "点击输入" }}
                  </div>
                  <p class="car-attr__name">车辆识别码后6位</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="car" class="button-sp-area">
          <div class="btn-container flex-row">
            <a href="javascript:;" class="btn" @click="deleteCar(car.id)">删除</a>
            <a
              href="javascript:;"
              class="btn btn-primary"
              @click="setDefaultCar(car.id)"
              :class="{ 'btn-disabled': car.isDefault }"
              >设为默认</a
            >
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>
<style lang="scss">
@import "~styles/mixin/index.scss";

.account-my-car {
  .swiper-pagination-bullet {
    width: 5px; /*px*/
    height: 5px; /*px*/
    border-radius: 50%;
    &.swiper-pagination-bullet-active {
      background: white;
    }
  }
  .cars-wrapper {
    height: 200px;
    background: #4175dd;
  }
  .cars {
    background: #4175dd;
    height: 200px;
    .car {
      width: 75%;
      .car-attr__name {
        overflow: hidden;
        text-overflow: ellipsis;
        width: 150px;
        white-space: nowrap;
      }
      .car-attr__value {
        width: auto;
      }
    }
    .car-card {
      height: 130px;
      background: rgba(255, 255, 255, 0.8);
      margin: 10px 0;
      border-radius: 5px;
      transform: scale(0.9);
      transition: all 100ms;
    }
    .car-default {
      &::before {
        content: "默认";
        position: absolute;
        left: 0;
        top: 0;
        color: white;
        font-size: 0.8em;
        padding: 0 5px;
        background: #f39626;
        z-index: 1;
      }
    }
    .car-add {
      text-align: center;
      flex: 1;
      color: #565656;
    }
    .swiper-slide-active .car-card {
      background: white;
      transform: scale(1);
    }
  }
  .picture.car-logo {
    width: 60px;
    height: 60px;
    flex: 1;
    background-position: right center;
    background-size: 60px; /* px */
    background-color: transparent;
    position: relative;
    margin-right: 5px;
    margin-left: 15px;
  }
  .car-block {
    background: white;
    margin-bottom: 10px;
    .flex-row {
      padding: 8px 0px;
      align-items: center;
    }
  }
  .bottom-line {
    @include border-bottom(#e0e0e0);
  }
  .car-attr {
    align-items: center;
    padding: 10px 10px;
    flex: 1;
    display: flex;
    @include border-right(#e0e0e0);
    .car-attr__name {
      color: gray;
    }
    .car-attr__value {
      font-weight: 700;
      color: #3a3a3a;
      font-size: 1.1em;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .car-main .car-attr__value {
    color: #5085fa;
  }
  .car-attr-empty {
    .car-attr__name {
      color: #353535;
    }
    .car-attr__value {
      font-weight: 400;
      color: #a9a7a7;
    }
  }
  .button-sp-area {
    .btn-container {
      @include border-top(#e0e0e0);
    }
    padding: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    .btn {
      flex: 1;
      background: white;
      text-align: center;
      padding: 10px 0;
      color: #4e85fb;
      @include border-right(#e0e0e0);
    }
    .btn-primary {
      background: #4e85fb;
      color: white;
    }
    .btn-disabled {
      background: white;
      color: #bdbbbb;
    }
  }
}
</style>
<script>
import { formatDate } from '@/utils';
import { Panel } from '@/components';
import { AppStatus } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
import { getMyCars, addMyCar, setMyDefaultCar, removeMyCar } from '@/api';
import { listen, Events, dialog, toast, loading } from '@/bus';

const CAR_ADD_LIMIT = 5;

export default {
  name: 'account-my-car',
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      CAR_ADD_LIMIT,
      status: AppStatus.LOADING,
      refreshAction: 1,
      carIndex: null,
      // car: null,
      page: {
        cars: null,
      },
    };
  },
  computed: {
    cars() {
      return this.page.cars;
    },
    car() {
      return this.cars[this.carIndex];
    },
  },
  components: {
    Slider: (resolve) => {
      import('@/components/Slider/index.vue').then(resolve);
    },
  },
  mounted() {
    const sourceId = this.$options.name;
    /* listen(Events.AFTER_CAR_MODEL_SELECTED, sourceId, (car) => {
        if (this.status != AppStatus.READY) return;
        console.log('AFTER_CAR_MODEL_SELECTED', this.$options.name);
        setTimeout(() => {
          this.onCarModelSelected(car);
        }, 500);
      }) */
  },
  methods: {
    initPageData() {
      return getMyCars().then(
        (cars) => {
          this.page.cars = cars;
          if (!/^\d+$/.test(this.carIndex)) {
            let carIndex = 0;
            cars.some((car, index) => {
              if (car.isDefault) carIndex = index;
              return car.isDefault;
            });
            this.carIndex = carIndex;
          }
          this.status = AppStatus.READY;
          return cars;
        },
        (err) => {
          this.status = AppStatus.ERROR;
          console.error(err);
        }
      );
    },
    init() {
      return this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      // 为了避免点击添加爱车中途若取消返回时refreshSlider会让slider焦点发生变化，此处做特别判断，只再车辆数量发生变化后再refreshSlider
      const cars1 = this.cars;
      this.initPageData().then((cars2) => {
        const shouldRefreshSlider = cars1.length != cars2.length;
        if (shouldRefreshSlider) {
          this.refreshSlider();
        }
      });
      // this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    deleteCar(carId) {
      const that = this;
      dialog().confirm('确定要删除这辆车吗？', {
        title: '提示',
        ok() {
          loading(true, '正在提交...');
          removeMyCar(carId).then(
            (res) => {
              loading(false);
              that.initPageData().then((res) => {
                that.onCarRemoved();
              });
            },
            (err) => {
              loading(false);
              err &&
                dialog().alert(err, {
                  title: '',
                });
            }
          );
        },
      });
    },
    setDefaultCar(carId) {
      const that = this;
      if (this.car && this.car.isDefault) return;
      loading(true, '正在提交...');

      setMyDefaultCar(carId).then(
        (res) => {
          loading(false);
          that.initPageData();
        },
        (err) => {
          loading(false);
          err &&
            dialog().alert(err, {
              title: '',
            });
        }
      );
    },
    editCar(type) {
      const url = `/account/car/${this.car.id}/edit`;
      this.$_router_push4inputting(url, {
        title: '修改爱车',
      });
    },
    onCarRemoved() {
      this.refreshSlider();
    },
    goAddCar() {
      const from = this.$options.name;
      this.$_router_push4inputting(`/car/brands?from=${from}`, { title: '添加爱车' });
    },
    refreshSlider() {
      const slider = this.$refs.slider;
      if (!slider) return;

      // 索引边界
      let maxRange =
        this.cars.length >= CAR_ADD_LIMIT ? CAR_ADD_LIMIT - 1 : this.cars.length - 1;
      if (maxRange < 0) maxRange = 0;
      console.log(this.carIndex, maxRange);
      if (this.carIndex > maxRange) {
        this.carIndex = maxRange;
        slider.slideTo(this.carIndex);
      }
      slider.refresh();
    },
    onCarModelSelected(car) {
      const that = this;
      loading(true, '正在添加...');
      addMyCar(car.id)
        .then((res) => {
          loading(false);
          that.initPageData().then((res) => {
            that.refreshSlider();
          });
        })
        .catch((e) => {
          loading(false);
          toast().tip('添加失败！');
        });
    },
    formatDistance(value) {
      const result = parseInt(value / 1000);
      return `${result}km`;
    },
    formatDate(...args) {
      return formatDate(...args);
    },
  },
};
</script>
