import { doGet, doPost } from '../request/';
import APIs from '../apis';

APIs.extend({
  '/mall/goods/list': '/app/goods/list',
  '/mall/goods/categories': '/app/goods/category/list',
  '/mall/goods/detail': '/app/goods/detail',

  '/mall/goods/buy': '/app/goods/order/create',

  '/mall/order/delete': '/app/goods/order/delete',
  '/mall/order/refund': '/app/goods/order/apply/refund',
  '/mall/order/list': '/app/goods/order/list',
  '/mall/order/detail': '/app/goods/order/detail',
  '/mall/order/address/edit': '/app/goods/order/receipt/info/modify',
  '/mall/order/modify': '/app/goods/order/receipt/info/modify',
  '/mall/order/close': '/app/goods/order/receipted/status/close',
  '/mall/order/receipt': '/app/goods/order/status/receipted',

  'comment/add': '/app/api/goods/comment/add', // 新增商品评价信息
  'comment/update': '/app/api/goods/comment/update/{id}', // 根据id更新商品评价信息
  'comment/delete': '/app/api/goods/comment/delete', // 根据id删除商品评价
  'comment/detail': '/app/api/goods/comment/detail', // 根据id查询商品评价信息
  'goods/score': '/app/api/goods/comment/goods/score', // 根据商品id查询商品评分
  'comment/list': '/app/api/goods/comment/list', // 查询商品评价列表

  'goods/vipdiscount/list': '/app/goods/vipdiscount/order/list', // 查询7天内,有会员优惠金额的订单 在商城首页 飘屏使用
  'goods/order/detail/floatmsg': '/app/goods/order/list/by/goods', // 查询7天内,该商品的购买记录 在商品详情飘屏使用
})

/**
 * 获取飘屏消息列表 在商品详情飘屏使用
 */
export function getFloatMsgList (id) {
  const url = APIs.get('goods/order/detail/floatmsg');
  return doGet(url, { goodsId: id });
}

/**
 * 在商城首页 飘屏使用
 */
export function getVipdiscountList () {
  const url = APIs.get('goods/vipdiscount/list');
  return doGet(url);
}

/**
 * 获取商品信息
 */
export function getGoodsDetail (id) {
  const url = APIs.get('/mall/goods/detail');
  return doGet(url, { id });
}

/**
 * 获取商品列表
 * @param { object } params 
 */
export function getGoodsList (params) {
  const url = APIs.get('/mall/goods/list');
  const data = Object.assign({
    rows: 10,
    page: 1,
  }, params);
  return doGet(url, data);
}

/**
 * 获取商品分类列表
 * @param {*} params 
 */
export function getGoodsCategories (params) {
  const url = APIs.get('/mall/goods/categories');
  const data = Object.assign({
    rows: 100,
    status: 1,
  }, params);
  return doGet(url, data);
}

/**
 * 下单
 * @param {*} params 
 */
export function buyGoods ([params, address], extraData) {
  const url = APIs.get('/mall/goods/buy');
  const orderAddress = address
    ? {
        recvName: address.name,
        phone: address.phone,
        address: address.address,
        postareaProv: address.province,
        postareaCity: address.city,
        postareaCountry: address.area,
      }
    : {};
  const data = Object.assign({
    goodsId: params.id,
    count: params.count,
    buyremark: params.note,
    goodsSpecificationId: params.goodsSpecificationId,
    ...extraData,
  }, orderAddress);
  return doPost(url, data);
}

/**
 * 获取微商城订单列表
 * @param { object } params 
 */
export function getMallOrderList (params) {
  const url = APIs.get('/mall/order/list');
  const data = Object.assign({
    rows: 10,
    page: 1,
  }, params);
  return doGet(url, data);
}

/**
 * 获取订单详情
 * @param {*} id 
 */
function getOrderDetail (id) {
  const url = APIs.get('/mall/order/detail');
  return doGet(url, { id });
}

export async function getMallOrderDetail (id) {
  const url = APIs.get('/mall/order/detail');
  // const order = await getOrderDetail(id);
  // const goods = await getGoodsDetail(order.goodsId);
  // order.goods = goods;
  return doGet(url, { id });
  // return Promise.resolve(order);
}

/**
 * 删除订单
 * @param {*} id 
 */
export function deleteMallOrder (id) {
  const url = APIs.get('/mall/order/delete');
  return doPost(url, { id });
}

/**
 * 确认收货
 * @param {*} id 
 */
export function confirmMallOrderReceipt (id) {
  const url = APIs.get('/mall/order/receipt');
  return doPost(url, { id });
}

/**
 * 关闭订单
 * @param {*} id 
 */
export function closeMallOrder (id) {
  const url = APIs.get('/mall/order/close');
  return doPost(url, { id });
}

/**
 * 订单申请退款
 * @param {*} id 
 */
export function applyRefundMallOrder (id) {
  const url = APIs.get('/mall/order/refund');
  return doPost(url, { id });
}

/**
 * 修改订单收货地址
 * @param {object} data 
 */
export function updateMallOrderAddress (address) {
  const url = APIs.get('/mall/order/address/edit');
  const info = {
    id: address.id,
    recvName: address.name,
    telephone: address.phone,
    address: address.address,
    postareaProv: address.province,
    postareaCity: address.city,
    postareaCountry: address.area,
  }
  return doPost(url, info);
}
