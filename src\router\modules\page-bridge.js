import { handleError } from '../error-handler';

export default [
  {
    path: '/mp/service',
    name: '小程序跳转到服务号',
    component: resolve => {
      import(
        /* webpackChunkName: "mp-service" */ '@/views/page-bridge/mpToService.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/app/sound',
    name: '有声书',
    component: resolve => {
      import(
        /* webpackChunkName: "mp-service" */ '@/views/page-bridge/appToSound.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/app/didi',
    name: '滴滴加油充电导航页',
    component: resolve => {
      import(
        /* webpackChunkName: "mp-service" */ '@/views/page-bridge/appToDiDiLanding.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/app/didi/agree',
    name: '滴滴加油充电',
    component: resolve => {
      import(
        /* webpackChunkName: "mp-service" */ '@/views/page-bridge/appToDiDi.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/web/to/app',
    name: 'webToApp',
    component: resolve => {
      import(
        /* webpackChunkName: "mp-service" */ '@/views/page-bridge/webToApp.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/nanfang/ai',
    name: 'nanfangAi',
    component: resolve => {
      import(
        /* webpackChunkName: "mp-service" */ '@/views/packages/nanfang/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  // {
  //   path: '/grid',
  //   name: '九宫格抽奖',
  //   component: resolve => {
  //     import(/* webpackChunkName: "mp-service" */ '@/views/page-bridge/grid.vue').then(resolve).catch(handleError);
  //   },
  // },
];
