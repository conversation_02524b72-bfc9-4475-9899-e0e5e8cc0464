<template>
  <div :class="pictureClass" @click="handleClick" :style="wrapperStyles">
    <img
      v-if="imgSrc"
      :src="imgSrc"
      :style="imgStyle"
      alt=""
      @load="onImgLoaded"
      @error="onImgError"
    />
    <!-- <div v-if="$slots.default" class="slot-content">
    </div> -->
    <slot></slot>
  </div>
</template>
<style lang="scss" scoped>
$bg-color: #f4f4f4;
.img-container {
  display: inline-block;
  position: relative;
  overflow: hidden;
  vertical-align: top;
  > img {
    // width: 100%;
    // height: 100%;
    // position: absolute;
    // left: 0;
    // top: 0;
    display: block;
    width: 100%;
    height: auto;
  }
  .slot-content {
    position: relative;
  }
  &.img-lazy {
    background-color: $bg-color;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNiYAAAAAkAAxkR2eQAAAAASUVORK5CYII=);
  }
  &.img-loading {
    // background-image: url(./placeholder1.png);
    background-color: $bg-color;
    background-size: contain;
    opacity: 0.9;
  }
  &.img-ready {
    opacity: 1;
    // background-size: cover;
    background-color: transparent;
  }
  &.img-error {
    background-image: url(./placeholder1.png);
    background-color: #f4f4f4;
    background-size: contain;
  }
}
</style>

<script>
/**
 * biz-image，脱胎于 c-picture
 * 早期object-fit属性兼容性不高，c-picture 通过 backgrund-size 属性达到 object-fit 的效果
 * c-picture 组件支持插槽，组件之间可放置其他内容
 * 因此若没有在c-picture插槽中放置其他内容可直接将 `c-picture` 替换为 `biz-image`
 * 若放置了其他内容，需要先测试布局是否混乱，若影响了布局，可暂不替换或调整页面样式后替换为 biz-image
 */
// TODO: 主题色背景功能，根据图片主题色设置背景，方案1通过js计算，方案2通过七牛云接口获取
import { loadImage } from './utils';
import { getImageURL } from '@/common/image';
import { ImageType } from '@/enums';

const ImageStatus = {
  EMPTY: 'empty',
  LOADING: 'loading',
  READY: 'ready',
  ERROR: 'error',
};

const ImageFillType = {
  COVER: 'cover',
  CONTAIN: 'contain',
};

function colorfulImg(imgEl) {
  let canvas = document.createElement('canvas'),
    context = canvas.getContext && canvas.getContext('2d'),
    height,
    width,
    length,
    data,
    i = -4,
    blockSize = 5,
    count = 0,
    rgb = { r: 0, g: 0, b: 0 };

  height = canvas.height = imgEl.height;
  width = canvas.width = imgEl.width;
  context.drawImage(imgEl, 0, 0);
  data = context.getImageData(0, 0, width, height).data;
  length = data.length;
  while ((i += blockSize * 4) < length) {
    ++count;
    rgb.r += data[i];
    rgb.g += data[i + 1];
    rgb.b += data[i + 2];
  }
  rgb.r = ~~(rgb.r / count);
  rgb.g = ~~(rgb.g / count);
  rgb.b = ~~(rgb.b / count);
  return rgb;
}
function getThemeColorOfImage(img) {
  const color = colorfulImg(img);
  return `rgba(${color.r}, ${color.b}, ${color.g}, 0.05)`;
}
function getPlaceholder(value) {
  if (value === 'none')
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNiYAAAAAkAAxkR2eQAAAAASUVORK5CYII=';
  return require('./placeholder3.png');
}

export default {
  name: 'BizImage',
  props: {
    /**
     * 图片地址或七牛云图片id
     * 合法的七牛云图片ID或合法的图片url
     * @param {string} src: 图片地址
     */
    src: String,
    // 值同object-fit: https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit
    fill: {
      type: String,
      default: ImageFillType.COVER,
    },
    /**
     * 图片类型
     * 合法的七牛云图片地址参数或后缀 https://developer.qiniu.com/dora/api/1279/basic-processing-images-imageview2
     * @param {string} type: 图片类型
     */
    type: {
      type: [String, Object],
      default() {
        return ImageType.LOGO;
      },
    },
    /**
     * 图片占位图
     * 图片懒加载时有效
     * @param {string} placeholder: 占位图，可以是任何合法的图片路径，当值为`none`时展示为透明背景
     */
    placeholder: {
      type: String,
      // default: require('./placeholder2.png'),
      default:
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNiYAAAAAkAAxkR2eQAAAAASUVORK5CYII=',
    },
    /**
     * @param { boolean } colorful : 是否展示主题色
     * true：会将取图片平均色作为背景主题色展示
     */
    colorful: {
      type: Boolean,
      default: false,
    },
    /**
     * @param { boolean } immediate : 是否立即加载
     * true：会将图片url立即设为图片url，利用浏览器自身的图片加载功能去加载
     * false：会先展示一个loading样式，利用js将图片完全下载后后再将图片展示出来，视觉上更佳，对页面加载速度也稍有帮助
     */
    immediate: {
      type: Boolean,
      default: false,
    },
    /**
     * 图片懒加载，等图片进入可见区域后再加载
     * 曾利用 getBoundingClientRect 实现，但过于复杂， 现利用 IntersectionObserver API 实现
     * @param {boolean} lazy: 懒加载
     */
    lazy: {
      type: Boolean,
      default: false,
    },
    // 懒加载参数，同 https://developer.mozilla.org/zh-CN/docs/Web/API/IntersectionObserver/IntersectionObserver
    lazyOptions: {
      type: Object,
      default() {
        return {
          root: null,
          rootMargin: '0px',
          threshold: 0,
        };
      },
    },
    // 自动适配宽度或高度
    autoFit: {
      type: String,
      default: '', // height|width
    },
    width: Number,
    height: Number,
  },
  mounted() {
    let lazy = this.lazy && !!window.IntersectionObserver;
    if (lazy) {
      this.lazyLoadingImage();
    } else {
      this.displayImage();
    }
  },
  data() {
    return {
      status: ImageStatus.EMPTY,
      url: null,
      originWidth: this.width,
      originHeight: this.height,
      displayWidth: this.width,
      displayHeight: this.height,
      themeColor: '', // 图片主题色
    };
  },
  computed: {
    wrapperStyles() {
      const style = {};
      const color = this.themeColor;
      if (color) {
        style.backgroundColor = color;
        style.backgroundImage = `linear-gradient(45deg, ${color}, transparent, ${color}, transparent)`;
      }
      return style;
    },
    imgStyle() {
      const objectFit =
        this.status === ImageStatus.LOADING ? 'contain' : this.fill;
      return {
        objectFit: objectFit,
      };
    },
    pictureClass() {
      const status = this.status;
      return {
        'img-container': true,
        picture: true,
        // 'img-contain': this.fill === ImageFillType.CONTAIN,
        // 'img-cover': this.fill === ImageFillType.COVER,
        'img-lazy':
          this.src &&
          this.lazy &&
          status !== ImageStatus.READY &&
          status !== ImageStatus.ERROR,
        'img-ready': status === ImageStatus.READY,
        'img-loading': status === ImageStatus.LOADING,
        'img-error': status === ImageStatus.ERROR,
      };
    },
    imgSrc() {
      return this.url || getPlaceholder(this.placeholder);
    },
  },
  watch: {
    src(val, oldVal) {
      this.displayImage();
      // console.log('picture change:', val, oldVal);
    },
  },
  methods: {
    lazyLoadingImage() {
      // console.log('lazy load:', this.src)
      const io = new IntersectionObserver(entries => {
        entries.forEach(entry => {
          // console.log(entry.intersectionRatio, entry.isIntersecting)
          if (entry.isIntersecting) {
            this.displayImage();
            io.disconnect();
          }
        });
        // Do something
      }, this.lazyOptions);
      io.observe(this.$el);
    },
    onImgLoaded(e) {
      // console.log(e);
      this.$emit('load');
    },
    onImgError(e) {
      // 只有在immediate为true时才生效
      // console.log("🚀 ~ file: BizImage.vue:269 ~ onImgError ~ e:", e)
      // console.log(e);
      this.$emit('error');
    },
    getWidthByHeight(height) {
      return height * this.radio;
    },
    getHeightByWidth(width) {
      return width / this.radio;
    },
    loadImage(src) {
      if (this.immediate) {
        return new Promise((resolve, reject) => {
          this.url = src;
          this.status = ImageStatus.READY;
        });
      }
      this.status = ImageStatus.LOADING;
      return loadImage(src).then(img => {
        this.originHeight = img.height;
        this.originWidth = img.width;
        try {
          if (this.colorful) {
            this.themeColor = getThemeColorOfImage(img.img);
          }
        } catch (err) {
          console.error(err);
        }
        // console.log()
        /* if (!this.height) {
            this.displayHeight = img.height;
          }
          if (!this.width) {
            this.displayWidth = img.width;
          } */
        if (this.autoFit) {
          const elRect = this.$el.getBoundingClientRect();
          // console.log(this.$el, elRect);
          // 宽高有一个没设置或为0的情况下才自动设置对应宽高
          if (this.autoFit === 'height' && elRect.width) {
            this.displayWidth = elRect.width;
            this.displayHeight = this.getHeightByWidth(elRect.width);
          } else if (this.autoFit === 'width' && elRect.height) {
            this.displayHeight = elRect.height;
            this.displayWidth = this.getWidthByHeight(elRect.height);
          }
        }
        this.url = img.src;
      });
    },
    displayImage() {
      if (!this.src) return;
      // if (this.status == ImageStatus.READY) return;
      const url = getImageURL(this.src, this.type);
      // this.status = ImageStatus.LOADING;
      return this.loadImage(url)
        .then(img => {
          this.status = ImageStatus.READY;
        })
        .catch(err => {
          this.status = ImageStatus.ERROR;
          console.error(err);
          return Promise.reject(err);
        });
    },
    handleClick(e) {
      this.$emit('click', e);
    },
  },
};
</script>
