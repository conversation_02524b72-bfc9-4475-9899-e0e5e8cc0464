<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  $bg-color: #F2F2F2;
  .container {
    background: $bg-color;
  }
  .weui-cells__title {
    background: $bg-color;
    color: black;
  }
  .weui-cells {
    background: transparent;
    margin: 10px;
    .weui-cell {
      background: white;
    }
    .weui-cell_tip {
      font-size: 13px;
      padding: 5px 0;
      margin: 5px;
      color: #e64340;
    }
    &::after, &::before {
      display: none;
    }
  }
  .weui-cell_code {
    padding: 10px 10px;
    .weui-input {
      font-weight: 700;
      &::placeholder {
        font-weight: 400;
      }
    }
  }
  .weui-btn_buy {
    background-size: cover;
    font-weight: 700;
    &:active {
      opacity: 0.8;
    }
  }
  .action {
    flex: 1;
  }
  .action-nouse-icode {
    color: black;
  }
  .action-howget {
    text-align: right;
  }
  .weui-btn-area {
    margin: 20px;
  }
</style>

<template>
  <container class="cashier-checkout" @ready="init" @leave="onLeave" @resume="onResume" :keep-alive="keepAlive">
    <x-header title="银联支付">
      <x-button  slot="left" type="back"></x-button>
    </x-header>

    <content-view ref="content" :status="status" @reload="reload">
        <div class="weui-cells__title">
          <h3>银行卡号</h3>
        </div>
        <div class="weui-cells">
          <div class="weui-cell">
            <div class="weui-cell__bd">
              <input class="weui-input input-bold" v-model="form.card" type="text" placeholder="请输入银行卡或信用卡号">
            </div>
          </div>
          <div class="weui-cell_tip">{{inputHelp}}</div>
        </div>
        <div class="weui-btn-area">
          <a class="weui-btn weui-btn_primary weui-btn_buy" href="javascript:" @click="confirmPay">去支付</a>
        </div>
    </content-view>
  </container>
</template>

<script>
import { getAbsoluteURL } from '@/utils';
import { mixinAuthRouter } from '@/mixins';
import { AppStatus, OrderBizType } from '@/enums';
import ENV from '@/common/env';
import { loading, dialog, toast, back } from '@/bus';
import { checkBankCard } from '@/api/modules/share';
import { validateWashCardBuyingBankCard } from '@pkg/carwash-card/api';
import { validateCCBInspectionBankCard } from '@pkg/vehicle-business/api';

/**
 * 校验银行卡号是否合法
 */
function validateBankCard(category, params) {
  // 需求：建行，中原银行等特价洗车，限定必须使用指定类型的银行卡付款
  if (category == OrderBizType.CarWashCard) {
    return validateWashCardBuyingBankCard(params);
  }
  // 需求：建行，中原银行等特价洗车，限定必须使用指定类型的银行卡付款
  if (category == OrderBizType.CarBeauty) {
    return checkBankCard(params);
  }
  // 需求：建行特价审车，代办审车，限定必须使用指定类型的建行卡付款
  if (category == OrderBizType.VehicleInspection || category == OrderBizType.AgentVehicleInspection) {
    return validateCCBInspectionBankCard(params);
  }
  return Promise.reject(`不支持的订单类型：${category}`)
}

export default {
  name: 'cashier-checkout-card-enter',
  components: {},
  mixins: [mixinAuthRouter],
  data() {
    const now = new Date();
    console.log(now)
    return {
      AppStatus,
      status: AppStatus.LOADING,
      form: {
        card: '',
        oid: this.$route.query.oid,
      },
      category: this.$route.query.category,
    }
  },
  computed: {
    inputHelp() {
      const helps = {
        [OrderBizType.CarBeauty.value]: '仅支持中原银行信用卡，每张卡在7天内限用一次',
        [OrderBizType.CarWashCard.value]: '仅支持中原银行信用卡，每张卡在7天内限用一次',
      };
      return ''
    }
  },
  mounted() {},
  methods: {
    init() {
      this.status = AppStatus.READY;
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    confirmPay() {
      const cardPattern = /^\d{12}|\d{19}$/;
      const card = this.form.card.trim();
      if (!card) {
        dialog().alert('请输入卡号！');
        return;
      }
      if (!cardPattern.test(card)) {
        dialog().alert('卡号不正确，请检查后重新输入！');
        return;
      }
      loading(true);
      validateBankCard(this.category, {
        sysOid: this.form.oid,
        accNo: card,
      }).then(res => {
        this.goToPay();
        loading(false);
      }).catch(err => {
        loading(false);
        dialog().alert(err, {
          title: '提示'
        })
      })
    },
    goToPay() {
      // 页面交互方式：从收银台到此页面时采用 `打开新窗口形式` 跳转，
      // 从此页面跳转到银联支付采用url重定向方式跳转
      // 支付成功后跳转到支付结果页面
      // 在交广领航内，在支付结果页面可直接关闭此webview
      // 中原银行信用卡 :*********
      const card = this.form.card;
      const oid = this.$route.query.oid;

      // 支付成功后跳转到 h5 页面收银台
      const backURL = encodeURIComponent(getAbsoluteURL(`/actions/app/cashier/?oid=${oid}`));
      const url = `${location.origin}/Radio/pay/charge/forunion?v=394&orderId=${oid}&channel=union_pay&returnBusinessFrontUrl=${backURL}&accNo=${card}`;
      location.replace(url);
      /* this.$_router_pageTo(url, {
          title: '银联支付',
          titleBar: true,
          shareButton: false,
          autoUpdateTitle: false,
        }); */
    },
    onResume() {},
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  }
};
</script>
