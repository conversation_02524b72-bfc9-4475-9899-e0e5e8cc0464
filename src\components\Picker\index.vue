<template>
  <div class="picker" @click="pickItems">
    <slot>
      {{content}}
    </slot>
  </div>
</template>
<style lang="scss">

</style>
<script>
  import { picker } from '@/lib/weui.js/picker/picker.js';
  export default {
    name: 'picker',
    props: {
      value: {
        type: Array,
        defaults() {
          return [];
        }
      },
      placeholder: {
        type: String,
        default() {
          return '请选择';
        }
      },
      items: Array,
      options: Object,
    },
    computed: {
      content() {
        const item = this.item || [];
        return item.map(v => v.label).join(' ') || this.placeholder;
      }
    },
    watch: {
    },
    beforeDestroy() {
      try {
        if (this.picker) {
          this.picker.destroy();
        }
      } catch(e) {
        // console.error('picker.destroy', e);
      }
    },
    mounted() {
      const items = this.items;
      const value  = this.value || [];
      this.item = value.reduce((last, curItem, index) => {
        const list = index === 0 ? items : last[index].children;
        let checkedItem = list.filter(item => item.value === curItem)[0] || list[0];
        last.push(checkedItem);
        return last;
      }, []);
    },
    data() {
      return {
        item: null,
        picker: null,
      };
    },
    components: {
    },
    methods: {
      pickItems() {
        const that = this;
        let defaultValue = this.value;
        if (!defaultValue) {
          defaultValue = [this.items[0].value];
        }
        // console.log('defaultValue2:', defaultValue);
        const options = Object.assign({
          className: 'custom-classname',
          defaultValue: defaultValue,
          onChange: function (result) {
            console.log(result);
          },
          onConfirm: function (result) {
             that.setItem(result);
          },
          onHide() {
            console.log('picker hide...');
            that.$emit('hide');
          },
          container: that.$el,
          id: 'picker'
        }, this.options);
        this.picker = picker(this.items, options);
        that.$emit('show');
        console.log(this.picker);
      },
      setItem(value) {
        this.item = value;
        const result = value.map(item => item.value);
        console.log('result:', result);
        this.$emit('input', result);
        this.$emit('change', result);
      },
    },
  };
</script>
