import APIModel from '../APIModel';
import { submitCarBeautyOrder } from '@/api/modules/wash';
import { submitCarMaintainOrder } from '@/api/modules/maintain';
import { createActivityOrder } from '@/api/modules/order';
import { createVipOrder } from '@/api/modules/vip-v2';
import {
  createOrder4OnlineVehicleInspection,
  createOrder4SpecialOnlineVehicleInspection,
  createOrder4OnlineVehicleInspectionReservation,
  createOrder4AgentVehicleInspection,
  createOrder4CarRegister,
  createOrder4CarTransfer,
  createPayableOrder4ServiceOrder,
  createOrderCarTransferToZz,
} from '@pkg/vehicle-business/api';
import { createMobileDirectOrder } from '@pkg/mobile-recharge/api';
import { buyCarWashCard } from '@pkg/carwash-card/api';
import { submitOrder as buyMallG2Goods } from '@pkg/mallg2/api';
import { buyGoods } from '@pkg/mall/api';
import { OrderType, OrderCategory, OrderBizType } from '@/enums';
import { formatDate } from '@/utils';
import { checkServiceStatus, ServiceEnum } from '@/utils/maintenance';
import { dialog } from '@/bus';

const api = new APIModel({
  '/ticket/list': '/Membership-app/user/cardbag/list',
  '/member/info': '/Membership-app/member/user/info',
  // '/order/ticket/list': '/app/car/user/coupon/list',
  '/order/ticket/list': '/app/car/user/can/use/coupon/list',
  '/order/ticket/list/v2': '/app/car/user/can/use/coupon/list/v2',
  '/ticket/detail': '/app/car/app/goods/detail',
});

/**
 * 获取代金券信息
 */
export function getTicketDetail(id) {
  const url = api.render('/ticket/detail');
  return api.doGet(url, { id });
}

/**
 * 查询可用代金券列表
 * @param {string} params.fristCategoryId 业务id
 * @param {string} params.goodsId 商品id
 * @param {string} params.bid 商家id
 */
export function selectOrderTicketList(params) {
  const url = api.render('/order/ticket/list');
  return api.doGet(url, params);
}

/**
 * 查询业务可用代金券列表
 */
export function getAvailableTicketList(params) {
  const url = api.render('/order/ticket/list');
  return api.doGet(url, params);
}

/**
 * @description: 查询可用代金券列表 2022-09-14新增，为适配多商品结算，使用此新接口
 * @param {number} params.category 一级业务id,0:美容 1：保养 2：审车，3：团购 4：会员
 * @param {Array} params.types 二级业务限制[预留] 不限 :-1 ，团购[商家id]
 * @param {number} params.amount 订单价格
 * @param {Array} params.rids 美容、保养、审车 传商家id 团购传商品id 会员传会员开通项目id
 */
export function getAvailableTicketList2(params) {
  const url = api.render('/order/ticket/list/v2');
  return api.doGet(url, params);
}

/**
 * 获取我的代金券列表
 * @param { object } params
 */
export function getTicketList(params) {
  const url = api.render('/ticket/list');
  const data = Object.assign(
    {
      pageSize: 50,
      page: 1,
    },
    params
  );
  /* return Promise.resolve([
    {
      'advertisementImage': 'Fmx9NzEtGRSOn09Y446tS-sCpb44',
      'bName': '河南省委机关汽修厂',
      'id': 123,
      'loseTime': 1516267782984,
      'pName': -1,
      'reelAmount': 10,
      'reelDiscount': 0,
      'reelId': 'sdfsfd',
      'reelName': '红包',
      'reelType': 1,
      'restrictionsAmount': 30,
      'status': 0,
      'unlimited': false,
      'validTime': 1516267982984
    },
    {
      'advertisementImage': 'Fmx9NzEtGRSOn09Y446tS-sCpb44',
      'bName': '河南省委机关汽修厂',
      'id': 123,
      'loseTime': 1516267782984,
      'pName': -1,
      'reelAmount': 0,
      'reelDiscount': 0.8,
      'reelId': 'sdfsfd',
      'reelName': '红包',
      'reelType': 2,
      'restrictionsAmount': 30,
      'status': 0,
      'unlimited': false,
      'validTime': 1516267982984
    },
  ]); */
  return api.doGet(url, data);
}

/**
 * 统一下单接口
 * @param { object } data 订单数据
 * @param { string } ticket  卡券id
 * @param { OrderType } type 订单类型
 */
// export function submitOrder(data, ticket, type) {
//   // console.log(arguments);
//   if (type == OrderType.VEHICLE_INSPECTION) {
//     return createOrder4OfflineVehicleInspaction(data, ticket)
//   }
//   if (type == OrderType.MAINTAIN) {
//     return submitCarMaintainPackageOrder2018(data, ticket)
//   }
//   if (type == OrderType.MAINTAIN_RESERVE) {
//     return submitCarMaintainReserveOrder(...data, ticket)
//   }

//   // 春节期间，汽车美容商家暂停接单
//   const endStop = window.END_STOP || 20180225;
//   const now = Number(formatDate(Date.now(), 'yyyyMMdd'));
//   if (now <= endStop) {
//     const tip = `
//     因临近春节，工人放假<br>
//     平台即日起不再受理洗车美容订单<br>
//     暂定2018年2月26日恢复正常<br>
//     感谢您的理解与支持! <br>
//     祝大家新春快乐！`;
//     return Promise.reject(tip);
//   }
//   if (type == OrderType.WASH) {
//     return submitCarBeautyOrder(...data, ticket)
//   }
//   return Promise.reject('暂不支持此类订单');
// }

/**
 * 统一下单接口
 * @param { object } data 订单数据
 * @param {object} extra 附加信息
 * @param {string} extra.ticket 优惠券id
 * @param {boolean} extra.redPocket 是否使用红包
 * @param {string} extra.vipPkg vip套餐id
 * @param { OrderType } type 订单类型
 */
export function submitOrder(data, extra = {}, type) {
  const args = arguments;
  const { available, message } = checkServiceStatus(ServiceEnum.CAR_INSPECTION);
  if (
    [
      OrderBizType.VehicleInspection,
      OrderBizType.VehicleInspectionReservation,
      OrderBizType.AgentVehicleInspection,
      OrderBizType.AgentVehicleInspectionReservation,
      OrderBizType.CarRegister,
      OrderBizType.CarTransfer,
      OrderBizType.CarTransferToZz,
    ].some(item => item == type)
  ) {
    // const shouldReject = Date.now() > new Date(2020, 1 - 1, 23, 0, 0, 0) && Date.now() < new Date(2020, 3 - 1, 22, 0, 0, 0);
    // if (shouldReject) {
    //   return Promise.reject('根据河南省人民政府“关于启动重大突发公共卫生事件一级响应的决定”和《关于加强新型冠状病毒感染的肺炎疫情防控工作的通告》，为切实做好疫情防控工作，平台线下业务暂停或延迟接单服务，恢复时间另行通知。');
    // }
    const startTime = new Date(2024, 1, 8, 0, 0, 0);
    const endTime = new Date(2024, 1, 17, 0, 0, 0);
    const shouldConfirm = Date.now() > startTime && Date.now() < endTime;
    // const shouldConfirm = Date.now() < endTime;
    if (!available) {
      return new Promise((resolve, reject) => {
        dialog().confirm(message, {
          title: '温馨提示',
          okText: '继续下单',
          ok: () => {
            if (Date.now() < startTime) {
              doSubmitOrder(...args)
                .then(resolve)
                .catch(reject);
            } else {
              reject('暂停接单');
            }
          },
          cancel: () => {
            reject('已取消下单');
          },
        });
      });
    }
  }
  // 免检审车，证牌补换：此项业务为先审核后付款
  if (
    type == OrderBizType.NoExamInspection ||
    type == OrderBizType.CardReplacement
  ) {
    const startTime = new Date(2024, 1, 8, 0, 0, 0);
    const endTime = new Date(2024, 1, 17, 0, 0, 0);
    // if (Date.now() > startTime && Date.now() < endTime) {
    //   return Promise.reject('因春节假期，2月10日-2月19日停止接单，2月20日开始接单。');
    // }
    const shouldConfirm = Date.now() > startTime && Date.now() < endTime;
    // const shouldConfirm = Date.now() < endTime;
    if (!available) {
      return new Promise((resolve, reject) => {
        dialog().confirm(message, {
          title: '温馨提示',
          okText: '继续下单',
          ok: () => {
            if (Date.now() < startTime) {
              doSubmitOrder(...args)
                .then(resolve)
                .catch(reject);
            } else {
              reject('暂停接单');
            }
          },
          cancel: () => {
            reject('已取消下单');
          },
        });
      });
    }
  }
  return doSubmitOrder(...arguments);
}

function doSubmitOrder(data, extra = {}, type) {
  const openVipParams = extra.vipPkg
    ? {
        openVip: 1,
        memberShipId: extra.vipPkg,
      }
    : {};
  const orderWayParams = extra.way
    ? {
        way: extra.way,
      }
    : {};
  // 渠道参数
  const channelParams = extra.cooperationChannel
    ? {
        cooperationChannel: extra.cooperationChannel,
      }
    : {};
  // 自提订单信息
  const selfPickUpInfo = extra.recvName
    ? {
        recvName: extra.recvName,
        phone: extra.phone,
        pickUpAddressId: extra.pickUpAddressId,
      }
    : {};
  // debugger
  const extraData = {
    recordId: extra.ticket,
    reelId: extra.ticket, // vip会员优惠券参数
    useOpenVipCouponId: extra.vipTicket, // 组合开通会员使用会员专享优惠券id
    useRedEnvelope: extra.redPocket ? 1 : 0,
    ...openVipParams,
    ...orderWayParams,
    ...channelParams,
    ...selfPickUpInfo,
  };
  // 提交报名活动付款订单
  if (type == OrderBizType.Activity) {
    return createActivityOrder(data, extraData);
  }
  // 购买VIP会员
  if (type == OrderBizType.VipBuying) {
    return createVipOrder(data, extraData);
  }
  // 团购
  if (type == OrderBizType.GroupBuy) {
    return buyGoods(data, extraData);
  }
  // 二代商城
  if (type == OrderBizType.MallG2) {
    return buyMallG2Goods(data, extraData);
  }
  // 汽车保养
  if (type == OrderBizType.CarMaintenance) {
    return submitCarMaintainOrder(data, extraData);
  }
  // 汽车美容
  if (type == OrderBizType.CarBeauty) {
    return submitCarBeautyOrder(data, extraData);
  }
  // 上线审车
  if (type == OrderBizType.VehicleInspection) {
    return createOrder4OnlineVehicleInspection(data, extraData);
  }
  // 上线审车-先审后付
  if (
    type == OrderBizType.VehicleInspectionReservation ||
    type == OrderBizType.AgentVehicleInspectionReservation
  ) {
    return createOrder4OnlineVehicleInspectionReservation(data, extraData);
  }
  // 上线审车(专场)
  if (type == OrderBizType.VehicleInspectionForSpecial) {
    return createOrder4SpecialOnlineVehicleInspection(data, extraData);
  }
  // 代办审车
  if (type == OrderBizType.AgentVehicleInspection) {
    return createOrder4AgentVehicleInspection(data, extraData);
  }
  // 新车上牌
  if (type == OrderBizType.CarRegister) {
    return createOrder4CarRegister(data, extraData);
  }
  // 二手车过户
  if (type == OrderBizType.CarTransfer) {
    return createOrder4CarTransfer(data, extraData);
  }
  // 外地车转入郑州
  if (type == OrderBizType.CarTransferToZz) {
    return createOrderCarTransferToZz(data, extraData);
  }
  // 洗车卡
  if (type == OrderBizType.CarWashCard) {
    return buyCarWashCard(data, extraData);
  }
  // 免检审车，证牌补换。此项业务是先提交订单审核后付款模式，此处为付款时需要调用的接口
  if (
    type == OrderBizType.NoExamInspection ||
    type == OrderBizType.CardReplacement
  ) {
    return createPayableOrder4ServiceOrder(data, extraData);
  }
  // 话费直冲
  if (type == OrderBizType.MobileRecharge) {
    return createMobileDirectOrder(data, extraData);
  }
  return Promise.reject(`暂不支持此类订单:${type}`);
}
