<template>
  <div class="search-bar">
    <div class="search-container" @click="onClickFilter">
      <van-search
        v-model="searchText"
        placeholder="搜索心仪对象"
        shape="round"
        background="transparent"
        readonly
        @search="onSearch"
        @input="onInput"
      />
      <div class="filter-button">
        <van-icon name="filter-o" size="20" />
      </div>
    </div>
  </div>
</template>

<script>
import { Search, Icon } from 'vant';
import { debounce } from '../utils';

export default {
  name: 'SearchBar',
  components: {
    [Search.name]: Search,
    [Icon.name]: Icon,
  },
  data() {
    return {
      searchText: '',
    };
  },
  created() {
    this.debouncedSearch = debounce(this.emitSearch, 300);
  },
  methods: {
    onSearch() {
      this.emitSearch();
    },
    onInput() {
      this.debouncedSearch();
    },
    emitSearch() {
      this.$emit('search', this.searchText);
    },
    onClickFilter() {
      this.$emit('click-filter');
    },
  },
};
</script>

<style lang="scss" scoped>
.search-bar {
  padding: 10px 0;
}

.search-container {
  display: flex;
  align-items: center;

  .van-search {
    flex: 1;
  }

  .filter-button {
    width: 40px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 17px;
    margin-right: 10px;
    color: #666;

    &:active {
      background-color: #e5e5e5;
    }
  }
}
</style>
