import html2canvas from 'html2canvas';

function getImageColor(canvas, img) {
  canvas.width = img.width;
  canvas.height = img.height;

  let [r, g, b] = [1, 1, 1];
  let context = canvas.getContext('2d');

  context.drawImage(img, 0, 0);

  // 获取像素数据
  let data = context.getImageData(0, 0, img.width, img.height).data;

  // 取所有像素的平均值
  for (let row = 0; row < img.height; row++) {
    for (let col = 0; col < img.width; col++) {
      r += data[(img.width * row + col) * 4];
      g += data[(img.width * row + col) * 4 + 1];
      b += data[(img.width * row + col) * 4 + 2];
    }
  }

  // 求取平均值
  r /= img.width * img.height;
  g /= img.width * img.height;
  b /= img.width * img.height;

  // 将最终的值取整
  r = Math.round(r);
  g = Math.round(g);
  b = Math.round(b);

  return {
    r,
    g,
    b,
  };
}

export function checkImage(src, name) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = '';
    // img.src = `/resource/image/${src}@200X100`;
    img.src = src;
    // img.src = `https://img.jgrm.net/79857c71101245d19bc74d5b8609e560_xs`;
    img.onload = e => {
      const canvas = document.createElement('canvas');
      const color = getImageColor(canvas, img);
      if (color.r > 10 && color.g > 10 && color.b > 10) {
        resolve();
      } else {
        console.warn(name, color);
        console.warn(
          '%c',
          `padding:50px 80px;line-height:120px;background:url('${
            location.origin + src
          }') no-repeat;background-size: cover;`
        );
        reject({
          tip: `${name}不清晰，请重新上传${name}`,
          data: {
            src,
            name,
            color,
          },
        });
      }
    };
    img.onerror = function () {
      console.error(name, src);
      reject({
        tip: `${name}加载失败`,
      });
    };
  });
  // return color.r > 10 && color.g > 10 && color.b > 10;
}

/**
 * @param {element} el       截图的dom区域(通过ref获取打的dom)
 * @param {Object} options   插件的一些自定义配置
 * 注意：1、获取dom的时候需要等待页面加载完成之后
 */
export function drawToPic(el) {
  return new Promise((resolve, reject) => {
    const Rect = el.getBoundingClientRect();
    const width = parseInt(Rect.width);
    const height = parseInt(Rect.height);
    const cs = document.createElement('canvas');
    const scale = 2;
    cs.width = width * scale;
    cs.height = height * scale;
    const options = {
      useCORS: true,
      canvas: cs,
      scale: scale,
      backgroundColor: null,
      // ignoreElements: (element) => element.id === 'clos',
    };
    html2canvas(el, options).then(canvas => {
      // const url = URL.createObjectURL(base64ToBlob(canvas.toDataURL('image/png')))
      const url = canvas.toDataURL('image/png');
      // debugger
      resolve(url);
      // downloadImage(url)
    });
  });
}
// 将base64转换为文件，dataurl为base64字符串，filename为文件名（必须带后缀名，如.jpg,.png）
export function dataURLtoFile(dataurl, filename) {
  let arr = dataurl.split(',');
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = window.atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}
// base64转blob
export function base64ToBlob(code) {
  const parts = code.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  return new Blob([uInt8Array], { type: contentType });
}
// 将Blob转换为base64字符串的函数
export function blobToBase64(blob, callback) {
  const reader = new FileReader();

  // 当读取完成时执行
  reader.onloadend = function () {
    // 读取结果是base64字符串
    const base64String = reader.result;
    callback(base64String);
  };

  // 读取Blob
  reader.readAsDataURL(blob);
}
// 下载图片
function downloadImage(url) {
  // 这里使用 img 是因为在客户端中，不能直接下载，要调用原生的方法
  const createImg = document.createElement('img');
  createImg.setAttribute('src', url);
  // 如果是在网页中可以直接创建一个 a 标签直接下载
  const a = document.createElement('a');
  a.href = url;
  window.open(a.href); // 测试时候使用，截图之后新开一个浏览器页面查看是否截图成功
  // a.download = new Date().getTime().toString()
  // a.click()
}
