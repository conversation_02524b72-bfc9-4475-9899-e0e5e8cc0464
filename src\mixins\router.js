import Vue from 'vue';
import { mixinAuth } from './auth';
import { isInJglh, isIOS, isInWeApp, isInAliApp, isInPC } from '@/common/env';
import { isValidURL, getAbsolutePath, getAppURL } from '@/utils';
import { pushWebView, parseJglhURL, openMiniProgram } from '@/bridge';
import { handleJglhAndWeixinWebViewConfigParam } from '@/bridge/weixin';
import { back } from '@/bus/index';
import alipayUtils from '@/bridge/alipay';

/**
 * 由于auth和router经常使用，此处对其做了合并，未来考虑作为全局mixin注入
 */
export const mixinAuthRouter = Vue.extend({
  mixins: [mixinAuth],
  data() {
    return {
      // 按规范应命名为$_route_keepAlive，但$开头的变量不会被vue监测
      keepAlive: true,
    };
  },
  methods: {
    $_router_back() {
      back();
    },
    $_router_push(data) {
      this.$router.push(data);
    },
    $_router_replace(data) {
      this.keepAlive = false;
      this.$router.replace(data);
    },
    /**
     * 需要登录权限的 push
     * @param {*} path 跳转路径
     * @param {*} fastGo 是否采用快速跳转，若为true，登录后自动跳转到path，否则跳回原页面
     */
    $_authRouter_push(path, fastGo = true) {
      if (typeof path !== 'string') throw new Error('path必须是字符串类型');
      const nextPage = fastGo ? getAbsolutePath(path) : null;
      this.$_auth_go(nextPage).then(res => {
        this.$_router_push(path);
      });
    },
    /**
     * 需要登录权限的 replace
     * @param {*} path 跳转路径
     * @param {*} fastGo 是否采用快速跳转，若为true，登录后自动跳转到path，否则跳回原页面
     */
    $_authRouter_replace(path, fastGo = true) {
      if (typeof path !== 'string') throw new Error('path必须是字符串类型');
      const nextPage = fastGo ? getAbsolutePath(path) : null;
      this.$_auth_go(nextPage).then(res => {
        this.$_router_replace(path);
      });
    },
    /**
     * 跳转小程序页面
     * @param {object} params 跳转配置
     * @param {string} params.type // 或redirect, 默认navigate
     * @param {string} params.url // 跳转页面地址, 默认首页
     * @param {string} params.query // 传递参数 a=1&b=2
     */
    $_router_pageToMp(params) {
      if (isInJglh) {
        openMiniProgram({
          id: 'gh_2397d4a35829', // 要打开的小程序 原始id
          env: 'release', // develop|trial|release
          path: `${params.url}?${params.query}`, // 'page/web/index?url={..app/auth?auth_form={..#/mp/service}}'
        })
          .then(() => {
            console.log('打开小程序成功');
          })
          .catch(err => {
            console.log(err);
          });
      } else if (isInWeApp) {
        if (window.JglhWeApp) {
          window.JglhWeApp.openMiniProgramPage(params);
        } else {
          throw new Error('jglh weapp js lib not work !');
        }
      } else if (isInAliApp) {
        // window.location.href = 'alipays://platformapi/startapp?appId=20170713077xxxxx&page=x/yz&query=xx%3dxx';
        // 支付宝webview环境下，使用 web-view 与小程序通信交互，然后再小程序页面 js 中调用 my.navigateToMiniProgram 跳转到其他小程序。
        alipayUtils.openMiniProgramPage({
          url: '/pages/web/index', // 跳转页面地址, 默认首页
          query: `${params.url}?${params.query}`, // 传递参数 a=1&b=2
        });
      }
    },
    /**
     * 以新窗口形式打开webview，若环境不支持，回退为当前窗口打开
     * @param {string} path 跳转路径，可能为一个目录路径，也可能是一个url
     * @param {object} options 跳转参数
     * @param {string} search 查询参数，目前主要用于动态配置web页面是否隐藏模拟的标题栏
     */
    $_router_pageTo(path, options = {}, search = '?jglh') {
      // pushWebView方法不支持 requireSignIn，但 `交广领航从App打开URL的协议` 支持 requireSignIn 参数，此处变相支持requireSignIn
      // http://jgrm.net:8018/zentao/www/index.php?m=story&f=view&storyID=85
      if (options.requireSignIn && !this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      // pushWebView方法依赖于交广领航JS-SDK，只能在交广领航环境下使用
      const fullURL = getAppURL(path, {
        search: options.titleBar ? '?jglh&nohead' : search,
      });
      // 解析jglh-url，忽略默认参数
      const parsedResult = parseJglhURL(fullURL, true);
      // lh_wvc参数用于交广领航app识别，其他环境中无用，此处将其移除
      const fullURL2 = handleJglhAndWeixinWebViewConfigParam(parsedResult.url);
      // debugger
      if (isInJglh) {
        const defaultWebViewOptions = {
          title: '',
          renderer: 'x5',
          shareButton: false,
          titleBar: false,
          progressBar: false,
          sbarColor: 'white',
          pulldownRefresh: false,
          autoUpdateTitle: false,
        };
        // 自定义theme字段，，简化传参
        if (options.theme === 'light') {
          options.sbarColor = 'black';
          options.sbarBgColor = '#ffffff';
          // options.sbarBgColor = '#fdfdfd';
          delete options.theme;
        }
        // 参数优先级：lh_wvc参数 > 方法传入参数 > 默认参数
        const opts = Object.assign(defaultWebViewOptions, options, {
          ...parsedResult,
          url: fullURL2,
        });
        pushWebView(opts);
      } else if (isInAliApp) {
        if (window.my) {
          let query = `url=${encodeURIComponent(fullURL2)}`;
          if (options.shareButton === false) {
            query += '&share=0';
          }
          alipayUtils.openMiniProgramPage({
            url: '/pages/web/index', // 跳转页面地址, 默认首页
            query: query, // 传递参数 a=1&b=2
          });
          // window.JglhWeApp.pushWebview(fullURL2);
        } else {
          throw new Error('jglh weapp js lib not work !');
        }
      } else if (isInWeApp) {
        if (window.JglhWeApp) {
          let query = `url=${encodeURIComponent(fullURL2)}`;
          if (options.shareButton === false) {
            query += '&share=0';
          }
          window.JglhWeApp.openMiniProgramPage({
            url: '/pages/web/index', // 跳转页面地址, 默认首页
            query: query, // 传递参数 a=1&b=2
          });
          // window.JglhWeApp.pushWebview(fullURL2);
        } else {
          throw new Error('jglh weapp js lib not work !');
        }
      } else if (isValidURL(path)) {
        // 非交广领航环境下只能通过location刷新页面跳转
        if (options.replace) {
          location.replace(fullURL2);
        } else {
          if (isInPC) {
            // 在新标签页打开
            window.open(fullURL2, '_blank');
            return;
          }
          location.href = fullURL2;
        }
      } else {
        console.warn('router: downgrade to SPA inner push', path);
        if (options.replace) {
          this.$_router_replace(path);
        } else {
          this.$_router_push(path);
        }
      }
    },
    /**
     * 打开一个带有输入框的页面
     * iOS端将以 新窗口的形式打开
     * Android端以普通方式打开
     * @param {string} path
     * @param {object} options
     */
    $_router_push4inputting(path, options = {}) {
      if (isIOS && isInJglh) {
        this.$_router_pageTo(
          path,
          Object.assign({}, options, {
            titleBar: true,
            autoUpdateTitle: false,
          }),
          '?nohead'
        );
        console.log(options);
      } else {
        this.$_router_push(...arguments);
      }
    },
    /**
     * 跳转到确认订单（选择优惠券页面），此逻辑需要重新设计
     * @param {object} params 选择代金券信息
     *
     * {
          // 提交订单参数列表
          form: Array<any>,

          // 订单类型
          type: OrderBizType,

          // 付款结束后跳转的页面地址渲染函数
          resultUrlRender(oid: string): string,

          // 优惠券查询参数
          ticketQuery: {
            fristCategoryId: TicketQueryCategory,
            goodsId: string | number, // 商品id
            bid: string | number, // 商家id
          },

          // 优惠券界面显示需要的订单信息
          order: {
            title: string, // 订单标题
            id: any, // 暂时无实际意义
            name: string, // 商品名称
            price: number, // 商品价格
            mprice: number, // 商品原价
            vipPrice: number, // vip价格
          },
        }
     */
    $_route_orderSelectTicket(params, routeType = 'push') {
      // if (!soid || !nextPath) throw new Error('参数不对！');
      // TODO: 校验参数格式
      //
      if (routeType === 'push') {
        this.$_router_push({
          name: 'buy-ticket-select',
          params,
          // query: params.ticketQuery,
        });
      } else {
        this.$_router_replace({
          name: 'buy-ticket-select',
          params,
        });
      }
    },
    /**
     * 打开客服聊天界面
     * @param {object} options
     * @param {object} options.uid
     */
    $_route_chat({ user, goods }) {
      // 后台配置：https://yzf.qq.com/xv/html/admin/setup/channel/web/edit/7939/kfh5ca042a2b0ca706#
      const goodsInfo = goods
        ? {
            d1: goods.title,
            d2: goods.price,
            // d3: '40',
            d4: goods.cover,
            d5: '#',
            d6: goods.id,
          }
        : {};
      const chatOptions = {
        sign: '37ef9b97812402c220159cbc1cb2e0655203cea250b267b25cfad58407620a096a29d3f1cd05b6ae2d68b1bcfb113f0388e43c',
        uid: user.uid,
        c1: user.name,
        c2: user.phone,
        c3: user.agent,
        c4: user.note,
        // c5: '',
        // d参数为内测参数
        ...goodsInfo,
      };
      const chatQuery = Object.keys(chatOptions)
        .map(key => {
          return `${key}=${encodeURIComponent(chatOptions[key])}`;
        })
        .join('&');
      const CHAT_URL = `https://yzf.qq.com/xv/web/static/chat/index.html?${chatQuery}`;
      this.$_router_pageTo(CHAT_URL, {
        titleBar: true,
        title: '在线客服',
      });
    },
    /**
     * 跳转到收银台付款页面
     * @param {string} options.type 订单类型
     * @param {string} options.soid 系统订单id
     * @param {string} options.nextPath 支付成功后的跳转页面
     * @param {string} options.method RouteMethod
     */
    $_route_cashierCheckout(options) {
      const RouteMethod = {
        Push: 'push',
        Replace: 'replace',
        Jump: 'jump',
      };
      const { type, query, soid, nextPath, method } = Object.assign(
        {
          method: RouteMethod.Push,
          type: 'jglh',
          query: {},
        },
        options
      );
      if (!soid || !nextPath) throw new Error('参数不对！');
      const next = encodeURIComponent(nextPath);
      // 订单类型暂时未用到，用 jglh 代替
      const path = `/cashier/${type}/checkout/${soid}/next/${next}`;
      if (method === RouteMethod.Replace) {
        this.$_router_replace(path);
      } else if (method === RouteMethod.Jump) {
        this.$_router_pageTo(path, {
          sbarColor: 'white',
          sbarBgColor: '#388efd',
        });
      } else if (method === RouteMethod.Push) {
        this.$_router_push({
          path,
          query,
        });
      } else {
        throw new Error('请指定合法的跳转方式！');
      }
    },
    // 以新窗口形式跳转到 VIP 会员首页
    $_route_vipPage(type = 'mall') {
      this.$_router_pageTo(`/vip?type=${type}`, {
        requireSignIn: false,
        sbarColor: 'black',
        sbarBgColor: '#f2f2f2',
      });
    },
    // 打开养车卡页面
    $_route_mtCardPage() {
      this.$_router_pageTo('/washcard/market', {
        requireSignIn: false,
      });
    },
    // 跳转到用户反馈页面
    $_router_pageToFeedback() {
      const feedBack = `${location.origin}/actions/app/feedback`;
      this.$_router_pageTo(feedBack, {
        requireSignIn: true,
        titleBar: true,
        shareButton: false,
      });
    },
  },
});
