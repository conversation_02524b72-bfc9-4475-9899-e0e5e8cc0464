/* eslint-disable vue/no-use-v-if-with-v-for */
<template>
  <container ref="container" :keep-alive="keepAlive" @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="售后订单">
      <x-button  slot="left" type="back"></x-button>
    </x-header>
    <content-view
      :status="status"
      :refreshAction="refreshAction"
      @refresh="refresh"
      @reload="reload"
      @scroll-bottom="loadMore"
    >
      <template v-if="status == AppStatus.READY">
        <transition-group name="order-list" tag="div" class="orders-wrap">
          <template v-for="item in orders">
            <!-- 此处用template包裹，防止后台数据item=null情况 -->
            <div v-if="item" :key="item.id" class="order-item">
              <div class="order-business">
                <div class="order-id">
                  <p>订单:
                    <span>{{ item.orderId }}</span>
                  </p>
                </div>
                <span
                  class="order-status"
                  >{{ formatOrderStatus(item) }}</span
                >
              </div>
              <div class="order-goods-info" @click="goDetail(item)">
                <biz-image
                  :src="getGoodsLogo(item)"
                  class="order-goods-logo"
                  :lazy="true"
                >
                </biz-image>
                <div class="order-goods-name">
                  <h2 class="title">{{ item.orderRefGoodsList[0].goodsName }}</h2>
                  <p class="spec">{{ item.orderRefGoodsList[0].goodsSpecificationName }}</p>
                </div>
                <!-- <div class="order-goods-price">
                  <p>
                    <i>￥</i
                    >{{ item.orderRefGoodsList[0].price }}
                  </p>
                  <span>共{{ item.orderRefGoodsList[0].count }}件</span>
                </div> -->
              </div>
              <div class="refund-amount">退款金额：￥{{ item.applyRefundAmount }}</div>
              <div class="refund-status">
                <span class="status">{{ formatProcessStatus(item.status) }}</span>
                <span class="desc">{{ formatProcessDesc(item.status) }}</span>
              </div>
              <div class="order-goods-btns">
                <!-- <span class="pay-btn">评价</span> -->
                <span
                  class="pay-btn"
                  v-if="item.status == ProcessStatus.AWAITING_REFUND"
                  @click="toDelivery(item.id)"
                  >寄回商品</span
                >
                <span
                  class="pay-btn"
                  @click="toRefundDetail(item.id)"
                  >查看详情</span
                >
              </div>
            </div>
          </template>
        </transition-group>
        <list-loader
          v-if="orders.length > 5"
          :options="$_loader_options"
          @load="loadMore"
        ></list-loader>
        <list-placeholder
          v-if="!orders.length"
          icon="~@/assets/images/mall/empty.png"
          >您还没有相关售后订单</list-placeholder
        >
      </template>
    </content-view>
  </container>
</template>

<script>
import { formatDate } from '@/utils';
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import { AppStatus, OrderBizType, ProcessStatus } from '@/enums';
import { NewOrdersStatus } from './enums';
import { dialog, toast, loading } from '@/bus';
import { getAfterSalesOrderList } from '@/api';
import { confirmMallOrderReceipt, getLogistics } from '@pkg/mall/api';
import { Popup, Step, Steps } from 'vant';

// localStorage.setItem('session', '279e1e03aa164fbe98b0b9e3a66a8c52')

function isSameModule(parent, child) {
  const parentBase = parent.split('/')[1];
  const childBase = child.split('/')[1];
  return parentBase === childBase;
}
const PAGE_SIZE = 10;
export default {
  name: 'OrderListRefund',
  props: {
    // category: {
    //   type: Number,
    //   default: null
    // }
  },
  mixins: [mixinLoader, mixinAuthRouter],
  components: {
    [Popup.name]: Popup,
    [Step.name]: Step,
    [Steps.name]: Steps
  },
  data() {
    return {
      AppStatus,
      ProcessStatus,
      status: AppStatus.LOADING,
      orders: [],
      refreshAction: 1,
      show: false,
      keepAlive: true,
      stepData: [],
      category: 5
    };
  },
  // TODO: 改成使用mixin_loader的形式
  computed: {
    filterParams() {
      return {
        ...this.$_loader_params
      };
    }
  },
  // mounted() {
  //   this.init();
  // },
  methods: {
    // 是否显示订单状态
    showStatus(status) {
      if (this.category && this.category == 5) {
        // 售后/退款 不显示状态
        return false;
      }
      return true;
    },
    // 是否寄回商品按钮
    showBtn(status) {
      if (this.category && this.category == 5) {
        // 售后/退款 不显示状态
        return false;
      }
      return true;
    },
    // 寄回商品
    toDelivery(id) {
      this.$_router_push({
        path: '/mall/aftersale/express/' + id
      });
    },
    // 售后详情
    toRefundDetail(id) {
      this.$_router_push({
        path: '/mall/aftersale/detail/' + id
      });
    },
    // 显示物流信息
    showLogistics(item) {
      loading(true, '查询中...');
      if (!item.id) return false;
      getLogistics(item.id)
        .then(res => {
          loading(false);
          this.show = !this.show;
          this.stepData = res;
          // this.stepData = []
        })
        .catch(e => {
          loading(false);
          e && dialog().alert(e);
        });
    },
    // 格式化状态
    formatOrderStatus(item) {
      if (item.afterSaleType == 'return_goods') {
        return '退货退款'
      }
      return '退款'
    },
    formatProcessStatus(status) {
      if (!status) return '';
      let str = '';
      switch (status) {
        case 1:
        case 4:
        case 6:
        case 8:
        case 17:
        case 27:
          str = '处理中';
          break;
        case 2:
        case 32:
        case 16:
        case 26:
          str = '申请失败';
          break;
        case 15:
        case 25:
          str = '退款成功';
          break;
        case -1:
        case -2:
          str = '申请关闭';
          break;
        default:
          break;
      }
      return str;
    },
    formatProcessDesc(status) {
      if (!status) return '';
      let str = '';
      switch (status) {
        case 1:
        case 6:
        case 8:
          str = '等待商家处理';
          break;
        case 2:
          str =
            this.type == 'refund' ? '商家拒绝退款申请' : '商家拒绝退款退货申请';
          break;
        case 32:
          str =
            this.type == 'refund' ? '平台拒绝退款申请' : '平台拒绝退款退货申请';
          break;
        case 4:
          str = '等待买家寄回商品';
          break;
        case 16:
          str = '商家拒绝退款';
          break;
        case 26:
          str = '平台拒绝退款';
          break;
        case 17:
        case 27:
          str = '退款中';
          break;
        case 15:
        case 25:
          str = '退款成功';
          break;
        case -1:
          str = '您已撤销申请';
          break;
        case -2:
          str = '超时自动关闭';
          break;
        default:
          break;
      }
      return str;
    },
    goDetail(item) {
      // 团购
      this.$_router_push('/mall/order/' + item.orderId);
    },
    getGoodsLogo(item) {
      let imageStr = require('./images/group-buy.png');
      return (item.orderRefGoodsList[0].image || '').split(',')[0] || imageStr;
      // this.$_router_push(url);.
    },
    onLeave() {
      console.log('leave');
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.refreshQuietly();
    },
    // 静默刷新列表数据
    refreshQuietly() {
      const currentPage = this.$_loader_getPage();
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(this.orders.length);
      return this.getList().then(
        res => {
          this.$_loader_setPage(currentPage);
          this.$_loader_setPageSize(PAGE_SIZE);
          return res;
        },
        err => {
          // this.status = AppStatus.ERROR;
          toast().tip('刷新失败，请重试！');
          console.error(err);
        }
      );
    },
    // 初始化函数
    init() {
      return this.initList();
    },
    // 失败重试
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    // 重新获取列表
    initList() {
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(PAGE_SIZE);
      return this.getList()
        .then(res => {
          this.status = AppStatus.READY;
          return res;
        })
        .catch(err => {
          this.status = AppStatus.ERROR;
          console.error(err);
          toast().tip(err);
          return Promise.reject(err);
        });
    },
    // 滚动加载更多
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      const nextPage = this.filterParams.page + 1;
      this.getList(nextPage).catch(e => {
        toast().tip(e);
        console.error(e);
      });
    },
    // 下拉刷新
    refresh() {
      this.initList()
        .then(res => {
          this.refreshAction = Date.now();
        })
        .catch(e => {
          toast().tip('刷新失败');
          this.refreshAction = Date.now();
        });
    },
    go(url) {
      this.$_router_push(url);
    },
    // 查询列表
    getList(page = 1) {
      const params = { ...this.filterParams, page };
      return this.$_loader_bind(getAfterSalesOrderList, res => {
        if (page === 1) {
          this.orders = res.list || [];
        } else {
          this.orders = this.orders.concat(res.list || []);
        }
        return res.list;
      }).load(params);
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~styles/mixin/index.scss";

$border-color: #ececec;
.tabs-orders {
  background: white;
  box-shadow: 0 1px 5px 1px #e7eaf5;
  z-index: 1;
}
.orders-wrap {
  position: relative;
  // max-width: 100%;
  box-sizing: border-box;
  padding: 0 10px;
}
.order-item {
  background: white;
  padding: 14px;
  border-radius: 5px;
  // width: 100%;
  box-sizing: border-box;
  margin: 10px 0;
  overflow: hidden;
  .order-status {
    color: #999999;
    font-size: 12px;
    line-height: 1;
  }
  .order-status-highlight {
    color: $lh-2022-primary-color;
  }
  .order-business {
    // width: 100%;
    box-sizing: border-box;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // line-height: 1.1;
    .order-business-name {
      flex: 1;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      color: #333333;
      line-height: 1;
      p {
        // line-height: 16PX;
        display: inline-block;
        max-width: 270px;
        text-overflow: ellipsis;
        // overflow: hidden;
        white-space: nowrap;
        // vertical-align: top;
        font-weight: 700;
      }
    }
    .order-id{
      color: #999;
      font-size: 12px;
      line-height: 1;
      span{
        user-select:text;
      }
    }
  }
  .order-goods-info {
    // width: 100%;
    display: flex;
    align-items: center;
    .order-goods-logo {
      width: 70px;
      height: 70px;
      border-radius: 5px; /* px */
      overflow: hidden;
      margin: 0px 3px 0 0;
    }
    .order-goods-name {
      padding-left: 6px;
      flex: 1;
      .title{
        line-height: 1.2;
        font-size: 14px;
        text-overflow: ellipsis;
        overflow: hidden;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      .spec{
        margin-top: 12px;
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #999999;
        line-height: 1;
      }
    }
    .order-goods-price {
      width: 80px;
      text-align: right;
      display: inline-flex;
      flex-direction: column;
      justify-content: flex-end;
      line-height: 1;
      p {
        vertical-align: bottom;
        font-size: 18px;
        font-weight: bold;
        i {
          font-style: normal;
          font-size: 10px;
        }
        padding-bottom: 5px;
      }
      span {
        font-size: 12px;
        color: #999999;
      }
    }
  }
  .refund-amount {
    font-size: 13px;
    font-family: PingFang SC;
    font-weight: bold;
    color: #F5222D;
    line-height: 1;
    padding-left: 80px;
    margin: 7px 0px 0 0;
  }
  .refund-status {
    margin-top: 10px;
    background: #F3F3F3;
    border-radius: 5px;
    display: flex;
    align-items: center;
    padding: 14px;
    .status{
      flex: 0;
      word-break: keep-all;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #F5222D;
      line-height: 20px;
      margin-right: 10px;
    }
    .desc{
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #333333;
      line-height: 20px;
    }
  }
  .order-goods-btns {
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .pay-btn {
      color: #333333;
      border: 1px solid #666666;
      padding: 5px 12px;
      border-radius: 13px;
      line-height: 1;
      font-size: 14px;
      margin-left: 12px;
      &.active {
        color: $lh-2022-primary-color;
        border-color: $lh-2022-primary-color;
      }
    }
  }
}
.icon-arrow-right {
  // color: #000000;
  font-size: 14px;
  transform: scale(0.8);
}
.order-foot {
  @include border-top($border-color, "before");
  // border-top:1px solid $border-color;
  padding-top: 5px;
  margin-left: 5px;
  margin-top: 4px;
  padding-right: 10px;
}
.order-head {
  padding-right: 10px;
  // margin-top: 5px;
}
// .order-end{
//   .order-head::before{
//    filter: grayscale(1); // 滤镜转为黑白
//   }
// }
.order-btn {
  color: #ee6e00;
  border-color: #f59645;
  background: white;
  line-height: 1.8;
  &:not(.weui-btn_disabled):active {
    background-color: #f59645;
    color: white;
  }
  &.order-btn__primary {
    background: #f59645;
    color: white;
  }
}

.scroller {
  overflow-y: hidden;
}
.vscroller {
  height: 600px;
  overflow-y: scroll;
}
.order {
  box-sizing: border-box;
}
.logistics-action {
  padding-bottom: 20px;
  max-height: 70%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .title {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 16px;
    // position: absolute;
    // top: 0;
    // left: 0;
    // z-index: 1;
    -webkit-transform: translateZ(0);
  }
  .steps {
    flex: 1;
    height: 100%;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 0;
      display: none;
      background-color: transparent;
    }
  }
  h3 {
    font-size: 14px;
    font-weight: normal;
  }
  .van-step:first-child {
    h3 {
      font-weight: bold;
    }
  }
  .empty {
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 20px;
    .empty-icon {
      width: 165px;
      height: 110px;
      margin: 15px auto;
      background: url(../../assets/images/empty.png) no-repeat;
      background-size: 100% 100%;
    }
    .empty-text {
      font-size: 14px;
      color: #6f6f6f;
      text-align: center;
    }
  }
}
</style>
