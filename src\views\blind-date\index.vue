<template>
  <container
    @ready="onReady"
    @leave="onLeave"
    @resume="onResume"
    @scroll-bottom="onScrollBottom"
  >
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="header-right">
        <div class="share-btn" @click="share('show')">
          <i class="icon_jglh icon-fenxiang1"></i>
        </div>
      </div>
    </x-header>

    <!-- 将content-view替换为普通div容器 -->
    <div class="tab-content-container" v-if="status === AppStatus.READY">
      <!-- 各个视图内容区域 -->
      <keep-alive>
        <component
          :is="currentView"
          ref="currentView"
          @switch-tab="switchTab"
          @apply="handleApply"
          @reload="reload"
          :unreadCount="receivedHeartbeatsUnreadCount"
        />
      </keep-alive>

      <FloatSideButton
        v-if="showApplyBtn"
        :width="80"
        :height="80"
        :offsetBottom="100"
        class="apply-f"
      >
        <div class="f-btn-apply" @click="handleApply">
          <!-- <van-icon name="plus" /> -->
          <img src="@/views/blind-date/images/td.png" alt="apply" />
        </div>
      </FloatSideButton>
      <FloatSideButton
        v-if="showSignInBtn"
        :width="80"
        :height="80"
        :offsetBottom="200"
        class="apply-f sign-btn"
      >
        <div class="f-btn-apply" @click="handleSignIn">
          <!-- <van-icon name="plus" /> -->
          <img src="@/views/blind-date/images/signin.png" alt="apply" />
        </div>
      </FloatSideButton>
      <!-- 底部导航栏 -->
      <tab-bar
        v-model="activeTab"
        @change="switchTab"
        :unreadCount="receivedHeartbeatsUnreadCount"
      />
    </div>

    <!-- 如果不是READY状态，使用page-loader显示加载状态 -->
    <page-loader v-else :status="status" @reload="reload"></page-loader>

    <!-- 签到规则弹窗 -->
    <sign-in-rule-dialog
      v-model="showSignInRuleDialog"
      @confirm="onSignInConfirm"
      @cancel="onSignInCancel"
    />
  </container>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { Toast, Icon } from 'vant';
import { getAppURL, getQueryParams } from '@/utils';
import { getImageURL } from '@/common/image';
import { getPaymentRecords, getReceivedHeartbeatsUnreadCount } from './api';

// 导入视图组件
import PageLoader from '@/components/Page/PageLoader.vue';
import FloatSideButton from '@/views/components/FloatSideButton.vue';
import HomeView from './components/HomeView.vue';
import RankView from './components/RankView.vue';
import ActivityView from './components/ActivityView.vue';
import ProfileView from './components/ProfileView.vue';
import CouplesView from './components/CouplesView.vue';
import TabBar from './components/TabBar.vue';
import SignInRuleDialog from './components/SignInRuleDialog.vue';

export default {
  name: 'BlindDate',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    HomeView,
    RankView,
    CouplesView,
    ActivityView,
    ProfileView,
    TabBar,
    FloatSideButton,
    SignInRuleDialog,
    [Toast.name]: Toast,
    [Icon.name]: Icon,
    PageLoader,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      activeTab: 'HomeView',
      titles: ['天生一对', '天生一对', '有缘人', '天生一对', '天生一对'], // 以备不同tab用不同标题
      receivedHeartbeatsUnreadCount: 0,
      showSignInRuleDialog: false,
    };
  },
  computed: {
    ...mapState({
      profile: state => state.blindDate.profile,
    }),
    currentView() {
      return this.activeTab;
    },
    pageTitle() {
      const views = [
        'HomeView',
        'RankView',
        'CouplesView',
        'ActivityView',
        'ProfileView',
      ];
      let index = views.indexOf(this.activeTab);
      return this.titles[index];
    },
    showApplyBtn() {
      // 显示按钮的集中情况
      // 1.用户未登录
      // 2.用户已登录，但未完善资料
      // 3.用户已登录，且已完善资料
      if (!this.$_auth_isLoggedIn) {
        return true;
      }
      if (!this.profile.name) {
        return true;
      }
      return false;
    },
    showSignInBtn() {
      return this.activeTab === 'HomeView';
    },
  },
  created() {
    this.init();
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 更新“我的”tab的未读数量
      if (from.path === '/blindDate/likeMe') {
        getReceivedHeartbeatsUnreadCount().then(res => {
          vm.receivedHeartbeatsUnreadCount = (res && res.unReadCount) || 0;
        });
      }
    });
  },
  methods: {
    ...mapActions('blindDate', ['fetchProfile']),
    init() {
      if (this.$_auth_isLoggedIn) {
        this.fetchProfile().then(() => {
          getReceivedHeartbeatsUnreadCount().then(res => {
            this.receivedHeartbeatsUnreadCount = (res && res.unReadCount) || 0;
          });
        });
      }
      this.share();
    },
    refresh() {
      this.init();
    },
    share(action = 'config') {
      const title = '天生一对 - 遇见心动的TA';
      const logo = getImageURL('Fr8IHG-bv-Ya5p65DzxJs53GOy8y');
      const desc = '寻找你生命中的另一半，真心相遇，缘分天定，一见心动！';
      const url = getAppURL(location.href);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    handleApply() {
      this.$_auth_requireLoggedIn().then(() => {
        // 获取URL参数，判断是否需要支付
        const urlObj = getQueryParams(location.href);
        const copChannel = urlObj.cop_channel;
        const notNeedToPay = copChannel !== undefined;
        // 检查用户是否已同意协议
        const hasAgreed =
          localStorage.getItem('blind_date_agreement_accepted') === 'true';

        if (notNeedToPay) {
          // 不需要支付的情况

          if (hasAgreed) {
            // 已同意协议，直接跳转至申请页面
            this.$router.push('/blindDate/apply');
          } else {
            // 未同意协议，跳转至协议页面
            this.$router.push('/blindDate/agreement');
          }
        } else {
          // 需要支付的情况
          // 检查用户是否已支付
          getPaymentRecords()
            .then(res => {
              const records = res || [];
              // 如果有支付记录，直接跳转至申请页面
              if (records.length > 0) {
                if (hasAgreed) {
                  // 已同意协议，直接跳转至申请页面
                  this.$router.push('/blindDate/apply');
                } else {
                  // 未同意协议，跳转至协议页面
                  this.$router.push('/blindDate/agreement');
                }
              } else {
                // 没有支付记录，跳转至支付提醒页
                this.$router.push('/blindDate/payment');
              }
            })
            .catch(err => {
              Toast(err.message || '获取支付记录失败');
              console.error(err);
            });
        }
      });
    },
    handleSignIn() {
      this.$_auth_requireLoggedIn().then(() => {
        this.showSignInRuleDialog = true;
      });
    },
    onSignInConfirm() {
      // 用户点击确定按钮时的处理
      this.$_router_pageTo('/virtual/get/gold', {
        theme: 'light',
      });
    },
    onSignInCancel() {
      // 用户点击取消按钮时的处理
      this.showSignInRuleDialog = false;
    },
    onReady() {
      this.status = AppStatus.READY;
    },
    onLeave() {
      // 页面离开的处理
    },
    onResume() {
      // this.refresh();
      const shouldRefreshTabs = ['HomeView', 'ProfileView'];
      if (shouldRefreshTabs.includes(this.activeTab)) {
        this.$refs.currentView.refresh();
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.status = AppStatus.READY;
    },
    switchTab(name) {
      this.activeTab = name;
      const shouldRefreshTabs = ['HomeView', 'ProfileView'];
      if (shouldRefreshTabs.includes(name)) {
        setTimeout(() => {
          this.$refs.currentView.refresh();
        }, 300);
      }
    },
    goToActivityDetail(activityId) {
      Toast('活动详情功能开发中');
    },
    onScrollBottom() {
      // console.log('滚动到底部');
    },
  },
};
</script>

<style lang="scss" scoped>
.header-right {
  display: flex;
  align-items: center;

  .share-btn {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon_jglh {
      font-size: 24px;
    }
  }
}

/* 添加新的容器样式 */
.tab-content-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
  /* 减去header的高度 */
  position: relative;
  overflow: hidden;
}

.apply-f {
  bottom: 100px;

  .f-btn-apply {
    width: 80px;
    height: 80px;
    display: flex;
    padding: 0px;
    border-radius: 100px;

    img {
      width: 100%;
      height: 100%;
      display: block;
      margin-bottom: 0;
    }
  }
}

.sign-btn {
  bottom: 200px;

  .f-btn-apply {
    width: 60px;
    height: 60px;
    border-radius: 60px;
  }
}
</style>
