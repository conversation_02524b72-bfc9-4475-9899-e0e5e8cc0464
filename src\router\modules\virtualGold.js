import { handleError } from '../error-handler';

export default [
  {
    path: '/virtual/get/gold',
    name: '金币获取',
    component: resolve => {
      import(/* webpackChunkName: "gt-gold" */ '@/views/packages/virtual-gold/Get-Gold.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/virtual/gold/record',
    name: '金币',
    component: resolve => {
      import(/* webpackChunkName: "gt-gold" */ '@/views/packages/virtual-gold/Gold-Record.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/virtual/gold/buy',
    name: '买金币',
    component: resolve => {
      import(/* webpackChunkName: "gt-gold" */ '@/views/packages/virtual-gold/Gold-Buy.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/virtual/gold/rule',
    name: '金币规则',
    component: resolve => {
      import(/* webpackChunkName: "gold-rule" */ '@/views/packages/virtual-gold/Gold-Rule.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/virtual/sign/rule',
    name: '签到规则',
    component: resolve => {
      import(/* webpackChunkName: "sign-rule" */ '@/views/packages/virtual-gold/Sign-Rule.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/virtual/gold/buy/xy',
    name: '购买协议',
    component: resolve => {
      import(/* webpackChunkName: "buy-xy" */ '@/views/packages/virtual-gold/Buy-xy.vue').then(resolve).catch(handleError);
    },
  }
];
