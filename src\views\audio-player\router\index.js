import { handleError } from '@/router/error-handler';

export const routes = [
  {
    path: 'profile',
    name: 'Profile',
    component: resolve => {
      import('@/views/audio-player/pages/profile.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: 'share/album',
    name: 'WxAlbum',
    component: resolve => {
      import('@/views/audio-player/pages/share-album-wx.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: 'share/player',
    name: 'WxPlayer',
    component: resolve => {
      import('@/views/audio-player/pages/share-player-wx.vue').then(resolve).catch(handleError);
    },
  }
];

const Landing = {
  path: '/audio',
  name: 'Audio',
  component: resolve => {
    import('@/views/audio-player/index.vue').then(resolve).catch(handleError);
  },
  children: routes,
}

export default Landing;
// export default routes;
