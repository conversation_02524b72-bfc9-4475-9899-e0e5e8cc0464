<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  .float-button {
    position: fixed;
    bottom: 40%;
    // transition: all 250ms;
    user-select: none;
    z-index: 999;
    right: 0;
  }
  .float-button-right {
  }
  .float-button-left {
    // left: 0;
  }
</style>
<template>
  <div @click="onClick" class="float-button" :class="classes" :style="style"><slot></slot></div>
</template>
<script>
import AlloyFinger from 'alloyfinger';
// import Transformjs from 'alloyfinger/transformjs/transformjs';

export default {
  name: 'FloatButton',
  props: {
    foldWidth: {
      type: Number,
      default: 0,
    }
  },
  components: {},
  data() {
    return {
      moving: false,
      currentPosition: {
        x: 0,
        y: 0,
      },
      lastPosition: {
        x: 0,
        y: 0,
      },
      btnFoldWidth: 0,
    };
  },
  computed: {
    classes() {
      const halfWidth = window.innerWidth / 2;
      const rect = this.getButtonRect();
      return {
        'float-button-moving': this.moving,
        'float-button-left': rect.left <= halfWidth,
        'float-button-right': rect.right > halfWidth,
      }
    },
    style() {
      const { x, y } = this.currentPosition;
      const x1 = `${x}px`;
      const y1 = `${y}px`;
      const rect = this.getButtonRect();
      // console.log(rect);
      const style = {
        transform: `translate(${x1}, ${y1})`,
        transition: this.moving ? '' : `all 250ms`,
        right: `${this.btnFoldWidth * -1}px`,
      }
      // if (x > window.innerWidth/2) style.right = 0;
      // else style.left = 0;
      return style;
    },
  },
  watch: {
    moving(val) {
      if (val) {
        this.cancelRestoreBtnPosition();
      }
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    onClick() {
      if (this.btnFoldWidth == 0) {
        this.$emit('click');
      } else {
        this.btnFoldWidth = 0;
      }
    },
    getButtonRect() {
      if (!this.$el) {
        console.log('this.$el:', this.$el);
        return {}
      } else {
        return this.$el.getBoundingClientRect();
      }
      // const rect =  this.$el && this.$el.getBoundingClientRect();
      // return rect;
    },
    init() {
      const element = this.$el;
      var af = new AlloyFinger(element, {
        touchStart: (evt) => {
          this.moving = true;
          this.lastPosition = {
            x: this.currentPosition.x,
            y: this.currentPosition.y,
          };
        },
        touchMove: function (e) {
          },
        touchEnd: evt => {
          this.moving = false;
          this.onTouchEnd();
        },
        pressMove: (evt) => {
          // console.log(evt.deltaX);
          // console.log(evt.deltaY);
          this.currentPosition.x = this.getPositionX(evt.deltaX);
          this.currentPosition.y = this.getPositionY(evt.deltaY);
          evt.preventDefault();
        },
      });
      setTimeout(() => {
        this.btnFoldWidth = this.foldWidth;
      }, 3000)
    },
    getPositionX(move) {
      const rect = this.getButtonRect();
      const border = {
        left: 0,
        right: window.innerWidth,
      };
      if (rect.left + move <= border.left || rect.right + move >= border.right) return this.currentPosition.x;
      return this.currentPosition.x + move;
    },
    cancelRestoreBtnPosition() {
      console.log('cancelRestoreBtnPosition ');
      clearTimeout(this.$_icode);
    },
    restoreBtnPosition() {
      this.cancelRestoreBtnPosition();
      this.$_icode = setTimeout(() => {
        this.btnFoldWidth = this.foldWidth;
      }, 3000)
    },
    getPositionY(move) {
      const rect = this.getButtonRect();
      const border = {
        top: 50,
        bottom: window.innerHeight - 50,
      };
      if (rect.top + move <= border.top || rect.bottom + move >= border.bottom) return this.currentPosition.y;
      return this.currentPosition.y + move;
    },
    onTouchEnd() {
      const { x: currentX } = this.currentPosition;
      const halfWidth = window.innerWidth / 2;
      const rect = this.getButtonRect();
      // 因左侧可能和系统侧栏右滑返回手势冲突，暂固定只能停靠在右侧
      this.currentPosition.x = 0;
      this.restoreBtnPosition();
      // this.currentPosition.x = (Math.abs(currentX) <= halfWidth ? 0 : currentX);
    },
  },
};
</script>
