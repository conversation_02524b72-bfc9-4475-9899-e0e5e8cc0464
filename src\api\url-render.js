// import template from 'lodash/template';

/**
 * 渲染字符串模版，支持REST风格接口
 * 示例：render('/test/{id}/update', { id: 123});
 * 结果：/test/123/update
 * @param { string } url 字符串模版
 * @param { object } data 数据对象
 * @returns {string} 渲染结果
 */
// export default function render(url, data = {}) {
//   const interpolate = /{([\s\S]+?)}/g;
//   const compiled = template(url, { interpolate });
//   return compiled(data);
// }

export default function render(template, data = {}) {
  return template.replace(/\{(.*?)\}/g, (match, key) => data[key.trim()]);
}
