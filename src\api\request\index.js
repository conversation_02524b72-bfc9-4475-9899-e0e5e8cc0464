import axios from 'axios';
import QS from 'qs';
import { isPlainObject, getAbsoluteURL, isThirdApp } from '@/utils';
import { getFingerprint, getSessionThirdAppParams } from '@/store/storage';
import { login, getSession, getSystemInfo } from '@/bridge/bridge';
import {
  isInWeApp,
  isInWeixin,
  isInJGLH,
  isInUnionPayMP,
  isInAliApp,
} from '@/common/env';
import { dialog, toast } from '@/bus';
import { captureExpectionWithData, captureException } from '@/utils/raven';
import ResponseError from '@/model/ResponseError';

/**
 * 参数中传入参数的值可能为自定义对象，qs不会自动转换，需要将其转换成字符串类型
 * @param {*} obj
 */
function preSerialize(obj) {
  if (isPlainObject(obj)) {
    return Object.keys(obj).reduce((previous, key) => {
      const item = obj[key];
      previous[key] = isPlainObject(item) ? String(item) : item;
      return previous;
    }, {});
  }
  return obj;
}

/**
 * 序列化参数
 * @param {*} data
 */
export function serialize(data) {
  return QS.stringify(preSerialize(data), { arrayFormat: 'repeat' });
}

function isAuthorityError(data) {
  return (
    data &&
    data.code &&
    (/^6\d+/.test(data.code) || data.code == 4003 || data.code == 20001)
  );
}

function isSuccessful(data) {
  const isValid = typeof data === 'object';
  if (!isValid) return false;
  else if (data.code && data.code != 200) {
    return false;
  }
  return true;
}

function getResponseData(body) {
  if (body.code) {
    return body.data;
  } else {
    return body;
  }
}

function onAuthorityFail(msg) {
  dialog('提示').confirm(msg, {
    okText: '去登录',
    ok() {
      login({ force: true });
    },
    cancel() {
      // popWebView(0);
    },
  });
  return Promise.reject(new ResponseError(403, '登录失效'));
}

const RequestMethod = {
  POST: 'post',
  GET: 'get',
  JSON: 'json',
};

function createHeaders() {
  let deviceNo = getFingerprint();
  // 交广领航环境下，获取app提供的id
  if (isInJGLH) {
    getSystemInfo().then(res => {
      if (res && res.id) {
        deviceNo = res.id;
      }
    });
  }
  let headers = {
    // 'authority': getSession(),
    'device-no': deviceNo,
    'jglh-agent': navigator.userAgent,
  };

  const urlParams = new URLSearchParams(window.location.search);
  const scene = urlParams.get('scene');
  if (scene) {
    headers.scene = scene; // 用于区分营销渠道
  }
  return headers;
}

/**
 * 上报错误处理
 */
axios.interceptors.response.use(
  function (res) {
    // console.log(res);
    if (!isSuccessful(res.data)) {
      if (isAuthorityError(res.data)) {
        // 自定义了无权限请求处理函数的请求，6xx错误都是符合预期，无需上报
        if (!res.config.onAuthFail) {
          reportError(res, true);
        } else {
          console.log('ignore report 6xx error ...');
        }
      } else {
        reportError(res);
      }
    }
    return res;
  },
  function (res) {
    // debugger
    // reportError(res);
    return Promise.reject(res);
  }
);

/**
 * 上报错误日志
 * @param {*} res axios返回的对象
 */
function reportError(res, authorityError = false) {
  // 权限错误通过 fingerprint 单独分组
  const url = res.config && res.config.url;
  const fingerprint = authorityError ? 'authorityError' : url;
  let title = '接口请求失败';
  if (authorityError) {
    title = '没有权限';
  } else if (res && res.data && res.data.msg) {
    title = `${res.data.msg}(${res.data.code})`;
  }
  captureExpectionWithData(
    `${title}: ${url}`,
    {
      method: res.config.method,
      api: url,
      authority: res.config.headers.authority,
      data: res.config.data || res.config.params,
      response: res.data,
      responseURL: res.request.responseURL,
      error: res.statusText,
    },
    fingerprint
  );
}

function addQuery(api, key, value) {
  let url = getAbsoluteURL(api);
  if (window.URL && window.URLSearchParams) {
    let theURL = new URL(url);
    theURL.searchParams.delete(key);
    theURL.searchParams.append(key, encodeURIComponent(value));
    return theURL.href;
  } else {
    let append = /\?/.test(url) ? '&' : '?' + [key, value].join('=');
    return url + append;
  }
}

/**
 * 处理请求地址，对必要的接口追加v=394参数
 * @param {*} url
 */
function handleRequestURL(url) {
  // Radio请求统一增加v=394参数
  try {
    // 统一增加v=458参数,路径集合
    const include458 = ['/Radio/vip/ads/infos'];
    const exclude440 = [
      // 'resource/cloud/bulk/gettoken', // 截止2018年9月20日10:04:18，此服务未部署到正式服务器，暂时不加394
    ];
    const shouleAddVersion440 =
      /\/Radio\//.test(url) && !exclude440.some(item => url.indexOf(item) > -1);
    const shouleAddVersion458 = include458.some(item => url.indexOf(item) > -1);

    if (shouleAddVersion458) {
      return addQuery(url, 'v', 458);
    } else if (shouleAddVersion440) {
      return addQuery(url, 'v', 440);
      // return url;
    } else {
      return url;
    }
  } catch (err) {
    captureException(err);
    return url;
  }
}
/**
 * 发起网络请求
 * @param {string} method
 * @param {string} url
 * @param {Object} data
 * @param {Function} onAuthFail 无权限时的处理回调函数
 */
const HEADERS = createHeaders();
function doRequest(method, url, data, onAuthFail) {
  // 临时兼容下服务端取authority的header字段，待服务端统一后再去掉
  // 添加source、platform两个字段区分平台及客户端，后期有时间统一下面不同端的token
  let source = getSessionThirdAppParams().platform || ''; // 区分平台，jglh(交广领航)、hnzgh(河南总工会)
  let platform = ''; // 区分请求来源 APP:手机端 XCX:微信小程序 UNIONPAY_XCX:银联小程序 WEIXIN:微信公众号

  if (isInWeApp) {
    platform = 'XCX';
    HEADERS.xcxtoken = getSession();
  } else if (isInUnionPayMP) {
    platform = 'UNIONPAY_XCX';
    HEADERS.unionpayxcxtoken = getSession();
  } else if (isInAliApp) {
    platform = 'ALIPAY_XCX';
    HEADERS['alipay-xcxtoken'] = getSession();
  } else if (isThirdApp()) {
    platform = 'APP';
    HEADERS.thirdapptoken = getSession();
  } else {
    platform = isInWeixin ? 'WEIXIN' : isInJGLH ? 'APP' : '';
    HEADERS.authority = getSession();
  }
  HEADERS.source = source;
  HEADERS.platform = platform;

  const options = {
    url: handleRequestURL(url),
    headers: HEADERS,
    timeout: 30 * 1000,
    // transformRequest: [function (data) {
    //   // return serialize(data);
    //   return data;
    // }],
    paramsSerializer: function (params) {
      return serialize(params);
    },
    withCredentials: true,
    onAuthFail,
  };

  if (method === RequestMethod.GET) {
    options.params = data;
  } else {
    if (method === RequestMethod.POST) {
      options.transformRequest = [
        function (data) {
          return serialize(data);
        },
      ];
    }
    options.method = RequestMethod.POST;
    options.data = data;
  }
  return axios
    .request(options)
    .then(response => {
      const data = response.data;
      if (isSuccessful(data)) {
        return getResponseData(data);
      } else {
        let { msg, code } = data;
        if (isAuthorityError(data)) {
          // TODO: 需要进一步研究，当接口返回登录失效时是否有必要更新SPA用户登录状态信息
          // logout();
          const tip = HEADERS.authority
            ? '您的登录会话已失效'
            : '请登录后再进行后续操作';
          return onAuthFail ? onAuthFail(tip) : onAuthorityFail(tip);
        }
        return Promise.reject(new ResponseError(code, msg || '请求失败'));
        // return Promise.reject(`${msg || '请求失败'}`);
      }
    })
    .catch(err => {
      let msg = err && err.message;
      let code = (err && err.code) || 0;
      if (typeof err === 'string') {
        msg = err;
      } else if (err && err.response) {
        const res = err.response;
        msg = `请求失败(${res.status})`;
        if (res.status == 503) {
          code = 503;
          msg = '服务正在升级中，请稍后再试';
          toast().tip(msg);
        } else if (res.status == 502) {
          code = 502;
          msg = '当前服务暂不可用，请稍后再试';
          toast().tip(msg);
        }
      }
      return Promise.reject(new ResponseError(code, msg));
      // return Promise.reject(msg);
    });
}

function doGet(api, data, options = {}) {
  return doRequest(RequestMethod.GET, api, data, options.onAuthorityFail);
}

function doPost(api, data, options = {}) {
  return doRequest(RequestMethod.POST, api, data, options.onAuthorityFail);
}

function postJSON(api, data, options = {}) {
  return doRequest(RequestMethod.JSON, api, data, options.onAuthorityFail);
}
export { doGet, doPost, postJSON };

/**
 * 实现fetch的timeout 功能
 * @param {object} config fetch配置
 * @param {Number} timeout 超时设置，默认5000ms
 * @returns {Promise} 返回fetch结果或超时错误
 */
export function fetch_timeout(config) {
  let { timeout = 15000 } = config; // 默认超时5000ms
  let controller = new AbortController();
  const signal = controller.signal;

  // 创建fetch请求的promise
  const fetchPromise = fetch(config.url, {
    ...config,
    signal,
  }).then(res => {
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  });

  // 创建超时的promise
  const timeoutPromise = new Promise((resolve, reject) => {
    const id = setTimeout(() => {
      controller.abort(); // 中止请求
      reject({ code: 504, message: '请求超时!' });
    }, timeout);

    // 清理定时器
    fetchPromise.finally(() => clearTimeout(id));
  });

  // 返回最快的promise
  return Promise.race([fetchPromise, timeoutPromise]).catch(err => {
    if (err.name === 'AbortError') {
      return { code: 504, message: '请求被中止!' };
    }
    throw err; // 其他错误继续抛出
  });
}
