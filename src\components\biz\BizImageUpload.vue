<template>
  <uploader ref="uploader" v-bind="options" @input="onInput" @change="onChange">
    <template v-slot:uploadIconSlot>
      <slot name="uploadIconSlot"></slot>
    </template>
    <slot></slot>
  </uploader>
</template>
<script>
import Uploader from '@/components/Uploader';
import { getQiniuUploadToken } from '@/api';
import { getImageURL } from '@/common/image';
import { playPhotos } from '@/bridge';
import { dialog, toast, loading } from '@/bus';

function getThumbnail(value) {
  return getImageURL(value, '_xslogo');
}

// kebab case to camelcase javascript
function kebab2camel(key) {
  return key.replace(/(-\w)/g, m => {
    return m.replace('-', '').toUpperCase();
  });
}

// 对象 key值 命名风格转换
function transformObjectCase(obj) {
  const ignores = ['input'];
  return Object.keys(obj).reduce((prev, key) => {
    if (ignores.indexOf(key) == -1) {
      prev[kebab2camel(key)] = obj[key];
    }
    return prev;
  }, {});
}

const onImageUploadParamsChange = function (rcallback) {
  // token刷新周期：30分钟
  const FETCH_INTERVAL = 1000 * 60 * 30;
  let params = {};
  let failCount = 0;
  // let rcallback;
  const getTokenFromServer = () => {
    return getQiniuUploadToken(1).then(res => {
      params = res;
    });
  };

  const refreshToken = () => {
    getTokenFromServer()
      .then(res => {
        failCount = 0;
        rcallback && rcallback(params);
        setTimeout(() => {
          refreshToken();
        }, FETCH_INTERVAL);
      })
      .catch(err => {
        // 获取失败一定时间后重试
        setTimeout(() => {
          refreshToken();
        }, 1000 * (20 + failCount * 10));
        failCount++;
      });
  };
  refreshToken();
};

export default {
  name: 'BizImageUpload',
  components: {
    Uploader,
  },
  props: {
    // 图床，不是qiniu时不需要获取七牛token
    bed: {
      type: String,
      default: 'qiniu',
    },
  },
  data() {
    // console.log(this.$attrs)
    return {
      // 默认参数
      settings: {
        params: {},
        // 上传接口
        autoUpload: false,
        uploadUrl: '//upload.qiniup.com',
        files: [],
        uploadName: 'file',
        maxFiles: 1,
        maxFileSize: 1024 * 1024 * 10,
        multiple: true,
        quality: 0.9,
        // beforeUpload: (file, action) => {
        //   return Promise.resolve.bind(Promise)
        // },
        onPreview: (img, index, list) => {
          this.playPhotos(list, index);
        },
        onRemove(file) {
          return new Promise((resolve, reject) => {
            dialog().confirm('确定要删除这张图片？', {
              title: '提示',
              ok() {
                resolve();
              },
            });
          });
        },
        onError(err) {
          dialog().alert(err);
        },
        onUploaded(result) {
          // 上传成功，返回图片id给组件
          return result.key;
        },
        imageUrlGetter(img) {
          // console.log(img);
          return getThumbnail(img);
        },
      },
    };
  },
  computed: {
    /**
     * 实测组件属性使用 kebab-case 可以传给组件，但无法通过继承传递给组件，通过 camelCased 可以，故所有组件属性都优先使用 camelCased
     */
    options() {
      return Object.assign(
        {},
        this.settings,
        transformObjectCase(this.$attrs),
        transformObjectCase(this.$listeners)
      );
    },
  },
  mounted() {
    if (this.bed === 'qiniu') {
      onImageUploadParamsChange(data => {
        this.settings.params.token = data.token;
      });
    }
  },
  methods: {
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, '_xm'),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },
    submit() {
      return this.$refs.uploader.submit();
    },
    onInput(value) {
      this.$emit('input', value);
    },
    onChange(value) {
      this.$emit('change', value);
    },
  },
};
</script>
