<template>
    <div class="container">
      <x-header title="付款">
         <x-button  slot="left" type="back"></x-button>
      </x-header>

      <div class="content">
        <div class="payment-head">
          <div class="order-info align-center">
            <p>{{order.title}}</p>
            <h4>
              支付<b class="rmb">{{order.amount}}</b>
            </h4>
          </div>
          <p class="align-center">请输入支付密码完成付款</p>
        </div>
        <div class="payment">
          <input type="tel" id="input" class="pay-input" @keyup="handleInput" v-model="input">
          <input type="hidden" id="password" v-model="password">
          <ul class="input-grid flex-row">
            <li v-for="item in passwordChars" :class="{'input-grid-checked' : item}"><i></i></li>
          </ul>
        </div>
      </div>
    </div>
</template>
<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  .payment-head {
    margin: 20px 0;
  }
  .payment {
    height: 40px;
    position: relative;
    margin: 5px 10px 0;
  }
  .input-grid {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    list-style: none;
    height: 100%;
    border: 1px solid #c3c3c3;
    background: white;
    border-right: 0;
  }

  .pay-input {
    width:100%;
    border: 0;
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    background: transparent;
    z-index:1;
    -webkit-appearance: none;
    opacity: 0;
    &:focus{
      outline:none;
    }
  }

  .input-grid>li {
    flex: 1;
    /*border-right: 1px solid #d6d6d6;*/
    @include border-right(#d6d6d6);
    display:flex;
    justify-content: center;
    align-items: center;
    &.input-grid-checked>i{
      display:block;
      width:10px;
      height:10px;
      border-radius: 50%;
      background:black;
    }
  }
</style>
<script>
import { mapState, mapActions } from 'vuex';
import { Header, HeaderButton, Panel } from '@/components';
import { toast, dialog, loading } from '@/bus';
import { accountPay } from '@/api';

export default {
  data() {
    return {
      input: '',
      password: '',
      order: {
        oid: null,
        title: '',
        amount: 0
      }
    };
  },
  created() {
    const { oid, title, amount } = this.$route.params;
    this.order = { oid, title, amount };
  },
  mounted() {
    this.$input = this.$el.querySelector('#input');
    this.$password = this.$el.querySelector('#password');
    setTimeout(() => {
      this.$input.focus();
    }, 500);
  },
  computed: {
    passwordChars() {
      const chars = this.password.split('');
      const pwdList = [0, 0, 0, 0, 0, 0];
      return pwdList.map((item, i) => /^\d$/.test(chars[i]));
    }
  },
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Panel,
  },
  methods: {
    ...mapActions(['submitAccountPay']),
    handleInput(e) {
      const inputChar = this.input;
      const password = this.password;
      const reg = /^\d$/;
      const passwordReg = /^\d{0,6}$/;

      if (!reg.test(inputChar)) {
        if (e.which === 8 && password.length) {
          this.password = password.substring(0, password.length - 1);
        }
        this.input = '';
      } else if (password.length < 6) {
        this.password = password + inputChar;
        this.input = '';
        if (this.password.length === 6) {
          this.$input.blur();
          this.pay();
        }
      }
    },
    clearInput() {
      this.password = '';
      this.$input.focus();
    },
    go(url) {
      this.$router.push(url);
    },
    pay() {
      const that = this;
      loading(true, '正在支付...');
      accountPay({
        oid: this.oid,
        password: this.password
      }).then(res => {
        loading(false);
        return res;
      }).then(res => {
        // toast().success('支付成功！');
      }).catch(msg => {
        dialog().confirm(msg, {
          okText: '重试',
          ok() {
            that.clearInput();
          },
          cancelText: '取消支付',
          cancel() {
            that.clearInput();
          }
        });
      });
    },
  }
};
</script>
