<template>
  <container @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="商家保养服务">
      <x-button  slot="left" type="back"></x-button>
    </x-header>
    <content-view class="shop-home-mt" ref="view" :status="status" @reload="reload" @scroll="onScroll">
      <template v-if="status == AppStatus.READY">
        <!-- <swiper :autoplay="pageActive" :slides="shop.images" @click="playShopPhotos">
          <div class="shop-info-main">
            <h4 class="shop-name">{{shop.title}}</h4>
            <div class="shop-time">营业时间：<i>{{serviceHours}}</i></div>
          </div>
        </swiper> -->
        <div class="banners">
          <slider ref="slider" :slides="shop.images" @click="playShopPhotos" >
            <template #item="{item}">
              <c-picture :type="ImageType.BANNER"  :src="item" class="swiper-slide"></c-picture>
            </template>
            <div class="shop-info-main">
              <h4 class="shop-name">{{shop.title}}</h4>
              <div class="shop-time">营业时间：<i>{{serviceHours}}</i></div>
            </div>
          </slider>
        </div>
        <div class="shop-info">
          <div class="shop-about flex-row center">
            <div class="shop-address" @click="goNavigate">{{shop.address}}</div>
            <a class="shop-phone" @click="showContacts"></a>
          </div>
        </div>

        <div class="panel-block flex-row"  @click="go(shopCommentsView)">
          <div class="flex-item">
            <h4 class="entrance-name">用户评价({{shop.mtScore}}分)</h4>
            <p class="entrance-note">
              <rater :value="shop.mtScore"></rater>
              <span>{{shop.mtCommentCount}}评价</span>
            </p>
          </div>
          <div class="flex-col center entrance-actions">
          </div>
        </div>

        <div v-if="dateItems" class="panel-block">
          <div class="row">
            <div class="row-label">预约时间</div>
            <div class="row-content">
              <div  class="reserve-time" :class="{'full': !dateItems.length}" id="picker-container">
                <picker
                  v-if="status == AppStatus.READY && dateItems.length"
                  v-model="form.date"
                  :items="dateItems"
                  :options="{ container: '#picker-container'}">
                </picker>
                <div v-else class="picker-tip">预约已满</div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-block panel-block-mt" @click="go('/about/mt/' + package.id)">
          <div class="row">
            <div class="row-content">
              <strong>{{package.name}}</strong>
              <!--<span class="package-tip">({{package.tip}})</span>-->
            </div>
            <div class="row-label">查看详情</div>
          </div>
          <div class="row">
            <div class="row-content">
              <div class="mt-note">{{package.name}}标准工时费</div>
            </div>
            <div class="row-label rmb">{{package.fee}}</div>
          </div>
        </div>

        <panel title="推荐机油">
          <div v-if="package.items && package.items.length" class="weui-cells weui-cells_radio">
            <label  v-for="(item, $index) in package.items" class="weui-cell weui-check__label" :for="'x'+$index" >
              <div class="weui-cell__bd flex-row check-item">
                <c-picture :src="item.sysPart.images" class="check-logo"></c-picture>
                <div class="check-content">
                  <h4 class="check-title">{{item.partsName}}</h4>
                  <p class="check-note">
                    <span class="rmb">{{item.price}}</span>
                  </p>
                </div>
              </div>
              <div class="weui-cell__ft">
                <input type="radio" class="weui-check" v-model="form.oil" :value="item.id" name="mt" :id="'x'+$index">
                <span class="weui-icon-checked"></span>
              </div>
            </label>
          </div>

          <div v-else class="empty-block flex-row">
            <c-picture src="~@/assets/images/duka.png" class="empty-img"></c-picture>
            <div class="empty-content flex-col">
              <h3 class="empty-title">门店业务上线中</h3>
              <p class="empty-desc">敬请期待</p>
            </div>
          </div>
        </panel>

        <div slot="foot" v-if="package.items && package.items.length > 0" class="shop-bottom flex-row" :class="{'shop-bottom-hidden' : hideBuyButton || showShopContact}">
          <div class="bottom-left">
            <div class="order-amount">
              <span>合计:</span>
              <b class="rmb">{{orderFee}}</b>
              <span>(不含其他配件费)</span>
            </div>
          </div>
          <div class="bottom-right weui-btn weui-btn_primary" :class="{'disabled': !dateItems.length}" @click="buy()">确认预约</div>
        </div>

      </template>
    </content-view>
    <transition name="actionsheet" @enter="() => { this.showShopContact = true }"  @beforeLeave="() => { this.showShopContact = false; }">
      <router-view>
      </router-view>
    </transition>
  </container>
</template>

<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  $border-color-gray: #e8e8e8;
  .row{
    display:flex;
    .row-label{
      width:100px;
    }
    .row-content{
      flex:1;
    }
  }

  .panel-block-mt{
    .row{
      margin-top:5px;
    }
    .row-label{
      text-align:right;
    }
    .mt-note{
      font-size:0.9em;
    }
    .rmb{
      margin:0;
    }
  }
  .shop-home-mt {
    .banners {
      height: 200px;
      background: #e4e4e4;
    }
  }
  .shop-info {
    background: white;
    border-bottom:1px solid $border-color-gray;
  }
  .shop-info-main{
    position: absolute;
    bottom: 0;
    width: 100%;
    z-index: 999;
    color: rgb(255, 255, 255);
    box-sizing: border-box;
    /*background: url(~@/assets/images/shop/gradient.png) center center no-repeat rgba(0, 0, 0, 0.6);
    background-size:contain;*/
    padding:2px 10px;
    &:after {
      content: "";
      height: 0;
      display: block;
      box-shadow: 1px 1px 50px 50px rgba(0, 0, 0, 0.6);
    }
  }
  .shop-name{
    font-weight:500;
    font-size:18px;
    line-height:1.2;
  }
  .shop-sales{
    float:right;
    font-size: 0.8em;
    position: relative;
    top: 2px;
  }
  .shop-time{
    font-size:0.9em;
    >i{
      color:#F7821F;
      font-style:normal;
    }
  }
  .shop-about{
    padding:8px 0 8px 8px;
  }
  .shop-address {
    flex: 1;
    color: #424242;
    line-height: 1.4;
    /*border-right: 1px solid #e4e4e4;*/
    @include border-right('#e4e4e4', 'after');
    font-size: 0.92em;
    padding: 0 8px;
    &:active{
      background: rgba(208, 208, 208, 0.11);
    }
  }
  .shop-about {
    /*.icon-ditu{
      font-size:18px;
    }*/
    &::before{
      /*display:none;*/
      content: "\e647";font-family: iconfont;font-size: 1.2em;
    }
  }
  .shop-nav{
    padding-left:15px;
    &::before {
      content: "\e679";
      font-family: iconfont;font-size: 18px;padding-right: 5px;
      display:block;
      color:#4E85FB;
    }
  }
  .shop-phone {
    padding: 0 15px 0 17px;
    font-size: 24px;
    color: #4e85fb;
    &:active {
      background: rgba(128, 128, 128, 0.1);
    }
    &:before {content: "\e618";font-family: iconfont;font-size: 22px;}
  }

  .panel-block {
    padding: 8px 10px;
    background: rgb(255, 255, 255);
    margin: 10px 0;
    border: 1px solid $border-color-gray;
    border-width: 1PX 0;
    .entrance-name{
      color:#4c4c4c;
      font-weight:400;
    }
    .entrance-note{
      color:rgb(165, 165, 165);
      font-size:.9em;
    }
    .entrance-actions {
      &::after{
        content: '\E605';
        font-family: iconfont;
        float: right;
        color: rgb(128, 128, 128);
      }
    }
  }
  .panel {
    .weui-cells_radio{
      margin-top:0;
      &::before{
        display:none;
      }
    }
  }
  .rmb{
    margin-left:0;
    font-weight:700;
    color:#FE861F;
  }
  .package-fee{
    text-align:right;
  }
  .reserve-time{
    text-align:right;
    display:flex;
    justify-content: flex-end;
    .picker{
      flex:1;
    }
    .picker::after{
      content: '\E605';
      font-family: iconfont;
      color: gray;
      margin-left:5px;
    }
    &.empty{
      color:gray;
    }
    &.full{
      .picker-tip{
        color:red;
      }
    }
  }

  .empty-block {
    padding: 20px 10px;
    .empty-content{
      flex: 1;
      justify-content: center;
    }
    .empty-title{
      font-weight: 400;
    }
    .empty-img{
      width: 100px;
      height: 100px;
      margin: 0 20px;
      background-size: contain;
    }
  }

  .check-item{
    align-items: center;
    .check-content{
      flex:1;
    }
    .check-title{
      font-weight:400;
      font-size: 0.9em;
    }
    .check-logo{
      width:60px;
      height:60px;
      background-size: contain;
      background-color: #EFEFEF;
      margin-right:5px;
      position:relative;
      display: flex;
      flex-direction: column-reverse;
      text-align: center;
      >.click-tip{
        color:white;
        background:rgba(0, 139, 139, 0.5);
        padding:2px;
        font-size:0.8em;
      }
    }
    .check-tip{
      font-size:0.9em;
      font-weight:400;
    }
  }

  @import '~styles/variable/global.scss';
  .net-price {
    color: #FE861F;
    margin:0;
    font-size:18px;
  }
  .shop-price{
    margin:0;
    font-size:0.9em;
    text-decoration: line-through;
    color:gray;
    text-align: right;
  }

  .shop-bottom{
    background:white;
    border-top: 1px solid #ececec;
    transition:transform 100ms;
    will-change: transform;
    .rmb{
      font-size: 1.5em;
      color: #FE8E33;
    }
    &.shop-bottom-hidden{
      transform:translate3d(0, 100%, 0);
    }
  }

  .shop-home-mt{
    .weui-actionsheet__cell > a{
      display:block;
      color:inherit;
    }
    .service-empty {
        padding: 20px;
    }
  }

  .bottom-left{
    flex:1;
  }
  .bottom-right {
    width: 100px;
    line-height: 47px;
    text-align: center;
    // background: rgb(78, 133, 251);
    color: white;
    &.disabled {
      background-color: #a3a3a3;
      &:active {
        background-color: #a3a3a3;
      }
    }
    // &:active{
    //   background-color: rgba(78, 133, 251, 0.8);
    // }
  }
</style>

<script>
import { fixRichHtmlImageSize, formatShopHours, parseAppointmentTime } from '@/utils';
import { AppStatus, ImageType } from '@/enums';
import { CarMaintainTypeData } from '@/common/data';
import { playPhotos, navigate } from '@/bridge';
import AppearDetector from '@/lib/appearDetector';
import { getImageURL } from '@/common/image';
import { Header, HeaderButton, Container, ContentView, Panel, Rater, Tabs } from '@/components';
// import Swiper from '@/components/Swiper/index';
import Picker from '@/components/Picker';
import { dialog, toast, loading } from '@/bus';
import { getShopData, getShopMTPackage, submitCarMaintainOrder } from '@/api';

function getInitialData() {
  return {
    AppStatus,
    ImageType,
    status: AppStatus.LOADING,
    pageActive: true,
    hideBuyButton: false,
    showShopContact: false,
    form: {
      date: null,
      car: null,
      pkg: null,
      oil: null,
    },
    // oil: null,
    page: {
      shop: null,
      package: null,
    },
  }
}

async function getShopMTPackageViewData(shopId, packageId, carId) {
  const shop = await getShopData(shopId);
  const params = {
    packageId,
    carId,
  };
  const packageData = await getShopMTPackage(packageId, carId);
  const mtType = packageData.mType;
  const mtTypeData = CarMaintainTypeData[mtType.name];
  return Promise.resolve({
    shop,
    package: {
      id: packageData.id,
      ...packageData.mType,
      ...mtTypeData,
      fee: packageData.hourPrice || 0,
      items: packageData.parts || [],
      schedules: packageData.dateList || [],
    },
  });
}

let isFirstIn = false;

export default {
  name: 'shop-home-mt',
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    ContentView,
    Slider: resolve => { import('@/components/Slider/index.vue').then(resolve) },
    Swiper,
    Rater,
    Panel,
    Picker,
  },
  data() {
    return getInitialData();
  },
  mounted() {
    this.initAppearDetector();
  },
  activated() {
    this.pageActive = true;
    this.setSliderAutoplay(true);
  },
  deactivated() {
    this.setSliderAutoplay(false);
    this.pageActive = false;
  },
  computed: {
    shopCommentsView() {
      return `/shop/${this.shop.id}/comment/mt`;
    },
    serviceHours() {
      const shop = this.shop;
      const startTime = formatShopHours(shop.serviceStartTime);
      const endTime = formatShopHours(shop.serviceEndTime);
      return `${startTime} ~ ${endTime}`;
    },
    shop() {
      return this.page.shop;
    },
    dateItems() {
      if (!this.package.schedules || !this.package.schedules.length) return null;
      return this.package.schedules.map(item => {
        return {
          label: parseAppointmentTime(item.date),
          value: item.date,
          disabled: !item.status
        }
      }).filter(item => !item.disabled);
    },
    oil() {
      const items = this.package.items;
      return items.filter(item => item.id == this.form.oil)[0] || items[0];
    },
    orderFee() {
      if (!this.oil) return 0;
      const result = (this.oil.price + this.package.fee).toFixed(2);
      return Number(result);
    },
    package() {
      return this.page.package;
    }
  },
  watch: {

  },
  methods: {
    go(url) {
      this.$router.push(url);
    },
    onLeave() {
      this.status = AppStatus.LOADING;
      // Object.assign(this.$data, getInitialData())
    },
    onResume() {
      // this.status = AppStatus.READY;
    },
    getPageData() {
      const { id, package: pid, car } = this.$route.params;
      getShopMTPackageViewData(id, pid, car).then(res => {
        const oil = res.package.items[0];
        this.form.pkg = pid;
        this.form.car = car;
        if (oil) {
          this.form.oil = oil.id;
        }
        // this.oil = oil;
        this.page = res;

        // this.form.date = [this.dateItems[0].value];
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    init() {
      if (!isFirstIn) {
        Object.assign(this.$data, getInitialData());
      }
      isFirstIn = false;
      this.getPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.getPageData();
    },
    goNavigate() {
      const { address, lat, lng, title } = this.shop;
      navigate({
        address,
        name: title,
        longitude: lng,
        latitude: lat,
        callback() {},
      });
    },
    setSliderAutoplay(flag) {
      this.$refs.slider && this.$refs.slider.setAutoplay(flag);
    },
    showContacts() {
      this.$router.push({
        name: 'mt/contacts',
        params: {
          list: [{
            name: '商家电话',
            value: this.shop.phone,
          }]
        }
      })
    },
    buy() {
      if (!this.dateItems.length) {
        toast().tip('抱歉，本店5日内预约已满');
        return;
      }
      // check form
      const params = { ...this.form };
      if (!params.date) {
        toast().tip('请选择预约日期');
        return;
      }
      params.date = params.date[0];
      loading(true, '正在提交...');
      submitCarMaintainOrder(params).then(res => {
        loading(false);
        const url = `/order/pay/${res.oid}`;
        this.go(url);
      }, err => {
        loading(false);
        err && dialog().alert(err, {
          title: '错误'
        });
      });
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, ImageType.MEDIUM),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos
      };
      playPhotos(option);
    },
    playShopPhotos(index) {
      this.playPhotos(this.shop.images, index);
    },
    initAppearDetector() {
      const detector = new AppearDetector({
        onAppear(el, cvm) {
          if (cvm) {
            cvm.$emit('appear');
          }
        }
      });
      this.detector = detector;
    },
    onScroll(top) {
      this.detector.lazyLoad();
      // this.updatePanelHeight();
    },
    addToAppearManager(e) {
      this.detector.addWatch(e.$el, e);
    },
  }
};
</script>
