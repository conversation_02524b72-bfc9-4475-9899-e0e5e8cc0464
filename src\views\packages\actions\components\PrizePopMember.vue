<template>
  <van-popup
    v-model="showPopup"
    overlay
    round
    :close-on-click-overlay="false"
    position="center"
    get-container="body"
    class="member-pop"
    @close="handleClose"
  >
    <div class="pop-title">
      <!-- <div></div> -->
      <img class="icon" src="../assets/images/member_icon.png" alt="" />
    </div>
    <div class="pop-content">
      <slot></slot>
      <template>
        <!-- <div class="receive-status">温馨提示</div> -->
        <div class="text">恭喜你！获得了</div>
        <div class="coupon-tip">
          {{ titleStr }}
        </div>
        <div class="pop-bottom-btn">
          <van-button class="" round type="danger" @click="handleConfirm"
            >去查看</van-button
          >
        </div>
      </template>
    </div>
    <van-icon
      class="close-icon"
      @click="emit('cancel')"
      name="close"
      size="40"
      color="#ffffff"
    />
  </van-popup>
</template>
<script>
import { formatDate } from '@/utils';
import { mixinAuthRouter } from '@/mixins';

import { Icon, Button, Popup } from 'vant';
export default {
  name: 'PrizePopMember',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    prize: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  mixins: [mixinAuthRouter],
  components: {
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Icon.name]: Icon,
  },
  data() {
    return {
      showPopup: this.show,
    };
  },
  computed: {
    titleStr() {
      return this.prize.prizeName;
    },
  },
  watch: {
    show(val) {
      this.showPopup = val;
    },
  },
  mounted() {},
  methods: {
    ...{ formatDate },
    toPage(url) {
      // this.handleClose()
      this.$_auth_push(url);
    },
    emit(event) {
      this.$emit(event);
    },
    handleConfirm(event) {
      this.$emit('confirm');
    },
    handleClose() {
      this.$emit('cancel');
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/variable/global.scss';
.member-pop ::v-deep {
  display: flex;
  flex-direction: column;
  // background: linear-gradient(0deg, #fef6f4 70%, #ffa6a6 100%);
  background: transparent;
  width: 250px;
  max-height: 80%;
  overflow-y: visible;
  padding-top: 38px;
  box-sizing: border-box;
  top: 45%;
  font-size: 0;
  text-align: center;
  .pop-title {
    box-sizing: border-box;
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    .text {
      font-size: 18px;
      color: #fef8db;
      margin-bottom: 20px;
    }
    .icon {
      display: block;
      width: 250px;
      height: 85px;
      // position: absolute;
      // left: 50%;
      // top: -74px;
      // transform: translate(-50%, 0);
    }
  }
  .pop-content {
    flex: 1;
    overflow: auto;
    padding: 15px 15px 22px;
    line-height: 1;
    background: #fff;
    border-radius: 0px 0px 19px 19px;
    margin-top: -1px;
    &::-webkit-scrollbar {
      width: 0 !important;
    }
    .text {
      font-weight: bold;
      font-size: 19px;
      color: #fd4925;
      line-height: 23px;
      margin-bottom: 10px;
    }
  }
  .pop-bottom-btn {
    margin-top: 12px;
    .van-button {
      font-size: 13px;
      line-height: 35px;
      height: 35px;
      padding: 0 46px;
      &:not(:first-child) {
        margin-left: 15px;
      }
    }
    .close-btn {
      background: transparent;
      border-color: $lh-2022-primary-color;
      color: $lh-2022-primary-color;
    }
    .van-button--danger {
      background: #fd4925;
    }
  }
  .van-popup--center.van-popup--round {
    border-radius: 10px;
  }
  .van-icon-cross {
    top: 19px;
    font-size: 12px;
    line-height: 1;
  }
  .close-icon {
    width: 40px;
    height: 40px;
    position: absolute;
    top: calc(100% + 15px);
    // bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    // margin: 0 auto;
  }
  .coupon-tip {
    font-size: 15px;
    color: #111111;
    line-height: 18px;
    text-align: center;
  }
}
</style>
