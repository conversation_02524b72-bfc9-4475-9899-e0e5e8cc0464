## 新增充电站

**接口地址**:`/api/chargingstations/add`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**:创建一个新的充电站

**请求示例**:

```javascript
{
	"acCount": 0,
	"address": "",
	"availableChargerCount": 0,
	"benefits": [],
	"businessHoursDesc": "",
	"businessHoursType": "",
	"chargerCount": 0,
	"chargingMethods": [],
	"city": "",
	"dcCount": 0,
	"delStatus": 0,
	"distance": 0,
	"district": "",
	"highwayMode": "",
	"id": 0,
	"images": "",
	"latitude": 0,
	"longitude": 0,
	"maxPower": 0,
	"maxVoltage": 0,
	"minPower": 0,
	"minVoltage": 0,
	"name": "",
	"operationType": "",
	"parkingFeeType": "",
	"parkingFeeTypeDesc": "",
	"parkingTypes": [],
	"pricePerKwh": 0,
	"province": "",
	"rating": 0,
	"services": [],
	"stationType": "",
	"status": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称                          | 参数说明                                                                             | in   | 是否必须 | 数据类型        | schema          |
| --------------------------------- | ------------------------------------------------------------------------------------ | ---- | -------- | --------------- | --------------- |
| station                           | 充电站实体类                                                                         | body | true     | ChargingStation | ChargingStation |
| &emsp;&emsp;acCount               | 交流充电桩数量                                                                       |      | false    | integer(int32)  |                 |
| &emsp;&emsp;address               | 地址                                                                                 |      | true     | string          |                 |
| &emsp;&emsp;availableChargerCount | 可用充电桩数量                                                                       |      | false    | integer(int32)  |                 |
| &emsp;&emsp;benefits              | 权益,可用值:TELD,PLUG_AND_CHARGE,V2G                                                 |      | false    | array           | string          |
| &emsp;&emsp;businessHoursDesc     | 具体营业时间描述                                                                     |      | false    | string          |                 |
| &emsp;&emsp;businessHoursType     | 营业时间类型,可用值:TWENTY_FOUR_HOURS,OPEN,UNKNOWN                                   |      | false    | string          |                 |
| &emsp;&emsp;chargerCount          | 充电桩数量                                                                           |      | false    | integer(int32)  |                 |
| &emsp;&emsp;chargingMethods       | 充电方式,可用值:DC_FAST,DC_SLOW,SUPER_FAST,AC_FAST,AC_SLOW                           |      | false    | array           | string          |
| &emsp;&emsp;city                  | 城市                                                                                 |      | false    | string          |                 |
| &emsp;&emsp;dcCount               | 直流充电桩数量                                                                       |      | false    | integer(int32)  |                 |
| &emsp;&emsp;delStatus             | 删除状态（0-正常，-1-已删除）,可用值:0,-1                                            |      | false    | integer(int32)  |                 |
| &emsp;&emsp;distance              | 距离(米) - 非持久化字段，查询时计算                                                  |      | false    | number(double)  |                 |
| &emsp;&emsp;district              | 区/县                                                                                |      | false    | string          |                 |
| &emsp;&emsp;highwayMode           | 高速模式类型,可用值:ON_HIGHWAY,NEAR_HIGHWAY,NONE                                     |      | false    | string          |                 |
| &emsp;&emsp;id                    | 主键ID                                                                               |      | true     | integer(int64)  |                 |
| &emsp;&emsp;images                | 充电站图片URL列表（多张图片用逗号分隔）                                              |      | false    | string          |                 |
| &emsp;&emsp;latitude              | 纬度                                                                                 |      | true     | number          |                 |
| &emsp;&emsp;longitude             | 经度                                                                                 |      | true     | number          |                 |
| &emsp;&emsp;maxPower              | 最大功率（千瓦）                                                                     |      | false    | integer(int32)  |                 |
| &emsp;&emsp;maxVoltage            | 最大电压（伏特）                                                                     |      | false    | integer(int32)  |                 |
| &emsp;&emsp;minPower              | 最小功率（千瓦）                                                                     |      | false    | integer(int32)  |                 |
| &emsp;&emsp;minVoltage            | 最小电压（伏特）                                                                     |      | false    | integer(int32)  |                 |
| &emsp;&emsp;name                  | 充电站名称                                                                           |      | true     | string          |                 |
| &emsp;&emsp;operationType         | 运营类型,可用值:SELF_OPERATED,NON_SELF_OPERATED,INTERCONNECTED,PRIVATE,COOPERATION   |      | false    | string          |                 |
| &emsp;&emsp;parkingFeeType        | 停车费类型,可用值:TIME_LIMITED_FREE,PAID,FREE                                        |      | false    | string          |                 |
| &emsp;&emsp;parkingFeeTypeDesc    | 停车费类型详情描述                                                                   |      | false    | string          |                 |
| &emsp;&emsp;parkingTypes          | 停车场类型,可用值:TRUCK,BUS,HEAVY_TRUCK,GROUND,UNDERGROUND                           |      | false    | array           | string          |
| &emsp;&emsp;pricePerKwh           | 充电单价(元/度)                                                                      |      | false    | number          |                 |
| &emsp;&emsp;province              | 省份                                                                                 |      | false    | string          |                 |
| &emsp;&emsp;rating                | 评分                                                                                 |      | false    | number          |                 |
| &emsp;&emsp;services              | 电站服务,可用值:LOUNGE,CONVENIENCE_STORE,CAR_WASH,LIGHTING,FREE_WIFI,SNACKS,RESTROOM |      | false    | array           | string          |
| &emsp;&emsp;stationType           | 电站类型,可用值:PUBLIC,PRIVATE                                                       |      | false    | string          |                 |
| &emsp;&emsp;status                | 电站状态,可用值:OPEN,CLOSED,DELETED                                                  |      | false    | string          |                 |

**响应状态**:

| 状态码 | 说明         | schema              |
| ------ | ------------ | ------------------- |
| 200    | OK           | Result«StationVO» |
| 201    | Created      |                     |
| 401    | Unauthorized |                     |
| 403    | Forbidden    |                     |
| 404    | Not Found    |                     |

**响应参数**:

| 参数名称                          | 参数说明                                | 类型           | schema         |
| --------------------------------- | --------------------------------------- | -------------- | -------------- |
| code                              |                                         | integer(int32) | integer(int32) |
| data                              |                                         | StationVO      | StationVO      |
| &emsp;&emsp;acCount               | 交流充电桩数量                          | integer(int32) |                |
| &emsp;&emsp;address               | 地址                                    | string         |                |
| &emsp;&emsp;availableChargerCount | 可用充电桩数量                          | integer(int32) |                |
| &emsp;&emsp;benefits              | 权益                                    | array          | string         |
| &emsp;&emsp;businessHoursDesc     | 具体营业时间描述                        | string         |                |
| &emsp;&emsp;businessHoursType     | 营业时间类型                            | string         |                |
| &emsp;&emsp;chargerCount          | 充电桩数量                              | integer(int32) |                |
| &emsp;&emsp;chargingMethods       | 充电方式                                | array          | string         |
| &emsp;&emsp;city                  | 城市                                    | string         |                |
| &emsp;&emsp;dcCount               | 直流充电桩数量                          | integer(int32) |                |
| &emsp;&emsp;delStatus             | 删除状态                                | integer(int32) |                |
| &emsp;&emsp;distance              | 距离（公里）                            | number(double) |                |
| &emsp;&emsp;district              | 区/县                                   | string         |                |
| &emsp;&emsp;highwayMode           | 高速模式                                | string         |                |
| &emsp;&emsp;id                    | 主键ID                                  | integer(int64) |                |
| &emsp;&emsp;images                | 充电站图片URL列表（多张图片用逗号分隔） | string         |                |
| &emsp;&emsp;latitude              | 纬度                                    | number         |                |
| &emsp;&emsp;longitude             | 经度                                    | number         |                |
| &emsp;&emsp;maxPower              | 最大功率（千瓦）                        | integer(int32) |                |
| &emsp;&emsp;maxVoltage            | 最大电压（伏特）                        | integer(int32) |                |
| &emsp;&emsp;minPower              | 最小功率（千瓦）                        | integer(int32) |                |
| &emsp;&emsp;minVoltage            | 最小电压（伏特）                        | integer(int32) |                |
| &emsp;&emsp;name                  | 充电站名称                              | string         |                |
| &emsp;&emsp;operationType         | 运营类型                                | string         |                |
| &emsp;&emsp;parkingFeeType        | 停车费类型                              | string         |                |
| &emsp;&emsp;parkingFeeTypeDesc    | 停车费类型详情描述                      | string         |                |
| &emsp;&emsp;parkingTypes          | 停车场类型                              | array          | string         |
| &emsp;&emsp;pricePerKwh           | 充电单价(元/度)                         | number         |                |
| &emsp;&emsp;province              | 省份                                    | string         |                |
| &emsp;&emsp;rating                | 评分                                    | number         |                |
| &emsp;&emsp;services              | 电站服务                                | array          | string         |
| &emsp;&emsp;stationType           | 电站类型                                | string         |                |
| &emsp;&emsp;status                | 电站状态                                | string         |                |
| msg                               |                                         | string         |                |
| success                           |                                         | boolean        |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"acCount": 0,
		"address": "",
		"availableChargerCount": 0,
		"benefits": [],
		"businessHoursDesc": "",
		"businessHoursType": "",
		"chargerCount": 0,
		"chargingMethods": [],
		"city": "",
		"dcCount": 0,
		"delStatus": 0,
		"distance": 0,
		"district": "",
		"highwayMode": "",
		"id": 0,
		"images": "",
		"latitude": 0,
		"longitude": 0,
		"maxPower": 0,
		"maxVoltage": 0,
		"minPower": 0,
		"minVoltage": 0,
		"name": "",
		"operationType": "",
		"parkingFeeType": "",
		"parkingFeeTypeDesc": "",
		"parkingTypes": [],
		"pricePerKwh": 0,
		"province": "",
		"rating": 0,
		"services": [],
		"stationType": "",
		"status": ""
	},
	"msg": "",
	"success": true
}
```
