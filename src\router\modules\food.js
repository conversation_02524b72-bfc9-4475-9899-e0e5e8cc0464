import { handleError } from '../error-handler';

const routers = [
  {
    path: '/food/district/:id',
    name: 'FoodDistrict',
    meta: {
      title: '美食商圈',
    },
    component: resolve => {
      import(/* webpackChunkName: "food-district" */ '@/views/packages/food/pages/FoodDistrict.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/food/shop/:id',
    name: 'FoodShop',
    meta: {
      title: '美食商家',
    },
    component: resolve => {
      import(/* webpackChunkName: "food-shop" */ '@/views/packages/food/pages/FoodShopInfo.vue').then(resolve).catch(handleError);
    },
  },
];

export default routers;
// export default BaseRouter;
