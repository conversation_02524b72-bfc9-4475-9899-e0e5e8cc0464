import APIModel from '@/api/APIModel';

const api = new APIModel({
  'traffic/info': '/Radio/traffic/highwaymessage/search?sid=HN0001', // GET 获取路况富文本
  'hightway/info': '/Radio/traffic/free/hightway/info', // 河南免费绕城高速
  'address/ocr': '/app/goods/address/ocr', // 地址识别
});

/**
 * 获取路况富文本
 */
export function getTrafficInfo(params) {
  const url = api.render('traffic/info');
  return api.doGet(url, params);
}

/**
 * 河南免费绕城高速
 */
export function getHightwayInfo() {
  const url = api.render('hightway/info');
  return api.doGet(url);
}

export function getAddressOcr(params) {
  const url = api.render('address/ocr');
  return api.doGet(url, params);
}
// /**
//  *
//  */
// export function postUgcComment(id, content) {
//   const url = api.render('comment/add');
//   const params = {
//     content,
//     topicId: id,
//   }
//   const payload = Object.assign({
//     // 'commentId': 0,
//     'content': content,
//     // 'id': 0,
//     'topicId': id
//   }, params);
//   return api.postJSON(url, payload);
// }

// /**
//  *
//  * @param {string} commentId 帖子评论id
//  */
// export function deleteUgcComment(commentId) {
//   const url = api.render('comment/delete');
//   return api.doPost(url, { commentId });
// }
