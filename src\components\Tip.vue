<template>
  <div :class="btnClass" v-if="show">
    <div class="tip-content">
      <slot></slot>
    </div>
    <div v-if="close" class="tip-close" @click.stop="hide"></div>
  </div>
</template>
<style lang="scss" scoped>
  .tip {
    display:block;
    overflow: hidden;
    display:flex;
    .tip-close{
      margin: 2px;
      &::after{
        font-size:18px;
        font-family:iconfont;
        display:inline-block;
      }
      &.tip-close::after{
        content:'\e602';
      }
    }
    .tip-content{
      padding:5px;
      flex:1;
    }
  }
  .tip-warn{
    background: #FDFFCE;
    color: #464742;
    border-bottom: 1px solid #eef1b3;
  }
</style>
<script>
  export default {
    name: 'tip',
    props: {
      type: {
        type: String,
        default: 'warn',
      },
      value: String,
      close: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        show: true,
      };
    },
    computed: {
      btnClass() {
        return `tip tip-${this.type}`;
      },
    },
    methods: {
      hide() {
        this.show = false;
      },
    },
  };
</script>
