<template>
  <container @ready="onReady" @leave="onLeave" @resume="onResume">
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="share" @click="share('show')"></x-button>
    </x-header>

    <content-view :status="status" @reload="reload">
      <template v-if="status === AppStatus.READY">
        <div class="online-pk-page">
          <!-- 背景图区域，整个页面除按钮外都使用背景图 -->
          <div class="bg-image" :style="bgImageStyle"></div>

          <!-- 按钮区域 -->
          <div class="start-btn" @click="handleStart"></div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { AppStatus } from '@/enums';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import {
  createRoom,
  getRoomsList,
  getActionDetail,
} from '@/views/live-battle/api';
import { Button, Toast } from 'vant';

export default {
  name: 'OnlinePK',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    [Toast.name]: Toast,
  },
  data() {
    let actionId = this.$route.query.id;
    let roomId = this.$route.query.roomId;
    let isOwner = this.$route.query.isOwner === '1';
    return {
      AppStatus,
      status: AppStatus.LOADING,
      pageTitle: '在线答题PK',
      actionId: actionId,
      roomId: roomId || '', // 15580ee004dc4fd88591264a15925c86
      uid: '',
      isHouseOwner: isOwner, // 是否是房主
      loading: false,
      actionDetail: {},
      bgImage: require('@/views/live-battle/assets/images/online_pk_bg.jpg'),
    };
  },
  computed: {
    bgImageStyle() {
      let bgImage = this.bgImage;
      if (this.actionDetail.backgroundImage) {
        bgImage = getImageURL(this.actionDetail.backgroundImage);
      }
      return {
        backgroundImage: `url(${bgImage})`,
      };
    },
  },
  methods: {
    ...mapActions('quiz', ['connect', 'disconnect']),
    onReady() {
      // 页面进入时初始化数据或其他操作
      this.init();
    },
    onLeave() {
      // 页面离开时的处理
    },
    onResume() {
      // 页面从后台切回时的处理
      this.init();
    },
    init() {
      this.getActionDetail();
    },
    createRoom() {
      const actionId = this.actionId;
      const uid = this.uid;
      return createRoom({
        actionId,
        uid,
      }).then(room => {
        this.isHouseOwner = true;
        this.roomId = room.id;
      });
    },
    getActionDetail() {
      if (!this.actionId) return;
      getActionDetail(this.actionId).then(res => {
        this.actionDetail = res.action;
        this.pageTitle = this.actionDetail.title;
        this.share();
        this.status = AppStatus.READY;
      });
    },
    reload() {
      // content-view 的刷新操作
      this.status = AppStatus.LOADING;
      this.init();
    },
    async handleStart() {
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      try {
        if (this.loading) return;
        this.loading = true;
        this.uid = this.$_auth_userInfo.uid || '';
        // 获取房间列表并过滤掉自己创建的房间
        const rooms = (await getRoomsList({ actionId: this.actionId })) || [];
        const availableRooms = rooms.filter(r => r.user1Id !== this.uid);

        // 路由参数
        const routeParams = {
          path: '/live/battle/room',
          query: {
            id: this.actionId,
            roomId: '',
          },
        };

        if (availableRooms.length > 0) {
          // 加入现有房间
          this.roomId = availableRooms[0].id;
          routeParams.query.roomId = this.roomId;
        } else {
          // 创建新房间
          const room = await this.createRoom();
          routeParams.query = {
            ...routeParams.query,
            roomId: this.roomId,
            isOwner: 1,
          };
        }

        this.loading = false;
        // 路由跳转
        await this.$router.push(routeParams);
      } catch (error) {
        console.error('初始化房间失败:', error);
        this.loading = false;
        Toast('初始化房间失败，请重试');
      }
    },
    // 分享
    share(action = 'config') {
      const title = this.actionDetail.title;
      const logo = getImageURL(this.actionDetail.shareImage);
      // const desc = this.actionDetail.description;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title || '交广领航在线答题PK，展示你的知识才华！',
        desc: title || '交广领航在线答题PK，展示你的知识才华！',
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.online-pk-page {
  width: 100%;
  height: 100%;
  position: relative;

  // 背景图全屏铺满
  .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/views/live-battle/assets/images/online_pk_bg.jpg')
      no-repeat center top;
    background-size: 100% auto;
    z-index: 1;
  }

  .start-btn {
    position: absolute;
    z-index: 2; // 确保按钮在背景图之上
    left: 50%;
    bottom: 160px;
    transform: translateX(-50%);
    width: 210px;
    height: 70px;
    background: url('@/views/live-battle/assets/images/online_pk_btn.png')
      no-repeat center center;
    background-size: 100% 100%;
    // 添加呼吸动画
    animation: breathing 1.5s ease-in-out infinite;
  }
  // 定义呼吸动画关键帧
  @keyframes breathing {
    0%,
    100% {
      transform: translateX(-50%) scale(1);
    }
    50% {
      transform: translateX(-50%) scale(1.08); // 中间时刻放大，可自行调整放大倍数
    }
  }
}
</style>
