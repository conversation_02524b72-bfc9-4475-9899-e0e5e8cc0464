<template>
  <div class="results-wrap">
    <div class="status-text">{{ statusText }}</div>
    <div class="players">
      <!-- 左侧选手：当前用户 -->
      <div class="animate player player-left">
        <div class="player-inner">
          <!-- 头像 -->
          <biz-image class="avatar" :src="leftPlayer.portrait" immediate>
          </biz-image>
          <p class="player-name">{{ leftPlayer.name }}</p>
          <!-- 分数 -->
          <p class="player-score">{{ myScore }}</p>
          <p class="player-time" v-if="myDuration != undefined">
            用时: {{ myDuration }}s
          </p>
        </div>
      </div>

      <!-- 右侧选手：对手 -->
      <div v-if="rightPlayer" class="animate player player-right">
        <div class="player-inner">
          <biz-image class="avatar" :src="rightPlayer.portrait" immediate>
          </biz-image>
          <p class="player-name">{{ rightPlayer.name }}</p>
          <!-- 分数 -->
          <p class="player-score">{{ opponentScore }}</p>
          <p class="player-time" v-if="opponentDuration != undefined">
            用时: {{ opponentDuration }}s
          </p>
        </div>
      </div>
    </div>
    <div class="status-error" v-if="errorTip">{{ errorTip }}</div>
    <div class="btns-wrap">
      <!-- 使用vant的Button组件 -->
      <van-button round type="default" @click="share('show')">
        分享战绩
      </van-button>
      <van-button round type="danger" @click="continuePk"> 继续PK </van-button>
    </div>
  </div>
</template>

<script>
import { mixinAuth } from '@/mixins/auth';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { mapState } from 'vuex';
import { Button } from 'vant';
export default {
  name: 'QuizResults',
  mixins: [mixinAuth, mixinShare],
  components: {
    [Button.name]: Button,
  },
  props: {
    // 头像
    // avatar: {
    //   type: String,
    //   default: '',
    // },
  },
  data() {
    return {
      errorTip: '',
    };
  },
  computed: {
    ...mapState('quiz', {
      quizResults: state => state.quiz.results,
      quizPlayers: state => state.quiz.players,
      quizUser: state => state.user,
    }),
    // 定义自己和对手的比赛结果
    myResult() {
      if (this.quizResults) {
        return this.quizUser.isHouseOwner
          ? this.quizResults.promoter
          : this.quizResults.opponent;
      }
      return null;
    },
    opponentResult() {
      if (this.quizResults) {
        return this.quizUser.isHouseOwner
          ? this.quizResults.opponent
          : this.quizResults.promoter;
      }
      return null;
    },
    myScore() {
      if (this.myResult) {
        return this.myResult.score;
      }
      return 0;
    },
    opponentScore() {
      if (this.opponentResult) {
        return this.opponentResult.score;
      }
      return 0;
    },
    myDuration() {
      if (this.myResult) {
        return this.myResult.duration;
      }
      return 0;
    },
    opponentDuration() {
      if (this.opponentResult) {
        return this.opponentResult.duration;
      }
      return 0;
    },
    leftPlayer() {
      let player = {
        portrait: this.$_auth_userInfo.portrait,
        name: this.$_auth_userInfo.name,
      };
      return player;
    },
    rightPlayer() {
      if (!this.quizPlayers.opponent) {
        return null;
      }
      return this.quizUser.isHouseOwner
        ? this.quizPlayers.opponent.userDetail
        : this.quizPlayers.promoter.userDetail;
    },
    statusText() {
      let str = '';
      if (!this.quizResults) {
        return '';
      }
      // // 判断胜负
      // const hasHigherScore = this.myScore > this.opponentScore;
      // const hasTieScore = this.myScore === this.opponentScore;
      // const hasFasterTime =
      //   this.opponentDuration == 0 || this.myDuration < this.opponentDuration; // 对手逃跑||或用时更短
      // const hasSameTime = this.myDuration === this.opponentDuration;

      // // 胜利条件：1.分数高 或 2.平分但用时少
      // const isWinner = hasHigherScore || (hasTieScore && hasFasterTime);
      // // 平局条件：1.分数相同 且 2.用时相同
      // const isTie = hasTieScore && hasSameTime;
      if (this.quizResults.winnerStatus === undefined) {
        this.errorTip = '连接已断开, 答题无法继续';
      } else {
        this.errorTip = '';
      }
      switch (this.quizResults.winnerStatus) {
        case 1:
          str = this.quizUser.isHouseOwner
            ? '恭喜您！比赛获胜！'
            : '你输了~再接再厉！';
          break;
        case 2:
          str = this.quizUser.isHouseOwner
            ? '你输了~再接再厉！'
            : '恭喜您！比赛获胜！';
          break;
        case 0:
          str = '平局！';
          break;
        default:
          str = ''; // 未知的比赛结果
          break;
      }
      // 根据胜负返回相应文本
      return str;
    },
  },
  methods: {
    // 分享战绩
    share() {
      this.$emit('share');
    },
    continuePk() {
      // 继续PK
      // 路由参数
      let actionId = this.$route.query.id;
      const routeParams = {
        path: '/live/battle/room',
        query: {
          actionId: actionId,
        },
      };
      this.$router.replace(routeParams);
    },
  },
};
</script>

<style lang="scss" scoped>
.results-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2005;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);

  .status-text {
    text-align: center;
    font-weight: 800;
    font-size: 28px;
    color: #ffffff;
  }
  .players {
    position: relative;
    height: 270px;
    width: 100%;
    margin: 60px 0;
  }
  // 选手区域
  .player {
    width: 246px;
    height: 164px; // 根据设计图调节
    background: url('~@/views/live-battle/assets/images/results_lf.png')
      no-repeat;
    background-size: 100% 100%;
    z-index: 2;
    line-height: 1;
    position: absolute;
    box-sizing: border-box;

    .player-inner {
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      text-align: center;
      transform: translateY(-24px);
    }
    .avatar {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      margin-bottom: 10px;
    }
    .player-name {
      font-weight: 500;
      font-size: 18px;
      color: #ffffff;
      margin-bottom: 10px;
      padding: 0 5px;
    }
    .player-score {
      font-weight: bold;
      font-size: 28px;
      color: #ffffff;
      margin-bottom: 10px;
    }
    .player-time {
      font-size: 12px;
      color: #ffffff;
    }
  }
  // 左侧选手
  .player-left {
    top: 0;
    left: 0;
    z-index: 6;
    padding-right: 83px;
    transform: translateX(-50%); // 初始位置在屏幕左侧外
    &.animate {
      animation: slide-in-left 1s forwards;
    }
  }
  // 右侧选手
  .player-right {
    bottom: 0;
    right: 0;
    padding-left: 83px;
    background-image: url('~@/views/live-battle/assets/images/results_rt.png');
    transform: translateX(50%); // 初始位置在屏幕右侧外
    &.animate {
      animation: slide-in-right 0.8s forwards;
    }
  }
  .btns-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    .van-button {
      width: 164px;
      margin: 0 6px;
      height: 52px;
      font-size: 23px;
    }
    .van-button--default {
      background: transparent;
      border-color: #ffffff;
      color: #ffffff;
    }
    .van-button--danger {
      background: #f146ad;
      border-color: #f146ad;
      color: #ffffff;
    }
  }
  .status-error {
    font-size: 16px;
    color: #ffffff;
    margin-bottom: 10px;
    text-align: center;
  }
}

/* 左侧进场动画 */
@keyframes slide-in-left {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0%);
  }
}

/* 右侧进场动画 */
@keyframes slide-in-right {
  0% {
    transform: translateX(50%);
  }
  100% {
    transform: translateX(0%);
  }
}
</style>
