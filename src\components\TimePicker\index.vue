<template>
  <div class="picker picker-time" @click="pickTime">{{value ? formatDate(value) : placeholder}}</div>
</template>
<style lang="scss">

</style>
<script>
  // import { datePicker } from 'weui.js';
  import { picker } from '@/lib/weui.js/picker/picker.js';
  import { formatDate } from '@/utils';
  import ENV from '@/common/env';

  const DataType = {
    TIMESTAMP: 'timestamp',
    DATE: 'date',
  }
  
  function isInRange(range, value) {
    try {
      const value2 = Number(value);
      const ok = range.some(item => {
        return !(value2 < item[0] || value2 > item[1]);
      })
      // console.log(range, value, ok);
      return ok;
    } catch(e) {
      return true;
    }
  }

  function parseRange(ranges) {
    return {
      hours: ranges.map(item => {
        return item.split('-').map(item2 => {
          const item3 = item2.trim().split(':');
          return Number(`1${item3[0]}`);
        });
      }),
      minutes: ranges.map(item => {
        return item.split('-').map(item2 => {
          const item3 = item2.trim().split(':');
          return Number(`1${item3[0]}${item3[1]}`);
        });
      })
    }
  }

  function padding(value, length) {
    const padLength = length - String(value).length;
    const padStr = Array.from(new Array(padLength)).map(item => 0).join('');
    return `${padStr}${value}`;
  }

  function formatTime(hour = '', minute = '') {
    const hour2 = (hour !== '') ? padding(hour, 2) : '';
    const minute2 = (minute !== '') ? padding(minute, 2) : '';
    return `1${hour2}${minute2}`;
  }

  function createLabels(step = 1, range) {
    const minuteStep = parseInt(step, 10) || 1;
    const ranges = parseRange(range);
    // console.log(range, ranges);
    const hours = Array.from(new Array(24)).map((item, i) => i).map(item => {
      const hourDisabled = !isInRange(ranges.hours, formatTime(item));
      // console.log(item, formatTime(item));
      return {
        label: `${item}点`,
        value: item,
        disabled: hourDisabled,
        children: Array.from(new Array(Math.floor(60/minuteStep))).map((item, i) => i).map(item2 => {
          const value = item2 * minuteStep;
          const hourDisabled = !isInRange(ranges.minutes, formatTime(item, value));
          return {
            label: `${value}分`,
            value: value,
            disabled: hourDisabled
          }
        }).filter(item2 => !item2.disabled),
      }
    }).filter(item => !item.disabled);
    return hours;
  }

  export default {
    name: 'datetime-picker',
    props: {
      value: {
        type: [Date, Number],
      },
      placeholder: {
        type: String,
        default() {
          return '请选择';
        }
      },
      selectableRange: {
        type: Array,
        default() {
          return [
            '00:00 - 23:59'
          ]
        }
      },
      step: {
        type: Number,
        default: 1,
      },
      options: {
        type: Object,
      },
      format: {
        type: String,
        default() {
          return 'yyyy-MM-DD'
        }
      }
    },
    watch: {
      visible(val, oldVal) {
        if (val) {
          document.body.classList.add('overlaying');
        } else {
          document.body.classList.remove('overlaying');
        }
      }
    },
    mounted() {},
    beforeDestroy() {
      try {
        if (this.picker) {
          this.picker.destroy();
        }
      } catch(e) {
        // console.error('picker.destroy', e);
      }
    },
    data() {
      const labels = createLabels(this.step, this.selectableRange);
      return {
        picker: null,
        labels: labels,
        visible: false,
      };
    },
    components: {
    },
    methods: {
      pickTime() {
        const that = this;
        const defaultDate = new Date(this.value || new Date());
        const defaultValue = [defaultDate.getHours(), defaultDate.getMinutes()]
        const options = Object.assign({
          defaultValue: defaultValue,
          onChange(result) {
            console.log(result);
          },
          onConfirm(result) {
            const [hours, minutes] =  result.map(item => item.value);
            const date = new Date();
            date.setHours(hours);
            date.setMinutes(minutes);
            that.setTime(date);
          },
          onHide() {
            console.log('date picker hide...');
            that.visible = false;
          },
          id: 'timePicker'
        }, this.options);
        this.picker = picker(this.labels, options);
        that.visible = true;
      },
      setTime(date) {
        // const result = this.type === DataType.TIMESTAMP ? date.getTime() : date;
        this.$emit('input', date.getTime());
      },
      formatDate(date, format = 'HH:mm') {
        const style = format || this.format;
        return formatDate(date, style);
      }
    },
  };
</script>
