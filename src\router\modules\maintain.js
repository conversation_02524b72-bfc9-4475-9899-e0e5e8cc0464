import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';

export default [
  {
    name: '保养首页',
    path: '/car/maintain',
    redirect: '/cm/shops',
    // redirect: '/mt/car/select',
  },
  {
    path: '/about/mt/:type',
    name: '保养详情',
    component: resolve => {
      import(/* webpackChunkName: "car-mt-about" */ '@/views/packages/maintain/CarMTAbout').then(resolve).catch(handleError);
    }
  },
  {
    path: '/mt/car/select/:car?',
    name: '选择爱车',
    component: resolve => {
      import(/* webpackChunkName: "car-mt-select" */ '@/views/packages/maintain/SelectCars').then(resolve).catch(handleError);
    }
  },
  {
    path: '/mt/account/car',
    name: '选择爱车-改',
    component: resolve => {
      import(/* webpackChunkName: "mycar" */ '@/views/packages/maintain/AccountMyCar.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/shop/:shop/mt/parts/:type/select',
    name: '选择机油1',
    component: resolve => {
      import(/* webpackChunkName: "mt-parts-select" */ '@/views/packages/maintain/SelectParts/index.vue').then(resolve).catch(handleError);
    }
  },
  {
    path: '/shop/:shop/mt/parts/select',
    name: '选择机油2',
    component: resolve => {
      import(/* webpackChunkName: "mt-parts-select" */ '@/views/packages/maintain/SelectParts/index.vue').then(resolve).catch(handleError);
    }
  },
  // {
  //   path: '/shop/:id/mt/:package/car/:car',
  //   name: '商家保养服务',
  //   component: resolve => {
  //     import(/* webpackChunkName: "shop-car-mt" */ '@/views/packages/maintain/ShopCarMT').then(resolve).catch(handleError)
  //   },
  //   children: [
  //     {
  //       name: 'mt/contacts',
  //       path: 'contacts',
  //       component: Contacts
  //     }
  //   ]
  // },
  // {
  //   path: '/shop/:id/mt/car/:car',
  //   name: '商家保养服务',
  //   component: resolve => {
  //     import(/* webpackChunkName: "shop-car-mt" */ '@/views/packages/maintain/ShopCarMT3').then(resolve).catch(handleError);
  //   },
  //   children: [
  //     {
  //       name: 'mt2/contacts',
  //       path: 'contacts',
  //       component: Contacts
  //     }
  //   ]
  // },
  {
    path: '/cm/shop/:id', // car care and maintenance
    name: '商家维修保养服务',
    component: resolve => {
      import(/* webpackChunkName: "shop-car-mt4" */ '@/views/packages/maintain/MaintainShopInfo.vue').then(resolve).catch(handleError);
    },
    children: [
      {
        name: 'mt4/contacts',
        path: 'contacts',
        component: Contacts
      }
    ]
  },
  // {
  //   path: '/shops/mt/:level/:mt/car/:car',
  //   name: 'shops-of-car-mt',
  //   component: resolve => {
  //     import(/* webpackChunkName: "mt-shops" */ '@/views/packages/maintain/MaintainShopList.vue').then(resolve).catch(handleError);
  //   },
  // },
  {
    path: '/shop/:shop/service/:service/car/:car',
    name: '保养下单预约',
    component: resolve => {
      import(/* webpackChunkName: "mt-shops" */ '@/views/packages/maintain/CarMTReserve.vue').then(resolve).catch(handleError);
    },
    meta: {
      keepAlive: true,
    },
  },
  // {
  //   path: '/shops/mt/car/:car',
  //   name: '保养门店报价',
  //   component: resolve => {
  //     import(/* webpackChunkName: "mt-shops" */ '@/views/packages/maintain/MaintainShopList.vue').then(resolve).catch(handleError)
  //   },
  //   meta: {
  //     keepAlive: true,
  //   },
  // },
  {
    path: '/cm/shops',
    name: '养车门店',
    component: resolve => {
      import(/* webpackChunkName: "mt-shops" */ '@/views/packages/maintain/MaintainIndex.vue').then(resolve).catch(handleError)
    },
  },
  {
    path: '/cm/search',
    name: '养车门店搜索',
    component: resolve => {
      import(/* webpackChunkName: "mt-shops" */ '@/views/packages/maintain/MaintainSearch.vue').then(resolve).catch(handleError)
    },
  },
]
