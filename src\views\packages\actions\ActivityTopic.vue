<template>
  <container
    class="activity-topic"
    :style="{background: infoData.bgcolor}"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="false"

  >
    <x-header :title="infoData.title" v-if="showShareHead">
      <x-button slot="left" type="back"></x-button>
      <div
        slot="right"
        class="share"
      >
        <i
          class="icon_jglh icon-fenxiang1"
          @click="share"
        ></i>
      </div>
    </x-header>
    <content-view
      ref="content"
      :status="status"
      :refreshAction="refreshAction"
      @refresh="refresh"
      @reload="reload" 
      @scroll="searchScroll"
    >
      <div ref="scrollBox">
        <div :class="showShareHead ? 'shareTop':'top'" :style="{background: `url(${imageURL(infoData.topSetting.image)}) no-repeat`, backgroundSize:'100% 100%'}" @click.stop="infoData.topSetting.url ? getRouter(infoData.topSetting.url):''">
          <van-sticky
            :offset-top="showShareHead? (isIosWithNotch && isInJglh ? '2.63rem' : '1.76rem'):(isIosWithNotch && isInJglh ? '1.06rem' : '0.46rem')"
            :z-index="999"
          >
            <div class="search" @click="goToSearch" :class="['search', showShareHead ? (isInJglh ? {'sticky-scroll-share-jglh': scrollTop > 110} : {'sticky-scroll-share': scrollTop > 130}) : (isIosWithNotch && isInJglh ? {'sticky-scroll-jglh': scrollTop > 170} : {'sticky-scroll': scrollTop > 160} )]" :style="{ '--bgColor':infoData.bgcolor }">
              <van-icon name="search" />
              商品搜索
            </div>
          </van-sticky>
        </div>
        <div class="activity-zone">
          <div class="image-row" @click="infoData.middleSetting.top.url ? getRouter(infoData.middleSetting.top.url):''" v-if="infoData.middleSetting.top.image">
            <img :src="imageURL(infoData.middleSetting.top.image)" />
          </div>
          <div class="image-row" @click="infoData.middleSetting.middle.url? getRouter(infoData.middleSetting.middle.url):''" v-if="infoData.middleSetting.middle.image">
            <img :src="imageURL(infoData.middleSetting.middle.image)" />
          </div>
          <div class="image-row" v-if="infoData.middleSetting.bottom.length">
            <img v-for="(item,index) in infoData.middleSetting.bottom" :key="index" :src="imageURL(item.image)" @click="item.url?getRouter(item.url):''"/>
          </div>
        </div>

        <div class="shop-box">
          <!-- <van-sticky :offset-top="100" :z-index="999">
            <div class="btn-more">更多<i class="active-below"></i></div>
          </van-sticky> -->
          <van-tabs
            v-model="active"
            ref="tabRef"
            sticky
            swipeable
            :class="showShareHead ? (isIosWithNotch && isInJglh ? 'ios-tab-box' : 'tab-box') : (isIosWithNotch && isInJglh ? 'ios-tab-box' : 'tab-box')"
            :style="{ '--bgColor':infoData.bgcolor }"
            title-inactive-color="#333"
            title-active-color="#FD4925"
            :offset-top="showShareHead ? (isIosWithNotch && isInJglh ? '3.83rem' : '2.73rem') : (isIosWithNotch && isInJglh ? '2.13rem':'1.53rem')"
          >
            <van-tab
              v-for="tabItem in tabsList"
              :title="tabItem.name"
              :key="tabItem.id"
            >
              <ActivityShopList :id="tabItem.id" :type="tabItem.type" :recommendStatus="tabItem.value" :childInfoData="infoData"></ActivityShopList>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </content-view>
  </container>
</template>

<script>
import { isInJglh, isInWeixin, isInWeApp, isIOS } from '@/common/env';
import { getGoodsCategories, geTopicDetail } from '@pkg/mall/api';
import { AppStatus } from '@/enums';
import { dialog, toast, loading } from '@/bus';
import { Icon, Sticky, Tab, Tabs, Toast } from 'vant';
import ActivityShopList from './components/ActivityShopList.vue';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getAppURL } from '@/utils';
import { getImageURL } from '@/common/image';
function hasNotch() {
  const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

  if (iOS && window.screen.height >= 812) {
    // iPhone X及更高型号的屏幕高度大于等于812像素
    return true;
  }

  return false;
}

// 调用函数判断是否有刘海
const isIosWithNotch = hasNotch();

export default {
  name: 'ActivityTopic',
  mixins: [mixinAuthRouter, mixinShare],
  
  data() {
    return {
      AppStatus,
      isInWeixin,
      isInWeApp,
      isInJglh,
      isIosWithNotch: isIosWithNotch,
      status: AppStatus.LOADING,
      keepAlive: true,
      refreshAction: 1,
      refGoodsSetting: {
        type: 'all',
        value: '',
        name: '',
      },
      active: null,
      tabsData: [],
      infoData: {
        topSetting: {},
        middleSetting: {
          top: {},
          middle: {},
          bottom: []
        }
      },
      topicId: this.$route.params.id,
      scrollTop: 0,
    };
  },
  components: {
    ActivityShopList,
    [Icon.name]: Icon,
    [Sticky.name]: Sticky,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Toast.name]: Toast
  },
  computed: {
    showShareHead () {
      // 判断分享显示隐藏，根据地址是否有type参数
      if (this.$route.query.type && !isInWeixin) {    
        return true
      } else {
        return false
      }
    },
    tabsList () {
      if (this.refGoodsSetting.type && this.refGoodsSetting.type != 'all') {
        return [...this.tabsData]
      } else { 
        // let tabsData = this.tabsData.map(item => {
        //   item.type = 'all'
        // })
        return [
          {
            name: '推荐',
            value: 1,
            // type: 'all',
          },
          {
            name: '全部',
            value: '',
            // type: 'all',
          },
          ...this.tabsData
        ];
      }
    }
  },
  mounted () {
    console.log(this.$refs.tabRef, 'fff')
  },
  methods: {
    imageURL(img) {
      return getImageURL(img);
    },
    searchScroll (top) {
      this.scrollTop = top;
      console.log(top, 'val')
    },
    // handleMiaoSha () {
    //   // Toast('活动暂未开始，敬请期待');
    //   this.$_router_pageTo('/flash/sale/center/102', {
    //     theme: 'light'
    //   });
    // },
    // 获取路由
    getRouter(url) {
      let router = url.split('#');
      if (router[1] == undefined || router[1] == '') {
        window.location.href = router[0];
      } else {
        return this.$_router_pageTo(router[1], {
          theme: 'light'
        })
      }
    },
    share() {
      const title = this.infoData.shareTitle
      const logo = getImageURL(this.infoData.shareImage);
      const desc = this.infoData.shareDesc;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      }
      this.$_share(shareInfo);
    },
    goToSearch() {
      this.$_router_pageTo('/mall/search', {
        theme: 'light'
      });
    },
    initPageData() {
      // const topicId = this.$route.params.id
      return Promise.all([
        getGoodsCategories(),
        geTopicDetail(this.topicId)
      ]).then(([res, info]) => {
        this.tabsData = [];
        if (info.refGoodsSetting && info.refGoodsSetting.type != 'all') {
          this.tabsData.push({
            name: info.refGoodsSetting.name,
            type: info.refGoodsSetting.type,
            id: info.refGoodsSetting.value,
          });
          this.$nextTick(() => {
            this.$refs.tabRef.$children[0].$el.hidden = true;
          })
        } else {
          this.tabsData = res;
        }
        this.infoData = info;
        if (info.refGoodsSetting) {
          this.refGoodsSetting = {
            name: info.refGoodsSetting.name,
            type: info.refGoodsSetting.type,
            id: info.refGoodsSetting.value
          } 
        } else {
          this.refGoodsSetting = {
            type: 'all',
            value: '',
            name: '',
          }
        }
        this.status = AppStatus.READY;
        this.initShareInfo();
      }).catch(err => {
        toast().tip(err);
        this.status = AppStatus.ERROR;
      });
    },
    initShareInfo() {
      // 设置邀请链接分享信息
      const wechatPath = `/activity/topic/${this.topicId}`;
      const jglhWechatURL = getAppURL(wechatPath, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share2' : '?jglh',
      });

      const shareInfo = {
        link: jglhWechatURL,
        title: this.infoData.shareTitle,
        desc: this.infoData.shareDesc,
        imgUrl: getImageURL(this.infoData.shareImage),
      };
      this.$_share_update(shareInfo);
    },
    init () {
      this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      // 重新加载页面
      this.init();
    },
    // 下拉刷新
    refresh() {
      this.initPageData().then(res => {
        this.$refs.tabRef.children[this.active].$children[0].onRefresh()
        this.refreshAction = Date.now();
      }).catch(e => {
        toast().tip('刷新失败');
        this.refreshAction = Date.now();
      });
    },
    onResume() {
      // 页面重新激活时在重新获取数据
      this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    }
  }
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.b-box {
  width: 100%;
  height: 100px;
  background: var(--bgColor);
}
::v-deep .scroller-inner{
  transform:none!important
}
.share {
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 46px;
  .icon_jglh {
    font-size: 24px;
  }
}
.content-wrapper{
  margin-top: 0!important;
}
.container {
  // background: #ff7a7a;
}
.container_box {
  position: relative;
  box-sizing: border-box;
}
.top {
  // background: url(./assets/images/activity-bg.png) no-repeat;
  // background-size: 100% 100%;
  height: 220px;
  padding: 10px 15px 0 15px;
  .search {
    height: 30px;
    background: #ffffff;
    border: 1px solid #ffffff;
    opacity: 0.7;
    border-radius: 15px;
    font-size: 14px;
    color: #999999;
    line-height: 30px;
    padding-left: 8px;
  }
  
}
.shareTop{
  // margin-top: 1.2rem;
  height: 220px;
  padding: 10px 15px 0 15px;
  .search {
    height: 30px;
    background: #ffffff;
    border: 1px solid #ffffff;
    opacity: 0.7;
    border-radius: 15px;
    font-size: 14px;
    color: #999999;
    line-height: 30px;
    padding-left: 8px;
  }
}
.sticky-scroll{
  opacity: 1!important;
  &::after{
    content: '';
    display: block;
    width: 100%;
    height: 70px;
    background: var(--bgColor);
    z-index: -1;
    position: fixed;
    left: 0;
    top:0
  }
}
.sticky-scroll-share{
  opacity: 1!important;
  &::after{
    content: '';
    display: block;
    width: 100%;
    height: 110px;
    background: var(--bgColor);
    z-index: -1;
    position: fixed;
    left: 0;
    top:0
  }
}
.sticky-scroll-jglh{
  opacity: 1!important;
  &::after{
    content: '';
    display: block;
    width: 100%;
    height: 80px;
    background: var(--bgColor);
    z-index: -1;
    position: fixed;
    left: 0;
    top:0
  }
}
.sticky-scroll-share-jglh{
  opacity: 1!important;
  &::after{
    content: '';
    display: block;
    width: 100%;
    height: 140px;
    background: var(--bgColor);
    z-index: -1;
    position: fixed;
    left: 0;
    top:0
  }
}
::v-deep .top .van-sticky--fixed {
  .search{
    margin: 0 15px;
  }
}
::v-deep .shareTop .van-sticky--fixed {
  .search{
    margin: 0 15px;
    // margin-top:1.2rem;
  }
}
::v-deep .tab-box .van-sticky--fixed{
  margin: 0 15px;
  .van-tabs__wrap::after {
    position: absolute;
    top: -57.5px;
    content: '';
    display: block;
    width: 100%;
    height: 110px;
    background: var(--bgColor);
    z-index: -1;
  }
}

::v-deep .ios-tab-box .van-sticky--fixed {
  margin: 0 15px;
  .van-tabs__wrap::after {
    position: absolute;
    top: -84.5px;
    content: '';
    display: block;
    width: 100%;
    height: 140px;
    background: var(--bgColor);
    z-index: -1;
  }
}

.shop-box {
  margin-top: 15px;
  padding: 0 15px;
  .btn-more {
    position: absolute;
    font-size: 14px;
    color: #333;
    line-height: 44px;
    width: 64px;
    height: 44px;
    background: #ffffff;
    border-radius: 0px 10px 10px 0px;
    text-align: center;
    z-index: 2;
    right: 15px;
    .active-below {
      background: url(./assets/images/active-below.png) no-repeat;
      background-size: 100%;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-left: 6px;
    }
  }
}
::v-deep .tab-box,.ios-tab-box {
  padding-bottom: 5vh;
}
::v-deep .van-tabs__wrap {
  border-radius: 10px !important;
  .van-tabs__nav {
    border-radius: 10px !important;
    padding-bottom: 0;
  }
}
::v-deep .van-tabs__line {
  display: none;
}
::v-deep .van-tab--active {
  font-size: 18px;
  &::before {
    content: '';
    display: block;
    background: url(./assets/images/active-icon.png) no-repeat;
    background-size: 100%;
    width: 8px;
    position: absolute;
    bottom: 5px;
    height: 3px;
  }
}
.activity-zone {
  display: flex;
  flex-direction: column;
  padding: 0 15px;
  .image-row {
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-bottom: 10px;
    &:first-child {
      margin-top: 20px;
    }
    img {
      flex: 1;
      width: 100%;
      height: 100%;
    }
    &:last-child {
      img {
        flex: 1;
        width: 40%;
        height: 100%;
        &:first-child {
          margin-right: 7.5px;
        }
        &:last-child {
          margin-left: 7.5px;
        }
      }
    }
  }
}
</style>
