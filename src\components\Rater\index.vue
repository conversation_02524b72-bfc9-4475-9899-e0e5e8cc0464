<template>
  <div class="vux-rater" :class="[`rater-theme-${theme}`]">
    <div class="rater-inner">
      <a class="vux-rater-box" v-for="i in max" @click="handleClick(i)" :class="getStarClass(i)"></a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'rater',
  mounted() {
    this.updateStyle();
  },
  props: {
    max: {
      type: Number,
      default: 5
    },
    value: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    star: {
      type: String,
      default: '★'
    },
    activeColor: {
      type: String,
      default: '#fc6'
    },
    theme: {
      type: String,
      default: 'default'
    },
    margin: {
      type: Number,
      default: 2
    },
    fontSize: {
      type: Number,
      default: 18
    }
  },
  computed: {
    sliceValue() {
      const val = this.value.toString().split('.');
      return val.length === 1 ? [val[0], 0] : val;
    },
    cutIndex() {
      return this.sliceValue[0] * 1;
    },
    cutPercent() {
      return this.sliceValue[1] * 10;
    }
  },
  methods: {
    handleClick(i) {
      if (!this.disabled) {
        this.selectValue = i;
        this.updateStyle();
      }
    },
    getStarClass(i) {
      return {
        'full-active': this.selectValue >= i,
        'half-active': this.selectValue < i && this.selectValue + 0.5 >= i
      };
    },
    getStarStyle(i) {
      return {
        color: this.colors && this.colors[i] ? this.colors[i] : '#ccc',
        marginRight: this.margin + 'px',
        fontSize: this.fontSize + 'px',
        width: this.fontSize + 'px',
        height: this.fontSize + 'px',
        lineHeight: this.fontSize + 'px'
      };
    },
    updateStyle() {
      for (let j = 1; j <= this.max; j++) {
        if (j <= this.selectValue) {
          this.$set(this.colors, j, this.activeColor);
        } else {
          this.$set(this.colors, j, '#ccc');
        }
      }
    }
  },
  data() {
    return {
      colors: [],
      // cutIndex: -1,
      // cutPercent: 0,
      selectValue: this.value,
    };
  },
  watch: {
    value(val) {
      this.selectValue = val;
      this.updateStyle();
    },
    selectValue(val, oldVal) {
      this.updateStyle();
      this.$emit('input', val);
    }
  }
};
</script>

<style lang="scss">
.vux-rater {
  text-align: left;
  display: inline-block;
  vertical-align: top;
  overflow:hidden;
}
.rater-inner{
  display:flex;
  align-items:center;
}
.vux-rater a {
  cursor: pointer;
}
.vux-rater a:last-child {
  margin-right: 0px!important;
}
.vux-rater a:hover {
  color: #ffdd99;
}
.vux-rater a.is-disabled {
  color: #ccc !important;
  cursor: not-allowed;
}
.vux-rater-box {
  position: relative;
}

$rater-size: 18px;
.vux-rater-box {
  width:$rater-size;
  height:$rater-size;
  line-height: $rater-size;
  margin-right:3px;
  position: relative;
  display: inline-block;
  color:rgba(0,0,0,0);
  background: url(./images/star-empty.png) center center no-repeat transparent;
  background-size:$rater-size - 1px;
  &.full-active {
    background-image:url(./images/star-active.png);
  }
  &.half-active {
    background-image:url(./images/half-active.png);
  }
  .rater-theme-red & {
    background-image:url(./images/theme-red/star-empty.png);
    &.full-active {
      background-image:url(./images/theme-red/star-active.png);
    }
    &.half-active {
      background-image:url(./images/theme-red/half-active.png);
    }
  }
}

.vux-rater-outer {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-block;
  overflow: hidden;
}
</style>
