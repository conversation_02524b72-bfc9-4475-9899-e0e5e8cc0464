<template>
  <div class="picker picker-time" :class="{'picker-empty': !this.value.length }" @click="pick">{{displayText}}</div>
</template>

<style lang="scss">
  /* weuijs动态插入的html没有vue id，故不加scoped  */
</style>
<script>
  import { picker } from '@/lib/weui.js/picker/picker.js';
  import Area from './area.json';

  function createLabels(filter) {
    return Area.data.map(item => {
      return {
        label: item.n,
        value: item.n,
        disabled: !filter(item, 1),
        children: item.s.length ? item.s.map(item2 => {
          return {
            label: item2.n,
            value: item2.n,
            disabled: !filter(item2, 2),
            children: (item2.s && item2.s.length) ? item2.s.map(item3 => {
              return {
                label: item3.n,
                value: item3.n,
                disabled: !filter(item3, 3),
              }
            }).filter(item => !item.disabled) : []
          }
        }).filter(item => !item.disabled) : []
      }
    }).filter(item => !item.disabled)
  }

  function formatName(value) {
    return value.filter(item => !!item).join(', ')
  }

  export default {
    name: 'AddressPicker',
    props: {
      value: {
        type: Array,
        default() {
          console.log('default:', this.value);
          return ['河南省', '郑州市', '金水区'];
        },
      },
      placeholder: {
        type: String,
        default() {
          return '请选择';
        }
      },
      options: {
        type: Object,
        default() {
          return {}
        }
      },
    },
    beforeDestroy() {
      try {
        this.visible = false;
        if (this.picker) {
          this.picker.destroy();
        }
      } catch(e) {
        // console.error('picker.destroy', e);
      }
    },
    data() {
      const labels = createLabels(this.options.filter || (e => true));
      return {
        picker: null,
        labels: labels,
        visible: false,
      };
    },
    computed: {
      displayText() {
        return this.value.length ? (formatName(this.value) || this.placeholder) : (this.placeholder || '请选择');
      }
    },
    watch: {
      visible(val, oldVal) {
        if (val) {
          document.body.classList.add('overlaying');
        } else {
          document.body.classList.remove('overlaying');
        }
      }
    },
    components: {
    },
    methods: {
      pick() {
        const that = this;
        const defaultValue = this.value.length ? this.value : ['河南省', '郑州市', '金水区'];
        const options = Object.assign({
          defaultValue: defaultValue,
          depth: 3,
          onChange(result) {
            console.log(result);
          },
          onConfirm(result) {
            const result2 =  result.map(item => item.value);
            that.setValue(result2);
          },
          onHide() {
            console.log('address picker hide...');
            that.visible = false;
          },
          container: 'body',
          id: `addressPicker_${Date.now()}`
        }, this.options);
        this.picker = picker(this.labels, options);
        this.visible = true;
      },
      setValue(value) {
        this.$emit('input', value);
      },
    },
  };
</script>
