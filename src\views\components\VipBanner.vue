<template>
  <c-picture
    v-if="vip"
    class="vip-banner"
    :src="vipBanner"
    @click="$emit('click')"
  >
  </c-picture>
  <c-picture
    v-else
    @click="$emit('click')"
    class="vip-banner"
    :src="unVipBanner"
  >
  </c-picture>
</template>

<style lang="scss" scoped>
.vip-banner {
  width: 100%;
  height: 68px;
  margin: 5px auto 0;
}
</style>

<script>
export default {
  name: 'VipBanner',
  props: {
    vip: {
      type: Boolean,
      default: false,
    },
    banners: {
      type: Array,
      default() {
        return [
          require('@pkg/vehicle-business/assets/images/vip0-banner.jpg'),
          require('@pkg/vehicle-business/assets/images/vip1-banner.jpg'),
        ];
      },
    },
  },
  computed: {
    vipBanner() {
      return this.banners[1];
    },
    unVipBanner() {
      return this.banners[0];
    },
  },
}
</script>
