import Picture from './Picture/index.vue';
import BizImage from './Picture/BizImage.vue';
import { <PERSON><PERSON>, HeaderButton, Container, ContentView, Panel, Rater, Tabs, Loading, PageLoader, ListLoader, ListPlaceholder } from '@/components';
import Page from '@/components/Page/Page.vue';
import PageContent from '@/components/Page/PageContent.vue';
import { BizImageUpload } from '@/components/biz';

const Plugin = {};

Plugin.install = function(Vue, options) {
  Vue.component('c-picture', Picture);
  Vue.component('biz-image', BizImage);
  Vue.component('x-header', Header);
  Vue.component('x-button', HeaderButton);
  Vue.component('container', Container);
  Vue.component('content-view', ContentView);
  Vue.component('rater', Rater);
  Vue.component('tabs', Tabs);
  Vue.component('panel', Panel);
  Vue.component('page-loader', PageLoader);
  Vue.component('loading', Loading);
  Vue.component('list-loader', ListLoader);
  Vue.component('list-placeholder', ListPlaceholder);

  Vue.component('page', Page);
  Vue.component('page-content', PageContent);
  Vue.component('page-view', ContentView);

  Vue.component('biz-image-upload', BizImageUpload);
}

export default Plugin;
