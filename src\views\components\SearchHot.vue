<template>
  <div v-if="hotWords.length" class="search-block">
    <div class="block-title">
      <h2>热门搜索</h2>
    </div>
    <div class="hot-wrap">
      <div class="hot-item" v-for="(item, index) in hotWords" :key="index" @click="hotItemClick(item)">
        <p>{{ item.name }}</p>
        <van-tag v-if="item.topic == '最新'" round color="#0DD7B3" text-color="#ffffff">新</van-tag>
        <van-tag v-if="item.topic == '推荐'" round color="#FD4925" text-color="#ffffff">荐</van-tag>
        <van-tag v-if="item.topic == '热门'" round color="#F5222D" text-color="#ffffff">热</van-tag>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, formatMoney, formatPrice } from '@/utils';
import { setGoodsSearchKeyword, getGoodsSearchKeyword } from '@/store/storage';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getHotKeywordsList } from '@pkg/mall/api';

import { Icon, Tag, Search } from 'vant';

export default {
  name: 'SearchHot',
  mixins: [mixinAuthRouter],
  props: {
    refresh: {
      type: Number
    },
    category: {
      type: String // 'mall', 'car',
    },
  },
  components: {
    [Tag.name]: Tag,
    [Icon.name]: Icon,
  },
  data() {
    return {
      hotWords: [],
      moreStatus: false,
    };
  },
  computed: {
    records() {
      return this.hotWords;
    },
    filterParams() {
      return {
        category: this.category,
        page: 1,
        pageSize: 10,
      };
    },
  },
  watch: {
    refresh: function (newVal) {
      this.init();
    }
  },
  created() {
    // setGoodsSearchKeyword(['月饼礼盒', '月饼礼盒', '月饼礼盒', '月饼礼盒', '月饼礼盒', '月饼礼盒', '月饼礼盒', '月饼礼盒', '月饼礼盒'])
    // this.hotWords = getGoodsSearchKeyword();
    this.init();
  },
  mounted() {},
  methods: {
    // 切换卖点展开状态
    init() {
      if (!this.category) {
        this.hotWords = []
        return
      }
      getHotKeywordsList(this.filterParams).then(res => {
        this.hotWords = res.list || []
      }, err => {
        this.hotWords = []
        console.error(err);
      });
    },
    hotItemClick(item) {
      if (item.url) {
        this.$_router_pageTo(item.url, {
          theme: 'light'
        });
        return
      }
      this.$emit('search', item.name)
    }
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.search-block {
  padding: 0 15px;
}
.block-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  line-height: 1;
  > h2 {
    font-size: 17px;
    font-weight: bold;
    color: #111111;
  }
  .title-right {
    display: inline-flex;
    align-items: center;
    span {
      margin-left: 6px;
      font-size: 12px;
      color: #999999;
    }
  }
}
.hot-wrap ::v-deep {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  line-height: 1;
  .hot-item {
    width: 48%;
    box-sizing: border-box;
    text-align: left;
    margin: 10px 0;
    font-size: 15px;
    color: #333333;
    display: inline-flex;
    align-items: center;
    >p{
      display: inline-block;
      max-width: 84%;
      // max-width: calc(84% - 5px);
      @include singleline-ov();
    }
  }
  .van-tag {
    margin-left: 5px;
  }
  .van-tag--round {
    border-radius: 5px;
  }
}
</style>
