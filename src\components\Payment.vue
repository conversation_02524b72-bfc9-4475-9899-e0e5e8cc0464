<template>
    <div class="payment-container" v-show="display">
      <div class="payment-content">
        <div class="payment-head">
          <h4>输入密码</h4>
          <span class="payment-close" @click="close">&times;</span>
        </div>
        <div class="payment">
            <ul class="input-grid flex-row">
              <li v-for="item in passwordChars" :class="{'input-grid-checked' : item}"><i></i></li>
            </ul>
        </div>
        <div class="payment-keyboard">
          <ul class="keyboard-buttons">
            <li v-for="item in buttons" @click="handleClick(item)">{{item}}</li>
          </ul>
        </div>
      </div>
    </div>
</template>
<style lang="scss" scoped>
  .input-grid {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    list-style: none;
    height: 100%;
    border: 1px solid #c3c3c3;
    background: white;
    border-right: 0;
  }

  .input-grid>li {
    flex: 1;
    border-right: 1px solid #d6d6d6;
    display:flex;
    justify-content: center;
    align-items: center;
    &.input-grid-checked>i{
      display:block;
      width:10px;
      height:10px;
      border-radius: 50%;
      background:black;
    }
  }

  .payment-container {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.23);
      z-index: 10;
  }

  .keyboard-buttons {
    display: flex;
    list-style:none;
    background:white;
    flex-wrap:wrap;
    box-sizing:border-box;
    margin: 10px 0 0;
    font-size:24px;
    border-top: 1px solid #dadada;
}

.keyboard-buttons>li {
    width:33.33%;
    text-align:center;
    padding:10px 0;
    border-right:1px solid #d8d8d8;
    border-bottom:1px solid #d8d8d8;
    box-sizing:border-box;
    &:nth-child(3n){
      border-right:0;
    }
    &:active{
      background:#ececec;
    }
}
.payment {
  height: 40px;
  position: relative;
  margin: 20px 10px;
}
.payment-head{
  text-align: center;
  border-bottom: 1px solid #e2e2e2;
  position: relative;
  margin: 5px 0;
  padding: 6px;
}
.payment-close{
  position: absolute;
  left: 10px;
  top: 50%;
  font-size: 26px;
  line-height: 1;
  transform: translateY(-50%);
}
.payment-content{
  background:white;
  position:absolute;
  width:100%;
  bottom:0;
  left:0;
}
</style>
<script>
  import { mapActions } from 'vuex';
  import { toast, dialog, loading } from '@/bus';

  export default {
    props: {
      counter: {
        type: Number,
        default: 0,
      },
      amount: {
        type: Number,
        default: 0
      },
      success: {
        type: Function,
      },
      data: {
        type: Object
      }
    },
    data() {
      return {
        display: false,
        password: [],
        order: {
          oid: null,
          title: '',
          amount: 0
        }
      };
    },
    created() {
      const { oid, title, amount } = this.$route.params;
      this.order = { oid, title, amount };
    },
    computed: {
      passwordChars() {
        const chars = this.password;
        const pwdList = [0, 0, 0, 0, 0, 0];
        return pwdList.map((item, i) => /^\d$/.test(chars[i]));
      },
      buttons() {
        const buttons = [
          1, 2, 3, 4, 5, 6, 7, 8, 9, '', 0, 'x'
        ];
        return buttons;
      },
    },
    watch: {
      counter(val) {
        this.display = true;
      }
    },
    methods: {
      ...mapActions(['submitAccountPay']),
      handleClick(input) {
        const numberPattern = /^\d$/;
        const deletePattern = /^x$/;
        const password = this.password;
        if (deletePattern.test(input)) {
          this.password = this.password.slice(0, this.password.length - 1);
        } else if (numberPattern.test(input)) {
          this.password.push(input);
          if (this.password.length === 6) {
            this.pay();
          }
        }
      },
      close() {
        this.display = false;
      },
      clearInput() {
        this.password = [];
      },
      getPassword() {
        return this.password.join('');
      },
      go(url) {
        this.$router.push(url);
      },
      pay() {
        const that = this;
        loading(true, '正在支付...');
        this.submitAccountPay(this.oid, this.getPassword()).then(res => {
          loading(false);
          return res;
        }).then(res => {
          toast().success('支付成功！');
        }).catch(msg => {
          dialog().confirm(msg, {
            okText: '重试',
            ok() {
              that.clearInput();
            },
            cancelText: '取消支付',
            cancel() {
              that.clearInput();
            }
          });
        });
      },
    }
  };
</script>
