<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$filter-height: 35px;
$border-color: rgb(242, 242, 242);

.filter-box {
  height: 35px;
}
.filters {
  margin: 0;
  position: relative;
  z-index: 8;
  background: white;
  /*@include border-bottom($border-color, 'after');*/
  // position: fixed;
  width: 100%;
  z-index: 100;

  .filter-item {
    flex: 1;
    text-align: center;
    list-style: none;
    border-right: 1PX solid $border-color;
    border-bottom: 1PX solid $border-color;
    &:last-child {
      border-right: 0;
    }
  }
  .filter-list {
    background: white;
  }
  .filter-name {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    line-height: $filter-height;
    >span{
      white-space: nowrap;
      max-width: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    &::after {
      content: '';
      display: inline-block;
      width: 1em;
      height: 1em;
      vertical-align: middle;
      background: url(~@/assets/images/arrow.svg) center center no-repeat transparent;
      background-size: 10px;
      /*
      此处使用动画在flyme6系统上会导致filter-options不能显示出来
      will-change:transform;
      transition:transform 100ms;
      */
    }
  }
  .filter-options {
    position: absolute;
    overflow: hidden;
    /*transform: translateY(1PX);*/
    top: 100%;
    left: 0;
    width: 100%;
    display: none;
    background: white;
    .filter-options-inner {
      max-height: 240px + 30px;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
    }
    .filter-list-bottom {
      background: url(./filter-bottom.png) center center no-repeat white;
      background-size: 86px 22px;
      height: 30px;
      @include border-top(#f3f3f3);
      position: relative;
      bottom: 2px;
    }
  }

  .filter-option {
    padding: 8px;
    padding-left: 30px;
    @include border-top($border-color, 'after');
    text-align: left;
    position: relative;
    &.active {
      color: #2196f3;
      background: whitesmoke;
      &::before {
        content: "\e60f";
        font-family: iconfont;
        position: absolute;
        left: 5px;
      }
    }
  }
  .filter-option:last-child {
    border-bottom: 0;
  }
  .filter-item.active {
    .filter-name::after {
      transform: rotate(180deg);
    }
    .filter-options {
      display: block;
    }
  }
}

.overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 6;
  background: rgba(0, 0, 0, 0.2);
}
</style>
<template>
  <div class="filter-box" ref="filter">
    <ul class="filters flex-row">
      <li v-for="(option, key) in items" :key="key" class="filter-item" v-show="!option.hidden" :class="{'active': active == key}" @click="toggleSelect(key)">
        <div class="filter-name-box">
          <div class="filter-name">
            <span v-text="getValue(key).name"></span>
          </div>
        </div>
        <div class="filter-options" @click.stop="toggleSelect(key)">
          <!-- 外层使用第三方滚动类库如better-scroll时，原生滚动失效，解决方案阻止touchstart事件冒泡 -->
          <div class="filter-options-inner" @touchstart.stop="() => {}">
            <ul class="filter-list">
              <li v-for="(item, index) in option.list" :key="index" @click="setOption(key, item)" :class="{'active': getValue(key).value === item.value}" class="filter-option">{{item.name}}</li>
            </ul>
          </div>
          <div v-if="option.list.length > 6" class="filter-list-bottom"></div>
        </div>
      </li>
    </ul>
    <div class="overlay" @click="hideSelectOption" v-show="active"></div>
  </div>
</template>

<script>
/**
 TODO 菜单展开后要确保选中项在可见区域中
 *items2: {
    key1: {
      name: '选项1',
      hidden: false, // 菜单默认都显示，若指定hidden：false，则隐藏此菜单
      value: 0,
      list: [
        {
          name: '选项1',
          value: 1,
        }
      ]
    },
    key2: {
      name: '选项2',
      value: 0,
      list: [
        {
          name: '选项2',
          value: 1,
        }
      ]
    },
  },
 */
export default {
  props: {
    options: {
      type: Object,
    },
    value: {
      type: Object
    },
  },
  computed: {
    items() {
      return this.options;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.refreshOptions();
    })
  },
  watch: {
    active(val, oldVal) {
      this.$overlay(!!val);
    },
    value(val, oldVal) {
      // console.log('value change', val);
      this.refreshOptions();
    },
    options(val, oldVal) {
      // console.log('value change', val);
      this.refreshOptions();
    },
  },
  data() {
    return {
      active: null,
      filter: {},
    }
  },
  methods: {
    refreshOptions() {
      // console.log('refresh options:', this.options);
      this.filter = Object.keys(this.options).reduce((last, key, index) => {
        const item = this.options[key];
        // console.log(item);
        last[key] = item.list.filter(item2 => {
          // const match = item2.value === item.value || this.value[key] === item2.value;
          const match = String(this.value[key]) === String(item2.value);
          // console.log(item2.value, item.value, this.value[key], match);
          return match;
        })[0];
        return last;
      }, {});
    },
    setOption(key, item) {
      this.filter[key] = item;
      const data = {};
      Object.keys(this.filter).reduce((last, key) => {
        const optionItem = this.filter[key];
        last[key] = optionItem && optionItem.value;
        return last;
      }, data);
      this.$emit('input', data);
    },
    getValue(key) {
      const option = this.filter[key];
      return this.filter[key] || this.options[key];
    },
    hideSelectOption() {
      this.active = '';
    },
    toggleSelect(key) {
      this.active = this.active === key ? '' : key;
    },
  }
}
</script>
