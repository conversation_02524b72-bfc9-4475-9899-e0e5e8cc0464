import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/drive',
    name: '代驾服务',
    component: resolve => {
      import(/* webpackChunkName: "drive" */ '@/views/packages/external/index.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/jump',
    // redirect: '/mall/503',
    name: '代驾跳转页',
    component: resolve => {
      import(/* webpackChunkName: "drive" */ '@/views/packages/external/jump-web.vue').then(resolve).catch(handleError);
    },
  }
]
