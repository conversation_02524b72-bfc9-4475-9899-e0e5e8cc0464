<template>
  <container @ready="init">
    <x-header title="我的余额">
      <x-button  slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status" :refreshAction="refreshAction" @refresh="refresh" @reload="reload" @scroll-bottom="loadMore">
      <template v-if="status == AppStatus.READY">
        <div class="flex-row flex-center user">
          <c-picture :src="user.portrait" class="user-avatar">
            <span v-if="isVIP" class="vip"></span>
          </c-picture>
          <div class="user-info">
            <h3 class="user-name">{{user.name}}</h3>
            <div class="user-account">
              <span class="user-balance">
                余额：<b class="rmb" v-html="account.balance"></b>
              </span>
              <a href="javascript:;" @click="goCharge" class="weui-btn weui-btn_mini weui-btn_primary btn-charge">充值</a>
            </div>
          </div>

        </div>
        <panel title="余额明细">
          <div v-for="(item, index) in trades" :key="index" class="trade-item">
            <div class="trade-content-up flex-row">
              <div class="trade-name">
                  {{item.title}}
              </div>
              <span class="trade-amount fn-right" :class="[ item.bType == 0 ? 'trade-out':'trade-in']">{{item.bType?'+':'-'}}{{formatAmount(item.amount)}}</span>
            </div>
            <div class="trade-content-down">
              余额<span class="rmb trade-balance">{{item.curBalance}}</span>
              <span class="trade-time fn-right">{{formatDate(item.createTime, 'YYYY-MM-DD HH:mm')}}</span>
            </div>
          </div>
          <div v-if="!trades.length" class="trade-item trade-empty">
            暂无记录
          </div>
        </panel>
        <div :class="{'scroll-loading': true, 'scroll-loading-show':scroll.busy || scroll.end}" v-html="scroll.text"></div>
    </template>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
  .picture.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #e2e2e2;
    margin-right: 5px;
    position:relative;
    background-image:url(~@/assets/images/account/avatar.png);
    .vip{
      width:20px;
      height:20px;
      background:url(~@/assets/images/vip.png) center center no-repeat transparent;
      background-size:cover;
      position:absolute;
      bottom:0;
      right:0;
    }
  }

  .user {
    padding: 10px;
    background: white;
  }
  .user-name{
    font-weight:400;
  }
  .user-info {
    flex: 1;
    position:relative;
  }
  .user-level {
    margin-right: 10px;
    &.vip{
      color:#F1883E;
    }
  }
  .user-balance .rmb{
    color:#ED7C30;
  }
  .charge-box{
    display:flex;
    justify-content: center;
    align-items: center;
  }
  .btn-charge{
    float:right;
    background: #FE8720;
    border-color: #FE8720;
    border-radius: 2px;
    &:active {
      background-color: rgba(254, 135, 32, 0.8);
    }
  }
  .user-account{
    line-height:30px;
  }

  .trade-item {
    padding: 10px;
    border-bottom: 1px solid #efefef;
  }

  .trade-content-down {
    color: #a7a7a7;
    font-size: 13px;
  }

  .trade-item:last-child {
    border-bottom: 0;
  }

  .trade-content-up {
    margin-bottom: 2px;
  }
  .trade-name{
    flex: 1;
    height: 25px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .trade-balance {
    margin: 0;
  }
  .trade-empty{
    text-align:center;
    color:gray;
    padding:10px;
  }
  .trade-amount{
    color:#616161;
  }
  .trade-in{
    color:#FE9A44;
  }

  .scroll-loading{
    text-align:center;
    color:#bfbfbf;
    padding:5px;
    visibility: hidden;
    &.scroll-loading-show{
      visibility: visible;
    }
  }
</style>
<script>
import { formatDate } from '@/utils';
import { Header, HeaderButton, Container, ContentView, Panel } from '@/components';
import { OrderStatus, AppStatus } from '@/common/enums';
import { toast } from '@/bus';
import { getAccountViewData, getAccountTrades } from '@/api';
import { mixinAuthRouter } from '@/mixins';

export default {
  name: 'account',
  mixins: [mixinAuthRouter],
  data() {
    return {
      OrderStatus,
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 1,
      page: {
        user: null,
        account: null,
        trades: null,
      },
      scroll: {
        page: 1,
        end: false,
        busy: false,
        text: '正在加载...',
      },
    };
  },
  computed: {
    user() {
      return this.page.user;
    },
    trades() {
      return this.page.trades;
    },
    account() {
      return this.page.account;
    },
    isVIP() {
      return this.user.level > 0;
    },
    userLevel() {
      // if (!this.user) return '';
      const levels = {
        0: '普通会员',
        1: 'VIP会员',
      };
      return levels[this.user.level];
    }
  },
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    Panel,
    ContentView,
  },
  methods: {
    init() {
      return getAccountViewData().then(res => {
        this.page = res;
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    formatAmount(value) {
      return value.toFixed(2);
    },
    goCharge() {
      this.$_router_pageTo('/account/charge', {
        title: '账户充值',
        titleBar: true,
      })
    },
    refresh() {
      this.init().then(res => {
        this.refreshAction = Date.now();
        this.scroll.end = false;
        this.scroll.page = 1;
        this.scroll.text = '正在加载...';
      }).catch(e => {
        toast().tip('刷新失败');
        this.refreshAction = Date.now();
        console.error(e);
      });
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    loadMore() {
      if (this.scroll.end || this.scroll.busy) return;
      const params = { page: this.scroll.page + 1 };
      this.scroll.busy = true;
      getAccountTrades(params).then(res => {
        this.page.trades = this.page.trades.concat(res);
        this.scroll.busy = false;
        this.scroll.page++;
        if (!res.length || res.length < 10) {
          this.scroll.end = true;
          this.scroll.text = '没有更多了';
        }
      }, err => {
        this.scroll.busy = false;
        console.error(err);
      });
    },
    go(url) {
      this.$router.push(url);
    },
    formatDate(t, style) {
      return formatDate(t, style);
    },
  }
};
</script>
