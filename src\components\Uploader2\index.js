import FileAPI from '@/lib/FileAPI.html5'
import { getOrientation, RotateDeg } from './utils'
import { captureExpectionWithData } from '@/utils/raven';

const FileStatus = {
  NONE: 1,
  UPLOADING: 2,
  FAILED: 3,
  UPLOADED: 4
}

function createFileData(fileId) {
  return {
    id: Math.random() * 1000,
    percent: 100,
    origin: null,
    file: fileId,
    msg: '',
    status: FileStatus.UPLOADED
  }
}

function transformFileList(files) {
  // 默认文件列表
  let fileList = [], values = [];
  if (typeof files === 'string' && files.length > 0) values = [files];
  else if (files instanceof Array) values = files;

  fileList = values.map(function (item, i) {
    if (typeof item === 'string') return createFileData(item)
    return item;
  })
  return fileList;
}

function getSettings(options) {
  const defaultSettings = {
    beforeUpload(resolve){
      resolve({});
    },
    onUpload(next){
      next();
    },
    onClick(item){
    },
    onAdd(value){
    },
    onError(tip){
      console.error(tip);
    },
    uploadName: 'file',
    name: 'file',
    maxFiles: 1,
    maxFileSize: 5 * 1024 * 1024,
    quality: 0.9,
    multiple: false,
    scale: [0.1, 10],
    width: [1, 9999],
    height: [1, 9999]
  }

  return Object.assign(defaultSettings, options);
}

function getThumbnail(file) {
  return new Promise((resolve, reject) => {
    if (window.URL && window.URL.createObjectURL) {
      resolve(URL.createObjectURL(file))
    } else {
      getOrientation(file, orientation => {
        const deg = RotateDeg[orientation] || 0;
        const imageFile = FileAPI.Image(file);
        imageFile.resize(1, 200, 'min').rotate(deg).get(function(e, minImg) {
          resolve(minImg && minImg.toDataURL(), deg);
        });
      })
    }
  })
}

export default {
  name: 'uploader',
  // props: ['config', 'files', 'name', 'maxlength', 'maxfiles', 'multiple', 'quality'],
  props: {
    config: Object,
    files: Array,
    name: String,
    maxlength: Number,
    maxfiles: Number,
    multiple: {
      type: Boolean,
      default: true,
    },
    quality: Number,
    value: Array,
  },
  data() {
    // 默认文件列表
    const files = this.files || this.value;
    console.log(files);
    const fileList = transformFileList(files);
    const settings = getSettings(this.config);
    if (this.maxfiles) settings.maxFiles = this.maxfiles;
    // console.log('fileList:', this.value);
    return {
      settings: settings,
      list: fileList,
    }
  },
  computed: {
    canAddFile(){
      return this.couldAddFile()
    },
    canMultipleSelect(){
      return this.settings.multiple && this.settings.maxFiles > 1;
    },
  },
  watch: {
    config(val, oldVal) {
      this.settings = getSettings(val);
    },
    files(val, oldVal) {
      this.list = transformFileList(val);
    },
    value(val, oldVal) {
      const [a, b] = [String(val), String(oldVal)]
      // console.log('values change:', a !== b);
      if (a !== b) {
        console.warn(a, 'vs', b);

        // console.warn('new:', transformFileList(val));
        this.$nextTick(() => {
          console.warn('comp:', val);
          // this.list = transformFileList(val);
        })
      }
    },
  },
  mounted () {
    console.log('ready...')
    // 跨域上传需要设为false
    FileAPI.withCredentials = false;
    /* this.$watch('files', function () {
      this.$set('list', transformFileList(files))
    }) */
    const fileInput = this.$el.querySelector('input[type="file"]');
    this.initFileInput(fileInput)
  },
  methods: {
    couldAddFile(){
      return this.couldPlusFiles() > 0
    },
    clear() {
      this.list = [];
    },
    couldPlusFiles(count){
      count = count || 0
      return this.settings.maxFiles - (this.list.length + count)
    },

    isFileSizeOk(size) {
      const maxFileSize = this.settings.maxFileSize
      return size <= maxFileSize;
    },

    isDimensionOk(width, height) {
      return this.checkDimension(width, height) === true
    },

    checkDimension(width, height) {

      const {width : widthConfig, height : heightConfig, scale : scaleConfig} = this.settings

      if (widthConfig instanceof Array) {
        if (width < widthConfig[0] || width > widthConfig[1]) return '图片宽度不合规范！'
      }
      else if (width != widthConfig)  return '图片宽度不合规范！'

      if (heightConfig instanceof Array) {
        if (height < heightConfig[0] || height > heightConfig[1]) return '图片高度不合规范！'
      }
      else if (height != widthConfig) return '图片高度不合规范！'

      const scale = width / height
      if (scaleConfig instanceof Array) {
        if (scale < scaleConfig[0] || scale > scaleConfig[1]) return '图片宽高比不合规范！'
      }
      else if (scale != scaleConfig) return '图片宽高比不合规范！'
      return true
    },

    getFileClass(item){
      return {
        'file': true,
        'file-uploading': item.status == FileStatus.UPLOADING,
        'file-upload-fail': item.status == FileStatus.FAILED,
        'file-uploaded': item.status == FileStatus.UPLOADED,
      }
    },

    handleClick(item, index) {
      if (item.status == FileStatus.FAILED) {
        this.reUploadFile(item)
      } else if (item.status == FileStatus.UPLOADED) {
        const files = this.list.filter(item => item.status === FileStatus.UPLOADED).map(item => item.file)
        this.settings.onPreview(item.file, index, files);
      }
    },

    /**
     * 上传重试
     */
    reUploadFile(item){
      const file = item.origin;
      this.updateFile(file, {
        status: FileStatus.UPLOADING,
        percent: 0
      })
      this.uploadOneFile(file, null, 'reupload');
    },

    formatFileSize(size){
      return Math.round(size / 1024) + 'KB'
    },

    clearFileInput(){
      this.$file.value = ''
    },

    initFileInput(file) {
      let that = this;
      this.$file = file;
      FileAPI.event.on(file, 'change', function (evt) {
        console.log('file change...')
        var files = FileAPI.getFiles(evt); // Retrieve file list
        FileAPI.filterFiles(files, function (file, info) {

          // 某些浏览器可能得不到文件大小信息，如IE9-
          // android4.4下file.type为空字符串
          if (file.type && !/^image/.test(file.type)) {
            that.handleError(file.name + '不是合法的图片文件！')
            return false
          }
          else if (!that.isFileSizeOk(file.size)) {
            let tip = '单张图片最大' + that.formatFileSize(that.settings.maxFileSize)
            that.handleError(tip)
            return false
          }
          else if (!that.isDimensionOk(info.width, info.height)) {
            let tip = that.checkDimension(info.width, info.height)
            that.handleError(tip)
            return false
          }
          return true
        }, function (files, rejected) {
          if (files.length) {
            if (that.couldPlusFiles() < files.length) {
              let tip = '最多还能添加' + that.couldPlusFiles() + '张图片'
              that.handleError(tip)
              console.warn(tip)
              return
            }
            FileAPI.each(files, function (file) {
              new Promise((resolve, reject) => {
                that.settings.beforeUpload.call(null, resolve, reject);
              }).then(data => {
                that.addFile(file)
                that.uploadOneFile(file, data);
              })
            })
          }
        })
      })
    },
    showImage(value){
      const showImage = this.settings.showImage
      if (/^\w+:/.test(value)) return value
      if (typeof showImage === 'function') {
        return showImage.call(null, value)
      }
      else if (typeof showImage === 'string') return showImage
      else return value
    },
    toJSON(data){
      try {
        return JSON.parse(data);
      }
      catch (e) {
        return data;
      }
      return data
    },
    getIdByFile({type, name, size, lastModified}){
      return [type, name, size, lastModified].join('_')
    },
    addFile(file){
      const that = this
      const fileId = this.getIdByFile(file)
      // 
      /* getOrientation(file, orientation => {
        const deg = RotateDeg[orientation] || 0;
        const imageFile = FileAPI.Image(file);
        imageFile.resize(1, 100, 'min').rotate(deg).get(function(e, minImg) {
          file.deg = deg;
          that.list.push({
            id: fileId,
            percent: 0,
            origin: file,
            file: minImg && minImg.toDataURL(),
            msg: '',
            status: FileStatus.NONE,
          });
        });
      }) */
      getThumbnail(file).then(thumbnail => {
        that.list.push({
          id: fileId,
          percent: 0,
          origin: file,
          // file: minImg && minImg.toDataURL(),
          file: thumbnail,
          msg: '',
          status: FileStatus.NONE,
        })
      }).catch(e => {
        that.handleError(e);
      })
    },
    updateFile(file, data){
      const fileId = this.getIdByFile(file)
      this.list = this.list.map(item => {
        if (item.id == fileId) {
          item = Object.assign(item, data)
        }
        return item
      });
      this.doInput();
    },
    doInput() {
      const list = this.list.filter(item => item.status === FileStatus.UPLOADED).map(item => item.file)
      this.$emit('input', list);
    },
    uploadOneFile(file, data, action = 'upload'){
      const that = this;
      const next = function (data2) {
        return new Promise((resolve, reject) => {
          that.uploadFile(file, data2 || data).then(result => {
            that.updateFile(file, {
              file: result,
              status: FileStatus.UPLOADED
            });
            that.settings.onAdd.call(null, result);
            resolve(result);
          }).catch(err => {
            captureExpectionWithData(err, {
              file,
              data,
              action,
            }, 'file-upload');
            that.handleError(err);
            that.updateFile(file, {
              status: FileStatus.FAILED,
              msg: err
            });
            reject(err);
          });
        })
      };
      this.settings.onUpload.call(this, next, file, action);
    },
    uploadFile(file, data) {
      const that = this, settings = this.settings;
      if (data) file.data = data;
      return new Promise(function (resolve, reject) {
        that.updateFile(file, {
          status: FileStatus.UPLOADING,
        });
        FileAPI.upload({
          url: settings.uploadUrl,
          files: {[settings.uploadName]: file},
          data: data || file.data,
          imageTransform: {
            type: 'image/jpeg',
            quality: settings.quality // jpeg quality
          },
          imageAutoOrientation: true,
          progress (evt){
            that.updateFile(file, {
              percent: Math.round(evt.loaded / evt.total * 100)
            })
          },
          complete: function (err, xhr) {
            that.clearFileInput()
            let fileStatus = FileStatus.FAILED
            if (xhr.readyState == 4 && xhr.status == 200) {
              let data = that.toJSON(xhr.responseText)
              let result = settings.onUploaded.call(null, data);
              if (result === false) reject('fail')
              else {
                fileStatus = FileStatus.UPLOADED
                resolve(result)
              }
            }
            else reject(xhr.statusText)
            that.updateFile(file, {
              status: fileStatus
            })
          }
        });
      })
    },
    removeFile(fileItem) {
      return new Promise((resolve, reject) => {
        this.settings.onRemove.call(this, fileItem.file, resolve);
      }).then(res => {
        this.list = this.list.filter(function (item) {
          return item.id != fileItem.id;
        })
        this.doInput();
      })
    },
    handleError(e){
      this.clearFileInput();
      this.settings.onError.call(null, e);
    },
  },
}
