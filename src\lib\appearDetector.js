// 本文件代码功能已废弃

/**
 * 
 * <AUTHOR>
 */
function isElementInViewport (el, distance) {
  var rect = el.getBoundingClientRect();
  return (
    rect.top + distance >= 0 && rect.left >= 0 && (rect.top - distance) <= (window.innerHeight || document.documentElement.clientHeight)
  );
}

function debounce(fn, delay) {
  let code;
  return function() {
    clearTimeout(code);
    code = setTimeout(fn, delay);
  }
}

export default class Detector {
  constructor(option = { target: window}) {
    this._watchList = [];
    this._ElManager = new Map();
    this.option = Object.assign({
      onAppear(e) {},
      distance: 50,
    }, option);

    const check = debounce(() => {
      this.checkElements();
    }, 50);
    if (option.target) {
      option.target.addEventListener('scroll', e => {
        check();
      });
    }
  }
  checkElements() {
    this._watchList = this._watchList.filter(el => {
      return this.checkElement(el);
    })
  }
  lazyLoad() {
    this.checkElements();
  }
  checkElement(el) {
    const isInViewport = isElementInViewport(el, this.option.distance);
    if (isInViewport) {
      const data = this._ElManager.get(el);
      this.option.onAppear.call(null, el, data);
      this._ElManager.delete(el);
    }
    return !isInViewport;
  }
  addWatch(el, data) {
    this._ElManager.set(el, data);
    this._watchList.push(el);
    this.checkElement(el);
  }
}
