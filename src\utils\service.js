import { isValidURL } from './type';
import { getAbsoluteURL } from './common';

/**
 * 获取h5收银台订单付款地址
 * 注：截至2018年11月2日 15:53:08，有两个收银台页面，SPA版和h5版本，此方法用于获取跳转到h5收银台的付款链接地址
 * @param {*} oid
 * @param {*} backURL
 */
export function getH5CashierCheckoutURL(oid, backURL) {
  // const backURL = encodeURIComponent(getAbsoluteURL(`/actions/app/cashier/?oid=${oid}`));
  const h5BackURL = encodeURIComponent(btoa(encodeURIComponent(backURL)));
  // const result = encodeURIComponent(backURL);
  return getAbsoluteURL(
    `/actions/app/cashier/?oid=${oid}&goback=${h5BackURL}&t=${Date.now()}`
  );
}

/**
 * 获取SPA应用完整url
 * 当前页面会通过判断url中是否包含nohead来动态设置是否采用模拟的标题栏
 * 调用此方法时，默认会将search参数重置
 * @param {string} path spa内路径
 * @param {object} options 配置参数
 */
export function getAppURL(
  path,
  options = {
    search: '?jglh',
  }
) {
  if (isValidURL(path)) return path;
  const location = window.location;
  // const search = options.search ? options.search : location.search;
  const urlParams = new URLSearchParams(location.search);
  const scene = urlParams.get('scene'); // 用于区分营销渠道
  let search = '';
  if (options.search) {
    search = scene ? `${options.search}&scene=${scene}` : options.search;
  } else {
    search = location.search;
  }
  // 目前页面依据location.search或location.hash中是否包含`nohead`来决定是否显示标题栏
  // 当第一次打开webview，使用原生标题栏，构造url如：http://test.com?nohead&a=1#/abc时，
  // 若从该页面再调用pushWebView方法，使用html的标题栏时，就会导致打开的新页面没有标题栏，
  // 为了避免此问题，需要将nohead参数做无效化处理（有一定几率误伤
  // const search2 = search.replace(/nohead/g, 'n_h');
  let path2 = path;
  /* eslint-disable */
  // 仅处理url中的中文字符，进行encodeURIComponent转义，解决iOS端打开带有白屏问题
  const toBeEncodes = /[^\x00-\xff]/g;
  // const toBeEncodes = /[\u4e00-\u9fa5]/g
  if (toBeEncodes.test(path2)) {
    path2 = path2.replace(toBeEncodes, w => {
      return encodeURIComponent(w);
    });
  }
  const result = `${location.origin}${location.pathname}${search}#${path2}`;
  console.log(result);
  return result;
}

/**
 * getAbsolutePath方法名容易产生歧义
 */
export function getAbsolutePath() {
  console.error('getAbsolutePath 方法即将废弃，请使用 getAppURL ');
  return getAppURL(...arguments);
}

/**
 * 格式化评分
 */
export function formatScore(value, decimal = 2) {
  if (!value) return '暂无评分';
  return Number(value).toFixed(decimal);
}

export function formatMoney(value, decimal = 2) {
  return Number(value).toFixed(decimal);
}

export function computePrice(value, decimal) {
  return Number(formatMoney(value), decimal);
}

export function getHTMLTextContent(html, maxLength) {
  var length = !maxLength ? 100 : maxLength;
  var div = document.createElement('div');
  div.innerHTML = html;
  var text = div.innerText;
  if (text) {
    text = text.replace(/\s/g, '');
    if (maxLength && text.length >= length) {
      text = text.slice(0, length);
    }
  }
  return text;
}
