<template>
  <container
    class="vehicle-inspection-online"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header title="一品天下美食">
      <x-button slot="left" type="back"></x-button>
      <x-button
        slot="right"
        type="share"
        @click="share"
      ></x-button>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="container_box" :class="{'disabled-animate': disabledAnimate}">
        <div class="banner">
          <div class="title animated fadeInDown">
            <img src="./assets/images/yptx_title.png" alt="">
          </div>
          <div class="xr animated fadeIn">
            <img src="./assets/images/yptx_xr.png" alt="">
          </div>
        </div>
        <div :class="{'show-waterfall': showWaterFall}" class="waterfall animated fadeIn" ref="waterfallWrapper">
          <div class="waterfall-left">
            <template v-for="(item, index) in cityIdsKeys">
              <div v-if="index < 8" :key="item" class="waterfall-item" @click="pageTo(item)">
                <img :src="cityBg(item)" alt="">
              </div>
            </template>
          </div>
          <div class="waterfall-right">
            <template v-for="(item, index) in cityIdsKeys">
              <div v-if="index > 7" :key="item" class="waterfall-item" @click="pageTo(item)">
                <img :src="cityBg(item)" alt="">
              </div>
            </template>
          </div>
        </div>
        <div class="bottom">
          <img src="./assets/images/yptx_bm.png" alt="">
        </div>
      </div>
    </content-view>
  </container>
</template>

<script>
import { isInJglh, isInWeixin } from '@/common/env';
import { getVersion, parseJglhURL } from '@/bridge';
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { dialog, loading } from '@/bus';
import { getBoxConfig, getBoxOrder, getBoxDetail } from './api';
import BizRichText from '@/views/components/BizRichText.vue';

const jglhVersion = getVersion();
export default {
  name: 'GourmetWorld',
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    const ref = this.$route.query.ref || ''; // 分享uid
    return {
      AppStatus,
      status: AppStatus.LOADING,
      keepAlive: true,
      ref,
      pageData: {},
      contentImgs: [
        require('@pkg/actions/assets/images/maintain.png'),
        require('@pkg/actions/assets/images/feature-interests.png'),
      ],
      disabledAnimate: false,
      showWaterFall: false,
      cityIds: {
        beijing: 143,
        shanxi: 150,
        shenyang: 145,
        jiangsu: 154,
        shandong: 149,
        anhui: 158,
        shanxi1: 151,
        qinghai: 152,
        hebei: 146,
        neimenggu: 144,
        shanghai: 156,
        zhejiang: 157,
        henan: 153,
        sichuan: 155,
        gansu: 148,
        ningxia: 147,
      },
      timer: null,
      shareInfo: {
        title: '寻全国美食美味，一品天下美食！',
        desc: '让我们一起踏上一场味蕾之旅，探索那些令人垂涎欲滴的美食吧！',
        shareImage: 'FmFHX5pWoYS17NkmG4LjG72nO-oP',
      }
    };
  },
  components: {
    BizRichText,
  },
  computed: {
    cityIdsKeys() {
      return Object.keys(this.cityIds) || []
    },
  },
  mounted() {
  },
  methods: {
    cityBg(item) {
      return require(`@pkg/actions/assets/images/yptx_city_${item}.png`)
    },
    pageTo(city) {
      // const options = parseJglhURL(url);
      // this.$_router_pageTo(options.url, {
      //   ...options,
      //   titleBar: false,
      //   progressBar: false,
      // });
      this.disabledAnimate = true
      let params = `?city=${city}&id=${this.cityIds[city]}${this.ref ? `&ref=${this.ref}` : ''}`;
      this.$_router_push('/gourmet/detail' + params);
    },
    initShareInfo() {
      // 设置邀请链接分享信息
      const wechatPath = `/gourmet/index?ref=${this.ref}`;
      const jglhWechatURL = getAppURL(wechatPath, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share2' : '?jglh',
      });

      const shareInfo = {
        link: jglhWechatURL,
        title: this.shareInfo.title,
        desc: this.shareInfo.desc,
        imgUrl: getImageURL(this.shareInfo.shareImage),
      };
      this.$_share_update(shareInfo);
    },
    share() {
      const title = this.shareInfo.title
      const logo = getImageURL(this.shareInfo.shareImage);
      const desc = this.shareInfo.desc;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      }
      this.$_share(shareInfo);
    },
    initPageData: function () {
      // 获取页面加载的数据
    },
    init() {
      // 初始化数据
      this.status = AppStatus.READY;
      this.$nextTick(() => {
        this.addWrapperSizeListener()
      })
      this.initShareInfo()
      // this.status = AppStatus.ERROR;
    },
    // add observer
    addWrapperSizeListener() {
      const dom = this.$refs.waterfallWrapper;
      if (!dom) {
        return;
      }

      this.wrapperObserver = new ResizeObserver((entries) => {
        if (this.timer) clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          this.showWaterFall = true
        }, 200);
      });
      this.wrapperObserver.observe(dom);
    },

    // remove observe
    removeWrapperSizeListener() {
      const dom = this.$refs.waterfallWrapper;
      if (!dom) {
        return;
      }

      this.wrapperObserver.unobserve(dom);
    },
    reload() {
      this.status = AppStatus.LOADING;
      // 重新加载页面
      this.init();
    },
    onResume() {
      // 页面重新激活时在重新获取数据
      // this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~styles/mixin/animate.scss";
@import "~styles/mixin/index.scss";
img {
  -webkit-user-drag: none;
}
.container_box {
  padding: 300px 0px 0px;
  background: #f1e0aa url(./assets/images/yptx_bg.jpg) no-repeat center top;
  background-size: 100% auto;
  position: relative;
  font-size: 0;
  transform:translate3d(0,0,0);
}
.disabled-animate{
  .animated{
    animation: none;
  }
}
.banner{
  width: 100%;
  // background: url(./assets/images/yptx_banner.png) no-repeat center top;
  // background-size: 100% auto;
  position: absolute;
  top: 0;
  left: 0;
  padding-top: 275px;
  // overflow: hidden;
  z-index: 2;
  .title{
    width: 100%;
    position: absolute;
    top: 40px;
    left: 0;
    animation-delay: 0.4s;
    img{
      display: block;
      user-select: none;
      width: 220px;
      height: auto;
      margin: 0 auto;
    }
  }
  .xr{
    width: 100%;
    position: absolute;
    top: 124px;
    left: 0;
    // animation-delay: 0.2s;
    img{
      display: block;
      user-select: none;
      width: 100%;
      height: auto;
    }
  }
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(./assets/images/yptx_banner.png) no-repeat center top;
    background-size: 100% auto;
    opacity: 0;
    animation: fadeInBg 1s forwards;
    animation-delay: 0.2s;
  }

}
@keyframes fadeInBg {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
.waterfall{
  visibility: hidden;
  display: flex;
  align-items: flex-start;
  // align-items: stretch;
  width: 100%;
  font-size: 0;
  margin-top: -10px;
  animation-delay: 1s;
  position: relative;
  z-index: 10;
  animation-delay: 0.6s;
  .waterfall-left,.waterfall-right{
    width: 50%;
    // height: 100%;
    display: inline-flex;
    flex-direction: column;
  }
  .waterfall-item{
    flex: 1;
    img{
      display: block;
      width: 100%;
      height: auto;
    }
  }
}
.show-waterfall{
  visibility: visible;
}
.bottom{
  width: 100%;
  img{
    display: block;
    width: 100%;
    height: auto;
  }
}
</style>
