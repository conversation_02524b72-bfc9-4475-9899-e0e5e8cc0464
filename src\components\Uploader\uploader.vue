<template>
  <div class="file-container">
    <div
      v-for="(item, index) in imageList"
      :class="getFileClass(item)"
      :title="item.msg"
      :key="index"
      @click="handleClick(item, index)"
      :percent="item.percent"
    >
      <img :src="item.url" />
      <a
        href="javascript:;"
        @click.stop.prevent="removeFile(item)"
        class="file-remove"
        >&times;</a
      >
      <input type="hidden" :name="uploadName" :value="item.file" />
    </div>

    <!-- 2018年10月31日17:46:08：增加文件格式限制后：在小米手机中图片显示为不可选择状态，故暂不加限制 -->
    <!-- 增加文件格式限制后：在小米手机中 accept="image/gif,image/jpeg,image/webp,image/jpg,image/png,image/bmp" -->
    <div
      title="上传图片"
      v-show="avaliableAdditionalImageCount > 0"
      class="file file-select upload-icon"
      @click="handleFileSelect"
    >
      <!-- <slot name="uploadIconSlot">
        <div class="upload-icon" title="上传图片"></div>
      </slot> -->
      <slot name="uploadIconSlot"></slot>
      <input
        v-if="couldMultipleSelect"
        type="file"
        class="text"
        accept="image/*"
        multiple="multiple"
      />
      <input v-else type="file" class="text" accept="image/*" />
    </div>
    <!-- <div class="files">
    </div> -->
    <slot></slot>
  </div>
</template>
<script src="./uploader.js"></script>
<style src="./uploader.scss" lang="scss" scoped></style>
