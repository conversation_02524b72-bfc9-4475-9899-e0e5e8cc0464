<template>
  <div
    class="panel"
    :class="{ fold: fold }"
    :style="{ '-webkit-line-clamp': fold && rows }"
  >
    <h3 v-if="title || $slots.title" class="panel-title">
      <slot name="title">
        <div class="title-content">{{ title }}</div>
        <span v-if="rows" @click="toggleFold" class="fold-btn">{{
          fold ? '展开' : '收起'
        }}</span>
        <div v-if="$slots.extra" class="title-extra">
          <slot name="extra"></slot>
        </div>
      </slot>
    </h3>
    <slot name="other"></slot>
    <div class="panel-content">
      <slot></slot>
    </div>
  </div>
</template>
<style lang="scss">
.panel {
  background: white;
  margin: 10px 0;
}
.panel-title {
  // font-weight:400;
  display: flex;
}
.title-content {
  flex: 1;
}
.fold-btn {
  color: RGB(90, 90, 90);
}
.fold {
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
/*  .panel-content{
    padding:0;
  }*/
</style>
<script>
export default {
  name: 'panel',
  props: {
    title: String,
    rows: {
      type: Number
    }
  },
  data() {
    return {
      fold: !!this.rows
    };
  },
  computed: {},
  methods: {
    toggleFold() {
      this.fold = !this.fold;
    }
  }
};
</script>
