<template>
  <container class="prize-records" @resume="onResume">
    <x-header title="中奖记录">
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="records-container">
        <!-- 无记录状态 -->
        <div v-if="records.length === 0" class="no-records">
          <page-placeholder :icon="require('@/components/Page/images/placeholder.png')">
            <div class="no-records-text">暂无中奖记录</div>
          </page-placeholder>
        </div>

        <!-- 中奖记录列表 -->
        <div v-else class="records-list">
          <div v-for="(record, index) in records" :key="index" class="record-item">
            <div class="record-content">
              <!-- <div class="record-icon">
                <img :src="getPrizeIcon(record)" alt="奖品图标">
              </div> -->
              <div class="record-info">
                <div class="prize-name">{{ record.prizeName || '未知奖品' }}</div>
                <div class="prize-time">{{ formatTime(record.createTime) }}</div>
              </div>
              <div v-if="record.thirdPlatformCouponCode" class="code-wrap">

                <div class="label">兑换码: </div>
                <div class="code-text">{{ record.thirdPlatformCouponCode }}</div>
              </div>
            </div>
            <div class="record-action">
              <!-- 普通奖品(实物)显示完善地址按钮 -->
              <template v-if="record.prizeType == PrizeType.NORMAL && !isFinished(record)">
                <button class="action-btn address" @click="completeAddress(record)">完善地址</button>
              </template>

              <!-- 兑换码显示复制按钮 -->
              <template v-else-if="record.prizeType == PrizeType.COUPONCODE">
                <div class="code-container">
                  <button class="action-btn copy" @click="copyCode(record.thirdPlatformCouponCode)">复制</button>
                </div>
              </template>

              <!-- 优惠券显示查看按钮 -->
              <template
                v-else-if="record.prizeType == PrizeType.COUPON || record.prizeType == PrizeType.WELFARE_COUPON">
                <button class="action-btn coupon" @click="viewCoupon(record)">查看</button>
              </template>

              <!-- 金币显示查看按钮 -->
              <template v-else-if="record.prizeType == PrizeType.GOLD">
                <button class="action-btn gold" @click="viewGold(record)">查看</button>
              </template>

              <!-- 红包显示查看按钮 -->
              <template v-else-if="record.prizeType == PrizeType.REDENVELOPE">
                <button class="action-btn redenvelope" @click="viewRedEnvelope(record)">查看</button>
              </template>

              <!-- 其他类型或已完成的显示状态 -->
              <template v-else>
                <div class="status-text" :class="{ 'finished': isFinished(record) }">
                  {{ isFinished(record) ? '已领取' : '未领取' }}
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore && records.length > 0" class="load-more" @click="loadMore">
          <span v-if="!loadingMore">加载更多</span>
          <span v-else class="loading">加载中...</span>
        </div>
      </div>

    </content-view>
  </container>
</template>

<script>
import { AppStatus, PrizeType } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
import { formatDate } from '@/utils';
import { toast, loading } from '@/bus';
import PagePlaceholder from '@/components/Page/PagePlaceholder.vue';
import { getSendBlessingWinRecords } from './api';
import { Toast } from 'vant';

export default {
  name: 'PrizeRecords',
  mixins: [mixinAuthRouter],
  components: {
    [Toast.name]: Toast,
    PagePlaceholder
  },
  data() {
    return {
      AppStatus,
      PrizeType,
      status: AppStatus.LOADING,
      actionId: this.$route.query.actionId,
      actionType: this.$route.query.actionType || 'sendBlessing',
      records: [],
      pageNo: 1,
      pageSize: 10,
      hasMore: false,
      loadingMore: false
    };
  },
  created() {
    this.fetchRecords();
  },
  methods: {
    // 是否已领取
    isFinished(record) {
      return record.userReciveAddressPostInfo && !!record.userReciveAddressPostInfo.phone;
    },
    // 获取奖品图标
    getPrizeIcon(record) {
      // 使用现有资源作为临时替代
      const iconMap = {
        [PrizeType.NORMAL]: require('./assets/images/gasha_prize01.png'),
        [PrizeType.COUPON]: require('@pkg/vip-v2/assets/images/gift_coupons.png'),
        [PrizeType.WELFARE_COUPON]: require('./assets/images/gasha_prize03.png'),
        [PrizeType.GOLD]: require('./assets/images/member_icon.png'),
        [PrizeType.REDENVELOPE]: require('./assets/images/gasha_prize01.png'),
        [PrizeType.COUPONCODE]: require('./assets/images/gasha_prize02.png'),
      };
      return iconMap[record.prizeType] || iconMap[PrizeType.NORMAL];
    },

    // 格式化时间
    formatTime(timestamp) {
      return timestamp ? formatDate(timestamp, 'yyyy-MM-dd HH:mm:ss') : '';
    },

    // 获取中奖记录
    fetchRecords() {
      this.status = AppStatus.LOADING;
      const params = {
        page: this.pageNo,
        pageSize: this.pageSize
      };

      getSendBlessingWinRecords(this.actionId, params)
        .then(res => {
          if (res && res.list) {
            this.records = this.pageNo === 1 ? res.list : [...this.records, ...res.list];
            this.hasMore = res.list.length >= this.pageSize;
          }
          this.status = AppStatus.READY;
        })
        .catch(err => {
          console.error('获取中奖记录失败:', err);
          this.status = AppStatus.ERROR;
        });
    },

    // 加载更多
    loadMore() {
      if (this.loadingMore) return;

      this.loadingMore = true;
      this.pageNo += 1;

      const params = {
        page: this.pageNo,
        pageSize: this.pageSize
      };

      getSendBlessingWinRecords(this.actionId, params)
        .then(res => {
          if (res && res.list) {
            this.records = [...this.records, ...res.list];
            this.hasMore = res.list.length >= this.pageSize;
          }
          this.loadingMore = false;
        })
        .catch(err => {
          console.error('加载更多中奖记录失败:', err);
          this.loadingMore = false;
          this.pageNo -= 1; // 恢复页码
          toast().tip('加载失败，请重试');
        });
    },

    // 复制兑换码
    copyCode(code) {
      if (!code) {
        toast().tip('兑换码不存在');
        return;
      }

      try {
        let selection = window.getSelection();
        let range = document.createRange();
        let referenceNode = document.createElement('div');
        referenceNode.innerHTML = code;
        document.body.appendChild(referenceNode);
        range.selectNodeContents(referenceNode);
        selection.removeAllRanges();
        selection.addRange(range);
        let ret = document.execCommand('copy');
        document.body.removeChild(referenceNode);

        if (ret) {
          toast().tip('复制成功');
        }
      } catch (err) {
        toast().tip('复制失败，请手动复制');
      }
    },

    // 完善地址
    completeAddress(record) {
      if (!record || !record.id) {
        toast().tip('记录ID不存在');
        return;
      }

      this.$_router_pageTo(`/prize/address/submit?action=sendBlessing&recordId=${record.id}`, {
        theme: 'light'
      });
    },

    // 查看优惠券
    viewCoupon() {
      this.$_router_pageTo('/ticket/list', {
        theme: 'light'
      });
    },

    // 查看金币
    viewGold() {
      this.$_router_pageTo('/virtual/gold/record', {
        theme: 'light'
      });
    },

    // 查看红包
    viewRedEnvelope() {
      this.$_router_pageTo('/account/redpocket', {
        theme: 'light'
      });
    },

    // 重新加载
    reload() {
      this.pageNo = 1;
      this.fetchRecords();
    },

    // 恢复
    onResume() {
      this.pageNo = 1;
      this.fetchRecords();
    }
  }
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.prize-records {
  .records-container {
    padding: 15px;
  }

  .no-records {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;

    .no-records-icon {
      font-size: 50px;
      margin-bottom: 20px;
    }

    .no-records-text {
      font-size: 16px;
      color: #999;
    }
  }

  .records-list {
    .record-item {
      background: #fff;
      border-radius: 8px;
      margin-bottom: 15px;
      padding: 15px;
      box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

      .record-content {
        // display: flex;
        margin-bottom: 10px;

        .record-icon {
          width: 50px;
          height: 50px;
          border-radius: 8px;
          overflow: hidden;
          margin-right: 10px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .record-info {
          display: flex;
          // flex-direction: column;
          // justify-content: center;
          justify-content: space-between;
          align-items: center;

          .prize-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
          }

          .prize-time {
            flex-shrink: 0;
            font-size: 12px;
            color: #999;
          }
        }

        .code-wrap {
          display: flex;
          align-items: center;

          .label {
            margin-right: 5px;
            font-size: 14px;
            color: #999;
            flex-shrink: 0;
          }

          .code-text {
            font-family: monospace;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 14px;
            color: #999;
            flex: 1;
            width: 0;
            // 超出换行
            word-break: break-all;
          }
        }
      }

      .record-action {
        padding-top: 10px;
        border-top: 1px solid #f5f5f5;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .action-btn {
          padding: 5px 15px;
          border-radius: 15px;
          font-size: 14px;
          cursor: pointer;
          border: none;
          outline: none;
          background: $lh-2022-primary-color;
          color: #fff;

          // &.address {
          //   background: #ff6b6b;
          //   color: white;
          // }

          // &.copy {
          //   background: #3498db;
          //   color: white;
          // }

          // &.coupon {
          //   background: #2ecc71;
          //   color: white;
          // }

          // &.gold {
          //   background: #f1c40f;
          //   color: white;
          // }

          // &.redenvelope {
          //   background: #e74c3c;
          //   color: white;
          // }
        }

        .code-container {
          display: flex;
          align-items: center;

        }

        .status-text {
          font-size: 14px;
          color: #f39c12;

          &.finished {
            color: #7f8c8d;
          }
        }
      }
    }
  }

  .load-more {
    text-align: center;
    padding: 15px 0;
    color: #666;
    font-size: 14px;

    .loading {
      color: #999;

      &:after {
        content: '';
        display: inline-block;
        width: 10px;
        animation: dots 1.5s infinite;
      }
    }
  }
}

@keyframes dots {
  0% {
    content: '.';
  }

  33% {
    content: '..';
  }

  66% {
    content: '...';
  }
}
</style>
