<template>
  <van-popup
    v-model="showPopup"
    overlay
    round
    closeable
    close-on-click-overlay
    safe-area-inset-bottom
    position="bottom"
    get-container="body"
    class="rights-pop"
    :style="{ height: '70%' }"
    @close="handleClose"
  >
    <div class="pop-title">
      <h2 class="panel-title">权益说明</h2>
    </div>
    <div class="pop-content">
      <van-tabs v-model="active" swipeable>
        <van-tab v-for="(item, index) in rights" :title="item.name" :key="index">
          <div class="right-content">
            <div class="right-content-header">
              <c-picture class="right-icon" :src="item.image" type='?imageView2/0'></c-picture>
              <div>
                <h2 class="title">{{ item.name }}</h2>
                <p class="label">{{ item.title }}</p>
              </div>
            </div>
            <div class="right-content-desc">
              <h3>权益说明</h3>
              <biz-rich-text :value="item.descInfo"></biz-rich-text>
              <div class="space"></div>
              <h3>使用指南</h3>
              <biz-rich-text :value="item.userGuide"></biz-rich-text>

            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    <!-- <div class="pop-bottom-btn">
      <van-button
        v-if="vip"
        class="close-btn"
        round
        block
        type="danger"
        @click="goToBuy"
      >会员续费</van-button>
      <van-button
        v-else
        class="close-btn"
        round
        block
        type="danger"
        @click="goToBuy"
      >立即开通</van-button>
    </div> -->
  </van-popup>
</template>
<script>
import { formatDate } from '@/utils';
import { mixinAuthRouter } from '@/mixins';

import { Icon, Button, Popup, Tab, Tabs } from 'vant';
import BizRichText from '@/views/components/BizRichText.vue';

export default {
  name: 'RightPop',
  props: {
    vip: {
      type: Boolean,
    },
    show: {
      type: Boolean,
      default: false
    },
    rights: {
      type: Array,
    },
    currentRights: {
      type: Object,
    },
    index: {
      type: Number,
    },
    vipConfig: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  mixins: [mixinAuthRouter],
  components: {
    BizRichText,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
  },
  data() {
    return {
      showPopup: false,
      active: this.index,
    };
  },
  computed: {
    // vipId() {
    //   return [this.user.vip, this.user.vipExpireTime].join(',');
    // },
  },
  watch: {
    show(val) {
      this.showPopup = val;
      this.active = this.index;
    },
    index(val) {
      this.active = val;
    },
  },
  mounted() {
  },
  methods: {
    ...{ formatDate },
    // go(path) {
    //   this.$_auth_push(path);
    // },
    // goToBuy() {
    //   let type = this.vipConfig.category || 'mall';
    //   this.$_auth_push(`/vip/buy?type=${type}`);
    // },
    handleClose() {
      this.$emit('close');
    },
  }
}
</script>
<style lang="scss" scoped src="./styles/vip-right.scss"></style>
<style lang="scss" scoped>
.rights-pop {
  display: flex;
  flex-direction: column;
  background: #363647!important;
  max-height: 80%;
  .pop-title {
    height: 50px;
    box-sizing: border-box;
    text-align: center;
  }
  .panel-title {
    font-size: 18px;
    color: #FFE9DB;
    height: 50px;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
    border-color: #454568;
    font-weight: normal;
  }
  .pop-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
    &::-webkit-scrollbar {
      width: 0 !important;
    }
  }
  .pop-bottom-btn {
    padding: 5px 15px;
    background: #363647;
    .close-btn {
      width: 100%;
      height: 40px;
      line-height: 40px;
      font-size: 18px;
      background: linear-gradient(90deg, #FFE9DB, #F7BC88);
      border-color: transparent;
      color: #333333;
      font-weight: bold;
    }
  }
  .van-tabs ::v-deep{
    .van-tabs__line{
      display: none;
    }
    .van-tabs__wrap{
      height: auto;
      .van-tabs__nav{
        padding-left: 0;
        padding-right: 0;
      }
    }
    .van-tabs__nav{
      background: #363647;
    }
    .van-tab{
      padding: 0 5px;
      // margin-right: 20px;
      line-height: 1;
      .van-tab__text{
        font-size: 12px;
        color: #8F8F9D;
        max-width: 80px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
    .van-tab--active{
      .van-tab__text{
        max-width: 100px;
        font-size: 14px;
        color: #FFE9DB;
      }
    }
  }
  .van-tabs ::v-deep{
    height: 100%;
    display: flex;
    flex-direction: column;
    .van-tabs__wrap{
      padding-bottom: 10px;
    }
    .van-tabs__content{
      flex: 1;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }
    }
  }
  ::v-deep .van-icon-cross{
    top: 19px;
    font-size: 12px;
    line-height: 1;
  }
  ::v-deep .van-icon-cross:before{
    line-height: 1;
    content: '收起';
    font-size: 12px;
    color: #FFE9DB;
  }
  .right-content{
    border-radius: 10px;
    padding: 1px;
    background-image: linear-gradient(-18deg, #FBD0AE, #777796, #FBD0AE);
    line-height: 1;
    overflow: hidden;
    .right-content-header{
      padding: 15px 10px;
      font-size: 16px;
      color: #FFE9DB;
      background: #3C3C52;
      border-radius: 10px 10px 0px 0px;
      display: flex;
      align-items: center;
      .right-icon{
        display: block;
        width: 32px;
        height: 32px;
        margin-right: 10px;
      }
      .title{
        font-weight: bold;
        font-size: 16px;
        display: flex;
        align-items: center;
      }
      .label{
        margin-top: 5px;
        font-size: 10px;
        color: #B2A4A3;
      }
    }
    .space{
      height: 28px;
      width: 100%;
    }
    .right-content-desc{
      padding: 15px;
      background: #363647;
      border-radius: 0px 0px 10px 10px;
      h3{
        padding: 6px 0 6px 10px;
        position: relative;
        font-size: 15px;
        font-weight: bold;
        color: #FFE9DB;
        margin-bottom: 5px;
        &::before{
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 15px;
          background: linear-gradient(0deg, #FFE9DB, #F7BC88);
          border-radius: 1px;
        }
      }
      .rich-text{
        background: #363647;
        color: #FEFEFE !important;
        font-size: 14px;
        white-space: inherit;
      }
    }
  }
}
</style>
