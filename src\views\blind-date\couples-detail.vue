<template>
  <!-- 页面包裹组件，处理初始化、前后台切换等状态 -->
  <container @ready="onReady" @leave="onLeave" @resume="onResume" @init="init">
    <!-- 头部导航 -->
    <x-header ref="header" :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <!-- 页面内容包裹组件 -->
    <content-view ref="content" :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div class="cp-detail-container">
          <!-- 顶部背景装饰 -->
          <div class="top-background" id="heartsContainer">
            <div class="header-content">
              <h1>心动时刻</h1>
              <p>遇见彼此, 是美好的开始</p>
            </div>
          </div>

          <!-- CP照片区域 -->
          <div class="cp-photos-section">
            <div class="photos-container">
              <div class="photo-card" @click="goUserDetail(leftUser.id)">
                <c-picture class="user-photo" :src="getImageUrl(leftUser.profileImages)" :type="`?imageView2/1/w/480/h/600/format/${supportWebP ? 'webp' : 'jpg'
                  }/q/90`">
                </c-picture>
              </div>
              <div class="photo-card" @click="goUserDetail(rightUser.id)">
                <c-picture class="user-photo" :src="getImageUrl(rightUser.profileImages)" :type="`?imageView2/1/w/480/h/600/format/${supportWebP ? 'webp' : 'jpg'
                  }/q/90`">
                </c-picture>
              </div>
            </div>
          </div>

          <!-- 用户信息区域 -->
          <div class="user-info-section">
            <div class="users-info-container">
              <div class="user-info-card">
                <div class="user-name">{{ leftUser.nickName }}</div>
                <div class="user-basic">{{ leftUser.age }}岁 · {{ leftUser.occupation }}</div>
                <div class="user-location">{{ leftUser.location }}</div>
                <div class="user-detail">{{ leftUser.maritalStatus }} · {{ leftUser.height }}cm</div>
                <div class="user-intro">{{ leftUser.introduction }}</div>
              </div>
              <div class="user-info-card">
                <div class="user-name">{{ rightUser.nickName }}</div>
                <div class="user-basic">{{ rightUser.age }}岁 · {{ rightUser.occupation }}</div>
                <div class="user-location">{{ rightUser.location }}</div>
                <div class="user-detail">{{ rightUser.maritalStatus }} · {{ rightUser.height }}cm</div>
                <div class="user-intro">{{ rightUser.introduction }}</div>
              </div>
            </div>
          </div>

          <!-- 底部间距 -->
          <div class="bottom-spacer"></div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
// 工具
import { AppStatus } from '@/enums';
import { toast, loading } from '@/bus';
import { getImageURL } from '@/common/image';
import { getCpInfo } from '@/views/blind-date/api';
import { mapState } from 'vuex';

// 组件
import { Button, Icon } from 'vant';

export default {
  name: 'CouplesMineDetail',
  components: {
    [Button.name]: Button,
    [Icon.name]: Icon,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      // CP用户信息
      leftUser: {},
      rightUser: {},

    };
  },
  computed: {
    ...mapState(['supportWebP']),
    pageTitle() {
      return '缘分详情';
    },
  },
  mounted() {
  },
  methods: {
    init() {
      this.status = AppStatus.READY;
      getCpInfo(this.$route.params.id)
        .then(res => {
          if (res) {
            this.leftUser = res.datingUser1;
            this.rightUser = res.datingUser2;
          }
        })
        .catch(err => {
          toast().tip(err || '加载失败');
          this.status = AppStatus.ERROR;
        })
        .finally(() => {
          loading.hide();
        });
      setTimeout(() => {
        this.createHearts();
      }, 1000);
    },
    getImageUrl(url) {
      if (!url) {
        return '';
      }
      let image = url.split(',')[0];
      return image;
    },
    // 创建浮动爱心
    createHearts() {
      const heartsContainer = document.getElementById('heartsContainer');
      const heartCount = 15;

      for (let i = 0; i < heartCount; i++) {
        const heart = document.createElement('div');
        heart.classList.add('heart');
        heart.innerHTML = '<i class="van-icon van-icon-like"></i>';

        // 随机位置
        heart.style.left = Math.random() * 100 + '%';
        heart.style.fontSize = (Math.random() * 20 + 10) + 'px';
        heart.style.animationDuration = (Math.random() * 6 + 4) + 's';
        heart.style.animationDelay = Math.random() * 5 + 's';

        heartsContainer.appendChild(heart);
      }
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
    onReady() {
      this.init();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    goUserDetail(id) {
      this.$router.push({
        path: '/blindDate/user/' + id,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/variable/global.scss';

.cp-detail-container {
  margin: 0 auto;
  background: #fff;
  min-height: 100%;
  position: relative;

  /* 顶部背景区域 */
  .top-background {
    height: 200px;
    // background: linear-gradient(135deg, #ffb3d1 0%, #ffc9e0 100%);
    background: url(./images/couple.jpg) no-repeat center center;
    background-size: cover;
    position: relative;
    overflow: hidden;

    .header-content {
      position: relative;
      z-index: 2;
      text-align: center;
      padding-top: 24px;

      h1 {
        font-size: 28px;
        margin-bottom: 2px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        letter-spacing: 1px;
        color: #FFB6C7;
      }

      p {
        font-size: 18px;
        opacity: 0.9;
        margin: 0 auto;
        font-weight: 300;
        color: #FFB6C7;
      }
    }
  }

  /* CP照片区域 */
  .cp-photos-section {
    padding: 0 20px;
    margin-top: -80px;
    position: relative;
    z-index: 10;
  }

  .photos-container {
    display: flex;
    gap: 16px;
    margin-bottom: 10px;
  }

  .photo-card {
    flex: 1;
    height: 200px;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .user-photo {
    display: block;
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  /* 用户信息区域 */
  .user-info-section {
    padding: 0 20px 20px;
  }

  .users-info-container {
    display: flex;
    gap: 16px;
  }

  .user-info-card {
    flex: 1;
    background: #fff;
  }

  .user-name {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }

  .user-basic {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
  }

  .user-location {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
  }

  .user-detail {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
  }

  .user-intro {
    font-size: 12px;
    color: #333;
    line-height: 1.5;
    background: #f8f8f8;
    padding: 12px;
    border-radius: 8px;
    margin-top: 8px;
  }

  /* 分享助力提示 */
  .share-tip-section {
    margin: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
    border-radius: 12px;
    text-align: center;
    border: 1px solid rgba(255, 107, 157, 0.2);
  }

  .share-tip-text {
    color: #ff6b9d;
    font-size: 14px;
    font-weight: 500;
  }

  .share-highlight {
    color: #ff1744;
    font-weight: bold;
  }

  /* 点赞进度区域 */
  .like-progress-section {
    margin: 0 20px 20px;
    background: #fff;
  }

  .progress-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 12px;
  }

  .progress-bar-container {
    background: #f0f0f0;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
  }

  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #ff6b9d 0%, #ff8fab 100%);
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 16px;
    color: #333;
    text-align: center;
    font-weight: bold;
  }

  .progress-count {
    color: #ff6b9d;
  }

  /* 底部操作区 */
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 375px;
    background: #fff;
    padding: 16px 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
  }

  .action-button {
    width: 100%;
    height: 48px;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .contact-button {
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
    color: white;
    margin-bottom: 8px;
  }

  .contact-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
  }

  .like-button {
    background: linear-gradient(135deg, #ff1744 0%, #ff5722 100%);
    color: white;
  }

  .like-button:hover {
    transform: scale(1.02);
  }

  .tip-text {
    text-align: center;
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  /* 底部间距 */
  .bottom-spacer {
    height: 100px;
  }

  /* 隐藏类 */
  .hidden {
    display: none;
  }

  ::v-deep .heart {
    position: absolute;
    color: #FFB1CB;
    animation: float 8s linear infinite backwards;
    font-size: 1.5rem;
  }

  @keyframes float {
    0% {
      transform: translateY(100vh) rotate(0deg);
      opacity: 0;
    }

    10% {
      opacity: 1;
    }

    90% {
      opacity: 0.8;
    }

    100% {
      transform: translateY(-100px) rotate(360deg);
      opacity: 0;
    }
  }
}
</style>
