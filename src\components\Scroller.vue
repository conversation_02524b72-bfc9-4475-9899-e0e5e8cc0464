<template>
  <div
    class="scroller"
    id="scroller"
    @scroll.passive="scroll($event)"
    :style="scrollerStyle"
  >
    <template v-if="refreshAction">
      <div class="pulldown" v-show="pulldown.y">
        <div class="pulldown-content" v-text="pulldownTip" ></div>
        <div class="pulldown-line" :style="pulldownLineStyle"></div>
      </div>
      <div class="scroller-inner" :style="innerScrollerStyle">
        <slot></slot>
      </div>
    </template>
    <template v-else>
      <slot></slot>
    </template>
  </div>
</template>

<script>
  /**
  * ios的webview里，手指在屏幕外释放无法触发panend事件（在屏幕较小的手机上下拉刷新很容易让手指在屏幕外释放），所以需要加【手指超出屏幕】的判断来主动触发panend
  * ios增加 -webkit-overflow-scrolling: touch;属性能让滚动更平滑，但其自带的回弹效果与下拉刷新功能实现的回弹效果有些冲突
  */
  import Hammer from 'hammerjs';
  import { AppStatus } from '@/enums';
  import { scrollerComponentScroll } from '@/bus';

  function getDRPPX(value) {
    if (window.lib && window.lib.flexible) {
      return window.lib.flexible.dpr * value;
    }
    return value;
  }

  function px2rem(value) {
    if (window.lib && window.lib.flexible) {
      return `${window.lib.flexible.px2rem(value)}rem`;
    }
    return `${value}px`;
  }

  const Direction = {
    UP: 1,
    DOWN: 2
  };

  export default {
    name: 'scroller',
    props: {
      status: {
        type: [Number, Object, String],
        default() {
          return AppStatus.LOADING;
        },
      },
      refreshAction: {
        type: Number,
        default: 0,
      },
    },
    components: {},
    data() {
      const CRITICAL = getDRPPX(50); // px
      const SCROLL_BOTTOM_DISTANCE = getDRPPX(80);
      return {
        AppStatus,
        scrollTop: 0,
        scrollDirection: null,
        SCROLL_BOTTOM_DISTANCE,
        pulldown: {
          CRITICAL,
          enable: false,
          y: 0,
          lastY: 0,
          end: false,
          refresh: false,
        },
      };
    },
    mounted() {
      if (this.refreshAction) {
        this.initPulldownRefresh();
      }
    },
    activated() {
      this.$el.scrollTop = this.scrollTop;
    },
    computed: {
      pulldownStyle() {
        const y = px2rem(this.pulldown.y);
        const end = this.pulldown.end;
        return {
          height: `${y}`,
          lineHeight: `${y}`,
          transition: end ? 'all 250ms' : 'none'
        };
      },
      scrollerStyle() {
        const enable = this.pulldown.enable || this.pulldown.y > 0;
        return {
          overflowY: enable ? 'hidden' : 'auto'
          // overflowY: 'auto'
        };
      },
      pulldownLineStyle() {
        let percent = (this.pulldown.y / this.pulldown.CRITICAL) * 100;
        if (percent > 100) percent = 100;
        if (this.pulldown.end) percent = 0;
        return {
          width: `${percent}%`,
        };
      },
      innerScrollerStyle() {
        // let y = px2rem(this.pulldown.y);
        let y = `${this.pulldown.y}px`;
        const end = this.pulldown.end;
        return {
          transform: `translate3d(0, ${y}, 0)`,
          webkitTransition: end ? 'transform 250ms' : 'none',
          transition: end ? 'transform 250ms' : 'none',
        };
      },
      pulldownTip() {
        let text = '下拉刷新';
        if (this.pulldown.refresh) {
          if (this.pulldown.end) text = '正在刷新...';
          else {
            text = '释放立即刷新';
          }
        }
        return text;
      },
    },
    methods: {
      reload() {
        this.$emit('reload');
        this.$ua && this.$ua.trackEvent('Page', 'Reload', this.$route.path);
      },
      scroll(e) {
        this.scrollTop = this.$el.scrollTop;
        scrollerComponentScroll(e)
        this.$emit('scroll', this.scrollTop, e);
        const scrollBottom = this.$el.scrollHeight - this.$el.scrollTop - this.$el.clientHeight;
        // console.log('scrollBottom:', scrollBottom);

        if (scrollBottom >= 0 && this.scrollDirection == Direction.DOWN) {
          // e.stopPropagation();
          this.scrollDown(scrollBottom);
        }
        if (scrollBottom < 0) {
          e.preventDefault();
        }
      },
      initPulldownRefresh() {
        let mc = null;
        let that = this;
        function panStart(e) {
          const enable = (e.angle > 45 && e.angle < 135 && this.scrollTop === 0);
          this.pulldown.enable = enable;
          if (this.scrollTop > 0) {
            this.pulldown.y = 0;
            this.pulldown.lastY = 0;
          }
          // console.log('touchstart', e);
        }

        function panEnd(e) {
          if (this.pulldown.enable) {
            const y = this.getPulldownHeight(e);
            if (y < this.pulldown.CRITICAL) {
              this.setPulldownHeight(0, true);
            } else {
              this.setPulldownHeight(this.pulldown.CRITICAL, true);
              this.$emit('refresh');
            }
            // console.log('panend:', e);
          }
        }
        function pan(e) {
          if (e.center.y >= window.innerHeight) {
            panEnd.call(this, e);
            return;
          }
          if (this.pulldown.enable) {
            const y = this.getPulldownHeight(e);
            // console.log('pan:', y);
            this.setPulldownHeight(y);
          }
          // console.log(e);
        }
        mc = new Hammer(this.$el, { touchAction: 'pan-y', inputClass: Hammer.TouchInput, });
        mc.add(new Hammer.Pan({ direction: Hammer.DIRECTION_ALL, threshold: 0 }));
        mc.on('panstart', e => {
          panStart.call(this, e);
        });
        mc.on('panup', e => {
          pan.call(this, e);
        });
        mc.on('pandown', e => {
          pan.call(this, e);
        });
        mc.on('panend', e => {
          panEnd.call(this, e);
        });
      },
      getPulldownHeight(e) {
        const y = e.deltaY * 0.35;
        if (this.pulldown.lastY > 0) {
          return this.pulldown.lastY + y;
        }
        return y;
      },
      setPulldownHeight(h, end = false) {
        const height = h;
        this.pulldown.y = height;
        this.pulldown.end = end;
        this.pulldown.refresh = height >= this.pulldown.CRITICAL;
        if (end) {
          this.pulldown.lastY = height;
          this.$el.querySelector('.scroller-inner').scrollTop = 0;
          this.pulldown.enable = height > this.pulldown.CRITICAL;
        }
      },
      scrollDown(distance) {
        const that = this;
        clearTimeout(this.__scroll_down_code);
        that.__scroll_down_code = setTimeout(() => {
          // console.log('scroll-bottom', that.scrollDirection);
          if (that.scrollDirection == Direction.DOWN) {
            // that.$emit('scroll-bottom', distance);
            that.$emit('scroll-down', distance);
          }
        }, 50);
      },
    },
    watch: {
      /**
       * iOS uiwebview scroll事件在滚动停止后才触发
       * iOS wkwebview scroll事件在滚动过程中实时触发，且惯性滚动回弹阶段也会实时触发scroll事件
       *
       * */
      scrollTop(val, oldVal) {
        // console.log(val, oldVal, val > oldVal);
        this.scrollDirection = val >= oldVal ? Direction.DOWN : Direction.UP;
      },
      refreshAction(val, oldVal) {
        this.$ua && this.$ua.trackEvent('Page', 'Refresh', this.$route.path, 1);
        setTimeout(() => {
          this.pulldown.y = 0;
          this.pulldown.lastY = 0;
        }, 200);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .scroller {
    position: relative;
  }
  .scroller-inner{
    touch-action: manipulation;
    height:100%;
  }
  .pulldown {
    height:50px;
    line-height:50px;
    color:gray;
    text-align: center;
    overflow: hidden;
    position: absolute;
    left: 0;
    width: 100%;
    top: 0;
  }
  .pulldown-line{
    display: block;
    width: 0;
    height: 2px;
    background: #03A9F4;
    position: absolute;
    top: 1px;
    left: 0;
    display:none;
  }

  .error {
    text-align: center;
    padding: 10px;
    margin: 20px;
    &::before {
      content: '\E64f';
      font-family: iconfont;
      display: block;
      font-size: 38px;
      color: #3a3a3a;
    }
  }
  .error {
    margin-top:30%;
  }
</style>
