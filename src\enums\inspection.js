import { createEnums } from './utils';

/**
 * version: 1.x
 */
export const InspectionStatus = createEnums({
  PENDING: ['待反馈', 0, '待反馈'],
  RESOLVED: ['已通过', 1, '通过审核'],
  REJECTED: ['未通过', 2, '未通过审核'],
});

// -1：全部 0：代付款，1：可使用，2：待评价 5：退款
export const InspectionOrderStatus = createEnums({
  UNPAID: ['未付款', 0, ''],
  // PAID: ['已付款', 1, ''],

  TO_BE_SERVE: ['未消费', 1, ''],
  // SERVED: ['已服务', 2, ''],

  UN_COMMENTED: ['未评价', 2, ''],
  COMMENTED: ['已评价', 3, ''],

  REFUNDING: ['退款中', 4, ''],
  REFUNDED: ['已退款', 5, ''],

  INVALID: ['已失效', -1, '未付款'],
  EXPIRED: ['已过期', 6, '有效期已过'],
  TIMEOUT: ['已失效', 7, '订单超时失效'],
  REFUND_APPLIED: ['待退款', 8, '已申请退款'],

  // 保养订单状态
  UN_CONFIRM: ['未确认', 9, '待确认'],
  CONFIRMING: ['未确认', 13, '已确认，待支付'],
  CONFIRMED: ['已确认', 10, '已确认，待服务'],
  SERVING: ['服务中', 11, '服务中'],
  SERVIED: ['未提车', 12, '服务结束，待提车'],
});

/**
 * version: 2.0
 * online: 线上审车，无须线下校验，即免检审车
 * offline: 线下审车，需要线下检验，即上线审车
 *  INSPECTION_ONLINE(1), INSPECTION_EXEMPTION(2),REPLACEDRIVERLICENSE(3),REPLACETRAVELLICENSE(4),REPLACENUMBERPLATE(5);
上线
免检
驾驶证补换
行驶补换
车牌补换
 */

export const InspectionOrderType = createEnums({
  // OFFLINE: ['上线审车', 1, ''],
  // ONLINE: ['免检审车', 2, ''],
  ONLINE_INSPECTION: ['上线审车', 1, ''],
  NEW_ENERGY_INSPECTION: ['新能源汽车上线审车', 101, ''],
  VAN_INSPECTION: ['面包车上线审车', 102, ''],
  NO_EXAM_INSPECTION: ['免检审车', 2, ''],

  DRIVER_LICENCE: ['补驾驶证', 3, ''],
  DRIVING_LICENCE: ['补换行驶证', 4, ''],
  CAR_PLATE: ['补换车牌', 5, ''],

  AGENT_INSPECTION: ['代办审车', 6, ''],
  CAR_REGISTER: ['新车上牌', 7, ''],
  CAR_TRANSFER: ['二手车过户', 8, ''],
});

export const VehicleBusinessType = createEnums({
  OFFLINE: ['上线审车', 1, ''],
  NEW_ENERGY_INSPECTION: ['新能源汽车上线审车', 101, ''],
  VAN_INSPECTION: ['面包车上线审车', 102, ''],
  ONLINE: ['免检审车', 2, ''],
  DRIVER_LICENCE: ['补驾驶证', 3, ''],
  DRIVING_LICENCE: ['补换行驶证', 4, ''],
  CAR_PLATE: ['补换车牌', 5, ''],
  AGENT_INSPECTION: ['代办审车', 6, ''],
  CAR_REGISTER: ['新车上牌', 7, ''],
  CAR_TRANSFER: ['二手车过户', 8, ''],
});

// 免检审车审核状态
export const Inspection2OnlineVerifyStatus = createEnums({
  PENDING: ['处理中', 3, ''],
  VALID: ['过审', 1, ''],
  INVALID: ['未过审', 2, ''],
});

// 免检审车订单状态，需要配合Inspection2OnlineVerifyStatus，审核通过后使用此状态判断
export const Inspection2OnlineOrderStatus = createEnums({
  UNPAID: ['审核通过', 0, '审核通过'],
  PAID: ['已付款', 1, '已付款'],
  // REFUNDED: ['已退款', 8, '已退款'],
  PICKING_UP: ['顺丰上门取件', 10, '顺丰上门取件'],
  OVER: ['已办结', 9, '已办结'],
  // REFUNDING: ['退款中', 4, '退款中'],
  REFUNDED: ['已退款', 5, '已退款'],
  FINISHED: ['已完成', 2, '已完成'],
  TIMEOUT: ['已失效', 7, '已失效'],
});

// 上线审车订单状态
export const Inspection2OfflineOrderStatus = createEnums({
  UNPAID: ['未付款', 0, '待付款'],
  PAID: ['已付款', 1, '待审车'],
  OVER: ['已核销', 2, '已核销'],
  COMMENTED: ['已评论', 3, '已完成'],
  REFUND_PENDING: ['退款处理中', 8, '退款中'],
  REFUNDING: ['退款中', 4, '退款中'],
  REFUNDED: ['已退款', 5, '已退款'],
  EXPIRED: ['已过期', 6, '已过期'],
  TIMEOUT: ['已失效', 7, '已失效'],
});

// 目前业务：反馈后才能发表评论
export const Inspection2OfflineVerifyStatus = createEnums({
  VALID: ['过审', 1, ''],
  INVALID: ['未过审', 2, ''],
});
