<template>
  <div class="swiper-container">
    <div class="swiper-wrapper">
      <slot></slot>
    </div>
    <!-- Add Pagination -->
    <div v-if="pagination" ref="pagination" class="swiper-pagination"></div>
    <div v-if="scrollbar" ref="scrollbar" class="swiper-scrollbar"></div>
    <slot name="extra"></slot>
  </div>
</template>
<style src="@/lib/swiper/5.4.5/css/swiper.min.css"></style>

<style lang="scss" scoped>
.swiper-container {
  width: 100%;
  height: 200px;
}
.swiper-pagination-bullet {
  width: 10px;
  height: 3px;
  border-radius: 0;
}
.swiper-pagination-bullet-active {
  background: white;
}
</style>
<script>
/**
 * 1: 同一个项目，不能同时安装两个版本的swiper，为兼容使用了swiper3.x的老代码，swiper4.x通过文件形式引入
 */
// import Swiper from 'swiper';
import Swiper from '@/lib/swiper/5.4.5/js/swiper.min';
// import Swiper from '@/lib/swiper/5.4.5/js/swiper.esm.bundle';

export default {
  name: 'Swiper',
  props: {
    slides: Array,
    pagination: {
      type: Boolean,
      default: false,
    },
    scrollbar: {
      type: Boolean,
      default: false,
    },
    value: [Number, Object, String],
    options: {
      type: Object,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.init();
    });
  },
  destroyed() {
    this.swiper && this.swiper.destroy(true, false);
  },
  activated() {
    if (this.autoplayed) {
      this.setAutoplay(true);
    }
  },
  deactivated() {
    this.autoplayed = this.swiper.autoplaying;
  },
  data() {
    return {
      autoplayed: false,
      swiper: null,
    };
  },
  watch: {
    slides(val, oldVal) {
      console.log('update slide size ...');
      this.refresh();
    },
  },
  components: {},
  methods: {
    init() {
      const $el = this.$el;
      const that = this;
      const settings = Object.assign(
        {
          // pagination: '.swiper-pagination',
          slidesPerView: 'auto',
          centeredSlides: true,
          clickable: true,
          spaceBetween: 20,
          autoplay: true, //等同于以下设置
          /*autoplay: {
            delay: 3000,
            stopOnLastSlide: false,
            disableOnInteraction: true,
          },*/
          iOSEdgeSwipeDetection: true, // iOS边缘滑动检测
          effect: 'slide',
          // centeredSlides: true,
          on: {
            click: function (e) {
              // console.log('click: ', this.realIndex)
              // alert(this.realIndex)
              that.$emit('click', this.realIndex);
              // e.preventDefault();
            },
            slideChange: function () {
              that.setActiveSlide(this.activeIndex);
              // console.log('slideChange: ', this.activeIndex);
            },
          },
        },
        this.options
      );
      // 分页指示器
      if (this.pagination) {
        settings.pagination = {
          el: this.$refs.pagination,
        };
      }
      // 滚动条
      if (this.scrollbar) {
        settings.scrollbar = Object.assign(
          {
            el: this.$refs.scrollbar,
          },
          settings.scrollbar
        );
      }
      setTimeout(() => {
        const swiper = this.swiper;
        if (swiper) swiper.destroy(true, false);
        this.swiper = new Swiper($el, settings);
      }, 1);
    },
    refresh() {
      this.swiper.update();
    },
    next() {
      this.swiper.slideNext();
    },
    prev() {
      this.swiper.slidePrev();
    },
    slideTo(index) {
      this.swiper.slideTo(index);
    },
    setAutoplay(flag) {
      const autoplay = this.swiper.autoplay;
      autoplay.stop();
      if (flag) {
        autoplay.start();
      }
    },
    setActiveSlide(index) {
      let emitData = index > 0 ? index : 0;
      this.$emit('input', emitData);
      this.$emit('change', emitData);
    },
  },
};
</script>
