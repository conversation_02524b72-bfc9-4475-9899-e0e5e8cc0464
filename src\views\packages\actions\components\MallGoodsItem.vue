<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  $item-border-color: #dadada;
  .item {
    // padding: 5px;
    position: relative;
    .tag{
      position: absolute;
      left: 0;
      top: 0;
      background: #FFF2E8;
      padding-right: 20px;
      border-radius: 20px 0px 20px 0px;
      line-height: 40px;
      font-size: 22px;
      color: $lh-2022-primary-color;
      z-index: 1;
      display: flex;
      align-items: center;
      transform: scale(.5);
      transform-origin: 0 0;
      span{
        background: linear-gradient(140deg, #FF5331, #FC360E);
        border-radius: 10px 0px 20px 0px;
        padding-left: 12px;
        padding-right: 12px;
        font-size: 24px;
        color: #ffffff;
        margin-right: 14px;
      }
    }
    .item-title {
      font-weight: 500;
      color: #272727;
      line-height: 1.4;
      font-size: 14px;
      // height: 42px;
      display: flex;
      word-break: break-all;
      // justify-content: center;
      // align-items: center;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
    }
    .item-logo ::v-deep{
      width: 100%;
      height: 180px;
      margin-bottom: 5px;
      border-radius: 5px 5px 0 0;
      img{
        border-radius: 10px 10px 0 0;
      }
    }
    .item-content {
      flex: 1;
      // @include border-bottom($item-border-color, 'after');
      padding: 0 8px;
      // margin-left: 5px;
      justify-content: space-between;
    }
    &:last-child {
      .item-content::after {
        display: none;
      }
    }
    .item-attr {
      // display: flex;
      align-items: center;
      margin-top: 5px;
      .rmb {
        line-height: 22PX;
        &::after {
          content: attr(data-precision);
          font-size: 0.8em;
        }
      }
    }
    .vip-price-tag {
      padding: 0 4px 0 4px;
      padding-left: 22px;
      background: #584D42;
      background: url(../assets/images/icon-vip.png) 4px 42% no-repeat #584D42;
      background-size: 16px;
      color: #EAC55A;
      font-size: 14px;
      border-radius: 5px 0 5px 0;
      white-space: nowrap;
      transform: scale(0.7);
      margin-left: -5px;
      font-weight: 700;
      display: inline-block;
    }
    .vip-price {
      font-weight: 700;
      color: #FE3E3E;
      margin-right: 5px;
      // margin-left: 5px;
      font-size: 18px;
      // font-family: "PingFang SC", sans-serif, "Helvetica Neue", Helvetica;
      &::before {
        font-size: 0.6em;
        font-weight: 500;
      }
    }
    .price {
      // text-decoration:line-through;
      color: #333333;
      display: inline-block;
      font-size: 13px;
      font-weight: 700;
      &::before {
        font-size: 0.6em;
      }
    }
    .tags {
      list-style: none;
      display: block;
      margin-top: 0;
      li {
        display: inline-block;
        border: 1px solid;
        padding: 0px 4px;
        border-radius: 2px;
        font-weight: 400;
        font-size: 12px;
        color: #ff5b0d;
        line-height: 1.4;
        margin-right: 5px;
      }
    }
  }
  .promotion-words{
    height: 20px;
    overflow: hidden;
    span{
      display: inline-block;
      padding: 0 8px;
      border: 2px solid #FD4925;
      border-radius: 4px;
      font-size: 20px;
      color: #FD4925;
      height: 28px;
      line-height: 28px;
      transform: scale(.5);
      transform-origin: 0 0;
      -webkit-text-size-adjust: none;
    }
  }
</style>
<template>
  <div class="item flex-col">
    <biz-image
      class="item-logo"
      :src="goods.image"
      type="?imageView2/1/w/500/h/500/format/jpg/q/100"
      :lazy="false"
    >
    </biz-image>
    <div class="item-content flex-col">
      <div class="item-title">
        <span>{{goods.title}}</span>
      </div>
      <div v-if="goods.promotionWords" class="promotion-words">
        <span>{{goods.promotionWords}}</span>
      </div>
      <div class="item-attr">
        <i class="vip-price-tag">VIP</i>
        <strong class="rmb vip-price" :data-precision="getPrecision(goods.vipPrice)">{{Math.floor(goods.vipPrice)}}</strong>
        <strong class="rmb price" :data-precision="getPrecision(goods.salesPrice)">{{Math.floor(goods.salesPrice)}}</strong>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, formatMoney, formatPrice } from '@/utils';
import { mixinAuthRouter } from '@/mixins';
/**
 *  用于下单填写收货地址页面显示商品信息
 */
export default {
  name: 'MallGoodsItem',
  mixins: [mixinAuthRouter],
  props: {
    goods: {
      type: Object,
      default() {
        return {};
      }
    },
  },
  components: {},
  data() {
    return {};
  },
  computed: {
    vipDiscountAmount() {
      return formatPrice(this.goods.minGoodsPrice - this.goods.minGoodsVipPrice)
    }
  },
  methods: {
    getPrecision(value) {
      const result = String(value).split('.');
      return result.length > 1 ? `.${result[1]}` : '';
    }
  }
};
</script>
