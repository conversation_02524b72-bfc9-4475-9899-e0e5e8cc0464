<template>
  <content-view
    ref="content"
    :status="status"
    :refreshAction="refreshAction"
    @refresh="refresh"
    @reload="reload"
    @reach-bottom="loadMore"
    :on-reach-bottom-distance="400"
  >
    <template v-if="(status == AppStatus.READY || status == AppStatus.LOADING) && showFilter">
      <tabs slot="head" class="tabs-orders" v-model="orderStatus" :tabs="orderTabs" @change="switchOrderType"></tabs>
    </template>
    <template v-if="status == AppStatus.READY">
      <transition-group name="order-list" tag="div" class="orders">
      <div
        v-for="item in orders"
        :class="getOrderClass(item)"
        :key="item.id"
        v-finger:longTap="onLongTap"
        @click="go('/order/' + item.id)"
      >
        <c-picture :src="getGoodsLogo(item.goodsLogo)" @click.stop="goShop(item)" class="order-goods-logo"></c-picture>
        <div class="order-content">
          <div class="order-head flex-row">
            <div class="flex-item order-title">
              <div class="order-name" @click.stop="goShop(item)">{{item.shopName}}</div>
              <svg class="icon-arrow-right" @click.stop="goShop(item)">
                <use xlink:href="#icon-arrow-right"></use>
              </svg>
            </div>
            <span class="order-status">{{displayOrderStatus(item.orderStatus, item)}}</span>
          </div>
          <div class="flex-row order-body">
            <!-- <c-picture :src="getGoodsLogo(item.goodsLogo)" class="order-goods-logo"></c-picture> -->
            <ul class="flex-item flex-col order-attrs">
              <li>
                <!-- <span class="order-attr__name">服务项目:</span> -->
                <span class="order-attr__content">{{item.orderTitle}}</span>
              </li>

              <li v-if="item.orderType == OrderType.MAINTAIN && item.appointmentTime">
                <span class="order-attr__name">预约时间:</span>
                <span class="order-attr__content">{{parseAppointmentTime(item.appointmentTime)}}</span>
              </li>
              <li v-else-if="item.orderType == OrderType.VEHICLE_INSPECTION && item.inspectionAppointmentTime">
                <span class="order-attr__name">预约时间:</span>
                <span class="order-attr__content">{{formatDate(item.inspectionAppointmentTime, 'yyyy-MM-dd HH:mm')}}</span>
              </li>
              <li v-else-if="item.expireTime">
                <span class="order-attr__name">有效期至:</span>
                <span class="order-attr__content">{{formatDate(item.expireTime)}}</span>
              </li>

              <li>
                <span class="order-attr__name">金额:</span>
                <span class="order-attr__content"> <b class="rmb rmb-normal">{{item.price}}</b> </span>
              </li>
            </ul>
          </div>
          <div class="order-foot flex-row">
            <div class="flex-item order-tip" :class="{ 'order-status-highlight': item.orderStatus == OrderStatus.UNPAID }">{{displayOrderTip(item)}}</div>
            <a v-if="item.orderStatus == OrderStatus.INVALID || item.orderStatus == OrderStatus.TIMEOUT" href="javascript:;" @click.stop="removeOrder(item.id)" class="weui-btn weui-btn_mini weui-btn_default">删除订单</a>
            <a v-else-if="item.orderStatus == OrderStatus.UNPAID" href="javascript:;" @click.stop="goToPay(item)" class="weui-btn weui-btn_mini weui-btn_warn order-btn order-btn__primary">立即付款</a>
            <a v-else-if="item.orderStatus == OrderStatus.TO_BE_SERVE" href="javascript:;" @click.stop="go('/order/' + item.id)" class="weui-btn weui-btn_mini weui-btn_warn order-btn">查看券码</a>
            <a v-else-if="item.orderStatus == OrderStatus.UN_COMMENTED && item.orderType != OrderType.VEHICLE_INSPECTION" href="javascript:;" @click.stop="$_router_push4inputting('/order/'+item.id + '/comment?from=list', { title: '发表评论' })" class="weui-btn weui-btn_mini weui-btn_warn order-btn">立即评价</a>
            <a v-else-if="item.orderStatus == OrderStatus.UN_COMMENTED && item.orderType == OrderType.VEHICLE_INSPECTION && item.inspectionStatus != InspectionStatus.PENDING" href="javascript:;" @click.stop="go('/order/'+item.id)" class="weui-btn weui-btn_mini weui-btn_warn order-btn">查看审车结果</a>
            <!-- <a v-else-if="item.orderStatus == OrderStatus.SERVIED" href="javascript:;" @click.stop="confirmGetCar(item)" class="weui-btn weui-btn_mini weui-btn_warn order-btn">确认提车</a> -->
            <!-- <a v-else-if="item.orderStatus == OrderStatus.EXPIRED && item.orderType == OrderType.WASH" href="javascript:;" @click.stop="go('/order/'+item.id + '/refund')" class="weui-btn weui-btn_mini weui-btn_warn order-btn">申请退款</a> -->
          </div>
        </div>
      </div>
      <!-- <div v-if="!orders.length" key="empty" class="order order-empty">没有记录</div> -->
      <!-- <div key="loadmore" v-if="orders.length >= 10"  :class="{'scroll-loading': true }" v-html="scroll.text"></div> -->
      </transition-group>
      <list-loader v-if="orders.length > 5" :options="$_loader_options" @load="loadMore"></list-loader>
      <list-placeholder v-if="!orders.length" icon="~@/assets/images/mall/empty.png">没有记录</list-placeholder>
    </template>
  </content-view>
</template>

<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';

  $border-color: #ececec;
  .tabs-orders{
    background: white;
    box-shadow: 0 1px 5px 1px #e7eaf5;
    z-index: 1;
  }
  .orders{
    position:relative;
  }
  .order-goods-logo{
    width:40px;
    height:40px;
    border-radius: 2px; /* px */
    margin: 3px 3px 0 0;
  }
  .order{
    background:white;
    padding:13px 0 6px 10px;
    margin:10px 0;
    display: flex;
    &.order-end .order-status{
      color:#AFAFAF;
    }
  }
  .icon-arrow-right {
    color: #6f6f6f;
    font-size: 14px;
    transform: scale(0.8);
  }
  .order-content {
    flex: 1;
  }
  .order-attrs{
    list-style:none;
    margin-top: 2px;
  }
  .order-foot {
    @include border-top($border-color, 'before');
    // border-top:1px solid $border-color;
    padding-top: 5px;
    margin-left: 5px;
    margin-top: 4px;
    padding-right: 10px;
  }
  .order-title {
    // color: gray;
    font-weight: 700;
    margin-left: 5px;
    font-size: 16px;
  }
  .order-name {
    display: inline-block;
    max-width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: top;
  }
  .order-head {
    padding-right: 10px;
    // margin-top: 5px;
  }
  .order-end{
    .order-head::before{
     filter: grayscale(1);
    }
  }
  .order-attr__name {
    margin-right: 5px;
  }
  .order-body {
    padding: 0 5px;
    align-items: center;
    font-size: 14px;
    color: #8a8a8a;
    color: #696969;
  }
  .order-status{
    color:#F2903F;
    font-size:.9em;
  }
  .weui-btn+.weui-btn{
    margin-top:auto;
  }
  .weui-btn{
    border-radius: 3px;
    border-radius: 3px;
    background: white;
    box-shadow: none;
    border: 1px solid #cecece;
    // @include border-1px();
    padding: 0 .6em;
    line-height: 1.9;
    margin-left: 5px;
    &::after{
      display:none;
    }
  }
  .order-btn {
    color: #EE6E00;
    border-color: #f59645;
    background: white;
    line-height: 1.8;
    &:not(.weui-btn_disabled):active{
      background-color:#f59645;
      color:white;
    }
    &.order-btn__primary {
      background: #f59645;
      color: white;
    }
  }

  .order-tip{
    // margin-left:5px;
    color:#9D9D9D;
    font-size:0.9em;
    margin-top: 2px;
    &.order-status-highlight {
      color: #f59645;
    }
  }
  .order.order-empty {
    text-align: center;
    justify-content: center;
    background: transparent;
    color: gray;
    margin: 20px;
  }
  
  /*.order {
    transition: transform 300ms;
  }*/

 .scroller{
    overflow-y:hidden;
  }
  .vscroller{
    height:600PX;
    overflow-y:scroll;
  }
  .order{
    box-sizing: border-box;
  }
</style>
<script>
import { formatDate, parseAppointmentTime } from '@/utils';
import { Tabs } from '@/components';
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import { OrderStatus, AppStatus, OrdersType, OrderType, ReOrderType, OrderListStatus } from '@/enums';
import { InspectionStatus } from '@pkg/vehicle-business/enums';
import { dialog, toast, loading } from '@/bus';
import { getOrderList, deleteOrder, confirmGetCar } from '@/api';

function getInitData(data = {}) {
  const orderTabs = data.orderType == OrdersType.INSPECTION ? OrderListStatus.list().filter(item => item.value != OrderListStatus.TO_BE_COMMENT) : OrderListStatus.list();
  const data2 = Object.assign({
    OrderStatus,
    AppStatus,
    OrderType,
    OrderListStatus,
    InspectionStatus,
    status: AppStatus.LOADING,
    orders: [],
    refreshAction: 1,
    orderType: '',
    pageTitle: '我的订单',
    orderStatus: OrderListStatus.ALL.value,
    // orderTabs: OrdersType.list(),
    orderTabs,
  }, data);
  return data2;
}

function parseOrderListStatus(value) {
  const alias = {
    'unused': OrderListStatus.UNUSED.value,
    'unpaid': OrderListStatus.UNPAID.value,
    'to_be_comment': OrderListStatus.TO_BE_COMMENT.value,
    'refund': OrderListStatus.REFUND.value,
  }
  const e = OrderListStatus.getEnum(alias[value]);
  return e ? e.value : OrderListStatus.ALL.value;
}

const PAGE_SIZE = 10;
export default {
  name: 'BeautyOrdersView',
  props: {
    showFilter: {
      type: Boolean,
      default: true,
    }
  },
  components: {
    Tabs,
  },
  mixins: [mixinLoader, mixinAuthRouter],
  data() {
    const orderType = this.$route.params.type;
    const orderStatus = parseOrderListStatus(this.$route.params.status);
    const pageTitle = this.$route.params.type != OrdersType.INSPECTION ? '我的订单' : '审车订单';
    const data = getInitData({ orderType, orderStatus, pageTitle })
    // console.log('data:', data);
    return data;
  },
  computed: {
    filterParams() {
      return {
        ...this.$_loader_params,
        type: this.orderType,
        status: this.orderStatus,
      }
    },
  },
  deactivated() {
    console.log(this.$options.name, 'deactivated...');
  },
  methods: {
    ...{ parseAppointmentTime, formatDate },
    go(url) {
      this.$router.push(url);
    },
    onLeave() {
      console.log('leave');
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.refreshQuietly();
    },
    refreshQuietly() {
      const currentPage = this.$_loader_getPage();
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(this.orders.length);
      return this.getList().then(res => {
        this.$_loader_setPage(currentPage);
        this.$_loader_setPageSize(PAGE_SIZE);
        return res;
      }, err => {
        // this.status = AppStatus.ERROR;
        toast().tip('刷新失败，请重试！');
        console.error(err);
      });
    },
    init() {
      const orderTabs = this.$route.params.type == OrdersType.INSPECTION ? OrderListStatus.list().filter(item => item.value != OrderListStatus.TO_BE_COMMENT) : OrderListStatus.list();
      this.orderTabs = orderTabs;
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(PAGE_SIZE);
      return this.getList().then(res => {
        if (this.status != AppStatus.READY) {
          this.status = AppStatus.READY;
        }
        return res;
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    goShop(item) {
      if (item.orderType == OrderType.WASH) {
        const path = `/shop/${item.bid}/wash`;
        this.$_router_pageTo(path);
      }
    },
    getGoodsLogo(str) {
      return str && str.split(',')[0];
    },
    removeOrder(oid) {
      const that = this;
      dialog().confirm('确定要删除该订单吗？', {
        title: '提示',
        ok() {
          loading(true, '正在提交...');
          deleteOrder(oid).then(function () {
            that.orders = that.orders.filter(item => item.id != oid);
            toast().tip('删除成功');
            loading(false);
          }, e => {
            loading(false);
            toast().tip('删除失败');
          });
        }
      });
    },
    // 确认提车
    confirmGetCar(order) {
      const that = this;
      const oid = order.id;
      dialog().confirm('您确认提车吗？', {
        title: '提示',
        ok() {
          loading(true, '正在提交...');
          confirmGetCar(oid).then(res => {
            loading(false);
            toast().success('确认成功');
            that.onResume();
          }, err => {
            loading(false);
            err && dialog().alert(err, {
              title: '',
            });
          });
        }
      })
    },
    refresh() {
      this.init().then(res => {
        this.refreshAction = Date.now();
      }).catch(e => {
        toast().tip('刷新失败');
        this.refreshAction = Date.now();
      });
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    switchOrderType() {
      // this.status = AppStatus.LOADING;
      // console.log(this);
      loading(true);
      this.init().then(res => {
        loading(false);
        this.$refs.content.scrollTo(0);
      }).catch(e => {
        loading(false);
        toast().tip(e);
        this.status = AppStatus.ERROR;
      });
    },
    getList(page = 1) {
      const params = { ...this.filterParams, page };
      // console.log('getList', params);
      return this.$_loader_bind(getOrderList, res => {
        if (page === 1) {
          this.orders = res;
        } else {
          this.orders = this.orders.concat(res);
        }
        return res;
      }).load(params).catch(err => {
        toast().tip(err);
        return Promise.reject(err);
      })
    },
    loadMore(e) {
      if (!this.$_loader_couldLoadMore) return;
      const nextPage = this.filterParams.page + 1;
      this.getList(nextPage).catch(e => {
        toast().tip(e);
        console.error(e);
      })
    },
    getOrderClass(item) {
      const status = OrderStatus.getEnum(item.orderStatus);
      const orderType = item.orderType;
      return {
        'order': true,
        'order-type-wash': orderType == OrderType.WASH,
        'order-type-maintain': orderType == OrderType.MAINTAIN || orderType == OrderType.SHEET_METAL_OR_REPAIR || orderType == OrderType.MAINTAIN_RESERVE,
        'order-end': status == OrderStatus.TIMEOUT || status == OrderStatus.INVALID || status == OrderStatus.EXPIRED || status == OrderStatus.COMMENTED,
      };
    },
    goToPay(item) {
      const { id, sysOrderId } = item;
      // 接口参数暂时不需要订单类型
      const paymentInfo = {
        soid: `${sysOrderId}-${id}`,
        nextPath: `/order/${id}`,
      }
      this.$_route_cashierCheckout(paymentInfo);
    },
    onLongTap(item) {
      console.log('long tap :', item);
    },
    displayOrderStatus(stat, order) {
      const status = OrderStatus.getEnum(stat);
      if (status === null) return `异常${stat}`;
      if (order.orderType == OrderType.VEHICLE_INSPECTION) {
        if (status == OrderStatus.UN_COMMENTED) {
          return '已核销';
        }
      }
      return status.getName();
    },
    displayOrderTip(order) {
      const status = OrderStatus.getEnum(order.orderStatus);
      if (status === null) return '异常';
      const unpayExpireTime = formatDate(order.unpayExpireTime, 'HH:mm');
      switch (status) {
        case OrderStatus.TIMEOUT:
          return '订单超时未付款已失效';
        case OrderStatus.INVALID:
          return '您的订单已失效';
        case OrderStatus.UNPAID:
          return `${unpayExpireTime} 前付款有效`;
        case OrderStatus.UN_COMMENTED:
          if (order.orderType == OrderType.VEHICLE_INSPECTION) {
            if (order.inspectionStatus == InspectionStatus.PENDING) return '车辆正在审核中';
            if (order.inspectionStatus == InspectionStatus.RESOLVED) return '您的车辆已过审';
            if (order.inspectionStatus == InspectionStatus.REJECTED) return '您的车辆未通过审核';
          }
          return '请对服务做出评价';
        case OrderStatus.COMMENTED:
          return '感谢您的信任，欢迎下次光临';
        case OrderStatus.REFUND_APPLIED:
          return '您的退款已提交商家处理';
        case OrderStatus.REFUNDING:
          return '退款正在处理中...';
        case OrderStatus.REFUNDED:
          return '您的订单退款已原路退回';

        case OrderStatus.TO_BE_SERVE:
          if (order.orderType == OrderType.VEHICLE_INSPECTION) {
            return '请按预约时间前往审车';
          }
          return '请及时在有效期内使用';
        case OrderStatus.UN_CONFIRM:
          return '您的订单信息有变动，点击查看详情';
        case OrderStatus.CONFIRMING:
          return '请及时补缴金额';
        case OrderStatus.CONFIRMED:
          return '即将对您的爱车进行保养';
        case OrderStatus.SERVING:
          return '您的爱车正在保养中...';
        case OrderStatus.SERVIED:
          return '您的爱车已保养完毕，请及时提车';
        default:
          return '';
      }
    },
  }
};
</script>
