<template>
  <prize-pop-base :prize="prize" :prizeType="prizeType" :prizeTip="prizeTipText" v-on="$listeners">
    <template #content>
      <div class="prize-name">
        <span class="name">{{ prize.thirdPlatformCouponCode || prize.prizeName }}</span>
      </div>
    </template>
  </prize-pop-base>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';
import PrizePopBase from '../PrizePopBase.vue';

export default {
  name: 'PrizePopCouponCode',
  components: {
    PrizePopBase
  },
  props: {
    prize: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prizeType: PrizeTypeEnum.COUPONCODE.valueOf()
    };
  },
  computed: {
    receiveDesc() {
      return '请及时使用兑换码';
    },
    prizeTipText() {
      return '凭借兑换码在郑州市锅圈食汇门店，即可领取锅圈食汇小龙虾畅享装650g一盒';
    }
  }
};
</script>

<style lang="scss" scoped>
.prize-name {
  font-size: 20px;
  color: #ff273b;
  text-align: center;
  margin-bottom: 12px;
}
</style>
