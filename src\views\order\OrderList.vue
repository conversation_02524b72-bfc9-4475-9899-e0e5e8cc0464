/* eslint-disable vue/no-use-v-if-with-v-for */
<template>
  <content-view
    :status="status"
    :refreshAction="refreshAction"
    @refresh="refresh"
    @reload="reload"
    @scroll-bottom="loadMore"
    class="orders-content-wrap"
  >
    <template v-if="status == AppStatus.READY && !loginGuidance">
      <transition-group name="order-list" tag="div" class="orders-wrap">
        <template v-for="item in orders">
          <!-- 此处用template包裹，防止后台数据item=null情况 -->
          <div v-if="item" :key="item.id" class="order-item">
            <div class="order-business">
              <!-- <div class="order-business-name">
                <p>{{
                  (item.businessInfo && item.businessInfo.name) || "交广领航"
                }}</p>
                箭头先屏蔽
                <svg class="icon-arrow-right">
                  <use xlink:href="#icon-arrow-right"></use>
                </svg>
              </div> -->
              <div class="order-id">
                <p>
                  订单:
                  <span>{{ item.orderId }}</span>
                </p>
              </div>
              <span
                class="order-status"
                :class="{
                  'order-status-highlight':
                    item.status == 1 && !isTimeout(item),
                }"
                v-if="showStatus(item.status)"
                >{{ formatOrderStatus(item) }}</span
              >
            </div>
            <div class="order-goods-info" @click="goDetail(item)">
              <!-- @click.stop="item.orderType=='group-buy'?go(`/mall/goods/${item.goodsInfo.goodsId}`):null;" -->
              <biz-image
                :src="getGoodsLogo(item)"
                class="order-goods-logo"
                :lazy="true"
              >
              </biz-image>
              <div class="order-goods-name">{{ item.title }}</div>
              <div class="order-goods-price">
                <p>
                  <i>￥</i
                  >{{
                    ['recharge', 'direct-recharge'].includes(item.orderType)
                      ? item.amount
                      : item.userPayAmount
                  }}
                </p>
                <span>共{{ item.count }}件</span>
              </div>
            </div>
            <div class="order-goods-btns" v-if="showBtns(item)">
              <!-- <span class="pay-btn">评价</span> -->
              <!-- <span
                class="pay-btn"
                v-if="item.status == 4 && item.orderType == 'group-buy'"
                @click="confirmExtended(item.id)"
                >延长收货</span
              > -->
              <span
                class="pay-btn"
                v-if="showDeleteBtn(item)"
                @click="deleteOrder(item.id)"
                >删除订单</span
              >
              <span
                class="pay-btn"
                v-if="item.status == 3"
                @click="remindDelivery(item.id)"
                >提醒发货</span
              >
              <span
                class="pay-btn"
                v-if="item.status == 4 && item.orderType == 'group-buy'"
                @click="showLogistics(item)"
                >查看物流</span
              >
              <span
                class="pay-btn active"
                v-if="item.status == 4 && item.orderType == 'group-buy'"
                @click="confirmReceipt(item.id)"
                >确认收货</span
              >
              <span
                class="pay-btn active"
                v-if="item.status == 1 && !isTimeout(item)"
                @click="goToPay(item)"
                >付款</span
              >
            </div>
            <div
              v-if="
                item.status == 4 &&
                item.orderType == 'group-buy' &&
                item.groupByOrderPostInfo &&
                item.groupByOrderPostInfo[0]
              "
              @click="showLogistics(item)"
              class="logistics-info"
            >
              <span class="icon_jglh icon-a-kuaidiyunshuzhong"></span>
              <span>{{
                `${item.groupByOrderPostInfo[0].status} | ${item.groupByOrderPostInfo[0].context}`
              }}</span>
            </div>
          </div>
        </template>
      </transition-group>
      <list-loader
        v-if="orders.length > 5"
        :options="$_loader_options"
        @load="loadMore"
      ></list-loader>
      <list-placeholder
        v-if="!orders.length"
        icon="~@/assets/images/mall/empty.png"
        >您还没有相关订单</list-placeholder
      >
    </template>
    <!-- 未登录状态引导用户登录 -->
    <template v-else>
      <list-placeholder icon="~@/assets/images/mall/empty.png">
        <p class="login-tips">登录后才能查看订单哦</p>
        <van-button
          class="btn"
          round
          type="warning"
          size="small"
          @click="toLogin"
          >去登陆</van-button
        >
      </list-placeholder>
    </template>
    <!-- 物流弹窗 -->
    <van-popup
      v-model="show"
      closeable
      round
      position="bottom"
      class="logistics-action"
      close-icon-position="top-right"
      get-container="body"
    >
      <h2 class="title">物流明细</h2>
      <van-steps
        v-if="stepData.length > 0"
        direction="vertical"
        :active="0"
        active-color="#38f"
        class="steps"
      >
        <van-step v-for="(item, index) in stepData" :key="index">
          <h3>{{ item.context }}</h3>
          <p>{{ item.ftime }}</p>
        </van-step>
      </van-steps>
      <div class="empty" v-else>
        <div class="empty-icon"></div>
        <div class="empty-text">暂无物流信息</div>
      </div>
    </van-popup>
  </content-view>
</template>

<script>
import { formatDate } from '@/utils';
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import { AppStatus, OrderBizType, MallDeliveryType } from '@/enums';
import { NewOrdersStatus } from './enums';
import { dialog, toast, loading } from '@/bus';
import { login } from '@/bridge';
import { getTypeOrderList } from '@/api';
import {
  confirmMallOrderReceipt,
  getLogistics,
  getOrderReceipted,
  getOrderExtended,
  delMallOrder,
  addRemindDelivery,
} from '@pkg/mall/api';
import { Popup, Step, Steps, Button } from 'vant';

// localStorage.setItem('session', '279e1e03aa164fbe98b0b9e3a66a8c52')

function isSameModule(parent, child) {
  const parentBase = parent.split('/')[1];
  const childBase = child.split('/')[1];
  return parentBase === childBase;
}
const PAGE_SIZE = 10;
export default {
  name: 'OrderList',
  props: {
    category: {
      type: Number,
      default: null,
    },
  },
  mixins: [mixinLoader, mixinAuthRouter],
  components: {
    [Popup.name]: Popup,
    [Step.name]: Step,
    [Steps.name]: Steps,
    [Button.name]: Button,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      orders: [],
      refreshAction: 1,
      show: false,
      loginGuidance: false, // 登录提示
      stepData: [],
    };
  },
  // TODO: 改成使用mixin_loader的形式
  computed: {
    filterParams() {
      return {
        status: this.category, // 只查询待付款的订单
        ...this.$_loader_params,
      };
    },
  },
  // mounted() {
  //   this.init();
  // },
  methods: {
    // 是否显示操作按钮
    showBtns(item) {
      // 待付款
      if (
        item.status == 1 &&
        !this.isTimeout(item) &&
        item.orderType != 'recharge'
      ) {
        return true;
      }
      // 待发货
      if (item.status == 3) {
        return true;
      }
      // 待收货
      if (item.status == 4 && item.orderType == 'group-buy') {
        return true;
      }
      // 显示删除订单按钮
      if (
        item.status === 6 ||
        item.status === 7 ||
        item.status === -1 ||
        (item.status === 1 &&
          this.isTimeout(item) &&
          item.orderType != 'recharge')
      ) {
        return true;
      }
      return false;
    },
    // 是否显示订单状态
    showStatus(status) {
      if (this.category && this.category == 5) {
        // 售后/退款 不显示状态
        return false;
      }
      return true;
    },
    isTimeout(item) {
      // 订单超时显示已关闭
      // 话费充值5分钟
      // 美食、团购15分钟
      // 洗车，审车是30分钟
      let t = item.t || 1636600271000; // 默认值2021-11-11 11:11:11
      let in5seconds = ['recharge', 'direct-recharge'];
      let in10seconds = ['memberNew'];
      let in15seconds = ['group-buy', 'foods'];
      let in30seconds = ['car', 'inspection', 'car_inspection'];
      if (in5seconds.includes(item.orderType)) {
        return Date.now() - t > 300000;
      } else if (in10seconds.includes(item.orderType)) {
        return Date.now() - t > 600000;
      } else if (in15seconds.includes(item.orderType)) {
        return Date.now() - t > 900000;
      } else if (in30seconds.includes(item.orderType)) {
        return Date.now() - t > 1800000;
      }
      return Date.now() - t > 900000;
    },
    // 付款
    goToPay(item) {
      // 洗车
      const { id, sysOid } = item;
      let paymentInfo = '';
      if (item.orderType == 'car') {
        // 洗车养车
        paymentInfo = {
          soid: `${sysOid}-${id}`,
          nextPath: `/order/${id}`,
        };
      } else if (
        item.orderType == 'inspection' ||
        item.orderType == 'car_inspection'
      ) {
        // 审车、车务
        paymentInfo = {
          soid: sysOid,
          type: OrderBizType.VehicleInspection,
          nextPath: `/vehicle-business/order/${id}`,
        };
      } else if (item.orderType == 'card-buy') {
        // 洗车卡、保养卡
        // paymentInfo = require('./images/card-buy.png');
      } else if (item.orderType == 'action') {
        // 活动
        // paymentInfo = require('./images/action.png');
      } else if (
        item.orderType == 'recharge' ||
        item.orderType == 'direct-recharge'
      ) {
        // 话费充值
        // 原定不显示付款
      } else if (item.orderType == 'group-buy') {
        // 团购
        paymentInfo = {
          soid: sysOid,
          method: 'jump',
          nextPath: `/mall/order/${id}`,
        };
      } else if (item.orderType == 'foods') {
        // 美食
        paymentInfo = {
          soid: sysOid,
          nextPath: `/mallg2/order/${id}`,
        };
      } else if (item.orderType == 'memberNew') {
        // 购买会员
        // paymentInfo = require('./images/vip.png');
      }
      this.$_route_cashierCheckout(paymentInfo);
    },
    // 显示物流信息
    showLogistics(item) {
      loading(true, '查询中...');
      if (!item.id) return false;
      getLogistics(item.id)
        .then(res => {
          loading(false);
          this.show = !this.show;
          this.stepData = res;
          // this.stepData = []
        })
        .catch(e => {
          loading(false);
          e && dialog().alert(e);
        });
    },
    // 确认收货
    confirmReceipt(oid) {
      const that = this;
      const tip = `
        确认后代表您已收到货物<br>
        若未收到货物请<b style="color:red">慎重操作</b>
        `;
      dialog().confirm(tip, {
        title: '确认收货吗？',
        ok() {
          loading(true, '正在提交...');
          confirmMallOrderReceipt(oid).then(
            res => {
              loading(false);
              that.$_router_push(`/mall/order/${oid}/comment`);
              toast().success('确认成功');
              that.onResume();
            },
            err => {
              loading(false);
              err &&
                dialog().alert(err, {
                  title: '',
                });
            }
          );
        },
      });
    },

    remindDelivery(oid) {
      loading(true, '正在提交...');
      addRemindDelivery({
        orderId: oid,
      })
        .then(res => {
          loading(false);
          toast().tip('已提醒商家尽快发货！');
        })
        .catch(e => {
          loading(false);
          e && dialog().alert(e);
        });
    },
    // 确认收货
    deleteOrder(oid) {
      const that = this;
      const tip = '删除后将无法恢复，确认删除吗？';
      dialog().confirm(tip, {
        title: '确认删除该订单吗？',
        ok() {
          loading(true, '正在提交...');
          delMallOrder(oid).then(
            res => {
              setTimeout(() => {
                loading(false);
                // that.$_router_push(`/mall/order/${oid}/comment`);
                toast().success('删除成功');
                that.onResume();
              }, 500);
            },
            err => {
              loading(false);
              err &&
                dialog().alert(err, {
                  title: '',
                });
            }
          );
        },
      });
    },
    // 获取延长发货时间
    handleOrderReceipted(id) {
      return new Promise((resolve, reject) => {
        // 执行请求
        getOrderReceipted(id).then(
          res => {
            resolve(res);
          },
          err => {
            loading(false);
            err &&
              dialog().alert(err, {
                title: '',
              });
          }
        );
      });
    },
    // 确认延长收货
    async confirmExtended(oid) {
      let content = await this.handleOrderReceipted(oid);
      if (content.userExtendedReceiptFlag === 0) {
        const that = this;
        const tip = `
        <div style="text-align: justify;color:black;">延长后将增加3天，仅可延长1次，订单原定于<span style="color:red">${content.receiptedTTL}</span>后将自动确认收货。<br/>
          <span style="font-size:12px;color:#999;display:inline-block;padding-top: 10px;">若无法满足请联系平台客服反应情况，平台运作人员会根据疫情与物流情况，为您延长确认收货时间。</span>
        </div>`;
        dialog().confirm(tip, {
          title: '确认延长收货吗？',
          ok() {
            loading(true, '正在提交...');
            getOrderExtended({ orderId: oid }).then(
              res => {
                loading(false);
                toast().success('确认成功');
                that.onResume();
              },
              err => {
                loading(false);
                err &&
                  dialog().alert(err, {
                    title: '',
                  });
              }
            );
          },
        });
      } else {
        let tip = `<div style="text-align: center;color:black;">订单仅可延长一次收货<br/>
                    <span style="font-size:12px;color:#999;display:inline-block;padding-top: 10px;">如需帮助请致电平台客服：400-606-1041</span>
                  </div>`;
        dialog().alert(tip, { title: '' });
      }
    },
    // 显示删除订单按钮，待评价、已完成、已关闭、超时关闭显示删除按钮
    showDeleteBtn(item) {
      return (
        item.status === 6 ||
        item.status === 7 ||
        item.status === -1 ||
        (item.status === 1 &&
          this.isTimeout(item) &&
          item.orderType != 'recharge')
      );
    },
    // 格式化状态
    formatOrderStatus(item) {
      return this.displayOrderStatus(NewOrdersStatus, item);
    },
    // 商城订单状态格式化
    displayOrderStatus(e, stat) {
      const status = e.getEnum(stat.status);
      if (stat.status === -1) return '已关闭'; // 订单状态时-1 显示已关闭
      if (stat.status === 1 && this.isTimeout(stat)) return '已关闭'; // 订单创建时间大于15分钟，超时关闭
      if (status === null) return `异常${stat.status}`;
      return status.desc;
    },
    goDetail(item) {
      if (item.orderType == 'car') {
        // 洗车养车
        this.$_router_push('/order/' + item.orderId);
      } else if (
        item.orderType == 'inspection' ||
        item.orderType == 'car_inspection'
      ) {
        // 审车、车务
        this.$_router_push('/vehicle-business/order/' + item.orderId);
      } else if (item.orderType == 'card-buy') {
        // 洗车卡、保养卡
        this.$_router_push('/washcard/mycard/' + item.orderId);
      } else if (item.orderType == 'action') {
        // 活动
        this.$_router_pageTo(item.url, {
          titleBar: true,
          pulldownRefresh: false,
          shareButton: true,
        });
      } else if (item.orderType == 'recharge') {
        // 话费充值
        // 规避 通过landing基座加载的模块，发生跨模块跳转的副作用
        const nextPath = `/mobile-recharge/order/${item.orderId}`;
        const parentPath = this.$route.path;
        if (isSameModule(parentPath, nextPath)) {
          this.$_auth_push(nextPath);
        } else {
          this.$_router_pageTo(nextPath, {
            theme: 'light',
          });
        }
      } else if (item.orderType == 'direct-recharge') {
        // 话费直冲
        this.$_auth_push(`/mobile-direct-recharge/order/${item.orderId}`);
      } else if (item.orderType == 'group-buy') {
        // 团购
        this.$_router_push('/mall/order/' + item.orderId);
      } else if (item.orderType == 'foods') {
        // 美食
        this.$_auth_push('/mallg2/order/' + item.orderId);
      } else if (item.orderType == 'memberNew') {
        // 购买会员
        // this.$_router_push(url);
      }
      // this.$_router_push(url);
    },
    getGoodsLogo(item) {
      let imageStr = '';
      if (item.orderType == 'car') {
        // 洗车养车
        imageStr = require('./images/car.png');
      } else if (
        item.orderType == 'inspection' ||
        item.orderType == 'car_inspection'
      ) {
        // 审车、车务
        imageStr = require('./images/inspection.png');
      } else if (item.orderType == 'card-buy') {
        // 洗车卡、保养卡
        imageStr = require('./images/card-buy.png');
      } else if (item.orderType == 'action') {
        // 活动
        imageStr = require('./images/action.png');
      } else if (
        item.orderType == 'recharge' ||
        item.orderType == 'direct-recharge'
      ) {
        // 话费充值
        imageStr = require('./images/recharge.png');
      } else if (item.orderType == 'group-buy') {
        // 团购
        imageStr = require('./images/group-buy.png');
      } else if (item.orderType == 'foods') {
        // 美食
        imageStr = require('./images/foods.png');
      } else if (item.orderType == 'memberNew') {
        // 购买会员
        imageStr = require('./images/vip.png');
      }
      return (item.goodsInfo.image || '').split(',')[0] || imageStr;
      // this.$_router_push(url);.
    },
    onLeave() {
      console.log('leave');
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.refreshQuietly();
    },
    // 静默刷新列表数据
    refreshQuietly() {
      const currentPage = this.$_loader_getPage();
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(this.orders.length);
      // HACK 2023.05.26, 服务端数据延迟问题(目前大概100ms延迟)
      setTimeout(() => {
        return this.getList().then(
          res => {
            this.$_loader_setPage(currentPage);
            this.$_loader_setPageSize(PAGE_SIZE);
            return res;
          },
          err => {
            // this.status = AppStatus.ERROR;
            toast().tip('刷新失败，请重试！');
            console.error(err);
          }
        );
      }, 200);
    },
    // 初始化函数
    init() {
      return this.initList();
    },
    // 失败重试
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    // 重新获取列表
    initList() {
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(PAGE_SIZE);
      return this.getList()
        .then(res => {
          this.status = AppStatus.READY;
          this.loginGuidance = false;
          return res;
        })
        .catch(err => {
          if (err.code == 403) {
            this.status = AppStatus.READY;
            this.loginGuidance = true;
          } else {
            this.loginGuidance = false;
            this.status = AppStatus.ERROR;
          }
          console.error(err);
          toast().tip(err);
          return Promise.reject(err);
        });
    },
    // 滚动加载更多
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      const nextPage = this.filterParams.page + 1;
      this.getList(nextPage).catch(e => {
        toast().tip(e);
        console.error(e);
      });
    },
    // 下拉刷新
    refresh() {
      this.initList()
        .then(res => {
          this.refreshAction = Date.now();
        })
        .catch(e => {
          toast().tip('刷新失败');
          this.refreshAction = Date.now();
        });
    },
    go(url) {
      this.$_router_push(url);
    },
    toLogin() {
      login();
    },
    // 查询列表
    getList(page = 1) {
      const params = { ...this.filterParams, page };
      return this.$_loader_bind(getTypeOrderList, res => {
        if (page === 1) {
          this.orders = res.rows || [];
        } else {
          this.orders = this.orders.concat(res.rows || []);
        }
        // HACK 下面代码存在原因，新版订单列表采用新接口，订单操作完成后，列表接口数据会有延迟，需要前端手动删除，此处采用sessionStorage保存操作过的订单编号供删除订单使用，只在待评价分类下使用
        // HACK 2023.05.26,服务端已解决数据延迟问题(目前大概100ms延迟)，下面代码屏蔽
        // if (this.category == 1 || this.category == 4 || this.category == 6) {
        //   console.log('已操作订单ID', sessionStorage.getItem('handledOrderId'));
        //   let haveHandledOrderId = sessionStorage.getItem('handledOrderId');
        //   let index = this.orders.findIndex(function (item) {
        //     return item.orderId == haveHandledOrderId;
        //   });
        //   index > -1 && this.orders.splice(index, 1);
        //   index > -1 && this.$_loader_setPageSize(PAGE_SIZE - 1);
        // }
        return res.rows;
      }).load(params);
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$border-color: #ececec;
.tabs-orders {
  background: white;
  box-shadow: 0 1px 5px 1px #e7eaf5;
  z-index: 1;
}
.orders-content-wrap {
  padding-bottom: 20px;
}
.orders-wrap {
  position: relative;
  // max-width: 100%;
  box-sizing: border-box;
  padding: 0 10px;
}
.order-item {
  background: white;
  padding: 14px;
  border-radius: 5px;
  // width: 100%;
  box-sizing: border-box;
  margin: 10px 0;
  overflow: hidden;
  .order-status {
    color: #999999;
    font-size: 12px;
    line-height: 1;
  }
  .order-status-highlight {
    color: $lh-2022-primary-color;
  }
  .order-business {
    // width: 100%;
    box-sizing: border-box;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // line-height: 1.1;
    .order-business-name {
      flex: 1;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      color: #333333;
      line-height: 1;
      p {
        // line-height: 16PX;
        display: inline-block;
        max-width: 270px;
        text-overflow: ellipsis;
        // overflow: hidden;
        white-space: nowrap;
        // vertical-align: top;
        font-weight: 700;
      }
    }
    .order-id {
      color: #999;
      font-size: 12px;
      line-height: 1;
      span {
        user-select: text;
      }
    }
  }
  .order-goods-info {
    // width: 100%;
    display: flex;
    align-items: center;
    .order-goods-logo {
      width: 70px;
      height: 70px;
      border-radius: 5px; /* px */
      overflow: hidden;
      margin: 0px 3px 0 0;
    }
    .order-goods-name {
      padding-left: 6px;
      flex: 1;
      line-height: 1.2;
      font-size: 14px;
    }
    .order-goods-price {
      width: 80px;
      text-align: right;
      display: inline-flex;
      flex-direction: column;
      justify-content: flex-end;
      line-height: 1;
      p {
        vertical-align: bottom;
        font-size: 18px;
        font-weight: bold;
        i {
          font-style: normal;
          font-size: 10px;
        }
        padding-bottom: 5px;
      }
      span {
        font-size: 12px;
        color: #999999;
      }
    }
  }
  .order-goods-btns {
    margin-top: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 12px;
    border-top: 1px solid #eeeeee;
    .pay-btn {
      color: #333333;
      border: 1px solid #666666;
      padding: 5px 12px;
      border-radius: 13px;
      line-height: 1;
      font-size: 14px;
      margin-left: 12px;
      &.active {
        color: $lh-2022-primary-color;
        border-color: $lh-2022-primary-color;
      }
    }
  }
  .logistics-info {
    background: #f3f3f3;
    font-size: 12px;
    line-height: 24px;
    width: 100%;
    box-sizing: border-box;
    padding-left: 10px;
    color: #333333;
    display: flex;
    align-items: center;
    margin-top: 10px;
    .icon_jglh {
      flex: 0 0 20px;
      font-size: 14px;
      color: #333333;
    }
    span {
      flex: 1;
      width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.icon-arrow-right {
  // color: #000000;
  font-size: 14px;
  transform: scale(0.8);
}
.order-foot {
  @include border-top($border-color, 'before');
  // border-top:1px solid $border-color;
  padding-top: 5px;
  margin-left: 5px;
  margin-top: 4px;
  padding-right: 10px;
}
.order-head {
  padding-right: 10px;
  // margin-top: 5px;
}
// .order-end{
//   .order-head::before{
//    filter: grayscale(1); // 滤镜转为黑白
//   }
// }
.order-btn {
  color: #ee6e00;
  border-color: #f59645;
  background: white;
  line-height: 1.8;
  &:not(.weui-btn_disabled):active {
    background-color: #f59645;
    color: white;
  }
  &.order-btn__primary {
    background: #f59645;
    color: white;
  }
}

.scroller {
  overflow-y: hidden;
}
.vscroller {
  height: 600px;
  overflow-y: scroll;
}
.order {
  box-sizing: border-box;
}
.logistics-action {
  padding-bottom: 20px;
  max-height: 70%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .title {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 16px;
    // position: absolute;
    // top: 0;
    // left: 0;
    // z-index: 1;
    -webkit-transform: translateZ(0);
  }
  .steps {
    flex: 1;
    height: 100%;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 0;
      display: none;
      background-color: transparent;
    }
  }
  h3 {
    font-size: 14px;
    font-weight: normal;
  }
  .van-step:first-child {
    h3 {
      font-weight: bold;
    }
  }
  .empty {
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 20px;
    .empty-icon {
      width: 165px;
      height: 110px;
      margin: 15px auto;
      background: url(../../assets/images/empty.png) no-repeat;
      background-size: 100% 100%;
    }
    .empty-text {
      font-size: 14px;
      color: #6f6f6f;
      text-align: center;
    }
  }
}
.login-tips {
  color: #333333;
}
.btn {
  margin-top: 15px;
  width: 100px;
  box-sizing: border-box;
  background: #fd4925;
  border-color: #fd4925;
  // &:active {
  //   background: #f6f6f6;
  //   border-color: #fd4925;
  //   &::before {
  //     opacity: 0.4;
  //     background: #f6f6f6;
  //     border-color: #f6f6f6;
  //   }
  // }
}
</style>
