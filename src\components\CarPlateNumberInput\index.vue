<template>
  <div class="car-number-input flex-row">
    <label for="car-number_province" class="select-inline">
      <select id="car-number_province" name="province" v-model="form.province" @change="onInputChange">
        <option v-for="item in CAR_NUMBER_PROVINCE" :key="item" :value="item" :disabled="!filter(item)">{{item}}</option>
      </select>
    </label>
    <input ref="input" class="weui-input input-bold" type="text" maxlength="7" v-model="form.number" @input="onInputChange" :placeholder="placeholder">
</div>
</template>
<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  .car-number-input {
    align-items: center;
  }
  .input-bold {
    font-weight: 700;
    text-transform: uppercase;
    &::placeholder {
      font-weight: 400;
    }
  }
  .select-inline {
    display: inline-block;
    position: relative;
    padding: 0 20px 0 5px;
    background: rgb(217, 234, 253);
    color: rgb(255, 255, 255);
    border-radius: 2px;
    margin-right: 5px;
    width: 35px;
    position: relative;
    height: 26px;
    select{
      -webkit-appearance: none;
      border: 0;
      font-size: 1em;
      height: 26px;
      line-height: 26px;
      min-width: 20px;
      background: transparent;
      color: rgb(80, 149, 232);

      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      z-index: 1;
      padding-left: 5px;
      &:focus {
        outline: none;
      }
    }
    &::after{
      content: " ";
      display: inline-block;
      border-style: solid;
      position: absolute;
      right: 6px;
      top: 50%;
      transform: translateY(-50%);
      border-width: 5px 5px 0;
      border-color: rgb(80, 149, 232) transparent transparent;
    }
  }
</style>
<script>
  const CAR_NUMBER_CITY = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
  const CAR_NUMBER_PROVINCE = ['豫', '京', '晋', '冀', '津', '蒙', '辽', '吉', '黑', '沪', '苏', '浙', '皖', '闽', '赣', '鲁', '鄂', '湘', '粤', '桂', '琼', '渝', '川', '贵', '云', '藏', '陕', '甘', '青', '新', '宁', '港', '澳', '台'];
  
  export function validateCarNumber(value) {
    // const regex = /^(.)([a-z])([0-9a-z]{5})$/i;
    const regex = /^(\S)([a-z][0-9a-z]{5,6})$/i;
    return regex.test(value);
  }
  
  function parseCarNumber(value) {
    // const regex = /^(.)([a-z])([0-9a-z]{5})$/i;
    const regex = /^(\S)([a-z][0-9a-z]{5,6})$/i;
    let result = regex.exec(value);
    if (!result || result.length !== 3) {
      result = Array(3);
    }
    return {
      province: result[1] || CAR_NUMBER_PROVINCE[0],
      // city: result[2],
      number: result[2] || '',
    }
  }

  export default {
    name: 'car-plate-number-input',
    props: {
      value: {
        type: String,
        default() {
          return CAR_NUMBER_PROVINCE[0];
        }
      },
      filter: {
        type: Function,
        default() {
          return true;
        }
      },
      placeholder: {
        type: String,
        default: '请输入车牌号'
      },
      autofocus: {
        type: Boolean,
        default: false
      },
    },
    computed: {
      carNumber() {
        return (this.form.province + this.form.number.trim()).toUpperCase();
      }
    },
    data() {
      return {
        CAR_NUMBER_PROVINCE: CAR_NUMBER_PROVINCE, //.filter(this.filter),
        CAR_NUMBER_CITY,
        form: {
          province: CAR_NUMBER_PROVINCE[0],
          city: CAR_NUMBER_CITY[0],
          number: '',
        },
        oldValue: this.value,
      }
    },
    watch: {
      value(val, oldVal) {
        console.log('value change:', val, oldVal);
        // debugger
        if (!val) {
          this.updateInput();
        }
      },
      ['form.number'](val, oldVal) {
        this.$nextTick(() => {
          this.form.number = val.trim();
        })
      },
    },
    mounted() {
      this.updateInput();
      if (this.autofocus) {
        setTimeout(() => {
          this.$refs.input.focus();
        }, 1000)
      }
    },
    methods: {
      ...{ validateCarNumber },
      onInputChange() {
        this.oldVal = this.carNumber;
        this.$emit('input', this.carNumber);
      },
      updateInput() {
        const result = parseCarNumber(this.value);
        this.form.province = result.province;
        this.form.number = result.number;
      }
    }
  };
</script>
