<template>
  <container
    class="vehicle-inspection-online"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header title="一品天下美食">
      <x-button slot="left" type="back"></x-button>
      <x-button
        slot="right"
        type="share"
        @click="share"
      ></x-button>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="container_box">
        <div class="banner">
          <img :src="bannerImg" alt="">
        </div>
        <div class="title-box">
          <h2>{{ cityInfos[city].title }}</h2>
        </div>
        <div class="city-intro">
          {{ cityInfos[city].desc }}
        </div>
        <div class="title-box">
          <h2>商品展示</h2>
        </div>
        <MallGoodsList :id="cityId" :uid="ref"/>
        <!-- <div class="goods">
          <MallGoods v-for="(item, index) in goods" :key="index" :goods="item" @click.native="toGoodsDetail(item)" />
        </div> -->
      </div>
    </content-view>
  </container>
</template>

<script>
import { isInJglh, isInWeixin } from '@/common/env';
import { getVersion, parseJglhURL } from '@/bridge';
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { dialog, loading } from '@/bus';
import { getBoxConfig, getBoxOrder, getBoxDetail } from './api';
import { getGoodsList } from '@pkg/mall/api';
import BizRichText from '@/views/components/BizRichText.vue';
import MallGoodsList from './components/MallGoodsList.vue';

const jglhVersion = getVersion();
export default {
  name: 'GourmetDetail',
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    const city = this.$route.query.city || null; // 城市
    const cityId = this.$route.query.id || null; // 城市id
    const ref = this.$route.query.ref || ''; // 分享uid
    return {
      AppStatus,
      city,
      cityId,
      ref,
      status: AppStatus.LOADING,
      keepAlive: true,
      pageData: {},
      goods: [],
      cityInfos: {
        beijing: {
          title: '北京',
          desc: '北京市（Beijing），简称“京”，古称燕京、北平，是中华人民共和国首都、直辖市、国家中心城市、超大城市 [186] ，国务院批复确定的中国政治中心、文化中心、国际交往中心、科技创新中心',
        },
        neimenggu: {
          title: '内蒙古',
          desc: '内蒙古自治区，简称“内蒙古”，首府呼和浩特；地处中国北部，东北部与黑龙江、吉林、辽宁、河北交界，南部与山西、陕西、宁夏相邻，西南部与甘肃毗连，北部与俄罗斯、蒙古接壤',
        },
        shenyang: {
          title: '沈阳',
          desc: '沈阳市，古称盛京、奉天，辽宁省辖地级市、省会、副省级市、特大城市  、沈阳都市圈核心城市，国务院批复确定的中国东北地区的中心城市、中国重要的工业基地和先进装备制造业基地',
        },
        hebei: {
          title: '河北',
          desc: '河北省，简称“冀”，是中华人民共和国省级行政区，省会石家庄，位于北纬36°05′-42°40′，东经113°27′-119°50′之间，环抱首都北京市，东与天津市毗连并紧傍渤海，东南部、南部衔山东省、河南省，西倚太行山与山西为邻，西北部、北部与内蒙古自治区交界，东北部与辽宁省接壤',
        },
        ningxia: {
          title: '宁夏',
          desc: '宁夏回族自治区，简称“宁”，中华人民共和国省级行政区，首府银川市，中国五大少数民族自治区之一；位于中国西北内陆地区，东邻陕西省，西、北接内蒙古自治区，南连甘肃省，位于西北地区',
        },
        gansu: {
          title: '甘肃',
          desc: '甘肃省，简称“甘”或“陇”，中华人民共和国省级行政区，省会兰州市。位于中国西北地区，东通陕西，西达新疆，南瞰四川、青海，北扼宁夏、内蒙古，西北端与蒙古接壤。',
        },
        shandong: {
          title: '山东',
          desc: '山东省（Shandong），简称“鲁”，别称“齐鲁”，是中华人民共和国省级行政区，省会济南市，地处中国华东地区的沿海，濒临渤海和黄海',
        },
        shanxi: {
          title: '山西',
          desc: '山西省，简称“晋”，中华人民共和国省级行政区，省会太原市，位于中国华北，东与河北省为邻，西与陕西省相望，南与河南省接壤，北与内蒙古自治区毗连。',
        },
        shanxi1: {
          title: '陕西',
          desc: '陕西省，简称“陕”或“秦”，中华人民共和国省级行政区，省会西安，位于中国内陆腹地，黄河中游，东邻山西、河南，西连宁夏、甘肃，南抵四川、重庆、湖北，北接内蒙古。',
        },
        qinghai: {
          title: '青海',
          desc: '青海省，简称“青”，是中华人民共和国省级行政区，省会西宁市；位于中国西北内陆，北部和东部同甘肃省相接，西北部与新疆维吾尔自治区相邻，南部和西南部与西藏自治区毗连，东南部与四川省接壤，地势总体呈西高东低，南北高中部低的态势，位于四大地理区划的青藏地区 ',
        },
        henan: {
          title: '河南',
          desc: '河南省，简称“豫”，中华人民共和国省级行政区，省会郑州，是全国农产品主产区和重要的矿产资源大省、人口大省、重要的综合交通枢纽和人流、物流、信息流中心、全国农业大省和粮食转化加工大省。',
        },
        jiangsu: {
          title: '江苏',
          desc: '江苏省，简称“苏”，是中华人民共和国省级行政区，省会南京，位于长江三角洲地区，中国大陆东部沿海',
        },
        sichuan: {
          title: '四川',
          desc: '四川省，简称“川”或“蜀”，对于中下游地区的生态安全十分重要。 [168]是中华人民共和国省级行政区，省会成都，位于中国西南地区内陆，地处长江上游，素有“天府之国”的美誉。为中国道教发源地之一，古蜀文明发祥地，全世界最早的纸币“交子”出现地。四川盐业文化，酒文化源远流长；三国文化，红军文化，巴人文化精彩纷呈。',
        },
        shanghai: {
          title: '上海',
          desc: '上海市，简称沪，别称申， 是中华人民共和国直辖市、国家中心城市、超大城市、上海大都市圈核心城市，中华人民共和国国务院批复确定的中国国际经济、金融、贸易、航运、科技创新中心，中国历史文化名城，世界一线城市',
        },
        zhejiang: {
          title: '浙江',
          desc: '浙江省，简称“浙”，是中华人民共和国省级行政区，省会杭州市，地处中国东南沿海，长江三角洲南翼；东临东海，北与上海市、江苏省接壤，南接福建省，西与安徽省、江西省相连',
        },
        shenzhen: {
          title: '深圳',
          desc: '深圳市，简称“深”，别称鹏城，广东省辖地级市，国家计划单列市，超大城市 ，国务院批复确定的经济特区、全国性经济中心城市和国家创新型城市，粤港澳大湾区核心引擎城市之一',
        },
        anhui: {
          title: '安徽',
          desc: '安徽省，简称“皖”，是中华人民共和国省级行政区，省会合肥市，位于中国华东长江三角洲地区， [1] 地跨东经114°54′—119°37′，北纬29°41′—34°38′，东连江苏省，西接河南省、湖北省，东南接浙江省，南邻江西省，北靠山东省；地势由平原、丘陵、山地构成，处暖温带与亚热带过渡地区',
        },
      },
      paging: {
        page: 1,
        pageSize: 10,
        total: null
      },
      shareInfo: {
        title: '寻全国美食美味，一品天下美食！',
        desc: '让我们一起踏上一场味蕾之旅，探索那些令人垂涎欲滴的美食吧！',
        shareImage: 'FmFHX5pWoYS17NkmG4LjG72nO-oP',
      }
    };
  },
  components: {
    BizRichText,
    MallGoodsList,
  },
  computed: {
    bannerImg() {
      return require(`@pkg/actions/assets/images/yptx_${this.city}.png`)
    },
  },
  mounted() {
    // 元素挂载结束
    // this.init()
  },
  methods: {
    toGoodsDetail(goods) {
      this.$_router_pageTo(`/mall/goods/${goods.rid}`, {
        theme: 'light'
      });
    },
    authPageTo(url) {
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      // this.$_router_push(url);
      this.pageTo(url);
    },
    requireAuth() {
      return new Promise((resolve, reject) => {
        if (!this.$_auth_isLoggedIn) {
          this.$_auth_login();
          return;
        }
        resolve();
      });
    },
    initPageData: function () {
      // 获取页面加载的数据
    },
    init() {
      // 初始化数据
      this.status = AppStatus.READY;
      // this.status = AppStatus.ERROR;
    },
    initShareInfo() {
      // 设置邀请链接分享信息
      const wechatPath = `/gourmet/index?ref=${this.ref}`;
      const jglhWechatURL = getAppURL(wechatPath, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share2' : '?jglh',
      });

      const shareInfo = {
        link: jglhWechatURL,
        title: this.shareInfo.title,
        desc: this.shareInfo.desc,
        imgUrl: getImageURL(this.shareInfo.shareImage),
      };
      this.$_share_update(shareInfo);
    },
    share() {
      const title = this.shareInfo.title
      const logo = getImageURL(this.shareInfo.shareImage);
      const desc = this.shareInfo.desc;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      }
      this.$_share(shareInfo);
    },
    reload() {
      this.status = AppStatus.LOADING;
      // 重新加载页面
      this.init();
    },
    onResume() {
      // 页面重新激活时在重新获取数据
      // this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~styles/mixin/animate.scss";
@import "~styles/mixin/index.scss";
img {
  -webkit-user-drag: none;
}
.container_box {
  min-height: 100%;
  // padding: 200px 0px 0px;
  background: #F9C488;
  // background-size: 100% auto;
  position: relative;
  // font-size: 0;
}
.banner{
  width: 100%;
  margin-bottom: 15px;
  img{
    display: block;
    width: 100%;
    height: auto;
    user-select: none;
  }
}
.title-box{
  width: 100%;
  text-align: center;
  margin-bottom: 25px;
  h2{
    display: inline-block;
    background: url(./assets/images/yptx_title_bg.png) no-repeat left center;
    background-size: 100% 100%;
    font-size: 15px;
    font-weight: 400;
    color: #DD3A36;
    min-width: 112px;
    height: 26px;
    line-height: 28px;
  }
}
.city-intro{
  font-size: 14px;
  color: #7F121C;
  line-height: 18px;
  margin-bottom: 28px;
  padding: 0 15px;
}

.goods {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;
  .mall-section-goods {
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
  }
  .mall-section-goods:nth-child(n + 3) {
    margin-top: 16px;
  }
}
</style>
