export * from '@/enums';

export const playMode = {
  sequence: 0,
  loop: 1,
  random: 2,
};
export const playListMode = {
  asc: 'asc',
  desc: 'desc',
};
// import Enum from '@/model/Enum';
// import Enums from '@/model/Enums';

// function createEnum(...args) {
//   return new Enum(...args);
// };

// export const PaymentChannel = new Enums({
//   ACCOUNT: createEnum('账户余额', 'jglh_account', '账户余额支付'),
//   // THIRD: createEnum('第三方支付', 'third', '支付宝或微信支付', true),
//   ALIPAY: createEnum('支付宝', 'alipay', '推荐有支付宝账户的用户使用', false),
//   WEIXIN: createEnum('微信支付', 'wx', '微信5.0以上版本用户使用', false),
// });

// export const NativeView = new Enums({
//   SET_PAYMENT_PASSWORD: createEnum('设置支付密码', 'set_payment_password', ''),
//   RESET_PAYMENT_PASSWORD: createEnum('重置支付密码', 'reset_payment_password', ''),
//   NOTIFICATION_OF_ORDERS: createEnum('订单通知', 'notification_of_order', ''),
//   YOUZAN_WEBVIEW: createEnum('有赞商城', 'youzan_webview', ''),
// });

// export const ImageType = new Enums({
//   BANNER: createEnum('banner', 'banner', ''),
//   LOGO: createEnum('LOGO', 'xslogo', ''),
//   LARGE: createEnum('LARGE', 'xl', ''),
//   MEDIUM: createEnum('LARGE', 'xm', ''),
//   SMALL: createEnum('SMALL', 'xs', ''),
// });

// export const AppStatus = new Enums({
//   NONE: createEnum('none', -1, ''),
//   LOADING: createEnum('loading', 0, ''),
//   READY: createEnum('ready', 1, ''),
//   ERROR: createEnum('error', -1, ''),
// });

// export const TransitionMode = new Enums({
//   INIT: createEnum('init', 0, ''),
//   FORWARD: createEnum('forward', 1, ''),
//   BACK: createEnum('back', 2, ''),
// });

// export const OrderStatus = new Enums({
//   UNPAID: createEnum('未付款', 0, ''),
//   // PAID: createEnum('已付款', 1, ''),

//   TO_BE_SERVE: createEnum('未消费', 1, ''),
//   // SERVED: createEnum('已服务', 2, ''),

//   UN_COMMENTED: createEnum('未评价', 2, ''),
//   COMMENTED: createEnum('已评价', 3, ''),

//   REFUNDING: createEnum('退款中', 4, ''),
//   REFUNDED: createEnum('已退款', 5, ''),

//   INVALID: createEnum('已失效', -1, '未付款'),
//   EXPIRED: createEnum('已过期', 6, '有效期已过'),
//   TIMEOUT: createEnum('已失效', 7, '订单超时失效'),
//   REFUND_APPLIED: createEnum('待退款', 8, '已申请退款'),

//   // 保养订单状态
//   UN_CONFIRM: createEnum('未确认', 9, '待确认'),
//   CONFIRMING: createEnum('未确认', 13, '已确认，待支付'),
//   CONFIRMED: createEnum('已确认', 10, '已确认，待服务'),
//   SERVING: createEnum('服务中', 11, '服务中'),
//   SERVIED: createEnum('未提车', 12, '服务结束，待提车'),
// });

// export const OrderRefundStatus = new Enums({
//   REFUNDING: createEnum('处理中', 4, '退款处理中'),
//   REFUNDED: createEnum('已退款', 5, '已退款'),
// });

// export const CarMT = new Enums({
//   LEVEL1: createEnum('车辆安全性能维护', 'level1', '车辆安全性能维护'),
//   LEVEL2: createEnum('车辆基础性能养护', 'level2', '车辆基础性能养护'),
//   LEVEL3: createEnum('车辆性能健康恢复', 'level3', '车辆性能健康恢复'),
// });

// export const CarMaintainType = new Enums({
//   LEVEL1: createEnum('车辆安全性能维护', 'level1', '车辆安全性能维护'),
//   LEVEL2: createEnum('车辆基础性能养护', 'level2', '车辆基础性能养护'),
//   LEVEL3: createEnum('车辆性能健康恢复', 'level3', '车辆性能健康恢复'),
// });

// /**
//  * 商家服务
//  */
// export const ShopService = new Enums({
//   MAINTAIN_LEVEL1: createEnum('车辆安全性能维护', 1, '小保养'),
//   MAINTAIN_LEVEL2: createEnum('车辆基础性能养护', 2, '中保养'),
//   MAINTAIN_LEVEL3: createEnum('车辆性能健康恢复', 3, '大保养'),
//   SHEET_METAL: createEnum('钣金', 4, '车辆性能健康恢复'),
//   REPAIR: createEnum('维修', 5, '车辆性能健康恢复'),
//   WASH: createEnum('洗车', 6, '车辆清洗'),
// });

// export const CommentType = new Enums({
//   ALL: createEnum('所有', 'all', '所有评论'),
//   WASH: createEnum('洗车', 'wash', '洗车评论'),
//   MAINTAIN: createEnum('保养', 'mt', '保养评论'),
// });

// export const OrdersType = new Enums({
//   ALL: createEnum('所有', 'all', '所有'),
//   WASH: createEnum('洗车', 'wash', '洗车'),
//   MAINTAIN: createEnum('保养', 'mt', '保养'),
// });

// export const OrderType = new Enums({
//   WASH: createEnum('洗车', 0, '洗车订单'),
//   CHARGE: createEnum('充值', 1, '充值订单'),
//   MAINTAIN: createEnum('保养订单', 2, '保养订单'),
//   MAINTAIN_RESERVE: createEnum('保养预约', 3, '保养预约订单'), // MaintainReserve
//   SHEET_METAL_OR_REPAIR: createEnum('钣金或维修预约', 4, '钣金维修预约订单'),
// });

// // 修改订单后补单类型
// export const ReOrderType = new Enums({
//   REFUND: createEnum('退款', -1, '退差价'),
//   PAY: createEnum('补缴', 1, '补差价'),
// });

// // 保养门店认证状态
// export const ShopLevel = new Enums({
//   PRIMARY: createEnum('初级认证', 0, ''),
//   MID: createEnum('中级认证', 1, ''),
//   HIGH: createEnum('高级认证', 2, ''),
// });
