<template>
  <div>
    <loading v-if="status === AppStatus.LOADING"></loading>
    <slot v-else-if="status === AppStatus.READY"></slot>
    <slot name="page-error" v-else-if="status === AppStatus.ERROR">
      <page-placeholder class="page-error" :icon="require('./images/error.png')">
        <div class="btn-areas">
          <div class="page-error__tip">{{error || '出错了...'}}</div>
          <a class="btn-reload"  @click="$emit('reload')" href="javascript:;">重新加载</a>
        </div>
      </page-placeholder>
    </slot>
  </div>
</template>

<script>
  import { AppStatus } from '@/enums';
  import Loading from '../Loading/Loading.vue';
  import PagePlaceholder from './PagePlaceholder.vue';

  export default {
    name: 'PageLoader',
    props: {
      status: {
        type: [String, Number, Object],
        default() {
          return AppStatus.LOADING;
        }
      },
      error: {
        type: String,
      }
    },
    components: {
      Loading,
      PagePlaceholder,
    },
    data() {
      return {
        AppStatus,
      }
    }
  }
</script>

<style lang="scss" scoped>
  .btn-areas {
    margin-top: 10px;
  }
  .page-error {
    height: 100vh;
    padding-top: 15vh;
    margin-top: 0;
    background: white;
  }
  .page-error__tip {
    margin-bottom: 10px;
  }
  .btn-reload {
    display: inline-block;
    color: rgba(56, 142, 253, 1);
    padding: 3px 20px;
    border: 1px solid;
    border-radius: 30px;
    &:active {
      color: rgba(56, 142, 253, 0.7);
    }
  }
</style>
