import { handleError } from '../error-handler';

export default [
  {
    path: '/traffic/home',
    name: '对话车管所',
    component: resolve => {
      import(/* webpackChunkName: "wechat" */ '@/views/packages/vehicle-department/Home.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/traffic/introduce',
    name: '对话车管所introduce',
    component: resolve => {
      import(/* webpackChunkName: "wechat-me" */ '@/views/packages/vehicle-department/Introduce.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/traffic/introduce/detail',
    name: '对话车管所detail',
    component: resolve => {
      import(/* webpackChunkName: "wechat-me" */ '@/views/packages/vehicle-department/IntroduceDetail.vue').then(resolve).catch(handleError);
    },
  }
];
