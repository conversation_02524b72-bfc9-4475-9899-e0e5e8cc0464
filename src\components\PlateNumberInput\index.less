.so-mask {
	position: fixed;
	top: 0;
	bottom: 0;
	right: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 998;
}
.so-plate {
	box-sizing: border-box;
	position: absolute;
	bottom: 0;
	width: 100%;
	left: 0;
	background: #fff;
	padding: 12px 12px 0 12px;
  z-index: 999;
	&-head {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	&-type {
		flex:1;
		display:block;
		label {
			display: inline-block;
			min-height: 16px;
			font-size: 14px;
			margin-right: 5px;
		}
	}
	&-body {
		box-sizing: border-box;
		padding: 15px 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	&-word {
		border: 1px solid #ccc;
		border-radius: 5px;
		height: 0;
		margin: 0 2px;
		box-sizing: border-box;
		padding-bottom: calc((100% - 35px) / 7);
		width: calc((100% - 35px) / 7);
		position: relative;
		&.active {
			border-color: #4352A9;
			box-shadow: 0 0 7px 0 #4352A9;
		}
		span {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translateX(-50%) translateY(-50%);
			font-weight: 700;
			font-size: 16px;
		}
	}
	&-dot {
		width: 7px;
		height: 7px;
		background: #ccc;
		border-radius: 50%;
		margin: 0 2px;
	}
	&-keyboard {
		background: #eee;
		margin-left: -12px;
		margin-right: -12px;
		padding: 10px 12px 5px 12px;
		box-sizing: border-box;
		transition: all .3s;
		&>#keyboard{
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
		}
	}
	&-key {
		display: block;
		background: #fff;
		border-radius: 5px;
		box-shadow: 0 0 4px 0 #bbb;
		width: 40px;
		height: 40px;
		margin: 2px 0;
		font-size: 16px;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		&.hover {
			background: #efefef;
		}
		&:active {
			background: #efefef;
		}
		&.fill-block {
			width: 40px;
			height: 40px;
			background: none;
			box-shadow: none;
		}
	}
	&-btn {
		display: inline-block;
		background: #fff;
		border-radius: 5px;
		box-shadow: 0 0 5px 0 #bbb;
		font-size: 14px;
		text-align: center;
		margin:0 0 0 5px;
		padding:0 12px;
    line-height: 2.5;
		&-group{
			display: flex;
			justify-content: space-between;
			background: #eee;
			margin-left: -12px;
			margin-right: -12px;
			box-sizing: border-box;
			padding: 0 12px 5px 12px;
		}
		&--cancel{
			margin:0;
		}
		&--submit{
			background:#4352A9;
			color:#fff;
		}
		&--delete{
			color:#FD4925;
		}
	}
}


.animation-scale-up {
	animation-duration: .2s;
	animation-timing-function: ease-out;
	animation-fill-mode: both;
    animation-name: scale-up
}
@keyframes scale-up {
    0% {
        opacity: .8;
        transform: scale(.8)
    }

    100% {
        opacity: 1;
        transform: scale(1)
    }
}
