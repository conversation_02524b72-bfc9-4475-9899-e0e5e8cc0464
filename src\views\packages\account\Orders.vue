
<style lang="scss" scoped>
 @import '~styles/mixin/index.scss';
  $border-color: #EBEBEB;
  $menu-border-color: #dadada;
  .page-content {
    // .no-header & {
    //   margin-top: 0;
    // }
  }
  .orders ::v-deep {
    .header {
      display: flex!important;
    }
    .swiper-container {
      display: flex;
      flex: 1;
    }
    .swiper-wrapper {
      flex: 1;
      height: auto;
    }
    .swiper-slide {
      display: flex;
      height: inherit;
      .content-wrapper, .page-content {
        margin-top: 0 !important;
      }
    }
  }
  .my-tabs {
    background: white;
    box-shadow: 0 0 0 0 #dcdcdc; /* px */
    z-index: 1;
    ::v-deep .tab-item {
      flex: auto;
    }
    ::v-deep .tab-items-inner {
      justify-content: space-evenly;
    }
  }
  .error {
    text-align: center;
    padding: 10px;
    margin: 20px;
    &::before {
      content: '\E64f';
      font-family: iconfont;
      display: block;
      font-size: 38px;
      color: #3a3a3a;
    }
  }
  .error {
    margin-top:30%;
  }
</style>
<template>
    <page class="orders" @ready="init" @resume="onResume">
      <x-header title="我的订单">
        <x-button  slot="left" type="back"></x-button>
        <x-button v-if="shouldDisplayAfterSaleBtn"  slot="right" type="text" @click="$_router_pageTo('/mall/order/list?mode=aftersale')">退款/售后</x-button>
      </x-header>
      <div class="page-content">
        <template v-if="status == AppStatus.READY">
          <tabs
            class="my-tabs"
            :index="currentTypeIndex"
            :scrollable="true"
            :tabs="tabList"
            @change="switchType"
          >
          </tabs>
          <swiper
            ref="swiper"
            class="my-swiper"
            @change="onSlide"
            :options="{
              autoplay: false,
              initialSlide: currentTypeIndex,
            }"
          >
            <swiper-item v-fix:height>
              <all-orders :show-filter="false" :ref="OrdersType.ALL"></all-orders>
            </swiper-item>
            <swiper-item v-fix:height>
              <orders-beauty :show-filter="false" :ref="OrdersType.BEAUTY"></orders-beauty>
            </swiper-item>
            <swiper-item v-fix:height>
              <orders-vehicle-business :ref="OrdersType.VEHICLE_BUSINESS"></orders-vehicle-business>
            </swiper-item>
            <swiper-item v-fix:height>
              <orders-activity :ref="OrdersType.ACTIVITY"></orders-activity>
            </swiper-item>
            <swiper-item v-fix:height>
              <orders-food :ref="OrdersType.FOOD"></orders-food>
            </swiper-item>
            <swiper-item v-fix:height>
              <orders-mall :ref="OrdersType.GROUP_BUY"></orders-mall>
            </swiper-item>
            <swiper-item v-fix:height>
              <orders-mobrecharge :ref="OrdersType.MOBILE_RECHARGE"></orders-mobrecharge>
            </swiper-item>
          </swiper>
        </template>
      </div>
    </page>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
// import { Tabs } from '@/components';
import Tabs from '@/components/Tabs2.vue';
import { Swiper, SwiperItem } from '@/components/Swiper';
import VehicleBusinessOrders from '@pkg/vehicle-business/order/_OrdersView.vue';
import MallOrders from '@pkg/mall/_OrdersView.vue';
import ActivityOrders from '@pkg/wechat/_OrdersView.vue';
import MallG2Orders from '@pkg/mallg2/pages/_MallG2OrderList.vue';
import MobileOrderList from '@pkg/mobile-recharge/pages/orders/_OrderList.vue';
import BeautyOrders from '@/views/order/_OrdersView.vue';
import AllOrders from '@/views/order/AllOrders.vue';
import { dialog } from '@/bus';
import { isInWeApp } from '@/common/env';

const OrdersType = {
  ALL: 'all', // 全部订单
  BEAUTY: 'beauty', // 洗车养车
  VEHICLE_BUSINESS: 'vehicle-business', // 车务
  ACTIVITY: 'activity', // 报名活动
  FOOD: 'food', // 餐饮
  GROUP_BUY: 'group-buy', // 团购
  MOBILE_RECHARGE: 'mobrecharge', // 手机充值
}

const OrderCategory = [
  {
    value: OrdersType.ALL,
    name: '全部',
  },
  {
    value: OrdersType.BEAUTY,
    name: '洗车养车',
  },
  {
    value: OrdersType.VEHICLE_BUSINESS,
    name: '交管车务',
  },
  {
    value: OrdersType.ACTIVITY,
    name: '报名活动',
  },
  {
    value: OrdersType.FOOD,
    name: '美食',
  },
  {
    value: OrdersType.GROUP_BUY,
    name: '商城',
  },
  {
    value: OrdersType.MOBILE_RECHARGE,
    name: '充值',
  },
]

export default {
  name: 'AccountOrderList',
  props: {
    type: {
      type: String,
      default: OrdersType.ALL,
    }
  },
  mixins: [mixinAuthRouter],
  components: {
    Tabs,
    Swiper,
    SwiperItem,
    'all-orders': AllOrders,
    'orders-vehicle-business': VehicleBusinessOrders,
    'orders-beauty': BeautyOrders,
    'orders-mall': MallOrders,
    'orders-activity': ActivityOrders,
    'orders-food': MallG2Orders,
    'orders-mobrecharge': MobileOrderList,
  },
  mounted() {
    // this.init();
  },
  data() {
    const defaultType = Object.values(OrdersType).indexOf(this.type);
    return {
      AppStatus,
      status: AppStatus.LOADING,
      OrdersType,
      offsetTime: Date.now(),
      currentTypeIndex: defaultType,
      // currentTypeIndex: OrderCategory[0].value,
      refreshAction: 1,
      scrollable: true,
    };
  },
  computed: {
    // 小程序自定义交易组件审核需要售后能力，即小程序内需要展示此按钮表明小程序有售后能力
    shouldDisplayAfterSaleBtn() {
      return isInWeApp;
    },
    defaultSwiperIndex() {
      return Object.values(OrdersType).indexOf(this.type);
    },
    tabList() {
      return OrderCategory.map(item => {
        return {
          id: item.value,
          name: item.name,
          value: item.value,
        }
      });
    },
  },
  methods: {
    init() {
      this.status = AppStatus.READY;
      console.log('init ...');
      this.$nextTick(() => {
        // this.$refs.swiper.slideTo(this.defaultSwiperIndex);
        // this.$refs[this.currentTypeIndex].init();
        this.showPage(this.currentTypeIndex);
      })
      const tip = `
        <div style="text-align:left;color: #f72626;">
        亲~春节期间各业务有调整，请看清楚说明后再进店哦~ <br>
        1、洗车服务：2月2日--2月27日停止营业(前期已下单客户需在1月17日前进店消费），2月28日开始接单。<br>
        2、保养服务：因春节假期，进店前请先与对应门店联系确认是否营业。<br>
        3、上线审车、上门代审、二手车过户、新车上牌：1月23日-1月31日停止下单，2月1日开始接单。 <br>
        4、免检审车、证牌补换：1月21日-1月31日停止办理，2月1日开始接单。
        </div>
      `
      if (Date.now() > new Date(2020, 1 - 1, 13, 0, 0, 0) && Date.now() < new Date(2020, 2 - 1, 9, 0, 0, 0)) {
        dialog().alert(tip, {
          title: '业务提醒',
          ok: '知道了',
        })
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    switchType(index) {
      // console.log('onswithc...', arg)
      // this.currentTypeIndex = arg.value;
      this.$refs.swiper.slideTo(index);
    },
    showPage(index) {
      console.log('showPage:', index)
      const page = this.$refs[OrderCategory[index].value];
      if (page.status != AppStatus.READY) {
        page.init();
      } else {
        console.log('refresh...')
        page.onResume();
      }
    },
    onResume() {
      console.log('home resume...');
      this.showPage(this.currentTypeIndex);
    },
    onSlide(index) {
      this.currentTypeIndex = index;
      console.log('onSlide showPage:');
      this.showPage(index);
    },
  }
};
</script>
