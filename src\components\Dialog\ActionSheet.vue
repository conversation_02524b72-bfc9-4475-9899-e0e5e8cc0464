<template>
  <div class="actionsheet" v-show="show">
    <div class="weui-mask" @click="close"  :class="{'weui-animate-fade-out': !toggle }" @touchmove.prevent="()=>{}"></div>
    <div class="weui-actionsheet" :class="{'weui-actionsheet_toggle': toggle }">
      <div class="weui-actionsheet__title">
        <p class="weui-actionsheet__title-text">{{options.title}}</p>
      </div>
      <div class="weui-actionsheet__menu">
        <div
          v-for="(item, index) in options.menus"
          :key="index"
          @click="onClickMenu(index)"
          class="weui-actionsheet__cell"
        >{{item}}</div>
      </div>
      <div class="weui-actionsheet__action">
          <div class="weui-actionsheet__cell"  @click="onClickCancel">取消</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
  .weui-actionsheet__title {
    position: relative;
    height: 56px;
    padding: 0 24px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    font-size: 12px;
    line-height: 1.4;
    background: #fff;
  }

  .weui-actionsheet__title:before {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    color: rgba(0,0,0,0.1);
    transform: scaleY(0.5);
  }

  .weui-actionsheet__title .weui-actionsheet__title-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

</style>
<script>
  export default {
    name: 'ActionSheet',
    props: {
      options: {
        type: Object,
        default() {
          return {};
        },
      },
      counter: {
        type: Number,
        default: 1
      },
    },
    data() {
      return {
        show: false,
        toggle: false,
      };
    },
    mounted() {
      this.$on('hide', () => {
        this.show = false;
      });
    },
    computed: {
    },
    methods: {
      hide() {
        this.show = false;
      },
      open() {
        this.show = true;
        setTimeout(() => {
          this.toggle = true;
        }, 0)
      },
      close() {
        // console.log('onCancelClick...', e);
        this.toggle = false;
        setTimeout(() => {
          this.hide();
        }, 400)
        // this.onCancelClick();
      },
      onClickCancel(e) {
        // console.log('onCancelClick...', e);
        this.close();
      },
      onClickMenu(index) {
        this.options.onClick.call(this, index);
        this.close();
      },
    },
    watch: {
      options(val) {
        this.open();
      },
      show(val) {
        this.$overlay(val);
      }
    }
  };
</script>
