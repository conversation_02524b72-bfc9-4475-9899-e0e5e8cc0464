<template>
  <panel v-if="order.orderGoodsInfoList && order.orderGoodsInfoList.length" class="order-items" :title="title">
     <slot name="pre"></slot>
    <template v-if="order.orderGoodsInfoList">
      <div v-for="(item) in order.orderGoodsInfoList" :key="item.name" class="weui-cell">
        <div class="weui-cell__hd">
          <label class="weui-label">{{item.name}}</label>
        </div>
        <div class="weui-cell__bd"><span v-if="item.count" class="item-count">&times;{{item.count}}</span> <span class="rmb ">{{item.amount}}</span></div>
      </div>
    </template>
    <template v-if="order.orderGoodsInfoList">
      <template v-for="(item) in order.discountInfoList">
        <div :key="item.name" v-if="item.amount" class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">{{item.name}}</label>
          </div>
          <div class="weui-cell__bd discount-amount">-<span class="rmb">{{Math.abs(item.amount)}}</span></div>
        </div>
      </template>
    </template>
      <div class="weui-cell">
        <div class="weui-cell__hd">
          <label class="weui-label">合计</label>
        </div>
        <div class="weui-cell__bd"><strong class="rmb order-amount">{{orderPayAmount}}</strong></div>
      </div>
  </panel>
</template>
<style lang="scss" scoped>
  $highlight-color:#F29E5C;
  .order-items {
    .weui-label {
      width: 200px;
    }
    .item-count {
      margin-right: 5px;
    }
    .weui-cell {
      border-top: 0;
    }
    .weui-cell__bd {
      text-align: right;
    }
    .discount-amount {
      color: #F29E5C;
    }
  }
</style>
<script>
import { Panel } from '@/components';

export default {
  name: 'OrderDetailItems',
  props: {
    title: {
      type: String,
      default() {
        return '订单项目';
      }
    },
    value: {
      type: Object
    },
  },
  computed: {
    order() {
      return this.value;
    },
    orderPayAmount() {
      // 团购订单详情金额字段为needPay，其他订单中为needPayAmount
      if (this.order.needPayAmount != null) return this.order.needPayAmount;
      return this.order.needPay || 0;
    }
  },
  components: {
    Panel,
  },
  data() {
    return {};
  },
};
</script>
