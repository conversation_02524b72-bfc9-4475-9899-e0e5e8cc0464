import PrizePopGold from './PrizePopGold.vue';
import PrizePopCoupon from './PrizePopCoupon.vue';
import PrizePopWelfareCoupon from './PrizePopWelfareCoupon.vue';
import PrizePopMallMember from './PrizePopMallMember.vue';
import PrizePopCarMember from './PrizePopCarMember.vue';
import PrizePopPromotionalGoods from './PrizePopPromotionalGoods.vue';
import PrizePopNormal from './PrizePopNormal.vue';
import PrizePopCouponCode from './PrizePopCouponCode.vue';
import PrizePopRedEnvelope from './PrizePopRedEnvelope.vue';

import { PrizeType as PrizeTypeEnum } from '@/enums';

// 创建组件映射表，用于根据奖品类型动态加载对应组件
export const PrizeTypeComponentMap = {
  [PrizeTypeEnum.GOLD]: PrizePopGold,
  [PrizeTypeEnum.COUPON]: PrizePopCoupon,
  [PrizeTypeEnum.WELFARE_COUPON]: PrizePopWelfareCoupon,
  [PrizeTypeEnum.PROMOTIONAL_GOODS]: PrizePopPromotionalGoods,
  [PrizeTypeEnum.NORMAL]: PrizePopNormal,
  [PrizeTypeEnum.COUPONCODE]: PrizePopCouponCode,
  [PrizeTypeEnum.REDENVELOPE]: PrizePopRedEnvelope,
  [PrizeTypeEnum.MALLMEMBER]: PrizePopMallMember,
  [PrizeTypeEnum.CARMEMBER]: PrizePopCarMember,
};

export {
  PrizePopGold,
  PrizePopCoupon,
  PrizePopWelfareCoupon,
  PrizePopPromotionalGoods,
  PrizePopNormal,
  PrizePopCouponCode,
  PrizePopRedEnvelope,
  PrizePopCarMember,
  PrizePopMallMember,
};
