import { createEnums } from '@/enums/utils';

// 洗车养车
export const CarOrdersStatus = createEnums({
  Status_8: ['申请退款', 8, '申请退款'],
  Status_7: ['未支付已过期', 7, '未支付已过期'],
  Status_6: ['已过期', 6, '已过期'],
  Status_5: ['已退款', 5, '已退款'],
  Status_4: ['退款中', 4, '退款中'],
  Status_3: ['已评价', 3, '已评价'],
  Status_2: ['已服务', 2, '已服务'],
  Status_1: ['已付款', 1, '已付款'],
  Status_0: ['未付款', 0, '未付款'],
});
// 审车、车务
export const InspectionOrdersStatus = createEnums({
  Status_9: ['完成', 9, '完成'],
  Status_8: ['申请退款', 8, '申请退款'],
  Status_7: ['未支付过期', 7, '未支付过期'],
  Status_6: ['已过期', 6, '已过期'],
  Status_5: ['已退款', 5, '已退款'],
  Status_4: ['退款中', 4, '退款中'],
  Status_3: ['已评价', 3, '已评价'],
  Status_2: ['已核销', 2, '已核销'],
  Status_1: ['已付款', 1, '已付款'],
  Status_0: ['未付款', 0, '未付款'],
});
// 洗车卡、保养卡
export const CardBuyOrdersStatus = createEnums({
  Status_minus_3: ['交易关闭', -3, '交易关闭'],
  Status_minus_2: ['部分退款', -2, '部分退款'],
  Status_minus_1: ['已退款', -1, '已退款'],
  Status_3: ['退款中', 3, '退款中'],
  Status_2: ['已付款', 2, '已付款'],
  Status_1: ['未付款', 1, '未付款'],
});
// 活动
export const ActionOrdersStatus = createEnums({
  Status_minus_2: ['部分退款', -2, '部分退款'],
  Status_minus_1: ['已退款', -1, '已退款'],
  Status_3: ['退款中', 3, '退款中'],
  Status_2: ['已付款', 2, '已付款'],
});
// 话费充值
export const RechargeOrdersStatus = createEnums({
  Status_minus_1: ['已退款', -1, '已退款'],
  Status_2: ['已付款', 2, '已付款'],
  Status_1: ['未付款', 1, '未付款'],
});
// 团购
export const GroupBuyOrdersStatus = createEnums({
  Status_minus_3: ['部分退款', -3, '部分退款'],
  Status_minus_2: ['已退款', -2, '已退款'],
  Status_minus_1: ['交易关闭', -1, '交易关闭'],
  Status_9: ['驳回退款', 9, '驳回退款'],
  Status_8: ['申请退款', 8, '申请退款'],
  Status_5: ['已评价', 5, '已评价'],
  Status_4: ['已收货', 4, '已收货'],
  Status_3: ['已发货', 3, '已发货'],
  Status_2: ['已付款', 2, '已付款'],
  Status_1: ['未付款', 1, '未付款'],
});
// 美食
export const FoodsOrdersStatus = createEnums({
  Status_1: ['已付款', 1, '已付款'],
  Status_0: ['未付款', 0, '未付款'],
});
// 购买会员
export const MemberNewOrdersStatus = createEnums({
  Status_2: ['已付款', 2, '已付款'],
  Status_1: ['未付款', 1, '未付款'],
});
// 审车、车务
export const NewOrdersStatus = createEnums({
  Status_7: ['已完成', 7, '已完成'],
  Status_6: ['待评价', 6, '待评价'],
  Status_5: ['售后/退款', 5, '售后/退款'],
  Status_4: ['待收货', 4, '待收货'],
  Status_3: ['待发货', 3, '待发货'],
  Status_2: ['待使用', 2, '待使用'],
  Status_1: ['待支付', 1, '待支付'],
});
