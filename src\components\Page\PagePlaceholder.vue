<template>
  <div class="page-placeholder">
    <div class="page-placeholder-icon" :style="iconStyle"></div>
    <slot></slot>
  </div>
</template>
<style lang="scss" scoped>
.page-placeholder {
  color: rgb(202, 202, 202);
  font-weight: 500;
  margin-top: 15vh;
  text-align: center;
}
.page-placeholder-icon {
  background: url(./images/placeholder.png) center center no-repeat;
  height: 150px;
  background-size: 220px, auto, contain;
}
</style>
<script>
  export default {
    name: 'PagePlaceholder',
    props: {
      icon: {
        type: String,
        default: '',
      }
    },
    computed: {
      iconStyle() {
        const style = {};
        if (this.icon) {
          style.backgroundImage = `url(${this.icon})`;
        }
        return style;
      }
    }
  };
</script>
