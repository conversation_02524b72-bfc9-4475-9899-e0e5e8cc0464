@import "./variable";
@import './mixin.less';
.net-player {
  width: 100%;
  height: 100%;
  .normal-player {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index:202203;
    background-color: #7F5959;
    overflow-x: hidden;
    overflow-y: scroll;
    padding-top: calc(45px + constant(safe-area-inset-top));
    padding-top: calc(45px + env(safe-area-inset-top));
    box-sizing: border-box;
    .player-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 45px;
      padding: 0 15px 0 15px;
      color: rgba(255,255,253);
      background-color: #7F5959;
      position: fixed;
      top: constant(safe-area-inset-top);
      top: env(safe-area-inset-top);
      left: 0;
      width: 100%;
      box-sizing: border-box;
      z-index: 200;
      .icon-xf-zhankai-xiala {
        color: #ffffff;
        font-size: 22px;
      }
      .icon-xf-fenxiang {
        color: #ffffff;
        font-size: 22px;
      }
      .name {
        font-size: 0;
        flex: 1;
        .song-name {
          font-size: 16px;
          padding: 0 15px;
          margin: 0 auto;
          /deep/ .van-notice-bar__wrap {
            margin-top: 10px;
          }
        }
        .singer {
          margin-top: 9px;
          display: flex;
          justify-content: center;
          color: rgb(116,136,149);
          .text {
            font-size: 16px;
            margin-right: 7px;
          }
          .icongengduo1 {
            font-size: 16px;
          }
        }
      }
    }
    .player-cover {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 15px;
      margin-bottom: 50px;
      .cd {
        width: 100%;
        height: 100%;
        position: relative;
        .inner-circle {
          margin: 24px auto 12px;
          width: 270px;
          height: 270px;
          border-radius: 50%;
          background-image: url("https://s3.music.126.net/mobile-new/img/disc-plus.png?b700b62e1971b351dcb8b8ce1c9ceea3=");
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          .album-img {
            width: 250px;
            height: 250px;
            z-index: 10;
            position: absolute;
            &.rotate {
              animation-name: rotate;
              animation-duration: 20s;
              animation-iteration-count: infinite;
              animation-timing-function: linear;
            }
            &.rotate_pause {
              animation-play-state: paused;
            }
            .image {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
          }
        }
        .music-pole {
          position: absolute;
          top: 0;
          width: 90px;
          height: 160px;
          background-color: transparent;
          left: 50%;
          z-index: 15;
          margin-left: -44px;
          background-image: url("../images/needle.png");
          background-size: 90px 160px;
          transform-origin: 0 0;
          transition: all 1s;
          &.rateCls {
            transform: rotate(0deg);
          }
          &.pase_rateCls {
            transform: rotate(-20deg);
          }
        }
        .current-text {
          text-align: center;
          color: #ffffff;
          font-size: 18px;
          font-weight: bold;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          // overflow: hidden;
          max-width: 90%;
          margin: 0 auto;
        }
      }
      .lyric {
        width: 100%;
        height: 100%;
        font-size: 12px;
        line-height: 26px;
        position: absolute;
        top: 0;
        left: 0;
        overflow: hidden;
        color: rgb(153,153,153);
        z-index: 14;
        text-align: center;
        .current {
          font-weight: 550;
          color: #ffffff;
        }
      }
    }
    .player-list-icon{
      text-align: center;
      .list-icon{
        height: 32PX;
        padding: 5px 10px;
        box-sizing: border-box;
        background: #8C6464;
        border-radius: 20px;
        display: inline-flex;
        align-items: center;
        color: #ffffff;
        font-size: 14PX;
        color: #ffffff;
        line-height: normal;
        .icon-svg{
          font-size: 20PX;
          margin-right: 4px;
        }
        span{
          padding: 0.05rem 0;
        }
      }
    }
    .player-control {
      width: 100%;
      box-sizing: border-box;
      padding: 15px;
      .operator_1{
        display: flex;
        justify-content: space-between;
        color: rgb(185,191,200);
        padding: 0 15px;
        align-items: center;
        .iconshoucang3 {
          font-size: 20px;
        }
        .iconcollection {
          font-size: 20px;
          color: #8f342d;
        }
        .icondibar-xiazai {
          font-size: 20px;
        }
        .iconicon-test {
          font-size: 20px;
        }
        .iconpinglun1 {
          font-size: 20px;
          position: relative;
          .comment-count {
            position: absolute;
            font-size: 10px;
            top: -8px;
            left: 15px;
          }
        }
        .iconsandian {
          font-size: 20px;
        }
      }
      .operator_2 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: rgb(185,191,200);
        padding: 0 15px;
        margin-top: 15px;
        .icon-svg{
          font-size: 20px;
          color: #ffffff;
        }
        .icon-Loop {
          font-size: 20px;
        }
        .icon-shangyiqu101 {
          font-size: 20px;
        }
        .icon-bofang1 {
          font-size: 50px;
        }
        .icon-bofang {
          font-size: 50px;
        }
        .icon-xiayiqu101 {
          font-size: 20px;
        }
        .icon-bofangliebiao {
          font-size: 20px;
        }
        .play-icon{
          width: 60px;
          height: 60px;
          background: #ffffff;
          border-radius: 50%;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          .icon-svg{
            font-size: 30px;
            color: #7F5959 !important;
          }

        }
      }
    }
  }

  .mini-player {
    position: fixed;
    left: 0;
    bottom: 0;
    // height: 55px;
    z-index: 202204;
    right: 0;
    background-color: #ffffff;
    color: var(--font-color);
    border-top: 1px solid #EEEEEE;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    .mini-icon {
      width: 50px;
      height: 50px;
      flex: 0 0 50px;
      font-size: 0;
      border: 5px solid #333333;
      border-radius: 50%;
      box-sizing: border-box;
      &.rotate {
        animation-name: rotate;
        animation-duration: 20s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
      }
      &.rotate_pause {
        animation-play-state: paused;
      }
      .image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }
    .text {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-left: 10px;
      font-size: 16px;
      font-weight: bold;
      color: #333333;
      line-height: 20px;
      .duration {
        margin-top: 5px;
        font-size: 10px;
        font-weight: 500;
        color: #333333;
        line-height: 1;
      }
    }
    .play-icon{
      width: 40px;
      height: 40px;
      background: #ffffff;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      .icon-svg{
        font-size: 24px;
        color: #7F5959 !important;
      }

    }
  }
}
.player-recommend{
  padding: 15px;
  .card{
    padding: 10px;
    box-sizing: border-box;
    width: 100%;
    background: #8C6464;
    border-radius: 8px;
    display: flex;
    align-items: center;
    color: #ffffff;
    .avatar{
      width: 50px;
      height: 50px;
      border-radius: 10px;
      margin-right: 10px;
    }
    .info{
      flex: 1;
      line-height: 1;
      p{
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      span{
        font-size: 14px;
        opacity: .8;
      }
    }
    .icon-svg{
      font-size: 22px;
      transform: rotate(90deg);
    }
  }
}
.net-player {
  /deep/ .van-notice-bar {
    background-color:transparent;
    color: rgb(254,254,254);
    text-align: center;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  //50% {
  //  transform: rotate(180deg);
  //}
  100% {
    transform: rotate(360deg);
  }
}

