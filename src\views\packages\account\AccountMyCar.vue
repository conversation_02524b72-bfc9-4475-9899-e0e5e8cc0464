<template>
  <container
    class="account-my-car"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
  >
    <x-header title="爱车档案">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <div class="car-box">
          <div class="content">
            <div class="car-list" v-if="cars && cars.length > 0">
              <div v-for="(item, index) in cars" :key="index">
                <div class="car-item" v-if="item.carModelInfo">
                  <div class="item-top">
                    <c-picture
                      class="card-logo"
                      :src="item.carModelInfo.brandLogo"
                    ></c-picture>
                    <div class="car-right">
                      <div class="title">
                        {{ item.carModelInfo.carSysName }}
                      </div>
                      <div class="des">{{ item.carModelInfo.name }}</div>
                    </div>
                  </div>
                  <div class="item-footer">
                    <div class="left">
                      <van-radio
                        :name="item.id"
                        v-model="radioVal"
                        checked-color="#FD4925"
                        icon-size="14px"
                        @click="radioChange(item)"
                      ></van-radio>
                      <span :class="[item.isDefault ? 'acvive-default' : '']"
                        >{{ item.isDefault ? '已' : '' }}设为默认</span
                      >
                    </div>
                    <div class="right">
                      <span @click="deleteCar(item.id, item)">删除</span>
                      <span @click="editCar(item)">修改</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="no-car" v-if="cars && cars.length === 0"></div>
          </div>
          <div v-if="cars && cars.length === 0 && lhxNum > 0" class="gold-tips">
            添加您的爱车信息，即可立即获得 <span> {{ lhxNum }} </span>个金币！
          </div>
          <div
            v-if="cars && cars.length < CAR_ADD_LIMIT"
            :class="[
              'btn-fixed-wrap',
              cars && cars.length > 0 ? 'btn-fixed' : '',
            ]"
          >
            <div
              v-if="cars && cars.length < CAR_ADD_LIMIT"
              :class="['btn-add']"
              :style="cars && cars.length == 0 ? 'width:100%' : ''"
              @click="showPopover = true"
            >
              添加爱车
            </div>
          </div>
        </div>
        <van-action-sheet
          v-model="showPopover"
          :actions="actions"
          cancel-text="取消"
          close-on-click-action
          @select="onSelect"
          @cancel="showPopover = false"
        />
      </template>
    </content-view>
  </container>
</template>
<script>
import { formatDate } from '@/utils';
import { Panel } from '@/components';
import { AppStatus, CarType } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
import { getMyCars, setMyDefaultCar, removeMyCar, getCarinfoTask } from '@/api';
import { listen, Events, dialog, toast, loading } from '@/bus';
import { Radio, ActionSheet } from 'vant';
const CAR_ADD_LIMIT = 5;
const CAR_TYPE = {
  OIL: 'oil',
  NEW_ENERGY: 'new_energy',
};

export default {
  name: 'AccountMyCar',
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      CarType,
      CAR_TYPE,
      CAR_ADD_LIMIT,
      status: AppStatus.LOADING,
      carIndex: null,
      radioVal: '',
      lhxNum: 0,
      page: {
        cars: null,
      },
      showPopover: false,
      // 通过 actions 属性来定义菜单选项
      actions: [
        { name: '燃油车', type: CarType.OIL },
        { name: '新能源', type: CarType.NEW_ENERGY },
      ],
    };
  },
  computed: {
    cars() {
      if (!this.page.cars) return null;
      return this.page.cars.filter(item => {
        // body
        return item.carModelInfo;
      });
    },
  },
  components: {
    [Radio.name]: Radio,
    [ActionSheet.name]: ActionSheet,
  },
  mounted() {
    this.status = AppStatus.READY;
  },
  methods: {
    onSelect(action) {
      if (action.type == CarType.NEW_ENERGY) {
        this.goAddCar(CarType.NEW_ENERGY);
      } else {
        this.goAddCar();
      }
    },
    goAddCar(carType = CarType.OIL) {
      // console.log(this.$options, 'f');
      const from = this.$options.name;
      this.$_router_push4inputting(
        `/car/brands?from=${from}&carType=${carType}`,
        {
          title: '添加爱车',
        }
      );
    },
    editCar(val) {
      const url = `/account/car/${val.id}/edit`;
      this.$_router_push4inputting(url, {
        title: '修改爱车',
      });
    },
    radioChange(val) {
      this.setDefaultCar(val);
    },
    // 设为默认值
    setDefaultCar(val) {
      const that = this;
      if (val.isDefault) return;
      loading(true, '正在提交...');

      setMyDefaultCar(val.id).then(
        res => {
          loading(false);
          that.initPageData();
        },
        err => {
          loading(false);
          err &&
            dialog().alert(err, {
              title: '',
            });
        }
      );
    },
    deleteCar(carId, item) {
      const that = this;
      dialog().confirm('确定要删除这辆车吗？', {
        title: '提示',
        ok() {
          loading(true, '正在提交...');
          removeMyCar(carId).then(
            res => {
              loading(false);
              that.initPageData().then(() => {
                if (item.isDefault && that.cars.length > 0) {
                  that.setDefaultCar(that.cars[0]);
                }
              });
            },
            err => {
              loading(false);
              err &&
                dialog().alert(err, {
                  title: '',
                });
            }
          );
        },
      });
    },
    initPageData() {
      return getMyCars().then(
        cars => {
          this.page.cars = cars;
          // console.log(this.page.cars, 'this.page.cars');
          if (cars.length > 0) {
            cars.forEach((car, index) => {
              if (car.isDefault) {
                this.radioVal = car.id;
              }
            });
          }
          if (cars.length == 0) {
            getCarinfoTask().then(res => {
              this.lhxNum = res && res.lhxNum ? res.lhxNum : 0;
            });
          }
          // if (!/^\d+$/.test(this.carIndex)) {
          //   let carIndex = 0;
          //   cars.some((car, index) => {
          //     if (car.isDefault) carIndex = index;
          //     return car.isDefault;
          //   });
          //   this.carIndex = carIndex;
          // }
          this.status = AppStatus.READY;
          return cars;
        },
        err => {
          this.status = AppStatus.ERROR;
          console.error(err);
        }
      );
    },
    init() {
      return this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      // 为了避免点击添加爱车中途若取消返回时refreshSlider会让slider焦点发生变化，此处做特别判断，只再车辆数量发生变化后再refreshSlider
      // const cars1 = this.cars;
      // // this.initPageData().then((cars2) => {
      // //   const shouldRefreshSlider = cars1.length != cars2.length;
      // //   if (shouldRefreshSlider) {
      // //     this.refreshSlider();
      // //   }
      // // });
      this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.content-wrapper {
  background: #f3f3f3;
}
.car-box {
  padding: 15px;
  position: relative;
  box-sizing: border-box;
  .no-car {
    background: url(~@/assets/images/account/mycard_bg.png) no-repeat center;
    background-size: 100% 100%;
    height: 129px;
    width: 244px;
    margin: 0 auto;
    margin-bottom: 57px;
    margin-top: 100px;
  }
  .btn-fixed-wrap {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    display: grid;
    grid-template-columns: 1fr;
    // grid-gap: 15px;
  }
  .btn-add {
    height: 44px;
    background: #fd4925;
    border-radius: 22px;
    text-align: center;
    line-height: 44px;
    font-size: 18px;
    color: #ffffff;
    // width: 345px;
  }
  .btn-add-green {
    // background: #2da2ff;
  }
  .btn-fixed {
    position: fixed;
    bottom: 35px;
    width: 345px;
  }
  .car-list {
    margin-bottom: 80px;
    .car-item {
      padding: 22px 15px 20px 15px;
      background: #ffffff;
      border-radius: 10px;
      box-sizing: border-box;
      margin: 15px 0;
      &:first-child {
        margin-top: 0;
      }
      .item-top {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 20px;
        .card-logo {
          width: 50px;
          height: 50px;
          margin-right: 15px;
          flex-shrink: 0;
        }
        .car-right {
          .title {
            font-size: 18px;
            font-weight: bold;
            color: #333333;
            line-height: 18px;
          }
          .des {
            font-size: 13px;
            color: #666666;
            line-height: 16px;
            margin-top: 10px;
          }
        }
      }
      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
        border-top: 1px solid #eee;
        padding-top: 15px;
        .left {
          display: flex;
          justify-content: center;
          align-items: center;
          span {
            margin-left: 9px;
          }
          .acvive-default {
            color: #fd4925;
          }
        }
        .right {
          span:nth-child(1) {
            margin-right: 30px;
          }
        }
      }
    }
  }
  .gold-tips {
    font-size: 12px;
    color: #999999;
    text-align: center;
    margin-bottom: 15px;
    span {
      color: $lh-2022-primary-color;
    }
  }
}
</style>
