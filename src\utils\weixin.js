import { isInWeixin } from '@/common/env';
/**
 * 获取微信的版本号
 * @param {*} path
 */
export function getWeixinVersion() {
  let version = ''
  if (isInWeixin) {
    let wechatInfo = navigator.userAgent.match(/MicroMessenger\/([\d.]+)/i)
    if (wechatInfo && wechatInfo.length > 1) {
      // 获取微信的版本号
      version = wechatInfo[1]
    }
  }
  return version
}

/**
 * 比较版本号
 * @param {*} path
 */
export function compareVersion(v1, v2) {
  v1 = v1.split('.')
  v2 = v2.split('.')
  const len = Math.max(v1.length, v2.length)

  // 调整两个版本号位数相同
  while (v1.length < len) {
    v1.push('0')
  }
  while (v2.length < len) {
    v2.push('0')
  }

  // 循环判断每位数的大小
  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i])
    const num2 = parseInt(v2[i])

    if (num1 > num2) {
      return 1
    } else if (num1 < num2) {
      return -1
    }
  }

  return 0
}
