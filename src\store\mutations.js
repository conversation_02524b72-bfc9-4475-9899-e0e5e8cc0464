import * as types from './mutation_types';

const mutations = {
  [types.UPDATE_TRANSITION_NAME]: (state, name) => {
    state.views.transitionName = name;
  },
  [types.UPDATE_TRANSITION_MODE]: (state, mode) => {
    state.views.transitionMode = mode;
  },
  [types.UPDATE_AUTH]: (state, sessionId) => {
    state.auth = {
      session: sessionId,
      loggedIn: true,
    };
  },
  [types.UPDATE_USER_INFO]: (state, user) => {
    state.user = user;
  },
  [types.UPDATE_LOGIN_STATE]: (state, value) => {
    state.auth.loggedIn = value;
    state.auth.lastUpdate = Date.now();
  },
  [types.UPDATE_SHARE_INFO]: (state, value) => {
    state.shareInfo = value;
  },
  [types.ADD_ROUTE]: (state, { key, value }) => {
    state.views.router[key] = value;
    state.views.routePaths = Object.values(state.views.router);
    state.views.routeList = Object.keys(state.views.router);
  },
  [types.REMOVE_ROUTE]: (state, key) => {
    delete state.views.router[key];
    state.views.routePaths = Object.values(state.views.router);
    state.views.routeList = Object.keys(state.views.router);
  },

  [types.SET_CURRENT_CAR]: (state, value) => {
    state.views.selectedCar = value;
  },
  [types.SET_FULLSCREEN](state, fullscreen) {
    state.audioPlayerInfo.fullScreen = fullscreen;
  },
  [types.SET_PLAYLIST](state, playlist) {
    state.audioPlayerInfo.playList = playlist;
  },
  [types.SET_CURRENTINDEX](state, currentIndex) {
    state.audioPlayerInfo.currentIndex = currentIndex;
  },
  [types.SET_PLAYING_STATUS](state, playing) {
    state.audioPlayerInfo.playing = playing;
  },
  [types.REVERSE_PLAYLIST](state, playlist) {
    state.audioPlayerInfo.playList.reverse();
  },
  [types.SET_RADIO_INFO](state, radioInfo) {
    state.audioPlayerInfo.radioInfo = radioInfo;
  },
  [types.SET_PLAYLIST_PAGE](state, page) {
    state.audioPlayerInfo.playListPage = page;
  },
  [types.SET_AUDIO_TOTAL](state, total) {
    state.audioPlayerInfo.audioTotal = total;
  },
  [types.SET_PLAYLIST_SEQUENCE](state, sequence) {
    state.audioPlayerInfo.playListSequence = sequence;
  },
  [types.SET_CURRENT_SONG](state, song) {
    state.audioPlayerInfo.currentSong = song;
  },
  [types.SET_WEBP_SUPPORT](state, status) {
    state.supportWebP = status;
  },
  [types.SET_APP_SETTING](state, setting) {
    state.appSetting = setting;
  },
};

export default mutations;
