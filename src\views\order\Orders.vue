<template>
  <container @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="洗车养车订单">
      <x-button  slot="left" type="back"></x-button>
      <x-button  slot="right" type="phone" @click="showContacts"></x-button>
    </x-header>
    <orders-view ref="orders"></orders-view>
    <transition name="actionsheet">
      <router-view></router-view>
    </transition>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import BeautyOrdersView from './_OrdersView.vue';

export default {
  name: 'BeautyOrderList',
  components: {
    'orders-view': BeautyOrdersView,
  },
  data() {
    return {
      AppStatus,
    };
  },
  methods: {
    showContacts() {
      this.$router.push({
        name: 'order/contact',
      })
    },
    onLeave() {
      this.$refs.orders.onLeave();
    },
    onResume() {
      this.$refs.orders.onResume();
    },
    init() {
      this.$refs.orders.init();
    },
  }
};
</script>
