import { doGet, doPost, postJSON } from '../request/';
import { getGeoData, getUserID, login } from '@/bridge';
import { getImageUploadToken, setImageUploadToken } from '@/common/image';
import { getCity } from '@/store/storage';
import APIs from '../apis';
import { getUserAccountInfo, getUserRedPocketAccountInfo } from '@/api/modules/account';
import APIModel from '../APIModel';

APIs.extend({
  // 获取天气信息
  '/weather': '',

  '/server/time': '/Radio/platform/time', // 服务器时间
  '/user/ip': '/Auth/v3/user/ip', // 获取用户外网ip
  // 获取门店信息
  '/shop': '/app/car/business/info',
  '/v2/shop': '/app/car/business/info',

  // 获取门店评论列表
  '/shop/comments': '/app/car/business/comments',

  // 获取新闻列表
  '/news': '/Radio/news/newslistbytype?type=3&max=10&orient=0',

  // 获取七牛上传token
  '/upload/token': '/Radio/resource/cloud/bulk/gettoken',

  // 获取通知数量
  '/notice/count': '/app/car/order/notice/unread/count',

  // 将通知标记为已读
  '/notice/read': '/app/car/order/notice/mark',

  // 获取洗车区域列表
  '/city/areas': '/app/car/city/areas',

  // 获取洗车城市列表
  '/cities': '/app/car/province/areas',

  // 统计用户拨打电话
  '/statistics/business/mc': '/app/car/user/call/business',

  // 检验session是否有效
  '/session/check': '/Auth/v3/user/session/status',

  // 收银台获取订单信息
  '/cashier/order/info': '/Radio/order/find/by/orderid',

  // 校验银行卡号能否用于支付
  '/cashier/bankcard/check': '/app/car/onedollar/wash/order/judge/bankAccNo',

  // 获取小程序码
  '/get/wxacode': '/app/goods/get/wxacode',
})

/**
 * 检验session信息是否有效
 * @param {*} url
 */
const AUTH_FLAG_KEY = '_u_auth_flag';

function getAuthFlag() {
  return JSON.parse(sessionStorage.getItem(AUTH_FLAG_KEY)) || false;
}

function setAuthFlag(value) {
  sessionStorage.setItem(AUTH_FLAG_KEY, JSON.stringify(value));
}

/**
 * 查询当前会话是否有效
 * 2019年7月1日，微信授权增加了匿名授权登录，不需要验证手机号即可通过验证(为了满足个别业务不需要验证手机号也可使用)
 * 在jglh-webapp项目中，暂无支持匿名授权登录的业务
 * 匿名授权登录的会话，此接口会都一个 anonymous 参数 值为true
 */
export function checkSession() {
  const api = APIs.get('/session/check');
  return doGet(api).then(res => {
    // if (!res || res.status !== true) {
    //   return Promise.reject('session已失效');
    // }
    const ok = res.status === true && res.anonymous !== true;
    return { ok };
  });
}

/**
 * 获取客户端与服务器时差
 */
export function getServerTimeDiffer() {
  const now1 = Date.now();
  return doGet('/Radio/platform/time').then(res => {
    const now2 = Date.now();
    const serverTime = res + (now2 - now1) / 2; // 请求耗时误差，取c->s，s->c时长的平均值
    const differ = serverTime - Date.now();
    return differ;
  })
}

/**
 * 获取用户ip地址
 */
export function getUserIp() {
  return doGet('/Auth/v3/user/ip');
}

/**
 * 初始化登录数据
 * @returns {Promise.<notice, weather, news>}
 */
export function initAuth() {
  return new Promise((resolve, reject) => {
    login()
  });
}

/**
 * 获取首页数据
 * @returns {Promise.<news>}
 */
export function getHomeData() {
  const params = {
    t: Date.now()
  };
  const requests = [
    doGet(APIs.get('/news'), params, {
      headers: {}
    }),
  ];
  return Promise.all(requests);
}

export function getUnreadNoticeCount() {
  return doGet(APIs.get('/notice/count'), {}, {
    onAuthorityFail(resolve, reject) {
      return {
        unreadCount: 0
      };
    }
  });
}

/**
 * 获取车主讲堂新闻列表
 * @param {*} params
 */
export async function getNewsList(params) {
  return doGet(APIs.get('/news'), params, {
    headers: {}
  });
}

/**
 * 根据图片url地址将图片上传到七牛云服务器
 * @param {string} url
 */
export function uploadToQiniuByImageURL(url) {
  return doPost('/Radio/resource/image/fetch', {
    url
  })
}

/**
 * 获取上传token
 * @param {number} type 上传类型 1：图片，2视频
 */
export function getQiniuUploadToken(type) {
  const url = APIs.get('/upload/token');
  return doGet(url, { type }, { headers: {} }).then(res => {
    return res;
  });
}

/**
 * 获取图片上传token，先取本地缓存中可用token，本地没有则从接口获取
 * @returns {*}
 */
export function getUploadToken() {
  const token = getImageUploadToken();
  if (token) return Promise.resolve(token);
  return getQiniuUploadToken(1).then(res => {
    setImageUploadToken(res.token);
    return res.token;
  });
}

/**
 * 获取城市列表
 */
export function getCityList(params) {
  return doGet(APIs.get('/cities'), params);
}

/**
 * 获取商家资料
 */
export async function getShopData(bid) {
  const { longitude: lng, latitude: lat } = await getGeoData();
  const url = APIs.get('/shop');
  return doGet(url, { bid, lng, lat });
}

/**
 * 获取城市区域列表
 */
export async function getCityAreas(geo) {
  const city = getCity().id;
  const params = {
    city,
    ...geo
  }
  let areaList = await doGet(APIs.get('/city/areas'), params);
  const areas = areaList.map(item => {
    return {
      name: item.name,
      value: item.id,
    }
  })
  return Promise.resolve(areas);
}

/**
 * 获取指定城市区域列表
 */
export function getAreasOfCity(city) {
  return doGet(APIs.get('/city/areas'), { city });
}

export function getShopComments({ bid, type = 'all', page = 1, rows = 10 }) {
  const url = APIs.get('/shop/comments');
  return doGet(url, { bid, type, page, rows });
}

/**
 * 记录拨打电话记录
 * @param { string } bid 商家id
 */
export function recordPhoneCall(bid) {
  const url = APIs.get('/statistics/business/mc');
  return doGet(url, { bid });
}

/**
 * 查询收银台订单信息
 * @param {*} oid
 */
export function getCashierOrderInfo(oid) {
  const url = APIs.get('/cashier/order/info');
  return doGet(url, { orderId: oid });
}

/**
 * 校验银行卡号能否用于支付
 * @param {object} params
 * @param {string} params.sysOid 订单号
 * @param {string} params.accNo 银行卡号
 */
export function checkBankCard(params) {
  const url = APIs.get('/cashier/bankcard/check');
  return doPost(url, params);
}

/**
 * 获取新收银台所需数据
 * @param {string} sid 系统订单号
 */
export function getCashierCheckoutViewData(sid) {
  return Promise.all([
    getUserRedPocketAccountInfo(),
    getCashierOrderInfo(sid),
    getServerTimeDiffer(),
  ]).then(([account, order, sdtime]) => {
    return {
      account,
      order,
      sdtime,
    }
  })
}

/**
 * 获取小程序码
 * @param {string} path 小程序完整路径
 */
export function getWxacode (options) {
  const url = APIs.get('/get/wxacode');
  return postJSON(url, options);
}
