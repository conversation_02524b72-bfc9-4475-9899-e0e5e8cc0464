<style lang="scss" scoped>
.vip-msg__content{
  height: 70px;
  width:315px;
  background: linear-gradient(90deg, #FFF5ED, #FFDBBC);
  border-radius: 10px;
  box-sizing: border-box;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  margin: 0 auto;
  &::after{
      content: '';
      display: block;
      width: 90px;
      height: 70px;
      background: url("./images/vip-bg.png") no-repeat;
      background-size: 100%;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
  }
  .top-title-box{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    z-index: 2;
    .vip-text-left{
      font-size: 11px;
      background: url("./images/vip-icon.png") no-repeat left center;
      background-size: 12px 10px;
      padding-left: 18px;
      display: inline-block;
      color: #A95930;
    }
    .vip-text-right{
      display: flex;
      align-items: center;
      .check-name{
        height: 13px;
        padding: 3px 5px;
        line-height: 13px;
        text-align: center;
        background: linear-gradient(90deg, #2B2B38, #39394D);
        border-radius: 8px 8px 0px 8px;
        font-size: 10px;
        color: #FFFFFF;
        display: inline-block;
      }
      .checkbox{
        background: #FFFFFF;
        border-radius: 2px;
        margin-left:5px;
        width: 15px;
        height: 15px;
      }
      .van-checkbox{
        .van-checkbox__icon .van-icon{
          border: none;
        }
      }
    }
  }
  .vip-msg-text{
    height: 30px;
    background: #FFF5ED;
    border-radius: 5px;
    font-size: 15px;
    color: #A95930;
    line-height: 30px;
    padding-left: 11px;
    font-weight: bold;
    z-index: 2;
    .text_many{
      color: #FD4925;
    }
  }
}

// 弹出层样式
.van-popup{
  background: none;
  border-radius: 10px 10px 0px 0px;
}
.vip-popup-content{
  background: #363647;
  border-radius: 10px 10px 0px 0px;
  color:#FFE9DB;
  .vip-popup-title{
    font-size: 18px;
    font-weight: bold;
    border-bottom: 1px solid #454568;
    text-align: center;
    line-height: 49px;
    position: relative;
    .close{
      position: absolute;
      right: 0;
      line-height: 49px;
      margin-right: 15px;
    }
  }

  .li-content{
    padding:16px 15px 8px 15px;
    padding-bottom: 62px;
    .li-popup{
      border: 1px solid;
      border-image: linear-gradient(-18deg, #FBD0AE, #777796, #FBD0AE) 1 1;
      border-image-source: none;
      border-radius: 10px;
      margin-bottom: 16px;
      .li-title{
        height: 60px;
        background: #3C3C52;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 10px;
        padding-right: 15px;
        border-radius: 10px 10px 0 0;
        .title-left{
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .img{
            width: 30px;
            height: 30px;
            margin-right: 10px;
            img{
              width: 100%;
            }
          }
          .text-c{
            .name{
              font-size: 16px;
              font-weight: bold;
            }
            .many{
              font-size: 10px;
              color: #B2A4A3;
            }
          }
        }
        .title-right{
          display: flex;
          justify-content: center;
          align-items: center;
          .check-name{
            font-size: 10px;
            color: #242435;
            height: 15px;
            background: linear-gradient(90deg, #FFE9DB, #F7BC88);
            border-radius: 8px 8px 0px 8px;
            display: inline-block;
            padding: 2px 4px;
            line-height: 15px;
            margin-right: 5px;
          }
        }
      }
      .li-box{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 16px 12px;
        flex-wrap: wrap;

        .item-box{
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 25%;
          margin-bottom: 10px;
          &:nth-last-child(-n+4){
            margin-bottom: 0;
          }
          .right-icon{
            width: 40px;
            height: 40px;
            margin-bottom: 12px;
          }
          .item-text,.item-desc{
            white-space: nowrap;
          }
          .item-text{
            font-size: 12px;
            color: #fff;
            font-weight: bold;
            line-height: 12px;
          }
          .item-desc{
            font-size: 10px;
            color: #676786;
            line-height: 12px;
            margin-top: 5px;
          }
        }
      }
      .no-check{
        border-radius: 10px;
      }
    }

  }
  .popup-btn-box{
    position: fixed;
    bottom: 0;
    width:100%;
    background: #363647;
    padding: 12px 0;
  }
  .popup-btn{
    height: 44px;
    background: linear-gradient(90deg, #FFE9DB, #F7BC88);
    border-radius: 22px;
    font-size: 18px;
    font-weight: bold;
    color: #363647;
    line-height: 44px;
    text-align: center;
    width: 345px;
    margin:0 auto;
  }
}
// 选中会员类型样式
.vip-check-box{
  padding: 0 10px;
  width: 315px;
  height: 50px;
  background: linear-gradient(90deg, #FFF5ED, #FFDBBC);
  border-radius: 10px;
  line-height: 50px;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-content: center;
  z-index: 2;
  &::after{
    content: '';
    display: block;
    width: 65px;
    height: 50px;
    background: url("./images/vip-bg.png") no-repeat;
    background-size: 100%;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
  }
  .vip-icon{
    background: url("./images/vip-icon.png") no-repeat left center;
    background-size: 21px 18px;
    padding-left: 31px;
    font-size: 15px;
    font-weight: bold;
    color: #A95930;
    z-index: 2;
    display: inline-block;
  }
}
</style>
<style>
.radioCheck .van-checkbox__icon .van-icon{
  background: #fff;
  z-index: 2;
}
</style>
<template>
  <div class="vip-msg">
    <!-- <div class="vip-msg__content">
      <div class="vip-msg__title"><span class="vip-icon"></span>勾选开通VIP会员，本单再减 <span class="rmb">{{formatMoney(discountAmount)}}</span></div>
      <div class="vip-msg__desc"><slot name="desc">开通VIP会员，专享11项特权</slot></div>
    </div>
    <div class="vip-msg__btn">
      <label
        class="weui-cells_radio weui-check__label"
      >
        <input type="checkbox" class="weui-check" v-model="joinVip">
        <span class="weui-icon-checked" @click.stop.prevent="toggle"></span>
      </label>
      <span class="vip-checkbox" :class="{ 'vip-checkbox-checked': joinVip }"></span>
    </div> -->
   <div class="vip-msg__content" v-if="joinVip==false">
      <div class="top-title-box">
        <span class="vip-text-left">交广领航会员</span>
        <label class="vip-text-right" @click="handleCheck">
          <span class="check-name">点我开通→</span>
          <div class="checkbox"></div>
          <!-- <van-checkbox class="checkbox"  v-model="joinVip" checked-color="#FD4925" icon-size="15px" shape="square" @click="handleCheck" @change="handleChange"></van-checkbox> -->
        </label>
      </div>
      <div class="vip-msg-text">开通会员，此报名费用立减 <span class="text_many">{{formatMoney(discountAmount)}}元</span></div>
    </div>

    <div class="vip-check-box" v-if="joinVip && checkName!='no'">
      <span class="vip-icon">已选择开通交广领航{{checkName=='mall'? '商城会员':'车主会员'}}</span>
      <van-checkbox v-model="joinVip" icon-size="20px" checked-color="#FD4925" class="radioCheck"></van-checkbox>
    </div>
    <!-- 弹出框 -->
    <van-popup v-model="show" position="bottom" :style="{ height: '70%' }">
      <div class="vip-popup-content">
        <div class="vip-popup-title">
          <span>选择会员类型</span>
          <van-icon name="close" size="18" color="#8F8F9D" class="close" @click="handleClose"/>
        </div>
        <!-- 会员权益 -->
        <div class="li-content" v-if="vipArray.length>0">
          <div class="li-popup" v-for="item in vipArray" :key="item.id">
            <div class="li-title">
              <div class="title-left">
                <div class="img">
                  <img :src="vipImageIcon(item.category)"/>
                </div>

                <div class="text-c">
                  <div class="name">{{ item.name }}</div>
                  <div class="many">费用：{{formatMoney(item.salePrice)}}/年</div>
                </div>
              </div>
              <div class="title-right">
                <span class="check-name">点我选中→</span>
                <van-radio checked-color="#FD4925" v-model="checkName" :name="item.category" icon-size="20px" @click="handleRadio(item)"></van-radio>
              </div>
            </div>
            <div class="li-box" v-if="item.routineItems.length>0">
              <div class="item-box" v-for="(list,index) in item.routineItems" :key="index" @click="handleRightsClick(item,list,index)">
                <!-- <c-picture class="right-icon" :src="list.image" type='?imageView2/0/format/png'></c-picture> -->
                <biz-image class="right-icon" :src="list.image" type='?imageView2/0' autoFit="height"></biz-image>
                <span class="item-text">{{ list.name }}</span>
                <span class="item-desc">{{ list.title }}</span>
              </div>
            </div>

          </div>

          <div class="li-popup">
            <div class="li-title no-check">
              <div class="title-left">
                暂不开通
              </div>
              <div class="title-right">
                <span class="check-name">点我选中→</span>
                <van-radio checked-color="#FD4925" v-model="checkName" name="no" icon-size="20px"></van-radio>
              </div>
            </div>
          </div>

        </div>
        <div class="popup-btn-box">
          <div class="popup-btn" @click="submit">
            确定
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 权益弹窗 -->
    <right-pop
      :show="showRight"
      :rights="currentVipConfig.routineItems"
      :currentRights="currentRights"
      :index="currentRightsIndex"
      :vipConfig="currentVipConfig"
      @close="closePop"
    ></right-pop>
  </div>
</template>
<script>
/**
 * 是否开通vip会员的checkbox
 */
import { toast } from '@/bus';
import { formatMoney } from '@/utils';
import { Checkbox, Radio, Popup, Icon } from 'vant';
import { getVipPkgDetail } from '@/api/modules/vip-v2';
import RightPop from './_RightPop.vue';
function getPageData() {
  return Promise.all([
    getVipPkgDetail('mall'),
    getVipPkgDetail('car')
  ]);
}
export default {
  name: 'VipActivityCheck',
  components: {
    RightPop,
    [Checkbox.name]: Checkbox,
    [Radio.name]: Radio,
    [Popup.name]: Popup,
    [Icon.name]: Icon,
  },
  props: {
    // 开通vip后可优惠金额
    discountAmount: {
      type: Number,
      default() {
        return 0;
      },
    },
  },
  data() {
    return {
      joinVip: false,
      show: false,
      showRight: false,
      currentRights: {},
      currentRightsIndex: null,
      page: {
        vipConfigMall: {},
        vipConfigCar: {}
      },
      checkName: '',
      vipArray: [],
      vipInfo: {},
      currentVipConfig: {},
      // checked:false,
    };
  },
  watch: {
    joinVip(val) {
      this.$emit('input', val);
    }
  },
  computed: {
    // checkVal(){
    //   return this.joinVip;
    // }
  },
  methods: {
    ...{ formatMoney },
    init() {
      getPageData().then(([vipConfigMall, vipConfigCar]) => {
        this.page.vipConfigMall = vipConfigMall || {};
        this.page.vipConfigCar = vipConfigCar || {};
        this.vipArray = [this.page.vipConfigMall, this.page.vipConfigCar] || []
      })
        .catch(err => {
          toast().tip(err);
          this.error = err;
        });
    },
    vipImageIcon(type) {
      return type == 'mall' ? require('./images/shop-popup-icon.png') : require('./images/car-popup-icon.png');
    },
    handleCheck() {
      this.checkName = '';
      this.show = true;
      this.init()
    },
    handleRadio(val) {
      this.vipInfo = val;
    },
    handleClose() {
      this.show = false;
    },
    // handleChange(){
    //   console.log(this.checked ,"fffffffff")
    //   // this.checkVal = !this.checkVal
    // },
    handleRightsClick(item, list, index) {
      this.showRight = true;
      this.currentRights = list;
      this.currentVipConfig = item;
      this.currentRightsIndex = index;
      this.show = false;
    },
    closePop() {
      this.showRight = false;
      this.show = true;
    },
    submit() {
      this.show = false;
      if (this.checkName != 'no') {
        this.joinVip = true;
        this.$emit('vipInfo', this.vipInfo)
      } else {
        this.joinVip = false;
      }
    }
  },
}
</script>
