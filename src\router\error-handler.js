import { loading, dialog } from '@/bus';
import {
  login,
  isLoggedIn,
  clearHistory,
  popWebView
} from '@/bridge';

function setReloadCount(count = 0) {
  const RELOAD_COUNT = 'reload_data';
  const storage = sessionStorage;
  storage.setItem(RELOAD_COUNT, JSON.stringify({ count }));
}

function getReloadCount() {
  const RELOAD_COUNT = 'reload_data';
  const storage = sessionStorage;
  const reloadData = JSON.parse(storage.getItem(RELOAD_COUNT)) || { count: 0 };
  return reloadData.count;
}

export function handleError(e) {
  console.error('error:', e);
  const reloadCount = getReloadCount();
  if (reloadCount <= 0) {
    setReloadCount(reloadCount + 1);
    location.reload();
  } else {
    loading(false);
    let tip = '页面已失效，请点击确定重新进入';
    dialog().alert(tip, {
      title: '加载失败',
      then() {
        popWebView(0);
      }
    })
  }
}
