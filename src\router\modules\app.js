import { handleError } from '../error-handler';

export default [
  {
    path: '/traffic/app',
    name: 'traffic-app',
    component: resolve => {
      import(/* webpackChunkName: "app" */ '@/views/packages/traffic/index.vue')
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/traffic/freeHighway',
    name: 'freeHighway',
    component: resolve => {
      import(
        /* webpackChunkName: "app" */ '@/views/packages/traffic/free-highway.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
