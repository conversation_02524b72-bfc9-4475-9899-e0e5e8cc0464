<style lang="scss" scoped>
  .lh-divider {
    text-align: center;
    color: #b3b3b3;
    background: transparent;
    position: relative;
    padding: 10px;
    margin: 0 10px;
    font-size: 14px;
    .lh-divider-content {
      position: relative;
      background: #EEEFF3;
      padding: 0 10px;
      z-index: 1;
    }
    &::before {
      content: '';
      position: absolute;
      width: 200%;
      background:#d4d4d4;
      top: 50%;
      height: 1px;
      left: -50%;
      transform: scale(0.5) translateY(-50%);
    }
  }
</style>
<template>
  <div class="lh-divider">
    <span class="lh-divider-content"><slot></slot></span>
  </div>
</template>
<script>
export default {
  name: 'Divider',
  data() {
    return {};
  },
}
</script>