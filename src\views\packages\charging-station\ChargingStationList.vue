<template>
  <container class="charging-station-list">
    <!-- 头部搜索栏 -->
    <x-header title="充电站">
      <x-button slot="left" type="back"></x-button>
      <!-- <x-button slot="right" type="text" @click="goSearch">
        <van-icon name="search" />
      </x-button> -->
    </x-header>

    <content-view
      :status="status"
      :refresh-action="refreshAction"
      @refresh="refresh"
      @reload="reload"
      @scroll-bottom="loadMore"
    >
      <!-- 筛选和排序栏 -->
      <template slot="head" v-if="status == AppStatus.READY">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <van-search
            placeholder="搜索充电站名称或地址"
            shape="round"
            readonly
            @click="goSearch"
          />
        </div>

        <div class="filter-bar">
          <!-- 快速筛选按钮区域 -->
          <div class="quick-filters">
            <div class="quick-filter-scroll">
              <!-- 排序按钮 -->
              <div class="quick-filter-item sort-item" @click="showSortPopup">
                <van-icon name="sort" />
                <span>排序</span>
                <van-icon name="arrow-down" class="arrow-icon" />
              </div>

              <!-- 快速筛选按钮 -->
              <div
                v-for="quickFilter in quickFilters"
                :key="quickFilter.key"
                class="quick-filter-item"
                :class="{ active: isQuickFilterActive(quickFilter.key) }"
                @click="toggleQuickFilter(quickFilter.key)"
              >
                <van-icon :name="quickFilter.icon" />
                <span>{{ quickFilter.name }}</span>
              </div>
            </div>
          </div>

          <!-- 固定筛选按钮 -->
          <div class="fixed-filters">
            <div class="filter-item" @click="goFilter">
              <van-icon name="filter-o" />
              <span>筛选</span>
              <van-badge
                v-if="filterCount > 0 || activeQuickFilters.length > 0"
                :content="filterCount + activeQuickFilters.length"
              />
            </div>
          </div>
        </div>
      </template>

      <template v-if="status == AppStatus.READY">
        <!-- 充电站列表 -->
        <div class="station-list">
          <template v-if="listStatus == AppStatus.LOADING">
            <div class="list-loading">正在查询...</div>
          </template>
          <template v-else-if="listStatus == AppStatus.ERROR">
            <div class="list-error">加载出错</div>
          </template>
          <template v-else-if="listStatus == AppStatus.READY">
            <charging-station-item
              v-for="station in stations"
              :key="station.id"
              :station="station"
              :geo="geo"
              @click.native="goDetail(station)"
              @navigate="navigateToStation(station)"
            />
            <list-loader
              v-if="stations.length"
              :options="$_loader_options"
              @load="loadMore"
            />
            <list-placeholder
              v-if="!stations.length"
              icon="~@/assets/images/empty.png"
            >
              暂无充电站数据
            </list-placeholder>
          </template>
        </div>
      </template>
    </content-view>

    <!-- 排序弹窗 -->
    <van-popup v-model="showSort" position="bottom" round>
      <div class="sort-popup">
        <div class="sort-header">
          <span>排序方式</span>
          <van-icon name="cross" @click="showSort = false" />
        </div>
        <div class="sort-list">
          <div
            v-for="sort in sortOptions"
            :key="sort.value"
            class="sort-item"
            :class="{ active: currentSort.value === sort.value }"
            @click="selectSort(sort)"
          >
            <span>{{ sort.name }}</span>
            <van-icon v-if="currentSort.value === sort.value" name="success" />
          </div>
        </div>
      </div>
    </van-popup>
  </container>
</template>

<script>
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import { AppStatus } from '@/enums';
import { getLocation } from '@/bridge';
import { toast, onSelectFilter } from '@/bus';
import { getChargingStationList } from '@/api/modules/charging-station';
import { getCity } from '@/store/storage';
import ChargingStationItem from './components/ChargingStationItem.vue';
import { generateNavigationUrl } from './utils';

// Vant组件
import { Icon, Badge, Popup, Button, Search } from 'vant';

export default {
  name: 'ChargingStationList',
  components: {
    ChargingStationItem,
    [Icon.name]: Icon,
    [Badge.name]: Badge,
    [Popup.name]: Popup,
    [Button.name]: Button,
    [Search.name]: Search,
  },
  mixins: [mixinLoader, mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      listStatus: AppStatus.LOADING,
      refreshAction: 0,

      // 地理位置
      geo: {
        latitude: null,
        longitude: null,
      },
      city: null,

      // 列表数据
      stations: [],

      // 搜索和筛选
      searchParams: {
        page: 1,
        size: 10,
        latitude: null,
        longitude: null,
        sortBy: 'distance',
        filters: [],
      },

      // 筛选条件
      filterCount: 0,

      // 排序
      showSort: false,
      sortOptions: [
        { name: '距离最近', value: 'distance' },
        { name: '评分最高', value: 'rating' },
        { name: '价格最低', value: 'price' },
        { name: '充电功率', value: 'power' },
      ],
      currentSort: { name: '距离最近', value: 'distance' },

      // 快速筛选配置
      quickFilters: [
        { key: 'star', name: '星级站', icon: 'star' },
        { key: 'parking_free', name: '停车减免', icon: 'free-postage' },
        { key: 'truck', name: '重卡可用', icon: 'logistics' },
        { key: 'fast_charge', name: '快充', icon: 'lightning' },
        { key: 'public', name: '对外开放', icon: 'shop-o' },
        { key: '24h', name: '24小时', icon: 'clock-o' },
        { key: 'super_charge', name: '超充', icon: 'fire' },
        { key: 'wifi', name: '免费WIFI', icon: 'wifi' },
      ],

      // 激活的快速筛选
      activeQuickFilters: [],
    };
  },
  created() {
    this.init();
    this.setupFilterListener();
  },
  methods: {
    init() {
      this.city = getCity();
      // {{ AURA-X: Add - 清理可能残留的筛选缓存. Confirmed via 寸止 }}
      // 确保每次进入列表页都是干净的状态
      this.clearFilterCache();
      this.initPageData();
    },

    // 清理筛选条件缓存
    clearFilterCache() {
      localStorage.removeItem('charging_station_filters');
    },

    // 设置筛选条件监听器
    setupFilterListener() {
      onSelectFilter(filters => {
        this.searchParams.filters = filters;
        this.filterCount = filters.length;
        this.searchParams.page = 1;

        // 重新加载列表
        this.listStatus = AppStatus.LOADING;
        this.getStationList(1)
          .then(() => {
            this.listStatus = AppStatus.READY;
            if (filters.length > 0) {
              toast().tip(`已应用${filters.length}个筛选条件`);
            }
          })
          .catch(e => {
            this.listStatus = AppStatus.ERROR;
            toast().tip(e);
          });
      });
    },

    initPageData() {
      return this.refreshLocation()
        .then(() => {
          this.status = AppStatus.READY;
          return this.getStationList(1);
        })
        .then(() => {
          this.listStatus = AppStatus.READY;
        })
        .catch(e => {
          console.error(e);
          this.status = AppStatus.ERROR;
          this.listStatus = AppStatus.ERROR;
        });
    },

    // 获取地理位置
    refreshLocation() {
      return getLocation()
        .then(location => {
          this.geo = {
            latitude: location.latitude,
            longitude: location.longitude,
          };
          this.searchParams.latitude = location.latitude;
          this.searchParams.longitude = location.longitude;
          return location;
        })
        .catch(e => {
          console.error('获取位置失败:', e);
          toast().tip('获取位置失败，请检查定位权限');
          throw e;
        });
    },

    // 获取充电站列表
    getStationList(page = 1) {
      const params = { ...this.searchParams, page };
      return this.$_loader_bind(getChargingStationList, res => {
        if (page === 1) {
          this.stations = res.list || res;
        } else {
          this.stations = this.stations.concat(res.list || res);
        }
        return res;
      }).load(params);
    },

    // 刷新
    refresh() {
      this.initPageData()
        .then(() => {
          this.refreshAction = Date.now();
        })
        .catch(e => {
          toast().tip(e);
          this.refreshAction = Date.now();
        });
    },

    // 重新加载
    reload() {
      this.status = AppStatus.LOADING;
      this.listStatus = AppStatus.LOADING;
      this.init();
    },

    // 加载更多
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      const nextPage = this.searchParams.page + 1;
      this.searchParams.page = nextPage; // 更新页码
      this.getStationList(nextPage).catch(e => {
        toast().tip(e);
        console.error(e);
      });
    },

    // 跳转搜索页面
    goSearch() {
      this.$_router_push('/charging-station/search');
    },

    // 跳转筛选页面
    goFilter() {
      // {{ AURA-X: Modify - 缓存当前筛选条件到localStorage. Confirmed via 寸止 }}
      // 缓存当前筛选条件，供筛选页面使用
      localStorage.setItem(
        'charging_station_filters',
        JSON.stringify(this.searchParams.filters)
      );
      this.$_router_push('/charging-station/filter');
    },

    // 显示排序弹窗
    showSortPopup() {
      this.showSort = true;
    },

    // 选择排序方式
    selectSort(sort) {
      this.currentSort = sort;
      this.searchParams.sortBy = sort.value;
      this.showSort = false;

      // 重新加载列表
      this.listStatus = AppStatus.LOADING;
      this.getStationList(1)
        .then(() => {
          this.listStatus = AppStatus.READY;
        })
        .catch(e => {
          this.listStatus = AppStatus.ERROR;
          toast().tip(e);
        });
    },

    // 跳转详情页
    goDetail(station) {
      this.$_router_push(`/charging-station/${station.id}`);
    },

    // 检查快速筛选是否激活
    isQuickFilterActive(key) {
      return this.activeQuickFilters.includes(key);
    },

    // 切换快速筛选
    toggleQuickFilter(key) {
      const index = this.activeQuickFilters.indexOf(key);
      if (index > -1) {
        // 移除筛选
        this.activeQuickFilters.splice(index, 1);
      } else {
        // 添加筛选
        this.activeQuickFilters.push(key);
      }

      // 应用筛选
      this.applyQuickFilters();
    },

    // 应用快速筛选
    applyQuickFilters() {
      // 将快速筛选转换为API筛选参数
      const filters = [];

      this.activeQuickFilters.forEach(key => {
        switch (key) {
          case 'star':
            filters.push({ type: 'rating', values: ['4.0以上'] });
            break;
          case 'parking_free':
            filters.push({ type: 'parkingFee', values: ['限时免费'] });
            break;
          case 'truck':
            filters.push({ type: 'parking', values: ['重卡'] });
            break;
          case 'fast_charge':
            filters.push({ type: 'chargingType', values: ['直流快充'] });
            break;
          case 'public':
            filters.push({ type: 'stationType', values: ['对外开放'] });
            break;
          case '24h':
            filters.push({ type: 'businessHours', values: ['24小时'] });
            break;
          case 'super_charge':
            filters.push({ type: 'chargingType', values: ['超级快充'] });
            break;
          case 'wifi':
            filters.push({ type: 'services', values: ['免费WIFI'] });
            break;
        }
      });

      // 更新搜索参数和筛选计数
      this.searchParams.filters = filters;
      this.searchParams.page = 1;
      this.filterCount = this.activeQuickFilters.length;

      // 显示加载状态
      this.listStatus = this.AppStatus.LOADING;

      // 重新加载数据
      this.getStationList(1).then(() => {
        this.listStatus = AppStatus.READY;
        // 显示筛选结果提示
        if (this.activeQuickFilters.length > 0) {
          toast().tip(`已应用${this.activeQuickFilters.length}个筛选条件`);
        }
      }).catch(e => {
        this.listStatus = AppStatus.ERROR;
        toast().tip(e);
      });
    },

    // 导航到充电站
    navigateToStation(station) {
      try {
        // 调用地图导航功能
        if (window.jsBridge && window.jsBridge.openMap) {
          window.jsBridge.openMap({
            latitude: station.latitude,
            longitude: station.longitude,
            name: station.name,
            address: station.address,
          });
        } else {
          // 备用方案：打开高德地图网页版
          const url = generateNavigationUrl(
            station.longitude,
            station.latitude,
            station.name
          );
          window.open(url);
        }
      } catch (error) {
        console.error('导航失败:', error);
        toast().tip('导航功能暂时不可用');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.charging-station-list {
  .search-bar {
    padding: 12px 16px 8px;
    background: #fff;

    .van-search {
      padding: 0;

      .van-search__content {
        background: #f7f8fa;
        border-radius: 18px;

        .van-field__control {
          font-size: 14px;
          color: #323233;
        }

        .van-search__action {
          color: #1989fa;
        }
      }
    }
  }

  .filter-bar {
    display: flex;
    align-items: center;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;

    .quick-filters {
      flex: 1;
      overflow: hidden;

      .quick-filter-scroll {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 0 12px 16px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;

        &::-webkit-scrollbar {
          display: none;
        }

        .quick-filter-item {
          display: flex;
          align-items: center;
          gap: 3px;
          padding: 4px 8px;
          background: #f7f8fa;
          border-radius: 12px;
          font-size: 11px;
          color: #646566;
          cursor: pointer;
          white-space: nowrap;
          flex-shrink: 0;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateZ(0); // 启用硬件加速

          .van-icon {
            font-size: 11px;
          }

          &.active {
            background: #fd4925;
            color: #fff;
          }

          &:active {
            transform: scale(0.95);
          }

          &.sort-item {
            .arrow-icon {
              margin-left: 1px;
              font-size: 9px;
              opacity: 0.6;
            }
          }
        }
      }
    }

    .fixed-filters {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px 12px 8px;
      border-left: 1px solid #f0f0f0;

      .filter-item {
        display: flex;
        align-items: center;
        gap: 3px;
        padding: 4px 8px;
        background: #f7f8fa;
        border-radius: 12px;
        font-size: 11px;
        color: #646566;
        cursor: pointer;
        position: relative;
        white-space: nowrap;

        .van-icon {
          font-size: 11px;
        }

        .van-badge {
          position: absolute;
          top: -4px;
          right: -4px;
        }

        &:active {
          background: #e8e8e8;
        }

        &.sort-item {
          .arrow-icon {
            margin-left: 1px;
            font-size: 9px;
            opacity: 0.6;
          }
        }
      }
    }
  }

  .station-list {
    padding: 12px 16px 0;
  }

  .list-loading,
  .list-error {
    padding: 40px 0;
    text-align: center;
    color: #999;
  }

  .sort-popup {
    padding: 20px 0;

    .sort-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 20px;
      border-bottom: 1px solid #f0f0f0;

      span {
        font-size: 16px;
        font-weight: 500;
      }

      .van-icon {
        font-size: 18px;
        color: #999;
      }
    }

    .sort-list {
      .sort-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        cursor: pointer;

        &.active {
          color: #1989fa;
        }

        .van-icon {
          font-size: 16px;
        }
      }
    }
  }
}
</style>
