$box-wrap-width: 256px;
$box-wrap-height: 156px;
$box-width: 280px;
$box-height: 180px;
$ball-width: 66px;
$ball-height: 66px;
$extra-offset: 30px; // 用于允许球在边界之外移动
/*盒子*/
.box-wrap {
  position: absolute;
  width: $box-wrap-width;
  height: $box-wrap-height;
  left: 50%;
  top: 47px;
  transform: translateX(calc(-50% + 1px));
  overflow: hidden;
}
.ball-box {
  position: absolute;
  width: $box-width;
  height: $box-height;
  left: 50%;
  top: -24px;
  transform: translateX(-50%);
  overflow: hidden;
  border-radius: 20px;
  // border: 5px solid #f1c99d;
  // background: #8ea5e3;
  img {
    height: $ball-width;
    width: $ball-height;
    position: absolute;
    transition: all linear 0.5s;
  }

  .ball0 {
    z-index: 3;
    transform: translate3d(15px, #{$box-height - $ball-height}, 0) rotate(0deg);
  }
  .ball1 {
    z-index: 3;
    transform: translate3d(
        #{$ball-width - 5px},
        #{$box-height - $ball-height},
        0
      )
      rotate(0deg);
  }
  .ball2 {
    z-index: 3;
    transform: translate3d(
        #{$ball-width * 2 - 5px},
        #{$box-height - $ball-height},
        0
      )
      rotate(0deg);
  }
  .ball3 {
    z-index: 2;
    transform: translate3d(
        #{$ball-width * 3 - 10px},
        #{$box-height - $ball-height},
        0
      )
      rotate(0deg);
  }
  .ball4 {
    z-index: 2;
    transform: translate3d(
        $ball-width / 2,
        #{$box-height - $ball-height * 2 + 10px},
        0
      )
      rotate(0deg);
  }
  .ball5 {
    z-index: 3;
    transform: translate3d(
        #{$ball-width + 18px},
        #{$box-height - $ball-height * 2 + 10px},
        0
      )
      rotate(0deg);
  }
  .ball6 {
    z-index: 1;
    transform: translate3d(
        #{$ball-width * 2 + 15px},
        #{$box-height - $ball-height * 2 + 20px},
        0
      )
      rotate(0deg);
  }
  .ball7 {
    z-index: 3;
    transform: translate3d(
        #{$ball-width * 3},
        #{$box-height - $ball-height * 2},
        0
      )
      rotate(0deg);
  }
  @function random-duration($min, $max) {
    $diff: $max - $min;
    @return $min + random($diff + 1) + s;
  }

  @function random-delay($min, $max) {
    $diff: $max - $min;
    @return $min + random($diff + 1) + s;
  }

  @for $i from 0 through 7 {
    .ball#{$i} {
      animation: move#{$i} 14s linear infinite;
    }
  }
  &.active {
    @for $i from 0 through 7 {
      .ball#{$i} {
        // animation: move#{$i} 6s linear infinite;
        animation-duration: 1s;
        // animation: move#{$i} #{random-duration(1, 2)} linear infinite;
        // animation-delay: #{random-delay(0, 1)};
      }
    }
  }
}
@function random-translateX($index) {
  @return random($box-width - $ball-width) + px;
}
@function random-translateY($index) {
  @return random($box-height - $ball-height) + px;
}
@function random-rotate($index) {
  @if $index == 0 or $index == 3 {
    @return rotate(#{random(90)}deg);
  } @else if $index == 1 or $index == 4 {
    @return rotate(#{random(180)}deg);
  } @else if $index == 2 or $index == 5 {
    @return rotate(#{random(100)}deg);
  } @else if $index == 6 {
    @return rotate(#{random(80)}deg);
  } @else {
    @return rotate(#{random(80)}deg);
  }
}
// 定义一个mixin，用于生成动画
@mixin generate-animation($index, $initial-transform) {
  @keyframes move#{$index} {
    0% {
      transform: #{$initial-transform};
    }
    // 10% {
    //   transform: translate3d(
    //       #{random-translateX($index)},
    //       #{random-translateY($index)},
    //       0
    //     )
    //     #{random-rotate($index)};
    // }
    20% {
      transform: translate3d(
          #{random-translateX($index)},
          #{random-translateY($index)},
          0
        )
        #{random-rotate($index)};
    }
    // 30% {
    //   transform: translate3d(
    //       #{random-translateX($index)},
    //       #{random-translateY($index)},
    //       0
    //     )
    //     #{random-rotate($index)};
    // }
    40% {
      transform: translate3d(
          #{random-translateX($index)},
          #{random-translateY($index)},
          0
        )
        #{random-rotate($index)};
    }
    // 50% {
    //   transform: translate3d(
    //       #{random-translateX($index)},
    //       #{random-translateY($index)},
    //       0
    //     )
    //     #{random-rotate($index)};
    // }
    60% {
      transform: translate3d(
          #{random-translateX($index)},
          #{random-translateY($index)},
          0
        )
        #{random-rotate($index)};
    }
    // 70% {
    //   transform: translate3d(
    //       #{random-translateX($index)},
    //       #{random-translateY($index)},
    //       0
    //     )
    //     #{random-rotate($index)};
    // }
    80% {
      transform: translate3d(
          #{random-translateX($index)},
          #{random-translateY($index)},
          0
        )
        #{random-rotate($index)};
    }
    // 90% {
    //   transform: translate3d(
    //       #{random-translateX($index)},
    //       #{random-translateY($index)},
    //       0
    //     )
    //     #{random-rotate($index)};
    // }
    100% {
      transform: #{$initial-transform};
    }
  }
}

// 使用mixin生成动画
@include generate-animation(
  0,
  'translate3d(15px, #{($box-height - $ball-height)}, 0) rotate(0deg)'
);
@include generate-animation(
  1,
  'translate3d(#{($ball-width - 5px)}, #{($box-height - $ball-height)}, 0) rotate(0deg)'
);
@include generate-animation(
  2,
  'translate3d(#{($ball-width * 2 - 5px)}, #{($box-height - $ball-height)}, 0) rotate(0deg)'
);
@include generate-animation(
  3,
  'translate3d(#{($ball-width * 3 - 10px)}, #{($box-height - $ball-height)}, 0) rotate(0deg)'
);
@include generate-animation(
  4,
  'translate3d(#{$ball-width / 2}, #{($box-height - $ball-height * 2 + 10px)}, 0) rotate(0deg)'
);
@include generate-animation(
  5,
  'translate3d(#{($ball-width + 18px)}, #{($box-height - $ball-height * 2 + 10px)}, 0) rotate(0deg)'
);
@include generate-animation(
  6,
  'translate3d(#{($ball-width * 2 + 15px)}, #{($box-height - $ball-height * 2 + 20px)}, 0) rotate(0deg)'
);
@include generate-animation(
  7,
  'translate3d(#{($ball-width * 3)}, #{($box-height - $ball-height * 2)}, 0) rotate(0deg)'
);
