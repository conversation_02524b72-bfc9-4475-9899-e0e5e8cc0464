<template>
  <div class="box-container">
    <!-- 订单状态 -->
    <div class="cell-box">
      <div class="box-center">
        <i
          :class="['icon_jglh', pageIconData.iconName]"
          :style="{ color: pageIconData.color }"
        ></i>
        <div class="c-right">
          <p class="s-text" :style="{ color: pageIconData.color }">
            {{ orderStatusText }}
          </p>
          <p class="d-text" v-if="pageIconData.desc">
            {{ pageIconData.desc }}
          </p>
          <!-- <p class="d-text flex-box" v-if="pageIconData.status == 1">
            <van-count-down
              :time="2 * 60 * 1000"
              format="mm:ss"
              @finish="finish"
              :auto-start="true"
            />后订单自动关闭
          </p> -->
        </div>
      </div>
    </div>
    <!-- 快递信息 -->
    <div
      class="cell-box"
      v-if="order.goods.receiptType == MallDeliveryType.EXPRESS && order.postId"
    >
      <div class="flex-box">
        <van-icon name="logistics" size="20" color="#333333" />
        <div class="c-right">
          <p class="express-text">
            <span class="info-text">快递信息</span>
            <span
              class="info-t-right"
              @click="showAction"
              v-if="postIds.length < 2"
            >
              查看详情<van-icon name="arrow" size="12" color="#666" />
            </span>
          </p>
          <div class="info-c-text" v-if="order.postId" style="color: #666">
            <p style="margin-bottom: 10px">
              <span class="express-time">快递公司：</span
              >{{ order.postCompany }}
            </p>
            <div class="flex-box-between">
              <p class="post-n">
                <span class="express-time">快递单号：</span>
              </p>
              <div class="post-i">
                <div
                  class="item-post"
                  v-for="(item, index) in postIds"
                  :key="index"
                >
                  <span>{{ item }}</span>
                  <span class="info-btn" @click="toCopy">复制</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 用户信息, 自提订单有自提点的不显示 -->
    <div v-if="!order.pickUpAddressId" class="cell-box">
      <div class="box-center">
        <van-icon name="location-o" size="20" color="#333333" />
        <div class="c-right">
          <template v-if="order.goods.receiptType == MallDeliveryType.EXPRESS">
            <p class="info-text">
              {{ order.truename
              }}<span style="margin-left: 12px">{{ order.telephone }}</span>
            </p>
            <p class="info-c-text">
              {{ order.postareaProv }}{{ order.postareaCity
              }}{{ order.postareaCountry }}{{ order.address }}
            </p>
          </template>
          <template v-else> 门店自提 </template>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.cell-box {
  background: #ffffff;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 16px 15px;
  margin: 10px 0;
}
.box-center {
  display: flex;
  align-items: center;
  line-height: 20px;
}
.icon_jglh {
  font-size: 20px;
}
.flex-box {
  display: flex;
}
.flex-box-between {
  display: flex;
  justify-content: space-between;
}
.c-right {
  line-height: 20px;
  margin-left: 10px;
  flex: 1;
  .s-text {
    font-size: 18px;
    color: #fd4925;
    font-weight: bold;
  }
  .d-text {
    font-size: 13px;
    font-weight: 500;
    color: #333333;
    margin-top: 10px;
  }
  .info-text {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 11px;
  }
  .info-c-text {
    font-size: 13px;
    font-weight: 500;
    color: #333333;
  }
  .info-t-right {
    font-size: 12px;
    font-weight: 500;
    color: #666666;
  }
  .post-n {
    flex-shrink: 0;
  }
  .post-i {
    flex: 1;
    .item-post {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      &:first-child {
        margin-top: 0;
      }
    }
  }
  .express-text {
    display: flex;
    justify-content: space-between;
  }
  .express-time {
    font-size: 12px;
    font-weight: 500;
    color: #999999;
    margin-top: 12px;
  }
  .info-btn {
    font-size: 12px;
    line-height: 20px;
    height: 20px;
    color: #333;
    padding: 0 9px;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-sizing: border-box;
  }
}
</style>
<script>
import { Icon, CountDown } from 'vant';
import { MallOrderStatus, MallDeliveryType } from '@/enums';
import { formatDate } from '@/utils';
import { dialog, toast, loading } from '@/bus';
import { getLogistics } from '@pkg/mall/api';
export default {
  name: 'OrderStatusInfo',
  components: {
    [Icon.name]: Icon,
    [CountDown.name]: CountDown,
  },
  props: {
    value: {
      type: Object,
    },
  },
  computed: {
    order() {
      return this.value;
    },
    orderStatusText() {
      // 订单状态：payStatus
      return this.getEnumDesc(MallOrderStatus, this.order.payStatus);
    },
    // 状态对应的图标和信息
    pageIconData() {
      let itemData;
      this.pageIcon.forEach(item => {
        if (item.status == this.value.payStatus) {
          itemData = item;
        }
      });
      return itemData;
    },
    // 多个快递拆分快递单号
    postIds() {
      if (this.value.postId) {
        return this.value.postId.split(',');
      } else {
        return [];
      }
    },
  },
  data() {
    return {
      MallDeliveryType,
      pageIcon: [
        {
          status: 1, // 待付款
          iconName: 'icon-xf-shijian',
          color: '#FD4925',
        },
        {
          status: 2, // 待发货
          iconName: 'icon-daifahuo',
          color: '#FD4925',
          desc: '请耐心等待商家发货',
        },
        {
          status: 3, // 待收货
          iconName: 'icon-daishiyong',
          color: '#FD4925',
        },
        {
          status: 4, // 已收货
          iconName: 'icon-daipingjia',
          color: '#FD4925',
          desc: '订单已完成，快对你收到的宝贝进行评价吧~',
        },
        {
          status: 5, // 已评价
          iconName: 'icon-wancheng',
          color: '#333',
        },
        {
          status: 8, // 退款中
          iconName: 'icon-a-shouhoutuikuan',
          color: '#FD4925',
        },
        {
          status: -2, // 已退款
          iconName: 'icon-tuikuan',
          color: '#FD4925',
        },
        {
          status: -3, // 部分退款
          iconName: 'icon-tuihuo',
          color: '#FD4925',
        },
        {
          status: 9, // 退款驳回
          iconName: 'icon-a-shouhoutuikuan',
          color: '#FD4925',
        },
        {
          status: -1, // 已关闭(未付款)
          iconName: 'icon-a-guanbiquxiao',
          color: '#F5222D',
        },
      ],
    };
  },
  mounted() {},
  methods: {
    getEnumDesc(e, value) {
      const status = e.getEnum(value);
      if (!status) return value;
      return status.desc;
    },
    formatDate(t, style = 'yyyy-MM-dd HH:mm:ss') {
      return formatDate(t, style);
    },
    showAction(url) {
      loading(true, '查询中...');
      if (!this.order.id) return false;
      getLogistics(this.order.id)
        .then(res => {
          loading(false);
          this.$emit('stepData', res);
        })
        .catch(e => {
          loading(false);
          e && dialog().alert(e);
        });
    },
    toCopy() {
      let selection = window.getSelection();
      let range = document.createRange();
      let referenceNode = document.createElement('div');
      referenceNode.innerHTML = this.order.postId;
      document.body.appendChild(referenceNode);
      range.selectNodeContents(referenceNode);
      selection.removeAllRanges();
      selection.addRange(range);
      let ret = document.execCommand('copy');
      document.body.removeChild(referenceNode);

      if (ret) {
        toast().tip('复制成功');
      }
    },
    // finish() {
    //   // 倒计时结束时
    // }
  },
};
</script>
