<template>
  <div class="picker picker-date" :class="{ 'picker-empty': !value}" @click="pickDate">{{value ? formatDate(value) : placeholder}}</div>
</template>
<style lang="scss">

</style>
<script>
  // import { datePicker } from 'weui.js';
  import { datePicker } from '@/lib/weui.js/picker/picker.js';
  import { formatDate } from '@/utils';

  const DataType = {
    TIMESTAMP: 'timestamp',
    DATE: 'date',
  }
  
  export default{
    name: 'date-picker',
    props: {
      value: {
        type: [Date, Number],
      },
      placeholder: {
        type: String,
        default() {
          return '请选择';
        }
      },
      type: {
        type: String,
        default() {
          return DataType.TIMESTAMP;
        },
      },
      options: {
        type: Object,
      },
      format: {
        type: String,
        default() {
          return 'yyyy-MM-DD'
        }
      }
    },
    watch: {
      visible(val, oldVal) {
        if (val) {
          document.body.classList.add('overlaying');
        } else {
          document.body.classList.remove('overlaying');
        }
      }
    },
    mounted() {
      
    },
    beforeDestroy() {
      try {
        this.visible = false;
        if (this.picker) {
          this.picker.destroy();
        }
      } catch(e) {
        // console.error('picker.destroy', e);
      }
    },
    data() {
      return {
        picker: null,
        visible: false,
      };
    },
    components: {
    },
    methods: {
      pickDate() {
        const that = this;
        const defaultDate = new Date(this.value || new Date());
        const defaultValue = [defaultDate.getFullYear(), defaultDate.getMonth()+1, defaultDate.getDate()]
        const options = Object.assign({
          start: 1990,
          end: new Date().getFullYear(),
          defaultValue: defaultValue,
          onChange(result) {
            console.log(result);
          },
          onConfirm(result) {
            const [year, month, day] = result.map(item => item.value);
            const date = new Date(year, month-1, day);
            that.setDate(date);
          },
          onHide() {
            console.log('date picker hide...');
            that.visible = false;
          },
          id: 'datePicker'
        }, this.options);
        this.visible = true;
        this.picker = datePicker(options);
      },
      setDate(date) {
        const result = this.type === DataType.TIMESTAMP ? date.getTime() : date;
        this.$emit('input', result);
      },
      formatDate(date, format) {
        const style = format || this.format;
        return formatDate(date, style);
      }
    },
  };
</script>
