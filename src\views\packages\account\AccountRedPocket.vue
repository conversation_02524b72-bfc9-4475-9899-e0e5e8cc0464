<template>
  <container class="account-red-pocket" @ready="init">
    <x-header title="我的红包">
      <x-button  slot="left" type="back"></x-button>
      <div class="btn-ask" slot="right">
        <x-button type="ask" @click="$_router_push('/account/redpocket/qa')">帮助</x-button>
      </div>
    </x-header>

    <content-view
      :status="status"
      :refreshAction="0"
      @refresh="refresh"
      @reload="reload"
      @scroll-bottom="loadMore"
    >
      <template v-if="status == AppStatus.READY">
        <div class="page-head">
          <div class="head">
            <div class="balance-tip">红包余额(元)</div>
            <div class="balance"><span class="rmb balance-integer">{{balance.integer}}.</span><span class="balance-decimal">{{balance.decimal}}</span></div>
          </div>
        </div>
        <div class="content">
          <!-- <tabs
            class="my-tabs"
            v-model="currentType"
            :tabs="tabList"
            @change="switchType"
          >
          </tabs> -->
          <div class="tabs">
            <div class="tab-item" :class="{'this': currentType == 1}" @click="switchType(1)">
              已获取
            </div>
            <div class="tab-item s" :class="{'this': currentType == 0}" @click="switchType(0)">
              已使用
            </div>
          </div>
          <div
            v-for="(item, index) in list"
            :key="index"
            class="trade-item"
          >
            <div class="trade-content-left">
              <div class="trade-name">{{item.title}}</div>
              <div class="trade-time">{{formatDate(item.createTime)}}</div>
            </div>
            <div class="trade-content-right">
              <span class="trade-amount" :class="{ 'trade-in': item.bType == TradeType.IN }">{{getAmountSymbol(item.bType)}}{{formatAmount(item.amount)}}</span>元
            </div>
          </div>
          <list-placeholder v-if="!list.length">{{emptyTip}}</list-placeholder>
          <list-loader
            v-if="list.length"
            :options="$_loader_options"
            @load="loadMore">
          </list-loader>
        </div>
    </template>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
@import '~styles/variable/global.scss';
  .account-red-pocket ::v-deep {
    background: white;
    .header-btn-ask {
      background-image: url(~@/assets/images/account/icon-ask.png);
      background-position: center center;
      background-repeat: no-repeat;
      background-size: 20px;
      text-indent: -999px;
    }
  }
  .page-head {
    color: white;
    text-align: left;
    padding: 12px;
    .head{
      width: 100%;
      box-sizing: border-box;
      background: url(~@/assets/images/account/head.png) no-repeat left top;
      background-size: 100% 100%;
      padding: 16px 20px;
    }
    .balance-tip {
      text-align: left;
      // padding-left: 20px;
    }
    .balance {
      margin: 10px auto 0;
      font-size: 22px;
      .balance-integer {
        font-size: 40px;
        &::before {
          font-size: 22px;
        }
      }
    }
  }
  /* .my-tabs ::v-deep {
    .tab-item {
      flex: unset;
      padding: 8px 15px;
    }
  } */
  .content {
    background: white;
  }
  .trade-item {
    padding: 12px 10px;
    border-bottom: 1px solid #efefef;
    display: flex;
  }

  .trade-content-left {
    flex: 1;
  }
  .trade-name {
    font-weight: 700;
    font-size: 16px;
    color: #313131;
  }
  .trade-time {
    color: #A5A5A5;
    margin-top: 3px;
  }
  .trade-content-right {
    display: flex;
    // justify-content: center;
    align-items: center;
    .trade-amount {
      font-size: 18px;
      margin-right: 2px;
      &.trade-in {
        color: #FF2F2F;
      }
    }
  }
.tabs {
  margin-top: 14px;
  position: relative;
  padding: 0 18px;
  padding-top: 10px;
  font-size: 15px;
  overflow: hidden;

  &::before {
    content: '';
    display: block;
    height: 1px;
    transform: scaleY(0.5);
    background: #EDEDED;
    position: absolute;
    left: -15px;
    right: -15px;
    bottom: 0;
    z-index: 3;
  }

  .tab-item {
    position: relative;
    display: inline-block;
    line-height: 2;
    padding-bottom: 6px;
    margin-right: 30px;

    &.s {
      margin-left: 20px;
    }

    &::before {
      content: '';
      position: absolute;
      bottom: 1px;
      left: 50%;
      transform: translateX(-50%);
      display: block;
      width: 100%;
      height: 2px;
      z-index: 5;
      background: #fff;
    }

    &.this {
      color: $lh-2022-primary-color;
      font-weight: bold;
      // border-bottom-color: #398EFF;
      &::before {
        background: $lh-2022-primary-color;
      }
    }
  }
}

</style>
<script>
import { formatDate, formatMoney } from '@/utils';
import { Tabs, Panel } from '@/components';
import { OrderStatus, AppStatus } from '@/enums';
import { toast, dialog, loading } from '@/bus';
import { getUserRedPocketAccountInfo, getUserRedPocketRecords } from '@/api/modules/account';
import { mixinAuthRouter, mixinLoader } from '@/mixins';
import storage from '@/store/storage';

const TradeType = {
  IN: 1,
  OUT: 0,
}

export default {
  name: 'AccountRedPocket',
  mixins: [mixinAuthRouter, mixinLoader],
  components: {
    Tabs,
    Panel,
  },
  data() {
    return {
      AppStatus,
      TradeType,
      status: AppStatus.LOADING,
      refreshAction: 1,
      currentType: TradeType.IN,
      page: {
        account: null,
        list: [],
      },
    };
  },
  computed: {
    emptyTip() {
      const tips = {
        [TradeType.IN]: '还没有获取红包哦！',
        [TradeType.OUT]: '暂没有使用记录哦',
      }
      return tips[this.currentType];
    },
    balance() {
      const balance = formatMoney(this.account.balance).split('.');
      return {
        integer: balance[0],
        decimal: balance[1],
      }
    },
    tabList() {
      return [
        {
          name: '已获取',
          value: TradeType.IN,
        },
        {
          name: '已使用',
          value: TradeType.OUT,
        },
      ]
    },
    list() {
      return this.page.list;
    },
    account() {
      return this.page.account;
    },
    searchParams() {
      return {
        type: this.currentType,
        ...this.$_loader_params,
      }
    }
  },
  methods: {
    init() {
      return getUserRedPocketAccountInfo().then(res => {
        this.page.account = res;
        this.status = AppStatus.READY;
        this.getList(1);
        this.showTip();
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    // 显示我的余额=>我的红包变动通知
    showTip() {
      // 2019年8月1日之前提示用户
      const KEY = 'tip-account2redpocket-change';
      // 1: 已提示过的不再提示，2: 提示截至时间为：2019/10/1
      const shouldAlert = !storage.getItem(KEY) && Date.now() < new Date(2019, 10 - 1, 1, 0, 0, 0).getTime();
      if (shouldAlert) {
        const tip = `
        <div style="color: black;text-align: left;">
        因交广领航业务升级，我的余额 即日起正式更名为 我的红包，
        <strong style="color:red">原账户中的余额直接转入我的红包余额里</strong>，
        红包余额在交广领航平台各项业务付款时可抵扣相应金额，给您带来不便，敬请谅解！
        </div>`;
        dialog().alert(tip, {
          title: '通知',
          ok: '我已知晓',
          then() {
            storage.setItem(KEY, true);
          }
        });
      }
    },
    getNumberDecimal(value) {
      return formatMoney(value).split('.')[1];
    },
    getAmountSymbol(type) {
      const symbol = {
        [TradeType.IN]: '+',
        [TradeType.OUT]: '-',
      }
      return symbol[type];
    },
    switchType(type) {
      this.currentType = type
      loading(true);
      this.getList(1).then(res => {
        loading(false);
      }).catch(err => {
        console.log(err);
        loading(false);
      })
    },
    formatAmount(value = 0) {
      return value.toFixed(2);
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    // 刷新列表
    refresh() {
      this.getList(1).then(res => {
        this.refreshAction = Date.now();
      }).catch(e => {
        this.refreshAction = Date.now();
        toast().tip(e);
        console.error(e);
      })
    },
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      const nextPage = this.searchParams.page + 1;
      this.getList(nextPage).catch(e => {
        toast().tip(e);
        console.error(e);
      })
    },
    getList(page = 1) {
      const params = { ...this.searchParams, page };
      return this.$_loader_bind(getUserRedPocketRecords, res => {
        if (page === 1) {
          this.page.list = res.list;
          // this.page.list = [];
        } else {
          this.page.list = this.page.list.concat(res.list);
        }
        return res.list;
      }).load(params);
    },
    formatDate(t, style = 'yyyy-MM-dd HH:mm:ss') {
      return formatDate(t, style);
    },
  }
};
</script>
