# 充电站H5页面

## 功能概述

充电站H5页面是一个完整的移动端充电站查找和管理系统，支持充电站的查询、筛选、搜索和导航功能。

## 页面结构

### 1. 充电站列表页 (`ChargingStationList.vue`)
- **路径**: `/charging-station/list`
- **功能**: 
  - 显示附近充电站列表
  - 支持下拉刷新和上拉加载更多
  - 支持按距离、评分、价格排序
  - 支持筛选和搜索入口
  - 一键导航到充电站

### 2. 筛选页面 (`ChargingStationFilter.vue`)
- **路径**: `/charging-station/filter`
- **功能**:
  - 11个维度的多选筛选
  - 实时显示筛选条件数量
  - 支持重置和确认操作

### 3. 搜索页面 (`ChargingStationSearch.vue`)
- **路径**: `/charging-station/search`
- **功能**:
  - 关键词搜索充电站
  - 搜索建议和历史记录
  - 防抖优化搜索性能

### 4. 详情页面 (`ChargingStationDetail.vue`)
- **路径**: `/charging-station/:id`
- **功能**:
  - 显示充电站完整信息
  - 支持分享和导航
  - 显示服务设施和评价

## 筛选维度

1. **电压**: 200V-500V, 700V以上
2. **充电功率**: 15KW-50KW, 50KW-120KW, 120KW-300KW, 300KW-500KW
3. **营业时间**: 24小时, 营业中, 不确定
4. **高速模式**: 高速路充电桩, 靠近高速路充电桩
5. **停车场**: 箱货, 大巴, 重卡, 地上, 地下
6. **停车费**: 限时免费, 收费
7. **运营类型**: 自营, 非自营, 互联
8. **电站状态**: 营业中, 停业中
9. **充电方式**: 直流快充, 直流慢充, 超级快充, 交流快充, 交流慢充
10. **电站类型**: 对外开放, 不对外开放
11. **电站服务**: 休息室, 便利店, 洗车, 场站照明, 免费WIFI, 简餐, 洗手间
12. **权益**: 特来电, 即插即充, V2G

## 技术特点

### 前端技术栈
- **Vue.js**: 组件化开发
- **Vant UI**: 移动端UI组件库
- **Vue Router**: 路由管理
- **Mixins**: 代码复用

### 性能优化
- **防抖搜索**: 300ms防抖优化搜索性能
- **虚拟滚动准备**: 支持大量数据展示
- **图片懒加载**: 优化页面加载速度
- **错误边界**: 完善的错误处理机制

### 移动端优化
- **触摸反馈**: 点击动画效果
- **响应式设计**: 适配不同屏幕尺寸
- **手势支持**: 下拉刷新、上拉加载
- **导航集成**: 支持地图导航

## 数据结构

### 充电站数据模型
```javascript
{
  id: '1',
  name: '万达广场充电站',
  address: '北京市朝阳区建国路93号万达广场B1层',
  lat: 39.9042,
  lng: 116.4074,
  distance: 1200,
  status: 'open',
  fastCharging: { total: 8, available: 5 },
  slowCharging: { total: 4, available: 3 },
  currentPrice: 1.5,
  rating: 4.5,
  commentCount: 128,
  tags: ['24小时', '快充', '免费停车'],
  services: ['休息室', '便利店', '免费WIFI', '洗手间'],
  businessHours: '24小时营业',
  phone: '************',
}
```

## API接口

### 接口列表
- `getChargingStationList`: 获取充电站列表
- `getChargingStationDetail`: 获取充电站详情
- `searchChargingStation`: 搜索充电站
- `getFilterConfig`: 获取筛选配置
- `getChargingStationCategories`: 获取充电站分类
- `getChargingStationTags`: 获取充电站标签

### 降级策略
所有API接口都采用"真实API + 静态数据备用"的策略：
- 优先调用真实API
- API失败时自动使用静态数据
- 用户无感知的错误处理

## 使用说明

### 开发环境
1. 确保项目依赖已安装
2. 启动开发服务器
3. 访问 `/charging-station/list` 查看充电站列表

### 生产环境
1. 配置真实API接口地址
2. 更新静态数据为实际数据
3. 测试所有功能正常运行

## 性能监控

### 自动监控指标
- **页面加载性能**: DNS查询、TCP连接、请求响应时间
- **长任务监控**: 超过50ms的JavaScript任务
- **内存使用**: JavaScript堆内存使用情况
- **网络请求**: API请求耗时和大小
- **用户交互**: 搜索、筛选、加载等操作性能

### 使用方式
```javascript
import { chargingStationPerformance } from './performance';

// 监控列表加载
const result = await chargingStationPerformance.monitorListLoad(loadFunction);

// 监控搜索性能
const searchResult = await chargingStationPerformance.monitorSearch(searchFunction, keyword);

// 监控筛选性能
const filterResult = await chargingStationPerformance.monitorFilter(filterFunction, filters);
```

## 测试

### 单元测试
项目包含完整的单元测试，覆盖工具函数和核心逻辑：

```bash
# 运行测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 测试覆盖范围
- ✅ 工具函数测试（utils.test.js）
- ✅ 距离格式化测试
- ✅ 状态文本转换测试
- ✅ 坐标验证测试
- ✅ 本地存储测试
- ✅ 性能计算测试

### 手动测试清单
- [ ] 页面加载和渲染
- [ ] 地理位置获取
- [ ] 充电站列表展示
- [ ] 搜索功能
- [ ] 筛选功能
- [ ] 排序功能
- [ ] 导航功能
- [ ] 详情页面
- [ ] 错误处理
- [ ] 离线模式

## 注意事项

1. **地理位置权限**: 需要用户授权获取位置信息
2. **网络状态**: 支持离线模式（静态数据）
3. **浏览器兼容**: 支持现代移动浏览器
4. **性能监控**: 已集成性能监控，可配置监控端点
5. **错误上报**: 建议集成Sentry等错误监控服务
6. **数据缓存**: 位置信息缓存5分钟，搜索历史持久化存储
