<template>
    <container @ready="init">
      <x-header title="充值协议">
        <x-button  slot="left" type="back"></x-button>
      </x-header>
      <content-view :status="status">
        <div class="licence">
            <p>
              尊敬的用户，为保障您的合法权益，请您在充值前，完整、仔细的阅读本充值协议以了解交广领航的充值及余额使用规则并避免产生任何误解。当您继续点击“去充值”按钮，即视为您已阅读、理解本协议，并同意按本协议规定参与充值及使用账户余额。
            </p>

            <h4>1.定义</h4>
            <div>
              <p>充值金额：您通过“交广领航”软件向您已注册的用户账号实际充值支付的金额。</p>
              <p>账户余额：即您APP账户中当前显示的金额，账户金额一般通过充值金额、充值赠送金额及其他赠送或补偿的金额获得。</p>
            </div>
            <h4>2.账户余额有效期</h4>
            <div>
              用户账户余额有效期为自充值日起至用完为止。
            </div>
            <h4>3.账户余额使用规则</h4>
            <div>
              <p>1）充值后，账户余额不能退还、不能转移或转增；请根据您的使用情况进行充值。</p>
              <p>2）用户账户余额仅可用于支付本APP平台提供的需要支付的服务。</p>
            </div>

            <h4>4.特别说明</h4>
            <div>
            <p>您完全理解并同意，交广领航有权随时修改本协议内容，届时将通过“交广
领航”软件公布修改后的协议，该公布将被视为交广领航已通知用户；如果
您选择继续充值即表示您同意并接受修改后的协议并受其约束；如果您不同
意我们对本协议的修改，请立即放弃充值或停止使用本服务。</p>
            </div>
            <div>在中华人民共和国法律允许的范围内，本协议下充值及账户余额的使用规则
的最终解释权归河南交广融媒文化传播有限公司所有。</div>
        </div>
      </content-view>
    </container>
</template>
<style lang="scss" scoped>
.licence {
    background: white;
    padding: 5px 15px;
    h4{
      font-weight:400;
      margin:20px 0 20px 0;
    }
    p{
      margin: 5px 0;
    }
}
</style>
<script>
import { Header, HeaderButton, Container, ContentView } from '@/components';
import { AppStatus } from '@/common/enums';

export default {
  name: 'cmi',
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    ContentView,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.READY,
    };
  },
  methods: {
    init() {},
  }
};
</script>
