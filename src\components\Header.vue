<template>
  <header
    v-if="showHeader"
    class="header flex-row center"
    :class="{ 'is-fixed': fixed, 'fullscroll-hide-title': shouldHideTitle }"
    :style="headerStyle"
  >
    <div class="header-button is-left">
      <slot name="left"></slot>
    </div>
    <slot
      ><h1 class="header-title">
        <span>{{ title }}</span>
      </h1></slot
    >
    <div class="header-button is-right">
      <slot name="right"></slot>
    </div>
  </header>
</template>

<script>
import { setTitle } from '@/bridge';
import { getSearchParams, initDltJs } from '@/utils';
import { isInWeixin, isInWeApp, isInAliApp } from '@/common/env';

// const shouldEnableAutoSyncTitle = location.href.indexOf('nohead') > -1;
const shouldEnableAutoSyncTitle =
  isInWeixin || isInWeApp || isInAliApp || location.href.indexOf('nohead') > -1;
export default {
  name: 'x-header',
  props: {
    fixed: Boolean,
    title: String,
    syncTitle: {
      type: Boolean,
      default: shouldEnableAutoSyncTitle,
    },
    /**
     * 全屏模式配置
     * 全屏模式下，标题栏默认透明，内容区域顶部延伸到标题栏顶部，当页面往下滚动时，标题栏背景色从全透明逐渐过渡到不透明
     * @param { string } backgroundColor 背景色，必须为rgba格式
     * @param { number } fullscreenHeight 全屏模式内容高度，当内容滚动距离大于此高度后，标题栏变为完全不透明，标题栏文字被显示出来
     * @param { number } scrolled 内容已滚动距离
     */
    fullscreenConfig: {
      type: Object,
      default() {
        return {};
      },
    },
    // 是否显示
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      bottom: 0,
      backgroundColor: '',
    };
  },
  computed: {
    showHeader() {
      return !(isInWeixin || isInWeApp || isInAliApp) || this.visible;
    },
    shouldHideTitle() {
      return this.fullscreenConfig && this.fullscreenScrollRadio < 1;
    },
    headerStyle() {
      const bg = this.bgColor;
      return {
        backgroundColor: bg,
      };
    },
    fullscreenScrollRadio() {
      // 滚动距离： fullscreen模式，header高度与banner图重合，故滚动距离减去header
      const distance = this.fullscreenConfig.fullscreenHeight - this.bottom;
      const radio = this.fullscreenConfig.scrolled / distance;
      return radio;
    },
    bgColor() {
      try {
        const bgColor =
          this.fullscreenConfig.backgroundColor || this.backgroundColor;
        const bgColorSplit = bgColor.split(',');
        if (bgColorSplit.length === 4) {
          // debugger
          return bgColorSplit
            .slice(0, 3)
            .concat([`${this.fullscreenScrollRadio})`])
            .join(',');
        }
        return bgColor;
      } catch (err) {
        console.error(err);
      }
    },
  },
  mounted() {
    // console.log('header mounted...');
    this.updateTitle(this.title);
    // console.warn('update title', this.title);
    if (this.showHeader) {
      this.bottom = this.$el.getBoundingClientRect().bottom;
      this.backgroundColor = window.getComputedStyle(this.$el).backgroundColor;
      // TODO 暂不启用，开启时注意个别页面会设置height:100vh，导致部分内容被遮挡，根据情况修改开启的页面样式、高度
      // // 检查是否已加载 dlt.js
      // const scriptId = 'dlt-script'; // 为 dlt.js 设置一个唯一的 ID
      // if (document.getElementById(scriptId)) {
      //   return;
      // }
      // // 初始化顶部广告条展示;
      // const appElement = document.getElementById('app');
      // const urlParams = new URLSearchParams(window.location.search);
      // const ad = +urlParams.get('ad');
      // // let hashParams = getSearchParams(window.location.hash);
      // if (ad && appElement) {
      //   initDltJs(ad).then(() => {
      //     setTimeout(() => {
      //       // 使用原生 JavaScript 选择器修改 #app 的样式
      //       appElement.style.top = '50px';
      //       appElement.style.height = 'calc(100vh - 50px)';
      //       appElement.style.transition = 'top linear 0.3s';
      //     }, 1000);
      //   });
      // }
    }
  },
  activated() {
    // console.log('header activated...');
    this.updateTitle(this.title);
    // console.warn('update title', this.title);
  },
  watch: {
    title() {
      this.updateTitle(this.title);
    },
  },
  methods: {
    updateTitle() {
      // console.log('header updateTitle...');
      // console.log(this.syncTitle && !isInWeixin && this.title);
      /**
       * 截止iOS-3.9.3
       * setTitle方法传入空字符串会导致App崩溃，所以web端做兼容判断
       *
       * 非交广领航环境中，会以html模拟的titleBar显示当前页面标题，所以不需要调用setTitle方法修改页面标题
       * ## 2019年11月13日
       * 交广领航环境下大部分时候不需要设置原生标题栏title，
       * 微信网页内，因为原生标题栏不可隐藏，动态修改页面标题会导致模拟标题栏与原生标题栏内容重复，故微信网页环境下不自动更新标题
       * 所以，仅在使用原生标题栏且隐藏模拟标题栏的情况下  才需要自动更新标题栏
       * 如，iOS端，有时需要使用原生标题栏来规避模拟标题栏达不到的效果时，需要动态修改原生标题栏名称
       * 再如，当原生标题栏不可隐藏，有时为了规避双标题栏影响UI美观度，隐藏模拟标题栏，此时也需要动态修改标题栏
       *
       */
      if (this.syncTitle /* && !isInWeixin */ && this.title) {
        setTimeout(() => {
          setTitle(this.title);
        }, 10);
      }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.header-inner {
  position: relative;
}
.header-button {
  padding: 0;
  position: absolute;
  bottom: 0;
  height: 45px;
  user-select: none;
}
.is-left {
  text-align: left;
  left: 0;
}
.is-right {
  right: 0;
  text-align: right;
}
.header-title {
  flex: 3;
  text-align: center;
  font-size: 18px;
  margin: 0;
  font-weight: 400;
  line-height: 2;
  letter-spacing: 1px;
  user-select: none;
  max-width: 80%;
  max-width: calc(100% - 90px - 10px);
  > span {
    display: block;
    // height: 1em;
    overflow: hidden;
    // line-height: 1em;
    text-overflow: ellipsis;
    // width: 60%;
    white-space: nowrap;
    margin: 0 auto;
  }
}
.header-btn::before {
  transition: all 100ms;
}
.fullscroll-hide-title {
  .header-title {
    text-indent: -9999px;
  }
  .header-btn {
    transition: all 250ms;
    &:active {
      background: transparent;
    }
    &::before {
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.5);
      transform: scale(0.7);
    }
  }
}
</style>
