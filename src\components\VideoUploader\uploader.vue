<template>
  <div class="file-container">
    <div v-for="(item, index) in list"
        :class="getFileClass(item)"
        :title="item.msg"
        :key="index"
        @click="handleClick(item, index)"
        :percent="item.percent">
      <img :src="(item.poster)"/>
      <a href="javascript:;" @click.stop.prevent="removeFile(item)" class="file-remove">&times;</a>
      <!-- <input type="hidden" :name="uploadName" :value="item.file"/> -->
      <div class="video-icon"></div>
      <!-- <div v-if="item.status === 'uploading'" class="uploading-frame" :style="{height: `${100 - item.percent}%`}"></div> -->
    </div>

    <div v-show="avaliableAdditionalImageCount > 0" class="file file-select" title="上传视频">
      <input v-if="couldMultipleSelect" type="file" class="text"   accept="video/*" multiple="multiple"/>
      <input type="file" accept="video/*"  class="text"/>
    </div>
    <!-- <div class="files">
    </div> -->
    <slot></slot>
  </div>
</template>
<script src="./uploader.js"></script>
<style src="./uploader.scss" lang="scss" scoped></style>
