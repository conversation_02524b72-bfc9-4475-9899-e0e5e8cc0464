<style lang="scss">
  // .rich-text {
  //   white-space: normal;
  //   img {
  //     max-width: 100%;
  //     // vertical-align: top;
  //   }
  // }
  .rich-text * {
    max-width: 100% !important;
  }
  .rich-text {
    margin:0;
    padding:5px;
    background:white;
    overflow: hidden;
    word-wrap: break-word;
    width: 100%;
    box-sizing: border-box;
    line-height: 1.8;
    white-space: pre-line;
    user-select: text;
    box-sizing: border-box;
    * {
      box-sizing: border-box;
    }
    img {
      // vertical-align: bottom;
      max-width: 100%;
    }
    .table-wrapper {
      max-width: 100%;
      overflow-x: scroll;
    }
    table {
      display: block;
      overflow-y: scroll;
      border-collapse: collapse;
      max-width: 100%;
      td {
        border: 1px solid rgb(209, 209, 209);
      }
    }
    h3, h4 {
      margin-top: 8px;
    }
  }
</style>

<template>
  <div
    ref="content"
    class="rich-text"
    v-html="value"
    @click="onClick"
  >
  <slot></slot>
</div>
</template>
<script>
import { parseJglhURL, playPhotos } from '@/bridge';
import { mixinAuthRouter } from '@/mixins';
import { getImageURL } from '@/common/image';

// 获取富文本中的comment节点
export function getComments(context) {
  let foundComments = [];
  let elementPath = [context];
  while (elementPath.length > 0) {
    let el = elementPath.pop();
    for (let i = 0; i < el.childNodes.length; i++) {
      let node = el.childNodes[i];
      if (node.nodeType === Node.COMMENT_NODE) {
        foundComments.push(node);
      } else {
        elementPath.push(node);
      }
    }
  }
  return foundComments;
}

function parents(node, tagName) {
  if (!node || !node.parentNode) return null;
  else {
    // const parent = node;
    if (node.tagName && node.tagName.toLowerCase() === tagName) {
      return node;
    } else {
      return parents(node.parentNode, tagName);
    }
  }
}

/**
 * TODO: 实现点击富文本中的链接跳转窗口
 */
export default {
  name: 'BizRichText',
  mixins: [mixinAuthRouter],
  props: {
    value: {
      type: String,
      default() {
        return '';
      }
    },
  },
  data() {
    return {};
  },
  mounted() {
    // this.clearRichText();
    this.adjustImage();
    // this.adjustTable();
  },
  directives: {
    myclick: {
      bind(el, binding) {
        console.log(el, binding);
        el.addEventListener('click', e => {
          console.log(e);
        }, true)
      }
    }
  },
  methods: {
    adjustTable() {
      const $root = this.$el;
      $root.querySelectorAll('table').forEach(table => {
        const tableWrapper = document.createElement('div');
        tableWrapper.className = 'table-wrapper';
        $root.insertBefore(tableWrapper, table);
        tableWrapper.append(table);
      })
    },
    adjustImage() {
      // 临时解决富文本中图片变形问题（因图片是从第三方同步过来
      this.$el.querySelectorAll('img').forEach(img => {
        img.style.height = 'auto';
        img.removeAttribute('height');
      })
    },
    clearRichText() {
      const $content = this.$refs.content;
      // 删除style标签
      $content.querySelectorAll('style').forEach(node => {
        node.parentNode.removeChild(node);
      });
      // 删除 xml 标签
      $content.querySelectorAll('xml').forEach(node => {
        node.parentNode.removeChild(node);
      });
      getComments($content).forEach(node => {
        node.parentNode.removeChild(node);
      });
      $content.querySelectorAll('style').forEach(node => {
        node.parentNode.removeChild(node);
      });
    },
    playPhotos(imgs, initIndex = 0) {
      const photos = imgs.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, '_xl'),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos
      };
      playPhotos(option);
    },
    onClick(e) {
      const a = parents(e.target, 'a');
      // a标签跳转处理
      if (a) {
        e.preventDefault();
        const href = a.href;
        const options = parseJglhURL(href);
        this.$_router_pageTo(options.url, options);
      } else if (e.target.tagName.toLowerCase() === 'img') {
        // 点击查看大图
        this.playPhotos([e.target.src], 0);
      }
    },
  }
}
</script>
