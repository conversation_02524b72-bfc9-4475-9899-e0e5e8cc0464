import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';
import { back } from '@/bus';

const routers = [
  {
    path: '/vehicle-business-v1',
    name: 'VehicleBusinessIndex',
    meta: {
      title: '交管车务',
    },
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business" */ '@/views/packages/vehicle-business/business-index/BusinessIndex.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/ccb',
    name: 'VehicleBusinessForCCB',
    meta: {
      title: '建行专区',
    },
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business-ccb" */ '@/views/packages/vehicle-business/ccb/Landing.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/ccb/uatuh-agreement',
    name: 'CCBAgreement',
    meta: {
      title: '建行专区用户协议',
    },
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business-ccb-agreement" */ '@/views/packages/vehicle-business/ccb/CCBAgreement.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/calculator1',
    name: 'VehicleCalculator1',
    meta: {
      title: '审车计算器',
    },
    component: resolve => {
      import(/* webpackChunkName: "inspection-calculator" */ '@/views/packages/vehicle-business/InspectionCalculatorV1.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/calculator2',
    name: 'VehicleCalculator2',
    meta: {
      title: '审车计算器',
    },
    component: resolve => {
      import(/* webpackChunkName: "inspection-calculator" */ '@/views/packages/vehicle-business/InspectionCalculatorV2.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/calculator',
    name: 'VehicleCalculator',
    meta: {
      title: '审车计算器',
    },
    component: resolve => {
      import(/* webpackChunkName: "inspection-calculator" */ '@/views/packages/vehicle-business/InspectionCalculatorV3.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/calculator/result',
    name: 'VehicleCalculatorResult',
    meta: {
      title: '审车计算器计算结果',
    },
    component: resolve => {
      import(/* webpackChunkName: "calculator-result" */ '@/views/packages/vehicle-business/InspectionResult.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/no-exam-inspection',
    name: 'NoExamInspection',
    meta: {
      title: '免检审车',
    },
    component: resolve => {
      import(/* webpackChunkName: "no-exam-inspection" */ '@/views/packages/vehicle-business/no-exam-inspection/NoExamInspection.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/no-exam-inspection/reserve',
    name: 'NoExamInspectionReserve',
    meta: {
      title: '免检审车预约',
    },
    component: resolve => {
      import(/* webpackChunkName: "no-exam-inspection-reserve" */ '@/views/packages/vehicle-business/no-exam-inspection/NoExamInspectionReserve.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/online-inspection',
    name: 'OnlineInspection',
    meta: {
      title: '上线审车',
    },
    component: resolve => {
      import('@/views/packages/vehicle-business/online-inspection/OnlineInspection.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/online-inspection/shops',
    name: 'OnlineInspectionShopList',
    meta: {
      title: '上线审车监测站',
    },
    component: resolve => {
      import(/* webpackChunkName: "inspection-online" */ '@/views/packages/vehicle-business/online-inspection/OnlineInspectionShopList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/car-inspection-station/:id',
    name: 'CarInspectionStation',
    meta: {
      title: '审车站点',
    },
    component: resolve => {
      import(/* webpackChunkName: "car-inspection-station" */ '@/views/packages/vehicle-business/online-inspection/CarInspectionStation.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/online-inspection/shop/:id',
    name: 'OnlineInspectionShop',
    meta: {
      title: '上线审车商家',
    },
    component: resolve => {
      import(/* webpackChunkName: "inspector-shop" */ '@/views/packages/vehicle-business/online-inspection/OnlineInspectionShop.vue').then(resolve).catch(handleError);
    },
    children: [
      {
        name: 'inspector/contacts',
        path: 'contacts',
        component: Contacts
      }
    ]
  },
  {
    path: '/vehicle-business/online-inspection/shop/:id/enter',
    name: 'OnlineInspectionReserve',
    meta: {
      title: '上线审车-下单',
    },
    component: resolve => {
      import(/* webpackChunkName: "online-inspection-reserve" */ '@/views/packages/vehicle-business/online-inspection/OnlineInspectionReserve.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/order/result/:id',
    name: 'VehicleOrderResult',
    meta: {
      title: '上线审车提交结果',
    },
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business-order-result" */ '@/views/packages/vehicle-business/InspectionOrderResult.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/order-exchange/result',
    name: 'VehicleExchangeOrderResult',
    meta: {
      title: '上线审车提交结果',
    },
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business-exchange-order-result" */ '@/views/packages/vehicle-business/online-inspection-exchange/InspectionExchangeResult.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/online-inspection-exchange',
    name: 'OnlineInspectionExchange',
    meta: {
      title: '上线审车',
    },
    component: resolve => {
      import('@/views/packages/vehicle-business/online-inspection-exchange/OnlineInspection.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/online-inspection-exchange/shops',
    name: 'OnlineInspectionShopExchangeList',
    meta: {
      title: '上线审车检测站',
    },
    component: resolve => {
      import(/* webpackChunkName: "inspection-online-exchange" */ '@/views/packages/vehicle-business/online-inspection-exchange/OnlineInspectionShopList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/online-inspection-exchange/shop/:id/enter',
    name: 'OnlineInspectionReserveExchange',
    meta: {
      title: '上线审车-下单',
    },
    component: resolve => {
      import(/* webpackChunkName: "online-inspection-reserve-exchange" */ '@/views/packages/vehicle-business/online-inspection-exchange/OnlineInspectionReserve.vue').then(resolve).catch(handleError);
    },
    children: [
      {
        name: 'inspector/contacts/exchange',
        path: 'contacts',
        component: Contacts
      }
    ]
  },
  {
    path: '/vehicle-business/agent-inspection',
    name: 'AgentInspection',
    meta: {
      title: '上门代审',
    },
    component: resolve => {
      import(/* webpackChunkName: "agent-inspection" */ '@/views/packages/vehicle-business/agent-inspection/AgentInspection.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/shops/:mode',
    name: 'VehicleServers',
    meta: {
      title: '车务服务商',
    },
    component: resolve => {
      import(/* webpackChunkName: "vehicle-servers" */ '@/views/packages/vehicle-business/pickers/VehicleServers.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/enter-car-owner/:mode',
    name: 'EnterCarOwnerInfo',
    meta: {
      title: '客户信息录入',
    },
    component: resolve => {
      import(/* webpackChunkName: "car-owner-enter" */ '@/views/packages/vehicle-business/pickers/EnterCarOwnerInfo.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/card-replacement',
    name: 'CardReplacement',
    component: resolve => {
      import(/* webpackChunkName: "card-replacement" */ '@/views/packages/vehicle-business/CardReplacement.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/car-register',
    name: 'CarRegister',
    component: resolve => {
      import(/* webpackChunkName: "car-register" */ '@/views/packages/vehicle-business/car-register/CarRegister.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/car-transfer',
    name: 'CarTransfer',
    component: resolve => {
      import(/* webpackChunkName: "car-transfer" */ '@/views/packages/vehicle-business/car-transfer/CarTransfer.vue').then(resolve).catch(handleError);
    },
  },
  // 外地车转郑州
  {
    path: '/vehicle-business/carTransferToZz',
    name: 'carTransferToZz',
    component: resolve => {
      import(/* webpackChunkName: "carTransferToZz" */ '@/views/packages/vehicle-business/car-transfer/CarTransferToZz.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/qa',
    name: 'VehicleBusinessQA',
    component: resolve => {
      import(/* webpackChunkName: "inspection-qa" */ '@/views/packages/vehicle-business/InspectionQA.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/help',
    name: 'VehicleBusinessHelp',
    component: resolve => {
      import(/* webpackChunkName: "inspection-help" */ '@/views/packages/vehicle-business/InspectionHelp.vue').then(resolve).catch(handleError);
    },
    children: [
      {
        name: 'inspection/help/contacts',
        path: 'contacts',
        component: Contacts
      }
    ]
  },
  {
    path: '/vehicle-business/orders',
    name: 'VehicleBusinessOrders',
    meta: {
      title: '车务订单列表',
    },
    component: resolve => {
      import(/* webpackChunkName: "vi-orders" */ '@/views/packages/vehicle-business/order/Orders.vue').then(resolve).catch(handleError);
    },
    props: router => ({ type: router.params.type }),
    children: [
      {
        name: 'inspection/order/contacts',
        path: 'contacts',
        component: Contacts
      }
    ]
  },
  {
    path: '/vehicle-business/order/:id',
    name: 'VehicleBusinessOrderDetail',
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business-order-detail" */ '@/views/packages/vehicle-business/order/OrderDetail.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/order/:id/comment',
    name: 'VehicleBusinessOrderComment',
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business-order-comment" */ '@/views/packages/vehicle-business/order/OrderComment.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/order/:id/refund',
    name: 'VehicleBusinessOrderRefund',
    component: resolve => {
      import(/* webpackChunkName: "vehicle-business-order-refund" */ '@/views/packages/vehicle-business/order/OrderRefund.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/:type/agreement',
    name: 'VehicleInspectionAgreement',
    component: resolve => {
      import(/* webpackChunkName: "inspection-agreement" */ '@/views/packages/vehicle-business/InspectionAgreement.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/online-inspection/qa',
    name: '上线审车',
    component: resolve => {
      import(/* webpackChunkName: "online-inspection-qa.vue" */ '@/views/packages/vehicle-business/online-inspection/OnlineInspectionQA.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/:type/about',
    name: 'intro',
    component: resolve => {
      import(/* webpackChunkName: "inspection-type-about" */ '@/views/packages/vehicle-business/InspectionAbout.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/vehicle-business/result',
    name: 'inspection-result',
    component: resolve => {
      import(/* webpackChunkName: "inspection-result" */ '@/views/packages/vehicle-business/InspectionResult.vue').then(resolve).catch(handleError);
    },
  },
  // ***********************旧页面**********************
  {
    path: '/inspection',
    redirect: '/vehicle-business',
  },
  {
    path: '/card-replacement',
    redirect: '/vehicle-business',
  },
  {
    path: '/vi/:type/orders',
    redirect: '/vehicle-business/orders',
  },
  {
    path: '/inspection/order/:id',
    redirect: '/vehicle-business/order/:id',
  },
  // {
  //   path: '/card-replacement',
  //   name: '证牌补换',
  //   component: resolve => {
  //     import(/* webpackChunkName: "card-replacement" */ '@/views/packages/vehicle-business/CardReplacement.vue').then(resolve).catch(handleError);
  //   },
  // },
  /* {
    path: '/inspection/offline',
    name: '车辆检测站',
    component: resolve => {
      import('@/views/packages/vehicle-business/InspectorList2.vue').then(resolve).catch(handleError);
    },
  }, */
  /* {
    path: '/inspection/online',
    name: '免检审车',
    component: resolve => {
      import('@/views/packages/vehicle-business/InspectionOnline.vue').then(resolve).catch(handleError);
    },
  }, */
  /* {
    path: '/inspector/:id',
    name: '门店详情',
    component: resolve => {
      import('@/views/packages/vehicle-business/InspectorShop.vue').then(resolve).catch(handleError);
    },
    children: [
      {
        name: 'inspector/contacts',
        path: 'contacts',
        component: Contacts
      }
    ]
  }, */
  // {
  //   path: '/inspection/order/result/:id',
  //   name: '提交结果',
  //   component: resolve => {
  //     import(/* webpackChunkName: "inspection-order-result" */ '@/views/packages/vehicle-business/InspectionOrderResult.vue').then(resolve).catch(handleError);
  //   },
  // },
  // {
  //   path: '/inspection/order/:id',
  //   redirect: '/vehicle-business/order/:id',
  //   name: '审车订单详情',
  //   component: resolve => {
  //     import(/* webpackChunkName: "order-detail" */ '@/views/packages/vehicle-business/order/OrderDetail.vue').then(resolve).catch(handleError);
  //   },
  // },
  // {
  //   path: '/inspection/order/:id/comment',
  //   name: 'inspection-order-comment',
  //   component: resolve => {
  //     import(/* webpackChunkName: "order-comment" */ '@/views/packages/vehicle-business/order/OrderComment.vue').then(resolve).catch(handleError);
  //   },
  // },
  // {
  //   path: '/inspection/order/:id/refund',
  //   name: '审车订单退款',
  //   component: resolve => {
  //     import(/* webpackChunkName: "order-refund" */ '@/views/packages/vehicle-business/order/OrderRefund.vue').then(resolve).catch(handleError);
  //   },
  // },
];

const BaseRouter = [
  {
    path: '/vehicle-business2',
    name: '交管车务',
    redirect: '/vehicle-business',
    component (resolve) {
      import(/* webpackChunkName: "vehicle-business1" */ '@/views/packages/vehicle-business/VehicleBusiness.vue').then(resolve).catch(handleError);
    },
    children: routers,
  }
];

export default routers;
// export default BaseRouter;
