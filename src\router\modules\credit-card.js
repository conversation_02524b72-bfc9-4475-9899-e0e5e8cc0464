import { handleError } from '../error-handler';

export default [
  {
    path: '/credit/card/list',
    name: 'creditCardList',
    component: resolve => {
      import(
        /* webpackChunkName: "credit-card" */ '@/views/packages/credit-card/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/credit/card/apply',
    name: 'creditCardList',
    component: resolve => {
      import(
        /* webpackChunkName: "credit-card" */ '@/views/packages/credit-card/apply.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/credit/card/form',
    name: 'creditCardForm',
    component: resolve => {
      import(
        /* webpackChunkName: "credit-card" */ '@/views/packages/credit-card/form.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/credit/card/records/detail',
    name: 'cardRecordDetail',
    component: resolve => {
      import(
        /* webpackChunkName: "credit-card" */ '@/views/packages/credit-card/record-detail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/credit/card/records/assist',
    name: 'creditCardRecordsAssist',
    component: resolve => {
      import(
        /* webpackChunkName: "credit-card" */ '@/views/packages/credit-card/list-assist.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/credit/card/records/share',
    name: 'creditCardRecordsShare',
    component: resolve => {
      import(
        /* webpackChunkName: "credit-card" */ '@/views/packages/credit-card/list-share.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/credit/card/records/card',
    name: 'creditCardRecordsCard',
    component: resolve => {
      import(
        /* webpackChunkName: "credit-card" */ '@/views/packages/credit-card/list-card.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
