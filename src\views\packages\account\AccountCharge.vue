<template>
    <container @ready="init" @leave="onLeave" @resume="onResume">
      <x-header title="账户充值">
         <x-button  slot="left" type="back"></x-button>
      </x-header>

      <content-view class="account-charge" :status="status" @reload="reload">
        <template v-if="status == AppStatus.READY">
          <div class="packages">
            <div
               v-for="(item, index) in pkgs"
               :key="index"
               :class="['package', item.id == pkg.id ? 'package-active' : '']"
               @click="setPackage(item)"
             >
              <span v-if="item.type == PackageType.REGULAR" class="package-title">{{item.realPrice}}元</span>
              <span v-else class="package-title">自定义金额</span>
              <!-- <p class="package-note">到账{{item.chargePrice}}元</p> -->
            </div>
          </div>
          <div v-if="couldTopUpCustomAmount" class="package-custom">
            <div class="weui-cells__title">自定义金额<!-- <span style="color:red">({{topUpTip}})</span> --></div>
            <div class="weui-cells">
              <div class="weui-cell">
                <div class="weui-cell__bd">
                  <input class="weui-input" type="text" v-focus v-model="amount" @input="onTopUpInput" :placeholder="`请输入金额，${topUpTip}`">
                </div>
              </div>
            </div>
          </div>
          <payments v-model="channel" :channels="PaymentChannels"></payments>
          <div class="button-sp-area">
            <a href="javascript:;" class="weui-btn weui-btn_primary" :class="{ 'btn-loading': paying,  'weui-btn_disabled': paying}" @click="charge">{{ paying ?  '正在支付' : '确认充值'}}</a>
          </div>
          <div class="charge-tip">
            <p>点击确认充值，即表示您同意<a href="javascript:;" @click="go('/account/charge/licence')">《充值协议》</a></p>
            <p>充值后，账户余额仅限消费，不可提现、转赠、退还</p>
          </div>
        </template>
      </content-view>
    </container>
</template>
<style lang="scss">
  .account-charge {
    .charge-tip{
      color: #adadad;
      font-size: 0.8em;
      text-align: center;
    }
  }
</style>

<style lang="scss" scoped>
  @import '~styles/variable/global.scss';
  $lh-blue : $lh-primary-color;
  .button-sp-area {
    padding: 10px;
  }
  .package-custom {
    .weui-cells__title {
      padding: 5px 10px;
    }
    .weui-cell__bd {
      position: relative;
      &::before {
        content: '¥';
        position: absolute;
        top: 3px;
        font-size: 16px;
        font-weight: 700;
      }
    }
    .weui-input {
      font-size: 22px;
      font-weight: 700;
      margin-left: 15px;
      color: #F79E4F;
      &::placeholder {
        color: #cccccc;
        font-weight: 400;
        font-size: 16px;
      }
    }
  }
  .packages {
    overflow: hidden;
    text-align: center;
    box-sizing: border-box;
    background: white;
    padding: 5px;
    text-align:center;
    margin-left:-3%;
  }

  .package {
    width:30.33%;
    float: left;
    margin: 3% 0 5px 3%;
    background: white;
    padding: 20px 10px;
    box-sizing: border-box;
    border: 1px solid #D6D6D6;
    border-bottom-color:#F2F2F2;
    color:#FD8F33;
    border-radius: 2px;
    transition:background 100ms;
  }
  .package-active {
    background: #E0E0E0;
    color:#F79E4F;
    border-color:#E0E0E0;
    font-weight: 700;
  }

  .package-note {
    color: $lh-primary-color;
    font-size: 14px;
  }

  .package-active .package-note {
      color: white;
  }

  .charge-tip{
    color: #adadad;
    font-size: 0.8em;
    text-align: center;
  }
</style>
<script>
import { Payments } from '@/views/components';
import { pay } from '@/bridge';
import { bus, Events, toast, dialog, back, loading } from '@/bus';
import { PaymentChannel, AppStatus, GAEventLabel } from '@/enums';
import { getChargePackages, submitTopUpOrder } from '@/api/modules/account';
import { isInWeixin } from '@/common/env';

const PackageType = {
  REGULAR: 0, // 固定
  CUSTOM: 1, // 自定义
}

export default {
  name: 'account-charge',
  components: {
    Payments,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      PackageType,
      paying: false,
      pkg: {},
      amount: '',
      channel: PaymentChannel.ALIPAY,
    };
  },
  computed: {
    couldTopUpCustomAmount() {
      return this.pkg && this.pkg.type == PackageType.CUSTOM;
    },
    topUpTip() {
      if (this.couldTopUpCustomAmount) {
        const { maxRechargeAmount, minRechargeAmount } = this.pkg;
        return `最低${minRechargeAmount}元，最高${maxRechargeAmount}元`
      }
      return '';
    },
    amount2() {
      const amount2 = this.amount.replace(/[^0-9.]/gi, '').replace(/^(\d+\.\d)\d+$/, '$1')
      console.log(this.amount, amount2);
      return amount2;
    },
    topUpAmount() {
      return Number(this.amount2);
    },
    PaymentChannels() {
      return PaymentChannel.list()
        .filter(item => !item.disabled)
        .filter(item => item != PaymentChannel.ACCOUNT)
        .filter(channel => {
          if (!isInWeixin && channel == PaymentChannel.WEIXIN_PUB) return false;
          if (isInWeixin && channel != PaymentChannel.WEIXIN_PUB) return false;
          return true;
        });
    },
  },
  directives: {
    focus: {
      inserted(el) {
        el.focus();
      },
    },
  },
  methods: {
    go(url) {
      this.$router.push(url);
    },
    onTopUpInput() {
      this.$nextTick(() => {
        this.amount = this.amount2;
      })
    },
    init() {
      getChargePackages().then(res => {
        this.pkgs = res;
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    onLeave() {
      this.status = AppStatus.LOADING;
      // this.$destroy();
    },
    onResume() {
      this.paying = false;
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    setPackage(item) {
      this.pkg = item;
    },
    charge() {
      const { id: pkg, name, price } = this.pkg;
      if (!pkg) {
        toast().tip('请选择一个充值套餐');
        return;
      }
      let topUpAmount = 0;
      if (this.couldTopUpCustomAmount) {
        if (!this.topUpAmount) {
          toast().tip('请输入充值金额');
          return;
        }
        if (this.topUpAmount < this.pkg.minRechargeAmount) {
          toast().tip(`最小起充金额是${this.pkg.minRechargeAmount}元哦`);
          return;
        }
        if (this.topUpAmount > this.pkg.maxRechargeAmount) {
          toast().tip(`最高充值金额是${this.pkg.maxRechargeAmount}元哦！`);
          return;
        }
        topUpAmount = this.topUpAmount;
      }

      loading(true, '正在提交...');
      submitTopUpOrder(pkg, topUpAmount).then(({ oid }) => {
        loading(false);
        this.pay(oid, name, price);
      }, err => {
        loading(false);
        err && dialog().alert(err, {
          title: '错误'
        });
      });
    },
    pay(oid, name, amount) {
      const channel = this.channel;
      const payInfo = {
        order_id: oid,
        channel,
      };

      const that = this;
      that.paying = true;
      pay({
        data: payInfo,
        success: () => {
          that.paying = false;
          toast().success('充值成功');
          back();
          this.$ua && this.$ua.trackEvent('Trade', GAEventLabel.CHARGE, this.channel.valueOf(), Math.round(this.order.amount || 0));
          this.$ua && this.$ua.trackEvent('Submit', 'Trade', GAEventLabel.CHARGE, Math.round(this.pkg.price));
        },
        fail() {
          that.paying = false;
          toast().tip('已取消支付');
        }
      });
    },
  }
};
</script>
