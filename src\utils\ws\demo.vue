<template>
  <div class="websocket-demo">
    <div class="status">
      WebSocket状态: <span :class="statusClass">{{ status }}</span>
    </div>

    <div class="message-list">
      <h3>接收到的消息：</h3>
      <div v-if="messages.length === 0" class="no-messages">暂无消息</div>
      <div v-else class="message" v-for="(msg, index) in messages" :key="index">
        {{ msg }}
      </div>
    </div>

    <div class="controls">
      <input v-model="messageToSend" placeholder="输入要发送的消息" />
      <button @click="sendMessage" :disabled="!isConnected">发送</button>
      <button v-if="!isConnected" @click="connectWebSocket">连接</button>
      <button v-else @click="disconnectWebSocket">断开</button>
    </div>
  </div>
</template>

<script>
import WebSocketClient from '@/utils/webSocketClient';

export default {
  name: 'WebSocketDemo',

  data() {
    return {
      wsClient: null,
      status: 'DISCONNECTED',
      messages: [],
      messageToSend: '',
      isConnected: false,
    };
  },

  computed: {
    statusClass() {
      return {
        'status-connected': this.status === 'OPEN',
        'status-connecting': this.status === 'CONNECTING',
        'status-disconnected':
          this.status === 'CLOSED' || this.status === 'DISCONNECTED',
      };
    },
  },

  created() {
    this.initWebSocket();
  },

  beforeDestroy() {
    // 组件销毁前关闭WebSocket连接，避免内存泄漏
    this.destroyWebSocket();
  },

  methods: {
    initWebSocket() {
      // 创建WebSocket客户端实例
      this.wsClient = new WebSocketClient({
        url: 'ws://***************:8800',
        heartbeatInterval: 30000,
        heartbeatMessage: { type: 'ping', timestamp: Date.now() },
        maxReconnectAttempts: 5,
      });

      // 注册事件处理函数
      this.wsClient
        .onopen(() => {
          this.status = this.wsClient.getStatus();
          this.isConnected = true;
          this.messages.push('WebSocket连接已建立');
        })
        .onmessage(event => {
          // 接收消息
          let message =
            typeof event.data === 'object'
              ? JSON.stringify(event.data)
              : event.data;

          this.messages.push(`收到消息: ${message}`);
        })
        .onclose(() => {
          this.status = 'CLOSED';
          this.isConnected = false;
          this.messages.push('WebSocket连接已关闭');
        })
        .onerror(() => {
          this.status = 'ERROR';
          this.messages.push('WebSocket连接发生错误');
        });
    },

    connectWebSocket() {
      // 如果wsClient不存在，则重新初始化
      if (!this.wsClient) {
        this.initWebSocket();
      }

      this.wsClient.connect();
      this.status = 'CONNECTING';
    },

    disconnectWebSocket() {
      if (this.wsClient) {
        this.wsClient.close();
        this.isConnected = false;
        this.status = 'DISCONNECTED';
      }
    },

    sendMessage() {
      if (!this.messageToSend.trim()) return;

      if (this.wsClient && this.isConnected) {
        const success = this.wsClient.send(this.messageToSend);
        if (success) {
          this.messages.push(`发送消息: ${this.messageToSend}`);
          this.messageToSend = ''; // 清空输入框
        } else {
          this.messages.push('消息发送失败');
        }
      } else {
        this.messages.push('WebSocket未连接，无法发送消息');
      }
    },

    destroyWebSocket() {
      if (this.wsClient) {
        this.wsClient.close();
        this.wsClient = null;
      }
    },
  },
};
</script>

<style scoped>
.websocket-demo {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
}

.status {
  margin-bottom: 15px;
}

.status-connected {
  color: green;
  font-weight: bold;
}

.status-connecting {
  color: orange;
}

.status-disconnected {
  color: red;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  padding: 10px;
  margin-bottom: 15px;
}

.no-messages {
  color: #999;
  font-style: italic;
}

.message {
  border-bottom: 1px solid #eee;
  padding: 5px 0;
}

.controls {
  display: flex;
  gap: 10px;
}

input {
  flex: 1;
  padding: 8px;
}

button {
  padding: 8px 15px;
  cursor: pointer;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
