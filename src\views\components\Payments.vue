<template>
    <div>
      <div class="weui-cells__title">选择支付方式</div>
      <div class="weui-cells weui-cells_radio">
        <label v-for="(item, $index) in PaymentChannels" :key="$index"  @click="setPayment(item)" class="weui-cell weui-check__label" :for="'x'+$index" >
          <div class="weui-cell__bd flex-row">
            <div class="img-container channel-logo" :class="['channel-logo-'+item.value]"></div>
            <div class="channel-content">
              <!-- 银联云闪付特殊处理-->
              <h4 v-if="item.value == 'union_pay_cashier'" class="channel-title" :class="['channel-title-'+item.value]">银联
                <span></span>
              </h4>
              <h4 v-else class="channel-title">{{item.name}}</h4>
              <!-- <p class="channel-note" v-html="item.desc"></p> -->
            </div>
          </div>
          <div class="weui-cell__ft">
            <input type="radio" class="weui-check"  :checked="item == value" :disabled="item.disabled" :value="item.value" name="channel" :id="'x'+$index">
            <span class="weui-icon-checked"></span>
          </div>
        </label>
      </div>
    </div>
</template>
<style lang="scss" scoped>
  @import '~styles/variable/global.scss';
  .weui-cell{
    padding: 8px 10px;
  }
  .weui-cells__title {
    margin-top: 10px;
  }
  .channel-logo {
    width: 35px;
    height: 35px;
    margin-right: 10px;
    background-size: contain;
  }
  .channel-title{
    font-size:14px;
  }
  .channel-note{
    color:#aba9a9;
    font-size:13px;
  }
  .flex-row{
    align-items: center;
  }
  .channel-logo-jglh_account{
    background-image:url(~@/assets/images/payment/account.svg);
  }
  .channel-logo-alipay{
    background-image:url(~@/assets/images/payment/alipay.svg);
  }
  .channel-logo-wx{
    background-image:url(~@/assets/images/payment/weixin.svg);
  }
  .channel-logo-wxpub{
    background-image:url(~@/assets/images/payment/weixin.svg);
  }
  .channel-logo-union_pay{
    background-image:url(~@/assets/images/payment/union_pay.svg);
  }
  .channel-logo-CCB{
    background-image:url(~@/assets/images/payment/ccblong.png);
  }
  .channel-logo-union_pay_mobile {
    background-image:url(~@/assets/images/payment/union_pay_mobile.png);
  }
  .channel-logo-union_pay_cashier {
    background-image:url(~@/assets/images/payment/union_pay.png);
  }
  .channel-logo-union_pay_xcx {
    background-image:url(~@/assets/images/payment/union_pay_mobile.png);
  }
  .channel-title-union_pay_cashier {
    display: inline-flex;
    align-items: center;
    span{
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-left: 10px;
      background: url(~@/assets/images/payment/union_pay_mobile.png) no-repeat center;
      background-size: 100%;
    }
  }
  .weui-cells_radio .weui-check:checked + .weui-icon-checked{
    background: $lh-2022-primary-color;
    border-color: $lh-2022-primary-color;
  }
</style>
<script>
import { PaymentChannel } from '@/enums';

export default {
  props: {
    value: {
      type: [String, Object]
    },
    channels: {
      type: Array,
    }
  },
  data() {
    return {
      channel: this.value,
    };
  },
  computed: {
    PaymentChannels() {
      return this.channels || PaymentChannel.list().filter(item => !item.disabled);
    },
  },
  methods: {
    setPayment(value) {
      console.log(value);
      this.$emit('input', value);
    },
  },
};
</script>
