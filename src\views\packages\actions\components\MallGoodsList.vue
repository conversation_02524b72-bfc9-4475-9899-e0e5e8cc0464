<template>
  <div>
    <template v-if="status == AppStatus.READY">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          class="vant-list"
          v-model="loading"
          :error.sync="error"
          error-text="请求失败，点击重新加载"
          :offset="100"
          :finished="finished"
          :finished-text="finishedText"
          @load="init"
          :immediate-check="false"
        >
          <MallGoodsItem
            class="goods-item"
            v-for="item in goods"
            :key="item.id"
            :goods="item"
            @click.native="look(item)"
          ></MallGoodsItem>
          <!-- v-for="(item,index) in pageData"
            :key="index" -->
          <template v-if="!pageData.length && !loading&&!error">
            <list-placeholder
              icon="~@pkg/actions/assets/images/yptx-list-empty.png"
            ></list-placeholder>
          </template>
        </van-list>
      </van-pull-refresh>
    </template>
  </div>
</template>

<script>
import { formatDate, formatMoney } from '@/utils';
import { AppStatus, MallGoodsType } from '@/enums';
import { mixinAuthRouter } from '@/mixins';
import { getGoodsList } from '@pkg/mall/api';
import { Button, Toast, List, PullRefresh } from 'vant';
import MallGoodsItem from './MallGoodsItem.vue';

// 规避微信小程序审核
const blackListInWeApp = [
  // '酒', // 2021年1月4日10:36:11，根据领导指示，在未获得相关类目许可情况下放开酒类
  '烟',
  // '牙',
  // '口腔',
]
function isValidGoods(title = '', isInMiniProgram) {
  if (isInMiniProgram) {
    return !blackListInWeApp.some(item => {
      return title.indexOf(item) > -1;
    })
  }
  return true;
}

export default {
  name: 'MallGoodsList',
  props: {
    id: {
      type: [String, Number],
    },
    uid: {
      type: [String, Number],
    },
  },
  mixins: [mixinAuthRouter],
  components: {
    MallGoodsItem,
    [Button.name]: Button,
    [Toast.name]: Toast,
    [PullRefresh.name]: PullRefresh,
    [List.name]: List,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.READY,
      pageData: [],
      loading: false,
      error: false,
      finished: false,
      refreshing: false,
      tempFinished: false, // 切换tab时，临时储存finished的值，防止在其他tab下触发本页van-list的load方法(vant-list组件bug，issues：#3430)
      paging: {
        page: 1,
        pageSize: 10,
        total: null
      }
    };
  },
  computed: {
    finishedText() {
      return this.pageData.length ? '没有更多数据了' : '';
    },
    filterParams() {
      return {
        sort: 'normal',
        // categoryId: this.id, // type为'all'或者'label' 类目类型时
        topicId: this.id, // type为专区类型时
        page: this.paging.page,
        pageSize: this.paging.pageSize,
        recommendStatus: ''
      }
    },

    goods() {
      return (this.pageData || []).map(item => {
        return {
          ...item,
          image: (item.image || '').split(',')[0],
          tags: item.flag ? item.flag.split(',') : [],
        }
      }).filter(item => {
        return isValidGoods(item.title, this.isInMiniProgram) && isValidGoods(item.categoryName, this.isInMiniProgram);
      })
    }
  },
  watch: {
  },
  mounted () {
    this.init()
  },
  methods: {
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      this.pageData = [];
      this.paging.page = 1;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.init();
    },
    init () {
      this.loading = true;
      this.getPageData();
    },
    getPageData () {
      getGoodsList(this.filterParams)
        .then((res) => {
          if (this.refreshing) {
            this.pageData = [];
            this.refreshing = false;
          }
          if (res.list.length < 10) {
            this.finished = true;
            this.tempFinished = this.finished;
          }
          this.paging.page += 1;
          this.pageData = this.pageData.concat(res.list);
          // this.pageData = []
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          this.error = true;
          console.log(err)
        });
    },

    look(item) {
      if (item.goodsType == MallGoodsType.OUTLINK) {
        this.$_router_pageTo(item.linkUrl, {
          title: '',
          titleBar: true,
          progressBar: true,
          autoUpdateTitle: true,
        });
      } else if (item.goodsType == MallGoodsType.NORMAL) {
        this.$_router_pageTo(`/mall/goods/${item.id}${this.uid ? `?ref=${this.uid}` : ''}`, {
          theme: 'light'
        });
      } else {
        this.$_router_pageTo(`/mall/goods/${item.id}${this.uid ? `?ref=${this.uid}` : ''}`, {
          theme: 'light'
        });
        // toast().tip('商品属性异常！');
      }
    },
  }
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
::v-deep .list-empty{
  margin-top: 40px;
  flex: 1;
  color: #7F121C;
  .list-empty-icon{
    // width: 175px;
    height: 200px;
  }
}
::v-deep .van-list__finished-text, ::v-deep .van-loading__text, ::v-deep .van-loading{
  color: #7F121C;
}
::v-deep .van-pull-refresh__head{
  color: #7F121C;
}
.vant-list ::v-deep{
  padding-top: 10px;

  display: flex;
  flex-wrap: wrap;
  padding: 0 5px;
  // background: white;
  margin-top: 0;
  .van-list__loading, .van-list__finished-text, .van-list__error-text, .van-list__placeholder{
    width: 100%;
    color: #7F121C;
  }
  .van-loading__text{
    color: #7F121C;
  }
  .goods-item {
    width: 49%;
    width: calc(50% - 10px);
    // width: 100%;
    margin: 5px;
    background: white;
    border-radius: 10px;
    padding-bottom: 10px;
    overflow: hidden;
    // height: 262px;
  }
}
</style>
