<template>
  <page class="share-home container-fullscreen" @resume="onResume">
    <loading v-if="status == AppStatus.LOADING"></loading>
    <div v-else-if="status == AppStatus.READY" class="audio-wrap">
      <wx-player :playList="playList" :currentSong="currentSong"></wx-player>
      <div class="play-list">
        <div class="album-info">
          <biz-image class="avatar" :src="currentSong.cover" :lazy="true"></biz-image>
          <div class="text">
            <p>{{ radioInfo.title }}</p>
            <span>已更新{{ radioInfo.programItemCount }}集</span>
          </div>
          <span class="view-album-btn" @click="toAlbum">查看专辑</span>
        </div>
        <!-- <h2 class="head">
          <van-button type="primary" size="small" round>
            <template slot="icon">
              <svg class="icon-svg">
                <use xlink:href="#icon-xf-bofang"></use>
              </svg>
            </template>
            全部播放
          </van-button>
          <div class="flex"></div>
          <div class="update-status">
            已更新{{ 132 }}集
          </div>
        </h2> -->
        <ul class="list-wrap">
          <li v-for="(item) in playList" :key="item.id" @click="selectItem(item)" class="song-item">
            <div class="audio-no">
              <!-- <div v-if="(currentSong.id == item.id)" class="equilizer">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
              </div> -->
              <span>{{ item.index }}</span>
            </div>
            <div class="audio-info">
              <p>{{ item.name }}</p>
              <div class="audio-duration">
                <svg class="icon-svg">
                  <use xlink:href="#icon-xf-shijian"></use>
                </svg>
                <span>{{formatTime(item.duration)}}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="tips">打开交广领航 倾听更多</div>
    </div>

    <page-loader
      v-else
      :status="status"
      :error="error"
      @reload="init"
    >
    </page-loader>
  </page>
</template>
<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { createSong, durationTrans } from '@/common/audio';
import { isInWeixin } from '@/common/env';
import { getImageURL } from '@/common/image';
import { toast } from '@/bus'
import { getAppURL } from '@/utils';
import { AppStatus } from '@/enums'
import { Button } from 'vant';
import { getShareAudioDetail } from '../api';
import PlayList from '../components/PlayList.vue';
import WxPlayer from '../components/WxPlayer.vue';
export default {
  name: 'SharePlayer',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    PlayList,
    WxPlayer
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      title: '欢迎来到music',
      error: '',
      $_share_info: {
        path: '/audio/share',
        title: '欢迎来到music',
        desc: '小疯的简史生活',
      },
      playList: [],
      currentSong: {},
      radioInfo: {},
      total: 0,
    };
  },
  computed: {
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.init()
    })
  },
  created() {
    // this.init()
  },
  methods: {
    init() {
      let id = this.$route.query.id
      if (!id) {
        toast().tip('请选择节目播放')
        this.$router.back()
        return
      }
      getShareAudioDetail({ id: id })
        .then((profile) => {
          this.status = AppStatus.READY
          this.radioInfo = profile.liveRadioProgram
          this.playList = this._createSong(profile.currentProgramItemList)
          this.currentSong = this._createSong([profile.currentProgramItem])[0]
          this.share()
        })
        .catch((err) => {
          this.status = AppStatus.ERROR
          toast().tip(err)
          // return Promise.reject(err)
        });
    },
    onResume() {

    },
    toAlbum() {
      this.$router.replace({
        path: '/audio/share/album'
      })
    },
    _createSong(list) {
      if (list instanceof Array) {
        return list.map(item => {
          return createSong({
            id: item.id,
            index: item.seqNo,
            cover: this.radioInfo.image,
            duration: item.duration,
            // singer: item.singer,
            name: item.title,
            audioId: item.radioResource
          });
        });
      }
    },
    formatTime(time) {
      if (!time) {
        return '00:00'
      } else {
        return durationTrans(time)
      }
    },
    selectItem(item) {
      this.currentSong = item
      this.share()
      // this.$router.replace({
      //   path: '/audio/share/player',
      //   query: { id: item.id },
      // })
    },
    share(action = 'update') {
      // 设置邀请链接分享信息
      const path = `/audio/share/player?id=${this.currentSong.id}`;
      const link = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share' : '?jglh'
      });
      const shareInfo = {
        link: link,
        title: `《${this.currentSong.name}》,快来一起收听吧~`,
        desc: `为您推荐《${this.radioInfo.title}》选章，快来一起来收听吧~`,
        imgUrl: this.currentSong.cover,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~../assets/style/player-list.scss";
.share-home{
  background: #F3F3F3;
  overflow-y: scroll;
}
.album-info{
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px 15px 10px;
  background: #fff;
  box-sizing: border-box;
  .avatar{
    display: block;
    width: 50px;
    height: 50px;
    border-radius: 10px;
    margin-right: 10px;
  }
  .text{
    display: inline-flex;
    flex-direction: column;
    flex: 1;
    line-height: 1;
    overflow: hidden;
    p{
      width: 100%;
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 5px;
    }
    span{
      font-size: 13px;
      font-weight: 500;
      color: #333333;
    }
  }
  .view-album-btn{
    background: #FFFFFF;
    border: 1px solid #FD4925;
    border-radius: 14px;
    padding: 8px 10px;
    font-size: 14px;
    font-weight: bold;
    color: #FD4925;
    line-height: 1;
  }
}
.play-list{
  margin-top: 10px;
  height: auto;
  min-height: auto;
  display: block;
  background: #fff;
}
.tips{
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #FD4925;
  padding: 15px 0 30px;
  background: #fff;
}
</style>
