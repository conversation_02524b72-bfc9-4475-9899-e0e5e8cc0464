<template>
  <div v-if="storageRecords.length" class="search-block">
    <div class="block-title">
      <h2>搜索记录</h2>
      <div class="title-right" @click="clearSearchKeyword">
        <van-icon name="delete-o" size="16" color="#999999" />
        <span>清空</span>
      </div>
    </div>
    <div
      class="record-wrap"
      ref="recordWrap"
      :class="{
        'has-more': keywordsLines > 2,
        'more-off': !moreStatus,
        'more-on': moreStatus,
      }"
    >
      <div
        class="record-item"
        v-for="(item, index) in records"
        :key="index"
        ref="recordItem"
        @click="emitEvent(item)"
      >
        {{ item }}
      </div>
      <div v-if="keywordsLines > 2" class="record-item">
        <span class="desc_more" @click="toggleMoreStatus">
          <i class="icon_jglh icon-xf-zhankai-xiala"></i>
          <i class="icon_jglh icon-xf-shouqi-wangshang"></i>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, formatMoney, formatPrice } from '@/utils';
import { setGoodsSearchKeyword, getGoodsSearchKeyword, setCarMtKeyword, getCarMtKeyword, setCarWashKeyword, getCarWashKeyword } from '@/store/storage';
import { Icon, Button, Dialog } from 'vant';

export default {
  name: 'SearchRecord',
  props: {
    refresh: {
      type: Number
    },
    category: {
      type: String // 'mall', 'car',
    },
  },
  components: {
    [Icon.name]: Icon,
    [Dialog.name]: Dialog,
  },
  data() {
    return {
      storageRecords: [],
      keywordsLines: 1,
      moreStatus: false,
    };
  },
  computed: {
    records() {
      let sliceStorageRecords = []
      if (this.keywordsLines > 2 && !this.moreStatus) {
        sliceStorageRecords = this.storageRecords.slice(0, this.lastVisibleIndex)
      } else {
        sliceStorageRecords = this.storageRecords
      }
      if (sliceStorageRecords.length > 8) {
        return sliceStorageRecords.slice(0, 8)
      }
      return sliceStorageRecords;
    },
    lastVisibleIndex() {
      let lastVisibleIndex = 0;

      let el = document.querySelector('.record-item')
      if (!el) return 0;
      let computedStyle = window.getComputedStyle(el);
      let itemMargin = parseFloat(computedStyle.margin) * 2;
      let offsetHeight = el.offsetHeight + itemMargin;

      for (let i = 0; i < this.storageRecords.length; i++) {
        let offsetTop = this.calculateItemOffsetTop(i);

        if (offsetTop <= 2 * offsetHeight) {
          lastVisibleIndex = i;
        } else {
          break;
        }
      }
      return lastVisibleIndex;
    },
  },
  watch: {
    storageRecords: function (newVal, oldVal) {
      this.keywordsLines = this.calculateWrapLines(
        document.querySelector('.record-wrap')
      );
    },
    refresh: function (newVal) {
      this.updateSearchKeyword();
    }
  },
  created() {
    // setCarWashKeyword(['月饼礼盒', '月饼', '月饼礼', '月', '月礼盒', '月饼礼盒', '月饼礼盒', '月饼盒', '礼盒', '月饼礼盒', '月饼盒', '礼盒'])
    this.updateSearchKeyword();
  },
  mounted() {
    this.keywordsLines = this.calculateWrapLines(
      document.querySelector('.record-wrap')
    );
  },
  methods: {
    emitEvent(item) {
      this.$emit('search', item)
    },
    calculateItemOffsetTop(index) {
      let el = this.$refs.recordItem[index]
      if (!el) return 0;
      return el.offsetTop;
    },
    // 获取行数
    calculateWrapLines(el) {
      if (!el) return;
      // var computedStyle = window.getComputedStyle(el);

      // 获取元素的高度和行高
      let elementHeight = el.clientHeight || el.offsetHeight;
      // var lineHeight = parseFloat(computedStyle.lineHeight);
      let child = document.querySelector('.record-item');
      let lineHeight = parseFloat(child.clientHeight) + 10;

      // 计算行数
      let lineCount = Math.round(elementHeight / lineHeight);

      return lineCount;
    },
    // 切换记录展开状态
    toggleMoreStatus() {
      this.moreStatus = !this.moreStatus;
    },
    // 清空搜索记录
    clearSearchKeyword() {
      Dialog.confirm({
        title: '提示',
        message: '确认删除全部搜索记录?',
        confirmButtonText: '确定',
        confirmButtonColor: '#FD4925'
      })
        .then(() => {
          this.emptySearchKeyword()
          this.storageRecords = []
        })
        .catch(() => {
          // on cancel
        });
    },
    // 更新搜索记录
    updateSearchKeyword() {
      switch (this.category) {
        case 'mall':
          this.storageRecords = getGoodsSearchKeyword();
          break;
        case 'car':
          this.storageRecords = getCarMtKeyword();
          break;
        case 'wash':
          this.storageRecords = getCarWashKeyword();
          break;

        default:
          this.storageRecords = []
          break;
      }
      // this.storageRecords = getGoodsSearchKeyword();
    },
    // 清空搜索记录
    emptySearchKeyword() {
      switch (this.category) {
        case 'mall':
          setGoodsSearchKeyword()
          break;
        case 'car':
          setCarMtKeyword()
          break;
        case 'wash':
          setCarWashKeyword()
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.search-block {
  padding: 0 15px;
  margin-bottom: 10px;
}
.block-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  line-height: 1;
  > h2 {
    font-size: 17px;
    font-weight: bold;
    color: #111111;
  }
  .title-right {
    display: inline-flex;
    align-items: center;
    span {
      margin-left: 6px;
      font-size: 12px;
      color: #999999;
    }
  }
}
.record-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin: -5px;
  line-height: 1;
  position: relative;
  .record-item {
    margin: 5px;
    background: #ffffff;
    border-radius: 14px;
    padding: 8px 10px;
    font-size: 13px;
    color: #333333;
    max-width: 100%;
    box-sizing: border-box;
    @include singleline-ov();

  }
}
.more-on{
  .icon-xf-zhankai-xiala{
    display: none;
  }
}
.more-off{
  .icon-xf-shouqi-wangshang{
    display: none;
  }
}
</style>
