<template>
  <div class="filter-tabs">
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: activeTabIndex === index }"
      @click="selectTab(index)"
    >
      {{ tab.text }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterTabs',
  data() {
    return {
      activeTabIndex: 0,
      tabs: [
        { text: '同城', type: 'city' },
        { text: '年龄相仿', type: 'age' },
        { text: '最新', type: 'latest' },
      ],
    };
  },
  methods: {
    selectTab(index) {
      this.activeTabIndex = index;
      this.$emit('filter-change', this.tabs[index].type);
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-tabs {
  display: flex;
  justify-content: center;
  // background-color: #fff;
  padding: 10px 0;
  // border-bottom: 1px solid #f5f5f5;

  .tab-item {
    padding: 5px 15px;
    margin: 0 10px;
    font-size: 14px;
    color: #333;
    border-radius: 16px;
    background-color: #f5f5f5;

    &.active {
      color: #fff;
      background-color: #ff5a5f;
    }
  }
}
</style>
