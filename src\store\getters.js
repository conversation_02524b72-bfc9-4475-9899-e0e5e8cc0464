export const isLoading = state => {
  return state.loader;
};

export const transitionName = state => {
  return state.views.transitionName;
};

export const transitionMode = state => {
  return state.views.transitionMode;
};

export const aliveViews = state => {
  return state.views.routeList;
};

export const isLoggedIn = state => {
  return state.auth.loggedIn;
};

export const isVip = state => {
  return (
    state.user &&
    state.user.vip &&
    (state.user.memberCategory == 'all' || state.user.memberCategory == 'mall')
  );
};

export const isCarVip = state => {
  return (
    state.user &&
    state.user.vip &&
    (state.user.memberCategory == 'all' || state.user.memberCategory == 'car')
  );
};

export const userInfo = state => {
  return state.auth.loggedIn ? state.user : state.initUserInfo;
};

export const selectedCar = state => {
  return state.selectedCar;
};

export const routePaths = state => {
  return state.views.routePaths;
};

export const shareInfo = state => {
  return state.shareInfo;
};

export const fullScreen = state => {
  return state.audioPlayerInfo.fullScreen;
};
export const playList = state => state.audioPlayerInfo.playList;
export const currentIndex = state => state.audioPlayerInfo.currentIndex;
// export const currentSong = state => {
//   if (state.audioPlayerInfo.currentIndex == -1) return {}
//   return state.audioPlayerInfo.playList[state.audioPlayerInfo.currentIndex] || {}
// }
export const currentSong = state => state.audioPlayerInfo.currentSong;
export const playing = state => state.audioPlayerInfo.playing;
export const radioInfo = state => state.audioPlayerInfo.radioInfo;
export const playListPage = state => state.audioPlayerInfo.playListPage;
export const playListSequence = state => state.audioPlayerInfo.playListSequence;
export const audioTotal = state => state.audioPlayerInfo.audioTotal;

export const appSetting = state => state.appSetting;
