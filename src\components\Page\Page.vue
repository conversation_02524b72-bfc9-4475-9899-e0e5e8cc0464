<template>
  <transition
    @after-enter="onAfterEnter"
    @after-leave="onAfterLeave"
    :name="theTransitionName"
  >
    <div class="container" :class="{ 'container-fullscreen': (mode == PageMode.FULLSCREEN), 'no-header': noHeader }" :transition-name="theTransitionName">
      <slot></slot>
    </div>
  </transition>
</template>
<style lang="scss">
.no-header ::v-deep{
  .page-content{
    margin-top: 0;
  }
}
</style>
<script>
  // 2022-08-03 微信端默认隐藏头部标题栏
  // 手动设置显示头部：可通过 container、page组件prop：header属性 && x-header组件prop：visible属性
  // 添加 no-header 类名, 增加noHeader属性, 如果noHeader为true, 则不显示头部标题栏
  // 个别页面没有使用统一的组件，无法统一覆盖，需要修改具体页面内逻辑，列表如下(不完全统计)：
  // views\audio-player\pages\profile.vue
  // views\packages\camera\pages\_CameraFormEditor.vue
  // views\packages\emergency\pages\home\Detail.vue
  import { mapGetters, mapActions } from 'vuex';
  import { TransitionMode } from '@/enums';
  import { isIOS, isInWeApp, isInWeixin, isInAliApp } from '@/common/env';
  import { bind, unbind, emit, Events as BusEvent } from '@/bus';

  const TRANSITION_TIME = 350;
  const Events = {
    READY: 'ready', // 初始化完成就绪
    RESUME: 'resume',  // 恢复可见状态
    HIDE: 'hide',  // 隐藏未销毁
    LEAVE: 'leave', // 即将离开被销毁
  };

  const PageMode = {
    FULLSCREEN: 'fullscreen', // 全屏模式，导航栏通到顶部
    NORMAL: 'normal',  // 普通模式，带导航栏
  };

  /**
   * props.transition 动画类型
   * props.forceUpdate
   * props.keepAlive 是否保持生存状态不被销毁
   * :
   */
  export default {
    name: 'Page',
    props: {
      transition: {
        type: String,
      },
      forceUpdate: {
        type: Boolean,
        default: false
      },
      keepAlive: {
        type: Boolean,
        default: true,
      },
      mode: {
        type: String,
        default: 'normal',
      },
      header: {
        type: Boolean,
        default: false,
      }
    },
    data() {
      return {
        PageMode,
        pageName: this.$parent.$options.name, // 页面名称
        countOfAfterPages: -1,
        pageActivated: false,
        isActive: true,
        status: null,
      };
    },
    computed: {
      ...mapGetters(['transitionName', 'transitionMode', 'aliveViews']),
      theTransitionName() {
        return this.transition || this.transitionName;
      },
      noHeader() {
        return (isInWeixin || isInWeApp || isInAliApp) && !this.header;
      },
    },
    mounted() {
      // this.onViewIn();
      bind(BusEvent.PAGE_VISIBLE, () => {
        if (this.isActive) {
          this.callEvent(Events.RESUME, 100);
        }
      })
      console.log('container mounted...')
      const that = this;
      // TODO
      // iOS下，页面首次加载，有时候(70%左右)container组件中transition标签的after-enter钩子函数未调用，导致页面一直现在正在加载
      // 尚未找到原因，临时采用以下方案：首次加载增加一个超时时间检测，未加载则主动调用onViewIn
      const shouldDelayCheck = (!window.isAppReady  && isIOS) || !this.pageActivated;
      if (shouldDelayCheck) {
        setTimeout(() => {
          if (!that.status) {
            console.log('activated onViewIn...');
            that.onViewIn(0);
          }
        }, TRANSITION_TIME * 2);
      }
    },
    activated() {},
    deactivated() {
      // console.log('view:', this.pageName, 'deactivated', this);
      // this.onViewOut();
    },
    beforeDestroy() {
      console.log('container ', this.pageName, 'destory...');
      this.removeRoute(this.pageName);
    },
    methods: {
      ...mapActions(['addRoute', 'removeRoute']),
      onPageEnter() {
        const payload = {
          key: this.pageName,
          value: this.$route.path,
        }
        this.addRoute(payload);
      },
      onPageLeave() {
        this.removeRoute(this.pageName);
        this.$destroy();
      },
      onBeforeEnter() {
        console.log('before enter...')
      },
      onEnter() {
        console.log('enter...')
      },
      onAfterEnter() {
        console.log('onAfterEnter onViewIn...');
        this.onViewIn(0);
      },
      onAfterLeave() {
        this.onViewOut(0);
      },
      setKeepAlive(value) {
        this.keepAlive = value;
      },
      onViewIn(delay = TRANSITION_TIME) {
        window.isAppReady = true;
        console.log('container view in ...', this.pageName, this.aliveViews);
        if (this.pageActivated) {
          this.callEvent(Events.RESUME, delay);
        } else {
          this.callEvent(Events.READY, delay);
        }
        this.pageActivated = true;
      },
      /**
       * viewout表明页面进入不可见状态，这种状态下页面不可交互
       * 判断页面隐藏和离开的依据：onViewIn是页面入栈，onViewOut页面出栈，入栈早于出栈
       * 若当前页面位于栈尾，说明没有新的入栈，页面可以销毁
       * 若当前页面位于栈中且不在栈尾，说明有新的入栈，页面需要保持alive状态
       */
      onViewOut(delay = TRANSITION_TIME) {
        // 通过 setTimeout 将判断逻辑放入下一个event-loop中，确保 aliveViews 已达到最新状态
        setTimeout(() => {
          console.log('container view out...', this.pageName, this.aliveViews);
          const views = this.aliveViews;
          const currentPageName = this.pageName;
          const lastView = this.aliveViews.slice(-1)[0];

          if (views.indexOf(currentPageName) > -1 && lastView != currentPageName) {
            this.callEvent(Events.HIDE, delay);
          } else {
            this.callEvent(Events.LEAVE, delay);
          }
          emit(BusEvent.VIEW_TRANSITION_END);
        }, 50);
      },
      callEvent(event, delay) {
        let timeout = delay;
        // if (event == Events.READY) timeout += 300;
        const isActive = [Events.READY, Events.RESUME].some(item => item == event);
        this.isActive = isActive
        // const pageName = this.pageName;
        // console.log(`container active:${isActive}`, pageName, event);
        this.status = event;
        setTimeout(() => {
          this.$emit(event);
          if (event == Events.READY) {
            this.onPageEnter();
          } else if (event == Events.LEAVE || (event == Events.HIDE && !this.keepAlive)) {
            this.onPageLeave();
          }
        }, timeout);
      },
    },
  };
</script>
