<template>
  <div @click="launch" class="launch-wrap">
    <!-- vue版本低的话，会导致标签渲染不正常，设置的宽高无效 -->
    <wx-open-launch-app
        :id="id"
        class="launch-btn"
        :appid="appId"
        :extinfo="extinfoStr"
        @ready="wxTagReady"
        @launch="wxTagLunch"
        @error="wxTagError"
      >
      <script type="text/wxtag-template">
        <style>.btn {height: 96px;}</style>
        <div class="btn">打开app</div>
      </script>
    </wx-open-launch-app>
  </div>
</template>
<script>
  import { getWeixinVersion, compareVersion } from '@/utils'

  let idIndex = 0
  export default {
    name: 'LaunchAppButton',
    props: {
      // 微信打开交广领航APP原生界面的类型参数
      // http://jgrm.net:8018/zentao/www/index.php?m=doc&f=view&docID=76
      // id可能值
      // "set_payment_password";//跳转到修改支付密码
      // "notification_of_order";//跳转到养车订单
      // "reset_payment_password";//养车支付密码找回
      // "user_profile";//打开个人资料页面
      // "traffic_map";//跳转路况界面 435 版本添加
      // "traffic_condition";//跳转上报路况 448 版本添加
      // "traffic_search";//跳转路况搜索 448 版本添加
      // "webview";//跳转页面 451 版本添加

      // extinfo格式
      // {
      //   id: 'webview', // 通知app打开webview的id
      //   data: link // 传递给app的webview链接地址
      // }
      extinfo: {
        type: Object | String,
        default: ''
      },
    },
    watch: {
      extinfo: {
        handler(n){
          if(typeof n == 'object'){
            this.extinfoStr = JSON.stringify(n)
          }else if(typeof n == 'string'){
            this.extinfoStr = n
          }
        },
        immediate: true
      }
    },
    data() {
      idIndex++
      return {
        id: 'wxopenLanchAppId' + idIndex,
        appId: 'wxb118af4f9843ef1b',
        enable: false,
        extinfoStr: '',
      }
    },
    methods: {
      wxTagReady: function () {
        console.log('标签初始化完毕');
      },
      wxTagLunch: function (res) {
        // console.log('用户点击跳转按钮并对确认弹窗进行操作后触发', res);
        console.log(res);
      },
      wxTagError: function (e) {
        // console.log('跳转失败', e.detail);
        console.log(e.detail);
        // this.redirectToApp()
      },
      redirectToApp(){
        setTimeout(()=>{
          window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.iwxlh.pta'
        }, 400)
      },
      launch(){
        if(!this.enable){
          this.redirectToApp()
        }
      }
    },
    created(){
      // 微信版本号大于 7.0.12 开放标签才可进行 唤醒 app 跳转
      const wxVersion = getWeixinVersion()
      if(wxVersion){
        if(compareVersion(wxVersion, '7.0.12')){
          this.enable = true
        }
      }
    },
    mounted(){

    }
  }
</script>
<style lang="scss" scoped>
.launch-wrap{
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 12;
}
  .launch-btn{
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    opacity: 0;
    // background: red;
  }
</style>
