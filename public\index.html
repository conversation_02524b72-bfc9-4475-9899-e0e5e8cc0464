<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no, viewport-fit=cover"
    />
    <% if (htmlWebpackPlugin.options.version) { %>
    <meta name="version" content="<%= htmlWebpackPlugin.options.version%>" />
    <% } %>
    <link rel="shortcut icon" href="./favicon2020.ico" type="image/x-icon" />
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="format-detection" content="telphone=no" />
    <!-- <meta name="referrer" content="no-referrer" /> -->
    <meta name="referrer" content="origin-when-cross-origin" />
    <!-- <meta
      http-equiv="Content-Security-Policy"
      content="upgrade-insecure-requests"
    /> -->
    <!-- <meta name="referrer" content="same-origin"> -->
    <link rel="dns-prefetch" href="pic.jgrm.net" />
    <link rel="dns-prefetch" href="img.jgrm.net" />
    <title></title>
    <script>
      // 强制使用https
      if (
        location.hostname === 'radio.jgrm.net' &&
        location.protocol !== 'https:'
      ) {
        location.replace(location.href.replace(/^http:/, 'https:'));
      }
      if (location.search.indexOf('dpr=2') != -1) {
        document.write(
          '<meta name="viewport" content="width=device-width,initial-scale=0.5,maximum-scale=0.5,user-scalable=no, viewport-fit=cover">'
        );
      }
      if (
        location.search.indexOf('nohead') != -1 ||
        (location.hash && location.hash.indexOf('nohead') != -1)
      ) {
        document.documentElement.classList.add('no-header');
      }
      // 动态加载js
      function loadScript(url, callback) {
        callback = typeof callback === 'function' ? callback : function () {};
        var head = document.getElementsByTagName('head')[0];
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onreadystatechange = function () {
          if (this.readyState == 'loaded' || this.readyState == 'complete') {
            callback();
          }
        };
        script.onload = callback;
        head.appendChild(script);
      }
      if (/MicroMessenger/i.test(navigator.userAgent))
        document.write(
          '<script src="./static/libs/jweixin-1.6.0.js"><\/script>'
        );
      if (/miniProgram/i.test(navigator.userAgent))
        document.write(
          '<script src="/actions/static/js/libs/jglh/weapp2.js?v=2019"><\/script>'
        );
      if (/com.unionpay/i.test(navigator.userAgent))
        document.write(
          '<script src="https://open.95516.com/s/open/js/upsdk.js"><\/script>'
        );
      if (navigator.userAgent.indexOf('AliApp') > -1) {
        document.writeln(
          '<script src="https://appx/web-view.min.js"' +
            '>' +
            '<' +
            '/' +
            'script>'
        );
      }
      // 若url指定了title，设置page title
      try {
        var pageTitle = new URL(location.href).searchParams.get('title');
        if (pageTitle) {
          document.title = pageTitle;
        }
      } catch (err) {
        console.error(err);
      }
    </script>
    <% if (process.env.NODE_ENV === 'production' ) { %>
    <script>
      (function (w, d, s, q, i) {
        w[q] = w[q] || [];
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s);
        j.async = true;
        j.id = 'beacon-aplus';
        j.src = 'https://d.alicdn.com/alilog/mlog/aplus/' + i + '.js';
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'aplus_queue', '203467608');
      //集成应用的appKey
      aplus_queue.push({
        action: 'aplus.setMetaInfo',
        arguments: ['appKey', '628b3cde88ccdf4b7e739f4e'],
      });
      //sdk提供手动pv发送机制，启用手动pv(即关闭自动pv)，需设置aplus-waiting=MAN;
      //注意：由于单页面路由改变时不会刷新页面，无法自动发送pv，所以对于单页应用，强烈建议您关闭自动PV, 手动控制PV事件
      aplus_queue.push({
        action: 'aplus.setMetaInfo',
        arguments: ['aplus-waiting', 'MAN'],
      });
    </script>
    <script>
      // baidu tongji fallback
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?89013efc8e3e9c252fad92d58b28ceba';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script>
    <% } %>
    <script src="./static/libs/flexible.js"></script>
    <script>
      if (!window.Promise)
        document.write(
          '<script src="./static/libs/es6-promise.min.js"><\/script>'
        );
    </script>
    <style>
      @-webkit-keyframes slide {
        0% {
          -webkit-transform: translateX(-100px);
          transform: translateX(-100px);
        }

        50% {
          -webkit-transform: translateX(0);
          transform: translateX(0);
        }

        100% {
          -webkit-transform: translateX(100px);
          transform: translateX(100px);
        }
      }
      @keyframes slide {
        0% {
          -webkit-transform: translateX(-100px);
          transform: translateX(-100px);
        }

        50% {
          -webkit-transform: translateX(0);
          transform: translateX(0);
        }

        100% {
          -webkit-transform: translateX(100px);
          transform: translateX(100px);
        }
      }
      .dots {
        text-align: center;
        width: 40%;
        overflow: hidden;
        margin: 0 auto;
      }
      .dots > span {
        display: inline-block;
        -webkit-animation: slide 3s infinite;
        animation: slide 3s infinite;
      }
      .dots .dot1 {
        -webkit-animation-delay: 0s;
        animation-delay: 0s;
      }
      .dots .dot2 {
        -webkit-animation-delay: -200ms;
        animation-delay: -200ms;
      }
      .dots .dot3 {
        -webkit-animation-delay: -400ms;
        animation-delay: -400ms;
      }
      .dots .dot4 {
        -webkit-animation-delay: -600ms;
        animation-delay: -600ms;
      }
      .dots .dot5 {
        -webkit-animation-delay: -800ms;
        animation-delay: -800ms;
      }
    </style>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      body {
        line-height: 1.6;
        font-size: 0.5rem;
      }
      pre {
        white-space: normal;
      }
      .app {
        font-size: 15px;
      }
      /*html,body,.app,.fallback{
        height:100%;
      }*/
      .fallback {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        padding-top: 50%;
        box-sizing: border-box;
      }
      /*  .container {
        padding-bottom: constant(safe-area-inset-bottom);
      } */
    </style>
    <script>
      window.JGLH_VERSION_LATEST = 430;
    </script>
  </head>
  <body>
    <div id="preloader" class="fallback">
      <div class="dots">
        <span class="dot1">.</span>
        <span class="dot2">.</span>
        <span class="dot3">.</span>
      </div>
    </div>
    <div class="app" id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
