<template>
  <container @ready="init" @leave="onLeave">
    <x-header title="找回支付密码">
      <x-button  slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status" @reload="reload">
      <div class="weui-cells weui-cells_form">
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">手机号</label>
          </div>
          <div class="weui-cell__bd">
            <input class="weui-input" type="tel" v-model="form.phone" placeholder="请输入您绑定的手机号">
          </div>
        </div>
        <div class="weui-cell weui-cell_vcode">
          <div class="weui-cell__hd">
            <label class="weui-label">验证码</label>
          </div>
          <div class="weui-cell__bd">
            <input class="weui-input" type="tel" v-model="form.code" placeholder="请输入验证码">
          </div>
          <div class="weui-cell__ft">
            <button v-if="couldGetCode" @click="getCode" class="weui-vcode-btn">获取验证码</button>
            <button v-else class="weui-vcode-btn weui-btn_disabled">{{countDownSeconds}}s</button>
          </div>
        </div>
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">支付密码</label>
          </div>
          <div class="weui-cell__bd">
            <input class="weui-input" type="number" v-model="form.password" placeholder="请输入新的支付密码">
          </div>
        </div>
      </div>
      <div class="buttons">
        <button class="weui-btn weui-btn_primary btn-blue" @click="submit">提交修改</button>
      </div>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
  .buttons{
    margin: 15px 5px;
  }
  .weui-vcode-btn{
    min-width:85px;
  }
  .weui-btn_disabled{
    color:gray;
  }
</style>
<script>
import { mapState, mapActions } from 'vuex';
import { Header, HeaderButton, Container, ContentView } from '@/components';
import { AppStatus } from '@/enums';
import { toast, back } from '@/bus';
import CountDown from '@/common/CountDown';

const timer = new CountDown();

export default {
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      countDownSeconds: 0,
      form: {
        code: null,
        phone: null,
        password: null,
      },
    };
  },
  computed: {
    couldGetCode() {
      return this.countDownSeconds === 0;
    }
  },
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    ContentView,
  },
  methods: {
    ...mapActions(['resetPayPassword', 'getResetPayPasswordCode']),
    init() {
      this.status = AppStatus.READY;
    },
    onLeave() {
      timer.stop();
      // this.$destroy();        
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    getCode() {
      const phone = this.form.phone;
      const result = this.checkForm({ phone });
      if (result !== true) {
        toast().tip(result);
        return;
      }
      this.countDown();
      this.getResetPayPasswordCode({ phone }).catch(e => {
        timer.stop();
        this.countDownSeconds = 0;
        toast().tip(e);
      });
    },
    countDown() {
      timer.run(seconds => {
        this.countDownSeconds = seconds;
      }, 60);
    },
    checkForm(form = { phone: '', code: '', password: '' }) {
      if ('phone' in form) {
        const phone = form.phone;
        if (!/1\d{10}/.test(phone)) {
          let msg = phone ? '请输入正确的手机号码' : '请输入手机号码';
          toast().tip(msg);
          return msg;
        }
      }
      if ('code' in form) {
        const code = form.code;
        if (!/^\w{4,8}$/.test(code)) {
          let msg = code ? '请输入正确的验证码' : '请输入手机收到的验证码';
          toast().tip(msg);
          return msg;
        }
      }
      if ('password' in form) {
        const password = form.password;
        if (!/^\d{6}$/.test(password)) {
          let msg = password ? '请输入正确的支付密码' : '请输入新支付密码';
          toast().tip(msg);
          return msg;
        }
      }
      return true;
    },
    submit() {
      const data = this.form;
      const result = this.checkForm(data);
      if (result !== true) {
        toast().tip(result);
        return;
      }
      this.resetPayPassword(data).then(res => {
        toast().success('支付密码已重置');
        back();
      }, e => {
        toast().tip(e);
      });
    },
  }
};
</script>
