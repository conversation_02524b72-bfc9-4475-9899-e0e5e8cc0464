<template>
  <div class="toast" @click="hide" v-show="show">
    <div v-show="type !== 'text'" class="weui-mask_transparent"></div>
    <div :class="toastClass">
      <i class="weui-icon-success-no-circle weui-icon_toast" v-show="type !== 'text'"></i>
      <p class="weui-toast__content">
        <slot>
          <div v-html="text"></div>
        </slot>
      </p>
    </div>
  </div>
</template>
<style scoped>
  .weui-toast {
    background:rgba(0, 0, 0, 0.8);
    z-index: 5001;
  }
  .weui-toast__bottom {
    top: 70vh;
  }
</style>

<script>
  export default {
    name: 'toast',
    props: {
      counter: {
        type: Number,
      },
      time: {
        type: Number,
        default: 1500
      },
      type: {
        type: String,
        default: 'success'
      },
      position: {
        type: String,
        default: 'default'
      },
      transition: {
        type: String,
        default: 'vux-fade'
      },
      width: {
        type: String,
        default: '7.6em'
      },
      text: [String, Number]
    },
    data() {
      return {
        show: false,
      };
    },
    computed: {
      toastClass() {
        return {
          'weui-toast': true,
          'weui-toast__text': this.type === 'text',
          'weui-toast__bottom': this.position === 'bottom',
        };
      },
    },
    methods: {
      hide() {
        this.show = false;
      },
    },
    watch: {
      counter(val) {
        if (val) {
          this.show = true;
          clearTimeout(this.timeout);
          this.timeout = setTimeout(() => {
            this.show = false;
          }, this.time);
        }
      },
      show(val) {
        this.$overlay(val);
      }
    }
  };
</script>

<style lang="scss">
  .weui-toast {
    transform: translateX(-50%);
    margin-left: 0!important;
  }
  .weui-toast.weui-toast__text{
    min-height: 0;
    max-width:80%;
    width: auto;
  }
  .weui-toast__text {
    width:auto;
    max-width:80%;
    border-radius: 2px;
    top: 50%;
  }
  .weui-toast__text .weui-toast__content {
    margin: 0;
    padding: 8px 10px;
    border-radius: 0;
  }
</style>
