<template>
  <div class="order-detail-wash">
    <coupon :order="order"></coupon>

    <template v-if="order.orderStatus == OrderStatus.COMMENTED">
      <div class="order-comment flex-row center" @click="goComment">
        <span style="flex:1;">我的评分</span>
        <rater class="comment-rater" :value="order.comment.score"></rater>
      </div>
    </template>

    <!--<a v-if="order.orderStatus == OrderStatus.UN_COMMENTED" href="javascript:;" class="weui-btn weui-btn_primary" @click="goComment">评价服务</a>
-->
    <panel  title="服务门店">
      <shop-card :shop="order.shop" @click.native="goShop"></shop-card>
    </panel>

    <order-detail-items :value="order"></order-detail-items>

    <!-- <panel class="order-panel order-info" v-if="order" title="订单项目">
      <template v-if="extra.length">
        <div v-for="(item, index) in order.orderGoodsInfoList" :key="index" class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">{{item.name}}</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-right">
                <div><span class="rmb">{{item.amount}}</span></div>
              </div>
          </div>
        </div>
        <div v-for="(item, index) in extra" :key="index" class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">{{item.name}}</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-right">
                <div class="ticket-amount">-<span class="rmb">{{item.amount}}</span></div>
              </div>
          </div>
        </div>
      </template>
      <div class="weui-cell">
        <div class="weui-cell__hd">
          <label class="weui-label">应付金额</label>
        </div>
        <div class="weui-cell__bd">
            <div class="align-right">
              <b class="rmb">{{order.needPayAmount}}</b>
            </div>
        </div>
      </div>
      <div v-if="order.amount" class="weui-cell">
        <div class="weui-cell__hd">
          <label class="weui-label">实付金额</label>
        </div>
        <div class="weui-cell__bd">
            <div class="align-right">
              <b class="rmb">{{order.amount}}</b>
            </div>
        </div>
      </div>
    </panel> -->

    <panel class="order-panel order-pay-info" >
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">订单编号</label>
          </div>
          <div class="weui-cell__bd">
            <div class="align-left">{{order.id}}</div>
          </div>
        </div>
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">下单时间</label>
          </div>
          <div class="weui-cell__bd">
            <div class="align-left">{{formatDate(order.createTime)}}</div>
          </div>
        </div>
        <template  v-if="order && order.payTime">
          <div class="weui-cell">
            <div class="weui-cell__hd">
              <label class="weui-label">付款时间</label>
            </div>
            <div class="weui-cell__bd">
                <div class="align-left">{{formatDate(order.payTime)}}</div>
            </div>
          </div>
          <div class="weui-cell">
            <div class="weui-cell__hd">
              <label class="weui-label">支付方式</label>
            </div>
            <div class="weui-cell__bd">
              <div class="align-left">{{getPayment(order.payChannel)}}</div>
            </div>
          </div>
          <!-- <div class="weui-cell">
            <div class="weui-cell__hd">
              <label class="weui-label">实付金额</label>
            </div>
            <div class="weui-cell__bd">
                <div class="align-left"><span class="rmb">{{order.needPayAmount}}</span></div>
            </div>
          </div> -->
        </template>
    </panel>

    <panel class="order-panel order-refund-info" v-if="refund" title="退款信息">
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">申请时间</label>
          </div>
          <div class="weui-cell__bd">
            <div class="align-left">{{formatDate(refund.applyTime)}}</div>
          </div>
        </div>
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">退款原因</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-left">
                <p :key="index" v-for="(item, index) in refund.sysRefundReason">{{item}}</p>
                <p v-if="refund.applyReason">{{refund.applyReason}}</p>
              </div>
          </div>
        </div>
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">退款状态</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-left">
                <span class="order-status">{{refundStatusText}}</span>
                <p v-if="refund.status == OrderRefundStatus.REFUNDING" class="refund-tip">预计5个工作日内到账</p>
                <p v-if="order.refundRejectionReason" class="refund-tip">{{order.refundRejectionReason}}</p>
              </div>
          </div>
        </div>
        <div v-if="refund.refundSuccessTime" class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label">退款时间</label>
          </div>
          <div class="weui-cell__bd">
              <div class="align-left">
                {{formatDate(refund.refundSuccessTime)}}
              </div>
          </div>
        </div>
    </panel>

    <div class="btn-area">
      <a v-if="order.orderStatus == OrderStatus.UNPAID" href="javascript:;" class="weui-btn weui-btn_primary" @click="goToPay">立即支付</a>
      <a v-if="actions.couldRefund" href="javascript:;" class="weui-btn weui-btn_primary" @click="goRefund">申请退款</a>
      <a v-if="order.orderStatus == OrderStatus.UN_COMMENTED" href="javascript:;" class="weui-btn weui-btn_primary" @click="goComment">评价服务</a>
    </div>
    </div>
</template>
<style lang="scss">
  $highlight-color:#F29E5C;
  .order-detail-wash{
    .see-more{
      text-align:center;
      display:block;
      font-size:0.9em;
      >b {
        color:$highlight-color;
        margin:2px;
      }
    }

    .panel {
      padding:5px;
    }
    .order-panel {
      .panel-title {
        border-bottom:0;
      }
      .weui-cell {
        border-top:0;
        padding:5px 10px;
        align-items: flex-start;
        &:last-child {
          border-bottom:0;
        }
      }
    }
    .order-info {
      .weui-label{
        width:auto;
      }
      .ticket-amount {
        color:#F29E5C;
      }
      .weui-cell {
        padding-top:8px;
        padding-bottom:8px;
      }
    }
    .order-pay-info{
      .weui-cell{
        padding-top:3px;
        padding-bottom:3px;
      }
      .weui-label{
        width:auto;
        margin-right:5px;
        &::after{
          content:':';
          padding:0 3px;
        }
      }
    }
    .order-status {
      color:#EF6C00;
    }
    .refund-tip{
      color:gray;
      font-size:0.8em;
    }
    .order-comment {
      background: white;
      padding: 10px;
      margin: 10px 0;
      border: 1px solid #eaeaea;
      border-width: 1px 0;
      &::after {
        content: '\E605';
        font-family: iconfont;
        // float: right;
        font-size: 14px;
        margin-left: 10px;
        color: #bdbdbd;
      }
    }
  }
</style>
<script>
/**
   * order.amount: 订单支付成功后实际支付的金额
   * order.needPayAmount: 订单需要支付的金额
   */
import { dialog, toast, loading } from '@/bus';
import { formatDate, formatPrice } from '@/utils';
import { Panel, Rater, Tip } from '@/components';
import { mixinAuthRouter } from '@/mixins';
import { ShopCard, ShopCell } from '@/views/components';
import Coupon from './OrderCoupon';
import { OrderStatus, OrderRefundStatus, PaymentChannel, OrderType, Ticket } from '@/enums';
import { refundOrder, getOrderRefundDetail } from '@/api';
import OrderDetailItems from '@/views/components/OrderDetailItems.vue';

export default {
  name: 'order-detail-wash',
  props: {
    order: {
      type: Object
    },
  },
  components: {
    Rater,
    Panel,
    ShopCard,
    Coupon,
    ShopCell,
    Tip,
    OrderDetailItems,
  },
  mixins: [mixinAuthRouter],
  data() {
    return {
      OrderStatus,
      OrderRefundStatus,
      OrderType,
      refund: null,
    };
  },
  mounted() {
    this.getOrderRefund();
  },
  computed: {
    actions() {
      const order = this.order;
      return {
        couldRefund: !order.refundRejectionReason && order.orderType == OrderType.WASH && [OrderStatus.EXPIRED, OrderStatus.TO_BE_SERVE].some(item => item == order.orderStatus),
      }
    },
    orderStatusText() {
      const orderStatus = this.order.orderStatus;
      const status = OrderStatus.getEnum(orderStatus);
      if (!status) return orderStatus
      return status.getName();
    },
    refundStatusText() {
      if (this.order.refundRejectionReason) return '已驳回'
      const refundStatus = this.refund.status;
      const status = OrderStatus.getEnum(refundStatus);
      if (!status) return refundStatus
      return status.getName();
    },
    extra() {
      const extraList = this.order.discountInfoList || []
      return extraList.sort((a, b) => {
        return b.amount - a.amount;
      });
    },
    orderPrice() {
      return formatPrice(this.order.price + this.order.vipDiscountAmount);
    },
  },
  methods: {
    getOrderRefund() {
      const status = this.order.orderStatus;
      const haveRefundInfo = this.order.refundRejectionReason || [OrderStatus.REFUND_APPLIED, OrderStatus.REFUNDING, OrderStatus.REFUNDED].some(value => {
        return status == value;
      });
      if (haveRefundInfo) {
        getOrderRefundDetail(this.order.id).then(detail => {
          this.refund = detail;
        }).catch(e => {
          console.error(e);
        })
      }
    },
    // 去店铺页面，后续流程可能会跳转到订单详情，导致订单详情页面显示不正常（组件采用keep-alive的缺陷），所以打开新webview
    goShop() {
      const shopId = this.order.shop.id;
      const url = `/shop/${shopId}`;
      this.$_router_pageTo(url);
    },
    goToPay(item) {
      const { id, sysOrderId } = this.order;
      // 接口参数暂时不需要订单类型
      const paymentInfo = {
        soid: `${sysOrderId}-${id}`,
        nextPath: `/order/${id}`,
      }
      this.$_route_cashierCheckout(paymentInfo);
    },
    getPayment(value) {
      const payment = PaymentChannel.getEnum(value);
      if (payment) return payment.getName();
      return value || '未知';
    },
    goRefund() {
      const nextPage = `/order/${this.order.id}/refund/${OrderType.WASH}`;
      this.$_router_push4inputting(nextPage, { title: '申请退款' });
    },
    goComment() {
      // 被申诉的评论不能再修改
      if (this.order.comment && this.order.comment.status == -2) {
        return;
      }
      const oid = this.order.id;
      this.$_router_push4inputting(`/order/${oid}/comment?from=detail`, { title: '评价服务' });
    },
    formatDate(t, style = 'YYYY-MM-DD HH:mm:ss') {
      return formatDate(t, style);
    },
  }
};
</script>
