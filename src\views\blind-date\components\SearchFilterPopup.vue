<template>
  <div class="search-filter">
    <van-popup
      v-model="showPopup"
      position="right"
      :style="{ height: '100%', width: '80%' }"
      :close-on-click-overlay="true"
      get-container="body"
      @closed="resetForm"
    >
      <div class="filter-container">
        <div class="filter-header">
          <div class="filter-title">筛选条件</div>
          <van-icon name="cross" size="20" @click="closePopup" />
        </div>

        <div class="filter-content">
          <!-- 性别筛选 -->
          <div class="filter-item">
            <div class="filter-label">性别</div>
            <div class="filter-options">
              <div
                v-for="option in genderOptions"
                :key="option.value"
                :class="[
                  'filter-option',
                  { active: formData.gender === option.value },
                ]"
                @click="formData.gender = option.value"
              >
                {{ option.label }}
              </div>
            </div>
          </div>

          <!-- 年龄区间筛选 -->
          <div class="filter-item">
            <div class="filter-label">年龄区间</div>
            <div class="age-selector-container">
              <div class="age-selector-item" @click="showAgePickerPopup">
                {{ formData.minAge || '不限' }}
              </div>
              <div class="age-separator">-</div>
              <div class="age-selector-item" @click="showAgePickerPopup">
                {{ formData.maxAge || '不限' }}
              </div>
            </div>
          </div>

          <!-- 学历筛选 -->
          <div class="filter-item">
            <div class="filter-label">学历</div>
            <div class="filter-options">
              <div
                v-for="option in educationOptions"
                :key="option.value"
                :class="[
                  'filter-option',
                  { active: formData.education === option.value },
                ]"
                @click="formData.education = option.value"
              >
                {{ option.label }}
              </div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="filter-footer">
          <van-button type="default" block @click="resetForm">重置</van-button>
          <van-button type="primary" block @click="handleSearch"
            >搜索</van-button
          >
        </div>
      </div>
    </van-popup>
    <!-- 年龄选择器弹窗 -->
    <van-popup
      v-model="showAgePicker"
      position="bottom"
      get-container="body"
      :style="{ height: 'auto' }"
    >
      <div class="picker-toolbar">
        <div class="picker-cancel" @click="cancelAgePicker">取消</div>
        <div class="picker-title">选择年龄区间</div>
        <div class="picker-confirm" @click="confirmAgePicker">确定</div>
      </div>
      <van-picker :columns="agePickerColumns" @change="onAgePickerChange" />
    </van-popup>
  </div>
</template>

<script>
import { Popup, Icon, Field, Button, Picker, Toast } from 'vant';
import Vue from 'vue';

export default {
  name: 'SearchFilterPopup',
  components: {
    [Popup.name]: Popup,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Button.name]: Button,
    [Picker.name]: Picker,
  },
  beforeCreate() {
    // 全局注册 Toast
    Vue.use(Toast);
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    defaultQuery: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showPopup: false,
      showAgePicker: false,
      formData: {
        gender: '',
        minAge: '',
        maxAge: '',
        education: '',
      },
      // 年龄选择器临时存储值
      tempAgeValues: ['不限', '不限'],
      // 年龄选择器配置
      agePickerColumns: [
        // 最小年龄列
        {
          values: [
            '不限',
            ...Array.from({ length: 43 }, (_, i) => String(i + 18) + '岁'),
          ],
          defaultIndex: 0,
        },
        // 最大年龄列
        {
          values: [
            ...Array.from({ length: 43 }, (_, i) => String(i + 18) + '岁'),
            '不限',
          ],
          defaultIndex: 43, // 默认选择"不限"
        },
      ],
      genderOptions: [
        { label: '不限', value: '' },
        { label: '男', value: '男' },
        { label: '女', value: '女' },
      ],
      educationOptions: [
        { label: '不限', value: '' },
        { label: '高中', value: '高中' },
        { label: '中专', value: '中专' },
        { label: '大专', value: '大专' },
        { label: '本科', value: '本科' },
        { label: '硕士', value: '硕士' },
        { label: '博士', value: '博士' },
      ],
    };
  },
  watch: {
    show(val) {
      this.showPopup = val;
      if (val) {
        let defaultQuery = { ...this.defaultQuery };
        if (defaultQuery.minAge) {
          defaultQuery.minAge = String(defaultQuery.minAge) + '岁';
        }
        if (defaultQuery.maxAge) {
          defaultQuery.maxAge = String(defaultQuery.maxAge) + '岁';
        }
        this.formData = Object.assign(this.formData, defaultQuery);
      }
    },
    showPopup(val) {
      this.$emit('update:show', val);
    },
  },
  methods: {
    closePopup() {
      this.showPopup = false;
    },
    resetForm() {
      this.formData = {
        gender: '',
        minAge: '',
        maxAge: '',
        education: '',
      };
      // 重置临时年龄值
      this.tempAgeValues = ['不限', '不限'];
    },
    handleSearch() {
      // 构造查询参数
      const query = {
        gender: this.formData.gender,
        education: this.formData.education,
        minAge: null,
        maxAge: null,
      };
      if (this.formData.minAge) {
        query.minAge =
          this.formData.minAge === '不限'
            ? null
            : parseInt(this.formData.minAge.replace('岁', '')) || null;
      }
      if (this.formData.maxAge) {
        query.maxAge =
          this.formData.maxAge === '不限'
            ? null
            : parseInt(this.formData.maxAge.replace('岁', '')) || null;
      }

      // 发送筛选事件
      this.$emit('filter', query);
      this.closePopup();
    },
    showAgePickerPopup() {
      // 打开选择器前，设置临时值
      // 如果已有值则使用，否则使用默认值
      this.tempAgeValues = [
        this.formData.minAge || '不限',
        this.formData.maxAge || '不限',
      ];
      this.showAgePicker = true;
    },
    cancelAgePicker() {
      // 取消不更新值
      this.showAgePicker = false;
    },
    confirmAgePicker() {
      // 验证最小年龄不能大于最大年龄
      const minAgeStr = this.tempAgeValues[0];
      const maxAgeStr = this.tempAgeValues[1];

      const minAge =
        minAgeStr === '不限' ? 18 : parseInt(minAgeStr.replace('岁', ''));
      const maxAge =
        maxAgeStr === '不限' ? 100 : parseInt(maxAgeStr.replace('岁', ''));

      if (minAge > maxAge) {
        this.$toast('最小年龄不能大于最大年龄');
        return;
      }

      // 确认后更新表单数据
      this.formData.minAge = minAgeStr;
      this.formData.maxAge = maxAgeStr;
      this.showAgePicker = false;
    },
    onAgePickerChange(picker, values, index) {
      // 记录当前选择的列值
      const minAgeValue = picker.getColumnValue(0);
      const maxAgeValue = picker.getColumnValue(1);

      this.tempAgeValues = [minAgeValue, maxAgeValue];

      // 实时验证最小年龄不能大于最大年龄
      const minAge =
        minAgeValue === '不限' ? 18 : parseInt(minAgeValue.replace('岁', ''));
      const maxAge =
        maxAgeValue === '不限' ? 999 : parseInt(maxAgeValue.replace('岁', ''));

      // 如果最小年龄大于最大年龄，自动调整最大年龄
      if (minAge > maxAge && maxAgeValue !== '不限') {
        picker.setColumnValue(1, minAgeValue);
        this.tempAgeValues[1] = minAgeValue;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f2f2f2;
}

.filter-title {
  font-size: 16px;
  font-weight: bold;
}

.filter-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.filter-item {
  margin-bottom: 20px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-option {
  padding: 5px 15px;
  background-color: #f5f5f5;
  border-radius: 15px;
  font-size: 14px;
  color: #333;
  text-align: center;

  &.active {
    background-color: #ff5a5f;
    color: #fff;
  }
}

.filter-range {
  display: flex;
  align-items: center;

  .van-field {
    flex: 1;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .range-separator {
    margin: 0 10px;
    color: #999;
  }
}

.filter-footer {
  padding: 15px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  border-top: 1px solid #f2f2f2;

  .van-button {
    height: 40px;
    line-height: 40px;
  }

  .van-button--primary {
    background-color: #ff5a5f;
    border-color: #ff5a5f;
  }
}

.age-selector-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  width: 70%;
}

.age-selector-item {
  padding: 5px 15px;
  background-color: #f5f5f5;
  border-radius: 15px;
  font-size: 14px;
  color: #333;
  text-align: center;
  flex: 1;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.age-separator {
  margin: 0 10px;
  color: #999;
}

.picker-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f2f2f2;

  .picker-cancel,
  .picker-confirm {
    font-size: 14px;
    color: #333;
    cursor: pointer;
  }

  .picker-title {
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
