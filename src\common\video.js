import { setItem, getItem } from '@/store/storage';

/**
 * 异步加载一个图片，返回图片地址和宽高信息
 * @param {*} src 
 */
export function loadImage(src) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    img.onload = function onload() {
      resolve({
        src,
        width: img.width,
        height: img.height,
      });
    };
    img.onerror = function onerror(e) {
      reject(src);
    };
    if (img.width > 0) {
      resolve({
        src,
        width: img.width,
        height: img.height,
      });
    }
  });
}

/**
 * 根据资源获取视频url，支持七牛云视频id，或dataurl
 * @param {string} value 资源ID或路径
 * @param {string} type 可选，七牛云图片参数
 * @param {string} protocol 协议
 */
export function getVideoURL(value, protocol = 'https:') {
  // file, data, bolb, http, https ...
  if (/^\w+:/.test(value)) return value;

  // `/` 开头认为是绝对路径
  if (/^\/\//.test(value)) return value;

  // 其他值都认为是
  return `${protocol}//vod.jgrm.net/${value}`;
}

const UPLOAD_TOKEN = 'iupvideo_token';

export function getVideoUploadToken() {
  try {
    const data = JSON.parse(getItem(UPLOAD_TOKEN));
    if (data.time > Date.now()) return data.token;
    else {
      return null;
    }
  } catch (e) {
    return null;
  }
}

export function setVideoUploadToken(token) {
  // const UPLOAD_TOKEN = 'iup_token';
  const DURATION = 1000 * 60 * 60;
  const data = JSON.stringify({
    time: Date.now() + DURATION,
    token,
  });
  setItem(UPLOAD_TOKEN, data);
}

/**
 * 获取七牛云视频封面
 * @param {string} id 资源id
 */
export function getQiniuVideoCover(id, vhost) {
  const host = vhost || 'https://vod.jgrm.net';
  return `${host}/${id}?vframe/jpg/offset/1`;
}
