<template>
  <div @click="launch" class="launch-wrap">
    <!-- vue版本低的话，会导致标签渲染不正常，设置的宽高无效 -->
    <wx-open-launch-weapp
        :id="id"
        class="launch-btn"
        :appid="appId"
        :username="username"
        :path="path"
        :extra-data="extinfoStr"
        @ready="wxTagReady"
        @launch="wxTagLunch"
        @error="wxTagError"
      >
      <script type="text/wxtag-template">
        <style>.btn {height: 960px;}</style>
        <div class="btn">打开app</div>
      </script>
    </wx-open-launch-weapp>
  </div>
</template>
<script>
  import { getWeixinVersion, compareVersion } from '@/utils'

  let idIndex = 0
  export default {
    name: 'LaunchWeappButton',
    props: {
      // 微信打开小程序的类型参数
      // extinfo格式
      // {
      //   id: 'webview', // 通知app打开webview的id
      //   data: link // 传递给app的webview链接地址
      // }
      // 所需跳转的小程序appid，即小程序对应的以wx开头的id
      appId: {
        type: String,
        default: ''
      },
      // 所需跳转的小程序原始id，即小程序对应的以gh_开头的id（跳转时，有appid会优先使用appid，没有appid才会使用username）
      username: {
        type: String,
        default: ''
      },
      // 所需跳转的小程序内页面路径及参数
      path: {
        type: String,
        default: 'pages/index/index'
      },
      extinfo: {
        type: Object | String,
        default: ''
      },
    },
    watch: {
      extinfo: {
        handler(n){
          if(typeof n == 'object'){
            this.extinfoStr = JSON.stringify(n)
          }else if(typeof n == 'string'){
            this.extinfoStr = n
          }
        },
        immediate: true
      }
    },
    data() {
      idIndex++
      return {
        id: 'wxopenLanchAppId' + idIndex,
        enable: false,
        extinfoStr: '',
      }
    },
    methods: {
      wxTagReady: function () {
        console.log('标签初始化完毕');
      },
      wxTagLunch: function () {
        console.log('用户点击跳转按钮并对确认弹窗进行操作后触发');
      },
      wxTagError: function (e) {
        console.log('跳转失败', e.detail);
      },
      launch(){
        if(!this.enable){
          console.log('微信版本太低，无法使用开放标签', e.detail);
        }
      }
    },
    created(){
      // 微信版本号大于 7.0.12 开放标签才可进行 唤醒 app 跳转
      const wxVersion = getWeixinVersion()
      if(wxVersion){
        if(compareVersion(wxVersion, '7.0.12')){
          this.enable = true
        }
      }
    },
    mounted(){

    }
  }
</script>
<style lang="scss" scoped>
.launch-wrap{
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 12;
  overflow: hidden;
}
  .launch-btn{
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    opacity: 0;
    // background: red;
  }
</style>
