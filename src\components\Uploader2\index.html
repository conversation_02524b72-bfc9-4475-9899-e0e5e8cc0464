<div class="file-container">
  <div v-for="item in list"
       :class="getFileClass(item)"
       :title="item.msg"
       @click="handleClick(item)"
       :percent="item.percent">
    <img :src="showImage(item.file)"/>
    <a href="javascript:;" @click="removeFile(item)" class="file-remove">&times;</a>
    <input type="hidden" :name="settings.name" :value="item.file"/>
  </div>

  <div v-show="canAddFile" class="file file-select">
    <input v-if="canMultipleSelect" type="file" class="text" multiple="multiple"/>
    <input v-else type="file" class="text"/>
  </div>
</div>
