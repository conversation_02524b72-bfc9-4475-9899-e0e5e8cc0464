<template>
  <page class="share-home container-fullscreen" @resume="onResume">
    <loading v-if="status == AppStatus.LOADING"></loading>
    <div v-else-if="status == AppStatus.READY" class="audio-wrap">
      <div class="album" :class="{ expland: showDetail}">
        <p class="album-title">{{ radioInfo.title }}</p>
        <div class="album-img">
          <img
            class="image"
            :src="currentSong.cover"
            alt=""
          />
        </div>
        <div class="radio-info">
          <p v-html="radioInfo.introduction"></p>
          <div class="expland-btn" @click="expland">
            <span>{{ showDetail ? "收起" : "展开" }}</span>
            <svg class="icon-svg">
              <use xlink:href="#icon-xf-shouqi-wangshang"></use>
            </svg>
          </div>
        </div>
        <div class="launch-app">
          <!-- <wx-launch-app v-if="isInWeixin" :extinfo="extinfo"></wx-launch-app> -->
          <van-button type="primary" @click="launchApp" round>打开APP 完整收听</van-button>
        </div>
      </div>
      <div class="play-list" v-show="!showDetail">
        <h2 class="head">
          <!-- <van-button type="primary" size="small" round>
            <template slot="icon">
              <svg class="icon-svg">
                <use xlink:href="#icon-xf-bofang"></use>
              </svg>
            </template>
            全部播放
          </van-button>
          <div class="flex"></div> -->
          <div class="flex">全部节目</div>
          <div class="update-status">
            已更新{{ radioInfo.programItemCount }}集
          </div>
        </h2>
        <ul class="list-wrap">
          <li v-for="(item, index) in playList" :key="item.id" @click="toPlayer(item)" class="song-item">
            <div class="audio-no">
              <!-- <div v-if="currentSong.id == item.id" class="equilizer">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
              </div> -->
              <span>{{index+1}}</span>
            </div>
            <div class="audio-info">
              <p>{{ item.name }}</p>
              <div class="audio-duration">
                <svg class="icon-svg">
                  <use xlink:href="#icon-xf-shijian"></use>
                </svg>
                <span>{{formatTime(item.duration)}}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div v-show="!showDetail" class="tips" @click="launchApp">
        打开交广领航 倾听更多
        <svg class="icon-svg">
          <use xlink:href="#icon-xf-shouqi-wangshang"></use>
        </svg>
      </div>
    </div>

    <page-loader
      v-else
      :status="status"
      :error="error"
      @reload="init"
    >
    </page-loader>
  </page>
</template>
<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { createSong, durationTrans } from '@/common/audio';
import { isInWeixin } from '@/common/env';
import { getImageURL } from '@/common/image';
import { toast } from '@/bus'
import { getAppURL, getHTMLTextContent } from '@/utils';
import { AppStatus } from '@/enums'
import { Button } from 'vant';
import { getShareAudioProfile } from '../api';
import PlayList from '../components/PlayList.vue';
import WxPlayer from '../components/WxPlayer.vue';
import WxLaunchApp from '@/components/WxLaunchApp.vue';
export default {
  name: 'ShareAlbum',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    PlayList,
    WxPlayer,
    WxLaunchApp
  },
  data() {
    const path = '/audio/profile';
    const link = getAppURL(path, {
      search: '?lh_wvc=JTdCJTIydGl0bGVCYXIlMjIlM0FmYWxzZSUyQyUyMnNiYXJDb2xvciUyMiUzQSUyMndoaXRlJTIyJTdE'
    });
    return {
      isInWeixin,
      AppStatus,
      status: AppStatus.LOADING,
      title: '欢迎来到music',
      error: '',
      $_share_info: {
        path: '/audio/share/album',
        title: '欢迎来到小疯的简史生活',
        desc: '小疯的简史生活',
        imgUrl: 'https://img.jgrm.net/FtFFR92Qst6RpzsYGUsmknpZ-Z2g', // 交广领航普通LOGO
      },
      playList: [],
      showDetail: false,
      currentSong: {},
      radioInfo: {},
      extinfo: {
        id: 'webview', // 通知app打开webview的id
        data: link // 传递给app的webview链接地址
      }
    };
  },
  computed: {
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.init()
    })
  },
  created() {
    this.playList = this._createSong([
      {
        id: 1,
        name: '杨和苏KeyNG - 从那天起',
        singer: '主播1',
        cover: '',
        duration: 230.736,
        audioId: 'lieYCzZGPVldsNaGtw02QsFl9cPG'
      },
      {
        id: 2,
        name: '徐秉龙,曹雅雯 - 若是明仔载',
        singer: '主播2',
        cover: '',
        duration: 149.159184,
        audioId: 'loHaIm4P5K83Y_Hl9hX1lgJdYSTE'
      },
      {
        id: 3,
        name: '木小雅 - 喝了一口星光酒（我只',
        singer: '主播3',
        cover: '',
        duration: 169.608,
        audioId: 'ljByONcgC2Yn__Z84JKNC1TVg7yr'
      },
      {
        id: 4,
        name: '后弦 - 破防',
        singer: '主播4',
        cover: '',
        duration: 239.490612,
        audioId: 'lvtsOnM7-TO92pGjmQ27WqTMWq1d'
      },
      {
        id: 5,
        name: 'iFM,张天峰 - 偏要',
        singer: '主播5',
        cover: '',
        duration: 224.424,
        audioId: 'liizUPt05P9qWo4n-EvYc341Hl_G'
      },
      {
        id: 6,
        name: 'Bo Peep - 一份来自热爱',
        singer: '主播6',
        cover: '',
        duration: 192.912,
        audioId: 'loneHxbMdcLvGl86WwrGpF3QrV8J'
      },
      {
        id: 7,
        name: '艾志恒Asen,KIV,mac',
        singer: '主播7',
        cover: '',
        duration: 262.44,
        audioId: 'lojaKAOj_hOh4xH0Sd6pWUg5pdzI'
      },
      {
        id: 8,
        name: '痴笑,飞行工作室 - 唱童谣',
        singer: '主播8',
        cover: '',
        duration: 209.424,
        audioId: 'lu0kYop8IhseFkWLYsa7YSkpkn35'
      },
      {
        id: 9,
        name: '凡清 - 我的光',
        singer: '主播9',
        cover: '',
        duration: 257.904,
        audioId: 'ltX7TTIXg63EMgAMQtNcEsTzAYol'
      }
    ])
    this.currentSong = this.playList[2]
  },
  methods: {
    init() {
      getShareAudioProfile({ id: 1 })
        .then((profile) => {
          this.status = AppStatus.READY
          this.radioInfo = profile.liveRadioProgram
          this.playList = this._createSong(profile.currentProgramItemList)
          this.currentSong = this.playList[0]
          this.share()
        })
        .catch((err) => {
          this.status = AppStatus.ERROR
          toast().tip(err)
          // return Promise.reject(err)
        });
    },
    onResume() {

    },
    launchApp() {
      window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.iwxlh.pta'
    },
    expland() {
      this.showDetail = !this.showDetail
      if (!this.showDetail) {
        document.querySelector('.share-home').scrollTop = 0
      }
    },
    _createSong(list) {
      if (list instanceof Array) {
        return list.map(item => {
          return createSong({
            id: item.id,
            index: item.seqNo,
            cover: this.radioInfo.image,
            duration: item.duration,
            // singer: item.singer,
            name: item.title,
            audioId: item.radioResource
          });
        });
      }
    },
    formatTime(time) {
      if (!time) {
        return '00:00'
      } else {
        return durationTrans(time)
      }
    },
    toPlayer(item) {
      // this.currentSong = item
      this.$router.push({
        path: '/audio/share/player',
        query: { id: item.id },
      })
    },
    share(action = 'update') {
      // 设置邀请链接分享信息
      const path = '/audio/share/album';
      const link = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share' : '?jglh'
      });
      const desc = getHTMLTextContent(this.radioInfo.introduction, 100)
      const shareInfo = {
        link: link,
        title: `我正在收听《${this.radioInfo.title}》~`,
        desc: desc,
        imgUrl: getImageURL(this.radioInfo.image)
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    }
  }
};
</script>
<style lang="scss">
// 解决动态html样式无效，不能使用scoped
.album{
  .radio-info {
    p {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #333333 !important;
      line-height: 20px !important;
      div{
        display: none;
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #333333 !important;
        line-height: 21px !important;
      }
    }
  }
  &.expland {
    .radio-info {
      p {
          div{
            display: block;
          }
        }
      }
    }
}
</style>
<style lang="scss" scoped>
@import "~../assets/style/player-list.scss";
.share-home{
  background: #F3F3F3;
  overflow-y: scroll;
}
.album{
  padding: 15px 15px 20px;
  background: #fff;
  text-align: center;
  p.album-title{
    font-size: 18px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20px;
    text-align: center;
  }
  .album-img{
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 auto 30px;
    text-align: center;
    .image{
      display: block;
      width: 100%;
      height: 100%;

    }
  }
  .launch-app{
    width: 250px;
    height: 44px;
    margin-top: 18px;
    display: inline-block;
    position: relative;
  }
  .van-button{
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    width: 250px;
    height: 44px;
    background: $lh-2022-primary-color;
    border-color: $lh-2022-primary-color;
    font-size: 18px;
    line-height: 1;
  }
}

.album-info{
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px 15px 10px;
  background: #fff;
  box-sizing: border-box;
  .avatar{
    display: block;
    width: 50px;
    height: 50px;
    border-radius: 10px;
    margin-right: 10px;
  }
  .text{
    display: inline-flex;
    flex-direction: column;
    flex: 1;
    line-height: 1;
    overflow: hidden;
    p{
      width: 100%;
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 5px;
    }
    span{
      font-size: 13px;
      font-weight: 500;
      color: #333333;
    }
  }
  .view-album-btn{
    background: #FFFFFF;
    border: 1px solid #FD4925;
    border-radius: 14px;
    padding: 8px 10px;
    font-size: 14px;
    font-weight: bold;
    color: #FD4925;
    line-height: 1;
  }
}
.radio-info{
  display: flex;
  align-items: center;
  p{
    flex: 1;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
    text-align: justify;
  }
  &>p{
    height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }
  .expland-btn{
    margin-left: 5px;
    font-size: 14px;
    font-weight: 500;
    color: #FD4925;
    display: inline-flex;
    align-items: center;
    .icon-svg{
      transform: rotate(180deg);
    }
  }

}
.album.expland{
  height: 100vh;
  box-sizing: border-box;
  .radio-info{
    flex-direction: column;
    padding-bottom: 110px;
    &>p{
      height: auto !important;
    }
    p{
      -webkit-line-clamp: 1000;
    }
    .expland-btn{
      margin-top: 20px;
    }
    .icon-svg{
      transform: rotate(0deg);
    }
  }
  .launch-app{
    position: fixed;
    left: 50%;
    bottom: 54px;
    transform: translateX(-50%);
  }
}
.play-list{
  margin-top: 10px;
  height: auto;
  min-height: auto;
  display: block;
  background: #fff;
}
.tips{
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #FD4925;
  padding: 15px 0 30px;
  background: #fff;
  .icon-svg{
    transform: rotate(90deg);
  }
}
</style>
