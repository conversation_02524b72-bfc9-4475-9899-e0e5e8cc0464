<template>
  <container @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="洗车服务">
       <x-button  slot="left" type="back"></x-button>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload" @scroll="contentScrollHandle">
      <template v-if="status == AppStatus.READY">
        <tip v-if="carWashData.unusedCount" type="warn" @click="go('/orders/unserv')">
          <div class="warn-content">您有 <b>{{carWashData.unusedCount}}</b>个订单待服务, 点击查看</div>
        </tip>
        <div class="weui-cells__title flex-row goods goods-wash">
          <div class="goods-icon"></div>
          <div class="flex-item">
            <h3 class="goods-name">车辆清洗通用券</h3>
            <p class="goods-desc">全市门店通用，操作规范，平台担保</p>
          </div>
        </div>
        <div class="weui-cells lhui-cells">
          <div class="weui-cell weui-cell_select weui-cell_select-after">
            <div class="weui-cell__hd">
              <label class="weui-label">车辆类型</label>
            </div>
            <div class="weui-cell__bd">
              <select class="weui-select" name="carType" v-model="carType">
                <option v-for="(item, index) in carWashData.carTypes" :key="index" :value="item.value">{{item.name}}</option>
              </select>
            </div>
          </div>
          <div class="weui-cell weui-cell_select weui-cell_select-after">
            <div class="weui-cell__hd">
              <label class="weui-label">服务项目</label>
            </div>
            <div class="weui-cell__bd">
              <select  class="weui-select" name="serviceType" v-model="serviceType">
                <option v-for="(item, index) in carWashData.serviceTypes" :key="index" :value="item.value">{{item.name}}</option>
              </select>
            </div>
          </div>
        </div>

        <template v-if="aroundShops">
          <div class="weui-cells__title">附近门店</div>
          <div class="weui-cells">
            <shop-cell v-for="(item, index) in aroundShops" :key="index" :shop="item" @click="go('/shop/'+item.id)">
            </shop-cell>
            <div class="weui-cell see-more" @click="go('/shops/map')">查看其它门店</div>
          </div>
        </template>

        <div class="weui-cells">
          <div class="weui-cell car-service">
            <tab-panel :list="carWashData.services"></tab-panel>
          </div>
        </div>

        <div class="cmi" @click="showCMIButton" :class="{'cmi-btn':true, 'cmi-btn-hide':!displayCMIButton}"></div>
      </template>
      <div slot="foot" v-if="status == AppStatus.READY" class="fixed-bottom flex-row">
        <div class="bottom-left">
          <div v-if="packagePrice" class="price">
            <b class="price-amount" v-text="packagePrice.price"></b>
            <span class="price-unit">元/次</span>
            <span class="price-tip"  @click="goCharge"><i class="charge">充值</i>会员更划算</span>
          </div>
          <div v-else>
            请选择车辆类型和服务类型
          </div>
        </div>
        <div class="bottom-right weui-btn weui-btn_primary" @click="buy()">立即购买</div>
      </div>
    </content-view>
  </container>
</template>

<style lang="scss" scoped>
  @import '~styles/variable/global.scss';
  $highlight-color:#F29E5C;
  $bottom-height: 45px;
  .weui-select{
    direction:rtl;
    option{
      direction:ltr;
    }
  }

  .weui-cell.car-service {
    padding-top: 2px;
  }
  .fixed-bottom{
    height: $bottom-height;
  }
  .carwash-title {
    color: #EE6E00;
    &::before{
      content:'';
      background:url(~@/assets/images/icon-car_wash.png) center center no-repeat transparent;
      background-size:cover;
      display: inline-block;
      width: 22px;
      height: 22px;
      margin-right: 3px;
    }
  }

  .lhui-cells .weui-select{
    color: $lh-primary-color;
  }

  .goods{
    .goods-icon{
      width:50px;
      background: center center no-repeat;
      background-size:95%;
      margin-right:5px;
    }
    .goods-name{
      font-size:18px;
      color:#464646;
    }
    .goods-desc{
      font-size:14px;
      color:#9e9e9e;
    }
  }
  .goods.goods-wash{
    .goods-icon{
      background-image:url(~@/assets/images/icon-wash.png);
    }
  }

  .see-more{
    text-align:center;
    display:block;
    font-size:0.9em;
    >b{
      color:$highlight-color;
      margin:2px;
    }
  }

  .bottom-left{
    flex:1;
  }
  .bottom-right {
    width: 100px;
    line-height: 45px;
    text-align: center;
    // background: rgb(78, 133, 251);
    color: white;
    // &:active{
    //   background-color: rgba(78, 133, 251, 0.8);
    // }
  }

  .price{
    margin: 0 0 0 10px;
    .price-amount{
      font-size: 26px;
      color: #FE8E33;
    }
    .price-unit{

    }
    .price-tip{
      color:#4F85FA;
      font-size: .9em;
    }
    .charge{
      font-style:normal;
      text-decoration: underline;
    }
  }
  .cmi-btn {
    position:fixed;
    right:0;
    bottom:60px;
    background:url(~@/assets/images/cmi.png) center center no-repeat transparent;
    background-size:contain;
    width:80px;
    height:80px;
    transition: transform .3s;
    display:none;
  }
  .cmi-btn-hide{
    opacity:.5;
    transform: translate3d(50px, 0, 0);
  }

  .warn-content{
    >b{
      margin:2px;
      color:red;
    }
  }
</style>
<script>
import { mapState, mapActions } from 'vuex';
import { Header, HeaderButton, Container, ContentView, TabPanel, Tip } from '@/components';
import { ShopCell } from './components';
import { AppStatus } from '@/common/enums';
import { dialog } from '@/bus';
import { formatDistance } from '@/utils';

export default {
  data() {
    return {
      displayCMIButton: true,
      serviceType: 'PX',
      carType: 5,
      AppStatus,
      status: AppStatus.LOADING
    };
  },
  computed: {
    ...mapState({
      aroundShops(state) {
        const shops = state.views.around_shops;
        if (shops && shops.length) {
          return shops.slice(0, 1);
        }
        return null;
      },
      carWashData(state) {
        const carWashData = state.views.car_wash;
        const fixedStandard = this.fixRichHtmlImageSize(carWashData.standard);
        const fixedFlow = this.fixRichHtmlImageSize(carWashData.flow);
        const services = [
          {
            name: '服务标准',
            value: fixedStandard,
          },
          {
            name: '服务流程',
            value: fixedFlow,
          },
        ];
        return {
          serviceTypes: carWashData.serviceTypes,
          carTypes: carWashData.carTypes,
          packagesPrice: carWashData.packagesPrice,
          services,
          account: carWashData.account,
          unusedCount: carWashData.unusedCount,
        };
      }
    }),
    goods() {
      return `${this.serviceType}_${this.carType}`;
    },
    packagePrice() {
      if (this.carWashData.packagesPrice) {
        const packagePrice = this.carWashData.packagesPrice[this.goods];
        const account = this.carWashData.account;
        if (account.vipLevel === 0) {
          packagePrice.price = packagePrice.normal;
        } else {
          packagePrice.price = packagePrice.vip;
        }
        return packagePrice;
      }
      return null;
    }
  },
  components: {
    'x-header': Header,
    'x-button': HeaderButton,
    Container,
    ContentView,
    TabPanel,
    Tip,
    'shop-cell': ShopCell,
  },
  methods: {
    ...mapActions(['getCarWashViewData', 'submitCarWashOrder']),
    go(url) {
      this.$router.push(url);
    },
    fixRichHtmlImageSize(str) {
      const pattern = /data-size="(\d+):(\d+)"/;
      const containerWidth = window.innerWidth - 40;
      if (str) {
        return str.replace(pattern, (term, w, h, all) => {
          const width = Number(w);
          const height = Number(h);
          if (width && height) {
            if (width < containerWidth) {
              return `width=${width} height=${height}`;
            }
            return `width=${containerWidth} height=${(containerWidth * height) / width}`;
          }
          return term;
        });
      }
      return str;
    },
    init() {
      return this.getCarWashViewData().then(res => {
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
      });
    },
    onLeave() {
      this.status = AppStatus.LOADING;
      this.$destroy();
    },
    onResume() {
      this.init();
    },
    formatDistance(...args) {
      return formatDistance(...args);
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    goCharge() {
      this.go('/account/charge');
      this.$ua && this.$ua.trackEvent('Click', 'Charge', this.$route.path);
    },
    buy() {
      const goods = this.goods;
      this.submitCarWashOrder(goods).then(res => {
        this.$ua && this.$ua.trackEvent('Click', 'BuyWashTickets', goods);
        const url = `/order/pay/${res.oid}`;
        this.$router.push(url);
      }, err => {
        err && dialog().alert(err, {
          title: '错误'
        });
      });
    },
    contentScrollHandle(e) {
      this.displayCMIButton = false;
    },
    showCMIButton() {
      if (this.displayCMIButton) {
        this.go('/cmi');
        return;
      }
      this.displayCMIButton = true;
    },
  }
};
</script>
