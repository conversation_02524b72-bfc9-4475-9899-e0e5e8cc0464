<template>
  <div class="swiper-container">
    <div class="swiper-wrapper">
      <div class="swiper-slide" v-for="(item, index) in slides" :key="index">
        <slot name="item" :item="item" :index="index"> </slot>
      </div>
      <slot name="last"></slot>
    </div>
    <!-- Add Pagination -->
    <div v-if="pagination" class="swiper-pagination"></div>
    <slot></slot>
  </div>
</template>
<style src="@/lib/swiper/4.5/css/swiper.min.css"></style>
<style lang="scss" scoped>
.swiper-container {
  width: 100%;
  height: 200px;
}
.swiper-pagination-bullet {
  width: 10px;
  height: 3px;
  border-radius: 0;
}
.swiper-pagination-bullet-active {
  background: white;
}
</style>
<script>
/**
 * 1: 同一个项目，不能同时安装两个版本的swiper，为兼容使用了swiper3.x的老代码，swiper4.x通过文件形式引入
 */
// import Swiper from 'swiper';
import Swiper from '@/lib/swiper/4.5/js/swiper.min';

const ValueMode = {
  INDEX: 'index',
  VALUE: 'value',
};

export default {
  name: 'Slider',
  props: {
    slides: Array,
    valueMode: {
      type: String,
      default() {
        return ValueMode.VALUE;
      },
    },
    pagination: {
      type: Boolean,
      default: true,
    },
    value: [Number, Object, String],
    initialSlide: [Number, String],
    allowEmpty: Boolean,
    options: {
      type: Object,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.init();
    });
  },
  destroyed() {
    this.swiper && this.swiper.destroy(true, false);
  },
  activated() {
    if (this.autoplayed) {
      this.setAutoplay(true);
    }
  },
  deactivated() {
    this.autoplayed = this.swiper.autoplaying;
  },
  data() {
    return {
      autoplayed: false,
    };
  },
  watch: {
    slides(val, oldVal) {
      console.log('update slide size ...');
      this.refresh();
    },
  },
  components: {},
  methods: {
    init() {
      const $el = this.$el;
      const that = this;
      const settings = Object.assign(
        {
          pagination: '.swiper-pagination',
          slidesPerView: 'auto',
          centeredSlides: true,
          clickable: true,
          spaceBetween: 20,
          autoplay: true, //等同于以下设置
          initialSlide: this.initialSlide || 0,
          /*autoplay: {
            delay: 3000,
            stopOnLastSlide: false,
            disableOnInteraction: true,
          },*/
          effect: 'slide',
          // centeredSlides: true,
          on: {
            slideChange: function () {
              that.setActiveSlide(this.activeIndex);
              // console.log('slideChange: ', this.activeIndex);
            },
          },
        },
        this.options
      );
      setTimeout(() => {
        const swiper = this.swiper;
        if (swiper) swiper.destroy(true, false);
        this.swiper = new Swiper($el, settings);
      }, 1);
    },
    refresh() {
      this.swiper.update();
    },
    next() {
      this.swiper.slideNext();
    },
    prev() {
      this.swiper.slidePrev();
    },
    slideTo(index) {
      this.swiper.slideTo(index);
    },
    setAutoplay(flag) {
      const autoplay = this.swiper.autoplay;
      autoplay.stop();
      if (flag) {
        autoplay.start();
      }
    },
    setActiveSlide(index) {
      // if (index < 0) index = 0;
      if (index + 1 > this.slides.length) {
        if (!this.allowEmpty) {
          return;
        }
      }
      let emitData = this.slides[index];
      if (this.valueMode == ValueMode.INDEX) {
        emitData = index > 0 ? index : 0;
      }
      this.$emit('input', emitData);
      this.$emit('change', emitData);
    },
  },
};
</script>
