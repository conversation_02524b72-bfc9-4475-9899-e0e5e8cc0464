<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';
  .layout-bottom {
    display: flex;
    background: white;
    border-top: 1px solid rgb(241, 241, 241);
    box-shadow: 0 -1px 10px 1px rgba(232, 232, 232, 0.4);
    height: 45px;
    .layout-body {
      flex: 1;
      padding-left: 10px;
      display: flex;
      align-items: center;
    }
    .layout-right {
      display: flex;
      align-items: center;
      background: #4e85fb;
      .weui-btn {
        height: 100%;
        flex: 1;
        border-radius: 0;
        display: inline-flex;
        align-items: center;
      }
    }
  }
  $vip-color: #FE8400;
  .vip-tag {
    background: $vip-color;
    color: white;
    border-radius: 2px;
    font-size: 12px;
    padding: 1px 3px;
    margin-left: 5px;
    line-height: 1.3;
    vertical-align: 2px;
  }
  .rmb {
    font-weight: 700;
    &::before {
      content: '¥';
      font-size: 0.7em;
    }
    &.price-normal {
      font-size: 24px;
    }
    &.price-vip {
      font-size: 18px;
      color: $vip-color;
      margin-left: 5px;
    }
  }
  .layout-bottom-vip {
    .price-normal {
      position: relative;
      &::after {
        content: '';
        height: 0;
        border-top: 1px solid black;
        position: absolute;
        top: 50%;
        width: 100%;
        left: 0;
      }
    }
  }
</style>
<template>
  <div class="layout-bottom" :class="{ 'layout-bottom-vip': vip }">
    <div class="layout-body">
      <span class="rmb price-normal">{{priceNormal}}</span>
      <span class="rmb price-vip">{{priceVip}}</span>
      <span class="vip-tag">VIP专享价</span>
    </div>
    <div class="layout-right">
      <a href="javascript:;" class="weui-btn weui-btn_primary" @click="$emit('go')">去支付</a>
    </div>
  </div>
</template>
<script>
import { formatMoney } from '@/utils';

export default {
  name: 'LayoutBottom',
  props: {
    vip: {
      type: Boolean,
      default: false,
    },
    price: {
      type: Number,
      default() {
        return 0;
      }
    },
    vipPrice: {
      type: Number,
      default() {
        return 0;
      }
    },
    decimal: {
      type: Number,
      default() {
        return 2;
      }
    }
  },
  components: {},
  data() {
    return {};
  },
  computed: {
    priceNormal() {
      return formatMoney(this.price, this.decimal);
    },
    priceVip() {
      return formatMoney(this.vipPrice, this.decimal);
    },
  },
  methods: {
  },
};
</script>
