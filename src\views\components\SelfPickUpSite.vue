<template>
  <div v-if="addressList.length || addressModel" class="pickup-address-picker">
    <slot>
      <div class="address-icon">
        <i class="icon_jglh icon-lianxishangjia"></i>
      </div>
      <template v-if="addressModel">
        <div class="address" @click="handleShowPop">
          <div class="address-contact">自提点</div>
          <div class="address-contact">
            {{ addressModel.name }} {{ addressModel.phone }}
          </div>
          <div class="address-detail">
            {{ addressModel.province }} {{ addressModel.city }}
            {{ addressModel.area }} {{ addressModel.address }}
          </div>
        </div>
      </template>
      <div v-else class="address" @click="handleShowPop">
        {{ placeholder }}
      </div>

      <div class="address-arrow">
        <i class="icon_jglh icon-xf-shouqi-wangshang"></i>
      </div>
      <div v-if="disabled" class="disabled-mask"></div>
    </slot>
    <!-- 自提点popup -->
    <van-popup
      v-model="showAddressList"
      overlay
      round
      closeable
      close-on-click-overlay
      position="bottom"
      get-container="body"
      class="address-list-pop"
    >
      <h3 class="card-title">选择自提点</h3>
      <van-radio-group v-model="addressName">
        <van-cell-group>
          <van-cell
            v-for="(item, index) in addressList"
            :key="index"
            center
            :title="`${item.name}  ${item.phone}`"
            :label="item.address"
            clickable
            @click="selectAddressItem(item)"
          >
            <template #icon>
              <div class="address-icon">
                <i class="icon_jglh icon-lianxishangjia"></i>
              </div>
            </template>
            <template #right-icon>
              <van-radio :name="item.name" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </van-popup>
  </div>
</template>

<script>
import { getDistance } from '@/utils';
import { mixinAuthRouter } from '@/mixins';
import { dialog, toast, loading, onSelectAddress } from '@/bus';
import { getLocation } from '@/bridge';
import {
  getSelfPickUpAddressList,
  getSelfPickUpAddress,
} from '@/api/modules/address';
import { Popup, Cell, CellGroup, RadioGroup, Radio } from 'vant';

/**
 * 地址选择器
 */
export default {
  name: 'SelfPickUpSite',
  mixins: [mixinAuthRouter],
  components: {
    [Popup.name]: Popup,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
  },
  props: {
    placeholder: {
      type: String,
      default: '请选择',
    },
    defaultAddress: {
      type: [String, Number],
    },
    goodsId: {
      type: [String, Number],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      addressList: [],
      addressModel: null,
      location: {},
      showAddressList: false,
      addressName: '',
    };
  },
  watch: {
    addressModel(val) {
      this.$emit('input', val);
    },
    goodsId(val) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      if (!this.goodsId) {
        return;
      }
      // 如果有默认自提地址，默认不能切换，只读模式
      if (this.defaultAddress) {
        this.getDefaultAdressModel();
        return;
      }
      if (Object.keys(this.location).length === 0) {
        await this.getLocation();
      }
      getSelfPickUpAddressList({
        goodsId: this.goodsId,
        lat: this.location.latitude,
        lng: this.location.longitude,
      })
        .then(addressList => {
          this.addressList = addressList || [];
          const result = this.addressList.filter(
            item => item.id == this.defaultAddress
          )[0];
          this.addressModel = result || this.addressList[0];
        })
        .catch(err => {
          console.error(err);
        });
    },
    getDefaultAdressModel() {
      return getSelfPickUpAddress(this.defaultAddress).then(res => {
        this.addressModel = res;
      });
    },
    getLocation() {
      return getLocation().then(res => {
        this.location = res;
      });
    },
    handleShowPop() {
      if (this.addressList.length === 0) {
        toast().tip('暂无自提点');
        return;
      }
      this.showAddressList = true;
    },
    selectAddressItem(item) {
      this.addressModel = item;
      this.addressName = item.name;
      this.showAddressList = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.pickup-address-picker {
  position: relative;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 10px;
  // width: 100%;
  box-sizing: border-box;
  .address-arrow {
    margin-left: 10px;
    .icon_jglh {
      transform: rotate(90deg);
      display: inline-block;
    }
  }
  .address {
    width: 0;
    flex: 1;
    .address-contact {
      font-weight: 700;
    }
    .address-detail {
      font-size: 0.8em;
      color: #6f6f6f;
      max-width: 100%;
      // white-space: nowrap;
      // text-overflow: ellipsis;
      // overflow: hidden;
    }
  }
  .disabled-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
  }
}
.address-icon {
  margin-right: 10px;
  .icon_jglh {
    font-size: 20px;
  }
}

.address-list-pop {
  box-sizing: border-box;
  padding: 15px;
  background: #f3f3f3;
  .card-title {
    font-weight: 500;
    font-size: 16px;
    color: #111111;
    line-height: 16px;
    padding-bottom: 10px;
  }
  .card-sub-title {
    // margin-top: 4px;
    font-size: 12px;
    color: #999999;
    line-height: 15px;
    text-align: left;
  }
  .van-radio-group {
    margin-top: 10px;
    padding: 0 15px;
    background: #ffffff;
    border-radius: 8px;
  }
  .van-cell {
    padding-left: 0;
    padding-right: 0;
  }
  .van-cell::after {
    right: -50%;
    left: -50%;
  }
  .van-hairline--top-bottom::after {
    display: none;
  }
  .van-popup__close-icon--top-right {
    color: #000;
    font-size: 18px;
  }
}
</style>
