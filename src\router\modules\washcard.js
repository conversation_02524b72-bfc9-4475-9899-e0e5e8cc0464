import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';

const MODULE = '/washcard';
function getRoutePath(path) {
  return `${MODULE}${path}`;
}

const routers = [
  {
    path: '/account/cards',
    name: 'AccountCards',
    component: resolve => {
      import(/* webpackChunkName: "account-cards" */ '@/views/packages/carwash-card/pages/mycards/MyCards.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/cards/all',
    name: 'AccountCardsAll',
    component: resolve => {
      import(/* webpackChunkName: "account-cards-all" */ '@/views/packages/carwash-card/pages/mycards/MyCardsAll.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/account/cards/invalid',
    name: 'AccountInvalidCards',
    component: resolve => {
      import(/* webpackChunkName: "account-cards-invalid" */ '@/views/packages/carwash-card/pages/mycards/MyInvalidCards.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard',
    redirect: '/washcard/market',
  },
  {
    path: '/washcard/market',
    name: 'CarWashCardMarket',
    meta: {
      title: '洗车卡',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card" */ '@/views/packages/carwash-card/pages/wash-card-market/CarWashCardMarket.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/exchange',
    name: 'CarWashCardExchange',
    meta: {
      title: '洗车卡兑换',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-exchange" */ '@/views/packages/carwash-card/pages/WashCardExchange.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/:id/buy',
    name: 'CarWashCardBuy',
    meta: {
      title: '购买洗车卡',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-buy" */ '@/views/packages/carwash-card/pages/wash-card/WashCardBuy.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/buy/result',
    name: 'CarWashCardResult',
    meta: {
      title: '洗车卡',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-result" */ '@/views/packages/carwash-card/pages/wash-card/WashCardResult.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/mycard/:id',
    name: 'MyWashCardInfo',
    meta: {
      title: '我的洗车卡',
    },
    component: resolve => {
      import(/* webpackChunkName: "mywash-card-info" */ '@/views/packages/carwash-card/pages/mycards/MyWashCardInfo.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/record',
    name: 'MyWashCardRecord',
    meta: {
      title: '核销记录',
    },
    component: resolve => {
      import(/* webpackChunkName: "mywash-card-info" */ '@/views/packages/carwash-card/pages/mycards/MyWashCardRecord.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/use',
    name: 'UseMyWashCard',
    meta: {
      title: '洗车卡核销',
    },
    component: resolve => {
      import(/* webpackChunkName: "mywash-card-info" */ '@/views/packages/carwash-card/pages/mycards/UseMyWashCard.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/mycard/:id/payment',
    name: 'MyCarWashCardPayment',
    meta: {
      title: '洗车卡',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-payment" */ '@/views/packages/carwash-card/pages/payment/WashCardPayment.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/:id/rules',
    name: 'CarWashCardRules',
    meta: {
      title: '洗车卡使用规则',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-rules" */ '@/views/packages/carwash-card/pages/wash-card/WashCardRules.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/washcard/:id/shops',
    name: 'CarWashCardShops',
    meta: {
      title: '适用门店',
    },
    component: resolve => {
      import(/* webpackChunkName: "wash-card-shops" */ '@/views/packages/carwash-card/pages/wash-card-shops/WashCardShops.vue').then(resolve).catch(handleError);
    },
  },
];

export default routers;
// export default BaseRouter;
