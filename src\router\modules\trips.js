import { handleError } from '@/router/error-handler';

const routers = [
  {
    path: '/trips',
    redirect: '/trips/index',
  },
  {
    path: '/trips/index',
    name: 'TripsIndex',
    component: resolve => {
      import(/* webpackChunkName: "trips-index" */ '@/views/packages/trips/pages/ticket-list/TripIndex.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/trips/search',
    name: 'TripSearch',
    component: resolve => {
      import(/* webpackChunkName: "trips-search" */ '@/views/packages/trips/pages/ticket-list/TripSearch.vue').then(resolve).catch(handleError);
    },
    children: [
      {
        path: '/trips/search/input',
        name: 'TripSearchInput',
        component: resolve => {
          import(/* webpackChunkName: "trip-search-input" */ '@/views/packages/trips/pages/ticket-list/_TripSearchInput.vue').then(resolve).catch(handleError);
        },
      }
    ]
  },
  // 兼容旧地址，重定向到新地址
  {
    path: '/trips/ticket/:id',
    redirect: '/trips/place/:id',
  },
  {
    path: '/trips/place/:id',
    name: 'PlaceInfo',
    component: resolve => {
      import(/* webpackChunkName: "place-info" */ '@/views/packages/trips/pages/ticket-list/PlaceInfo.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/trips/place/:id/reserve',
    name: 'PlaceReserve',
    component: resolve => {
      import(/* webpackChunkName: "place-reserve" */ '@/views/packages/trips/pages/ticket-list/PlaceReserve.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/trips/orders',
    name: 'TicketOrderList',
    component: resolve => {
      import(/* webpackChunkName: "ticket-order-list" */ '@/views/packages/trips/pages/orders/TicketOrderList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/trips/order/:order',
    name: 'TicketOrderDetail',
    component: resolve => {
      import(/* webpackChunkName: "ticket-order-detail" */ '@/views/packages/trips/pages/orders/TicketOrderDetail.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/trips/order/result/:id',
    name: 'TicketOrderResult',
    component: resolve => {
      import(/* webpackChunkName: "ticket-order-result" */ '@/views/packages/trips/pages/ticket-list/TicketOrderResult.vue').then(resolve).catch(handleError);
    },
  },
];

export default routers;
