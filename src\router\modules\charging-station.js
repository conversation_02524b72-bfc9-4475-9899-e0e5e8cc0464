import { handleError } from '../error-handler';

export default [
  {
    path: '/charging-station',
    name: '充电站首页',
    redirect: '/charging-station/list',
  },
  {
    path: '/charging-station/list',
    name: 'ChargingStationList',
    component: resolve => {
      import(/* webpackChunkName: "charging-station-list" */ '@/views/packages/charging-station/ChargingStationList.vue').then(resolve).catch(handleError);
    },
    meta: {
      title: '充电站列表',
      keepAlive: true,
    },
  },
  {
    path: '/charging-station/filter',
    name: 'ChargingStationFilter',
    component: resolve => {
      import(/* webpackChunkName: "charging-station-filter" */ '@/views/packages/charging-station/ChargingStationFilter.vue').then(resolve).catch(handleError);
    },
    meta: {
      title: '筛选条件',
    },
  },
  {
    path: '/charging-station/search',
    name: 'ChargingStationSearch',
    component: resolve => {
      import(/* webpackChunkName: "charging-station-search" */ '@/views/packages/charging-station/ChargingStationSearch.vue').then(resolve).catch(handleError);
    },
    meta: {
      title: '搜索充电站',
    },
  },
  {
    path: '/charging-station/:id',
    name: 'ChargingStationDetail',
    component: resolve => {
      import(/* webpackChunkName: "charging-station-detail" */ '@/views/packages/charging-station/ChargingStationDetail.vue').then(resolve).catch(handleError);
    },
    meta: {
      title: '充电站详情',
    },
  },
];
