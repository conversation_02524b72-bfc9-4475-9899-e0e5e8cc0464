import { handleError } from '../error-handler';

export default [
  {
    path: '/forum',
    name: '车友圈',
    component: resolve => {
      import(/* webpackChunkName: "forum-index" */ '@/views/packages/forum/ForumIndex.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/message',
    name: '消息',
    component: resolve => {
      import(/* webpackChunkName: "forum-message" */ '@/views/packages/forum/ForumMessage.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/release',
    name: '论坛发布',
    component: resolve => {
      import(/* webpackChunkName: "release" */ '@/views/packages/forum/Release.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/taglist',
    name: '论坛话题标签',
    component: resolve => {
      import(/* webpackChunkName: "taglist" */ '@/views/packages/forum/TagList.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/list',
    name: '论坛列表',
    component: resolve => {
      import(/* webpackChunkName: "list" */ '@/views/packages/forum/List.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/info/:id/:index',
    name: '论坛详情',
    component: resolve => {
      import(/* webpackChunkName: "intro" */ '@/views/packages/forum/Info.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/notify',
    name: '动态通知',
    component: resolve => {
      import(/* webpackChunkName: "notify" */ '@/views/packages/forum/Notify.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/myrelease',
    name: 'myrelease',
    component: resolve => {
      import(/* webpackChunkName: "myrelease" */ '@/views/packages/forum/MyRelease.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/myactive',
    name: '我参与的列表',
    component: resolve => {
      import(/* webpackChunkName: "myactive" */ '@/views/packages/forum/MyActive.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/forum/greatlist/:id',
    name: '点赞人员列表',
    component: resolve => {
      import(/* webpackChunkName: "greatlist" */ '@/views/packages/forum/GreatList.vue').then(resolve).catch(handleError);
    },
  },
];
