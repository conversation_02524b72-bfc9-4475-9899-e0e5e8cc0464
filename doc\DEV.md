# 开发准备工作

1. 手机安装交广领航App测试版（允许设置代理的版本）

2. WIFI代理地址指向自己搭建的代理服务，如 Whistle, 代理转发到指定环境如测试环境

3. App登录成功，复制App网络请求头中的 `authority` 参数值 存入 localStorage，键名 `session`

4. 到此，可在Chrome模拟器中模拟需要登录状态的页面调试

# 真机调试

## Android

Android端使用的腾讯X5内核，App访问 `debugx5.qq.com`可开启页面调试

Chrome通过 `chrome://inspect` 访问远程调试入口，调试App端打开的WebView

## iOS

.iPhone连接Mac，通过Safari远程调试

.页面使用第三方调试工具如 vConsole

## 部署

1. git cz 提交代码
2. 推送到git 后，会自动部署测试
3. 登录jenkins 找到需要部署的项目(test测试环境，online正式环境)，选择分支选择发布版本开始构建
