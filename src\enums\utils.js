import { isPlainObject } from '@/utils/type';
import Enum from '@/model/Enum';
import Enums from '@/model/Enums';

function createEnum(...args) {
  return new Enum(...args);
};

export function createEnums(enums) {
  const result = Object.keys(enums).reduce((last, key) => {
    const o = enums[key];
    if (o instanceof Array) {
      last[key] = createEnum(...o);
    } else if (isPlainObject(o)) {
      // console.log(o);
      const values = [o.name, o.value, o.desc, o.disabled];
      last[key] = createEnum(...values);
    } else {
      console.error(o);
    }
    return last;
  }, {})
  return new Enums(result)
}

/* 
createEnums({
  A: {
    name: '名称',
    value: 1,
    desc: '描述信息',
  },
  B: {
    name: '名称',
    value: 1,
    desc: '描述信息',
  }
})

createEnums({
  A: ['名称', 1, '描述信息'],
  B: ['名称', 1, '描述信息'],
})
 */
