<template>
  <div :class="pictureClass" :style="styles" @click="handleClick">
    <slot></slot>
  </div>
</template>
<style lang="scss" scoped>
$bg-color: #f4f4f4;
.img-container {
  // background: url(./placeholder1.png) center center no-repeat transparent;
  background-size: contain;
  display: inline-block;
  // transition:all 250ms;
  transition-duration: 350ms;
  transition-property: opacity;
  will-change: opacity;
  vertical-align: top;
  &.img-contain {
    background-size: contain;
    background-position: center center;
  }
  &.img-cover {
    background-size: cover;
    background-position: center center;
  }
  &.img-lazy {
    background-color: $bg-color;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNiYAAAAAkAAxkR2eQAAAAASUVORK5CYII=);
  }
  &.img-loading {
    // background-image: url(./placeholder1.png);
    background-color: $bg-color;
    background-size: contain;
    opacity: 0.9;
  }
  &.img-ready {
    opacity: 1;
    // background-size: cover;
    background-color: transparent;
  }
  &.img-error {
    background-image: url(./placeholder1.png);
    background-color: #f4f4f4;
    background-size: contain;
  }
}
</style>

<script>
import { loadImage } from './utils';
import { getImageURL } from '@/common/image';
import { ImageType } from '@/enums';

const ImageStatus = {
  EMPTY: 1,
  LOADING: 2,
  READY: 3,
  ERROR: 4,
};

const Events = {
  READY: 'ready',
  APPEAR: 'appear',
};

const ImageFillType = {
  COVER: 'cover',
  CONTAIN: 'contain',
};

export default {
  name: 'c-picture2',
  props: {
    src: String,
    /*class: {
        type: String,
        default() {
          return '';
        },
      },*/
    fill: {
      type: String,
      default: ImageFillType.COVER,
      // default: ImageFillType.CONTAIN,
    },
    type: {
      type: [String, Object],
      default() {
        return ImageType.LOGO;
      },
    },
    placeholder: {
      type: String,
      default: '',
    },
    lazy: {
      type: Boolean,
      default: false,
    },
    // 自动适配宽度或高度
    autoFit: {
      type: String,
      default: '', // height|width
    },
    width: Number,
    height: Number,
  },
  created() {
    if (!this.lazy) {
      this.displayImage();
    }
    this.$on(Events.APPEAR, e => {
      this.displayImage();
      this.$off(Events.APPEAR);
    });
  },
  mounted() {
    console.warn('<c-picture>组件即将废弃，请更换为<biz-image>');
    this.$emit(Events.READY, this);
  },
  data() {
    return {
      status: ImageStatus.EMPTY,
      url: null,
      originWidth: this.width,
      originHeight: this.height,
      displayWidth: this.width,
      displayHeight: this.height,
    };
  },
  computed: {
    pictureClass() {
      const status = this.status;
      return {
        'img-container': true,
        picture: true,
        'img-contain': this.fill === ImageFillType.CONTAIN,
        'img-cover': this.fill === ImageFillType.COVER,
        'img-lazy':
          this.src &&
          this.lazy &&
          status !== ImageStatus.READY &&
          status !== ImageStatus.ERROR,
        'img-ready': status === ImageStatus.READY,
        'img-loading': status === ImageStatus.LOADING,
        'img-error': status === ImageStatus.ERROR,
      };
    },
    styles() {
      const url = this.url || this.placeholder;
      return {
        backgroundImage: url ? `url(${url})` : '',
        width: `${this.displayWidth}px`,
        height: `${this.displayHeight}px`,
      };
    },
    radio() {
      return this.originWidth / this.originHeight;
    },
  },
  watch: {
    src(val, oldVal) {
      this.displayImage();
      // console.log('picture change:', val, oldVal);
    },
  },
  methods: {
    displayImage() {
      this._displayImage();
      /* setTimeout(() => {
        }, 1); */
    },
    getWidthByHeight(height) {
      return height * this.radio;
    },
    getHeightByWidth(width) {
      return width / this.radio;
    },
    _displayImage() {
      if (!this.src) return;
      // if (this.status == ImageStatus.READY) return;
      const url = getImageURL(this.src, this.type);
      this.status = ImageStatus.LOADING;
      loadImage(url)
        .then(img => {
          this.originHeight = img.height;
          this.originWidth = img.width;
          /* if (!this.height) {
            this.displayHeight = img.height;
          }
          if (!this.width) {
            this.displayWidth = img.width;
          } */
          if (this.autoFit) {
            const elRect = this.$el.getBoundingClientRect();
            // console.log(this.$el, elRect);
            // 宽高有一个没设置或为0的情况下才自动设置对应宽高
            if (this.autoFit === 'height' && elRect.width) {
              this.displayWidth = elRect.width;
              this.displayHeight = this.getHeightByWidth(elRect.width);
            } else if (this.autoFit === 'width' && elRect.height) {
              this.displayHeight = elRect.height;
              this.displayWidth = this.getWidthByHeight(elRect.height);
            }
          }
          this.status = ImageStatus.READY;
          this.url = img.src;
        })
        .catch(err => {
          this.status = ImageStatus.ERROR;
          console.error(err);
        });
    },
    handleClick(e) {
      this.$emit('click', e);
    },
  },
};
</script>
