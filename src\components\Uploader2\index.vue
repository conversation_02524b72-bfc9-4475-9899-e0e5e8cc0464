<template>
  <div class="file-container">
    <div class="files">
      <div v-for="(item, index) in list"
          :class="getFileClass(item)"
          :title="item.msg"
          :key="index"
          @click="handleClick(item, index)"
          :percent="item.percent">
        <img :src="showImage(item.file)"/>
        <a href="javascript:;" @click.stop.prevent="removeFile(item)" class="file-remove">&times;</a>
        <input type="hidden" :name="settings.name" :value="item.file"/>
      </div>

      <!-- 2018年10月31日17:46:08：增加文件格式限制后：在小米手机中图片显示为不可选择状态，故暂不加限制 -->
      <!-- 增加文件格式限制后：在小米手机中 accept="image/gif,image/jpeg,image/webp,image/jpg,image/png,image/bmp" -->
      <div v-show="canAddFile" class="file file-select">
        <input v-if="canMultipleSelect" type="file" class="text" multiple="multiple"/>
        <input v-else type="file"  class="text"/>
      </div>
    </div>
    <slot></slot>
  </div>
</template>
<script src="./index.js"></script>
<style src="./index.scss" lang="scss" scoped></style>
