<template>
  <div class="shop flex-row">
    <c-picture :src="shop.logo" class="shop-logo">
    </c-picture>
    <div class="shop-content">
      <h3 class="shop-title">{{shop.title}}
      <!-- <a @click.stop="goNavigate" class="btn-navigate"></a> -->
      </h3>
      <div class="shop-score">
        <rater :value="counts.score"></rater>
        <span class="shop-comment-count">{{counts.comment}}条评价</span>
        <span class="shop-sales">已售{{shop.salesVolume || 0}}单</span>
      </div>
      <div class="shop-body">
          <p class="shop-address">{{shop.address}}</p>
          <span class="shop-distance">{{formatLength(shop.distance || 0)}}</span>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
  .shop{
    position:relative;
  }
  .shop-title {
    font-size: 16px;
    color:#303030;
    font-weight:400;
    .number{
      color:#F23240;
      padding-right:2px;
    }
  }
  .shop-logo {
    width: 70px;
    height: 70px;
    margin:2px 0 0 3px;
    border-radius: 50%;
  }
  .shop-body{
    position:relative;
  }
  .shop-content {
    flex: 1;
    margin-left:5px;
    min-width:0;
  }
  .shop-distance{
    position:absolute;
    right:0;
    top:0;
  }
  .shop-address, .shop-score {
    color: gray;
  }
  .shop-score{
    display:flex;
  }
  .shop-comment-count{
    margin-left:3px;
    flex:1;
  }
  .shop-sales {
    float: right;
  }
  .shop-address {
    color: gray;
    min-width: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width:80%;
  }
  .shop-score, .shop-body{
    font-size:0.9em;
    margin-top:2px;
  }
  .btn-navigate {
    float: right;
    padding: 0;
    width: 30px;
    text-align: center;
    &:active {
      background: #e0e0e0;
    }
    &:after {
      font-family: iconfont;
      content: "\e60b";
      font-size: 1.2em;
      color: gray;
    }
  }

</style>
<script>
import { Rater } from '@/components';
import { formatDistance } from '@/utils';
import { navigate } from '@/bridge';
import { OrderType } from '@/common/enums';

export default {
  name: 'shop-item',
  props: {
    shop: {
      type: Object,
    },
    orderType: Number,
  },
  computed: {
    counts() {
      const shop = this.shop;
      if (this.orderType == OrderType.MAINTAIN) {
        return {
          comment: shop.mtCommentCount,
          score: shop.mtScore,
        }
      } else {
        return {
          comment: shop.commentsCount,
          score: shop.score,
        }
      }
    }
  },
  methods: {
    formatLength(...args) {
      return formatDistance(...args);
    },
    goNavigate() {
      const { address, lat, lng } = this.shop;
      navigate({
        address,
        longitude: lng,
        latitude: lat,
        callback() {},
      });
    }
  },
  components: {
    Rater
  },
  data() {
    return {};
  }
};
</script>
