@import '~styles/variable/global.scss';
// $lh-2022-primary-color
.play-list{
  width: 100%;
  height: auto;
  max-height: 100%;
  box-sizing: border-box;
  padding: 0 0px 10px;
  display: inline-flex;
  flex-direction: column;
  .head ::v-deep{
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #EEEEEE;
    // position: absolute;
    // top: 0;
    // left: 0;
    width: 100%;
    box-sizing: border-box;
    background: #ffffff;
    .van-button{
      height: 28px;
      background: $lh-2022-primary-color;
      border-color: $lh-2022-primary-color;
    }
    .flex{
      flex: 1;
      font-size: 18px;
      font-weight: bold;
      color: #333333;
    }
    .play-order{
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      // margin-right: 20px;
      span{
        margin-left: 4px;
      }
    }
    .update-status{
      font-size: 14px;
      font-weight: 500;
      color: #666666;
    }
  }
  .list-wrap{
    overflow-y: scroll;
    flex: 1;
    padding: 0px 15px;
    background: #fff;
    .song-item{
      display: flex;
      align-items: center;
    }
    .audio-no{
      font-size: 12px;
      font-weight: bold;
      color: #666666;
      width: 36px;
      text-align: center;
    }
    .audio-info{
      overflow: hidden;
      flex: 1;
      padding: 15px 0;
      border-bottom: 1px solid #EEEEEE;
      line-height: 1;
      p{
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        margin-bottom: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
      }
      .audio-duration{
        font-size: 12px;
        font-weight: 500;
        color: #999999;
        span{
          margin-left: 3px;
        }
      }
    }
    .song-item:last-child{
      .audio-info{
        border-bottom: 0;
      }
    }
  }
  .equilizer{
    display: inline-flex;
    align-items: flex-end;
    justify-content: center;
    margin: 0 auto;
    height: 20px;
  }
  .bar {
    background: #FD4925;
    width: 3px;
    height: 5px;
    animation: equalize 1s 0s infinite;
    margin-left: 3px;
    transition: all linear;
    border-radius: 1px 1px 0 0;
  }
  .bar:nth-child(1) {
    animation-delay: 0.6s;
  }
  .bar:nth-child(2) {
    animation-delay: 0s;
  }
  .bar:nth-child(3) {
    animation-delay: 0.8s;
  }
  @keyframes equalize {
    0%{
        height: 5px;
    }
    25%{
        height: 10px;
    }
    50%{
        height: 15px;
    }
    75%{
        height: 10px;
    }
    100%{
        height: 5px;
    }
  }
}
