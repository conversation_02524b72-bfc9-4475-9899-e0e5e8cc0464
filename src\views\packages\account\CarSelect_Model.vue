<template>
  <container
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="false"
  >
    <x-header title="选择车型">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="text" @click="$_router_pageToFeedback()"
        >反馈</x-button
      >
    </x-header>
    <content-view
      class="car-select-model"
      ref="view"
      :status="status"
      @reload="reload"
    >
      <template slot="head" v-if="status == AppStatus.READY">
        <div class="select-head">
          <div class="flex-row card">
            <c-picture class="card-logo" :src="car.logo"></c-picture>
            <div class="card-content">
              <h3 class="card-name">{{ car.name }}</h3>
              <div class="card-attrs">
                <span>{{ car.desc }}</span>
              </div>
            </div>
          </div>
          <!--<div class="breads">
              <span>A4L</span>
              <i></i>
              <span>3.0T</span>
              <i></i>
              <span>2016年款</span>
              <i></i>
            </div>-->
        </div>
      </template>
      <template v-if="status == AppStatus.READY">
        <div class="item-list">
          <div
            class="item"
            v-for="(item, index) in list"
            :key="index"
            @click="next(item)"
          >
            {{ item.name }}
          </div>
          <div v-if="!list.length" class="empty-tip">没有可选的车型</div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.car-select-model {
  .card {
    background: white;
    padding: 5px;
    align-items: center;
    box-shadow: 0 1px 1px 1px #eeeff3;
    z-index: 1;
    position: relative;
    .card-logo {
      width: 60px;
      height: 60px;
      margin-right: 5px;
    }
    .card-content {
      flex: 1;
    }
    .card-name {
      font-weight: 700;
    }
    .card-attrs {
      color: gray;
    }
  }
  .item-list {
    background: white;
    margin-top: 5px;
    dt {
      padding: 2px 10px;
      background: #f3f3f3;
      font-weight: 700;
      color: #6d6d6d;
    }
    .item-logo {
      width: 40px;
      height: 40px;
      margin-right: 5px;
    }
    .item {
      padding: 10px;
      align-items: center;
      @include border-bottom(#e4e4e4);
    }
  }
}
</style>
<script>
import { AppStatus, CarType } from '@/enums';
import { pushWebView } from '@/bridge';
import { Events, emit, dialog, back } from '@/bus';
import { getCarList, getNewEnergyCarList } from '@/api';
import { mixinAuthRouter } from '@/mixins';

export default {
  name: 'car-model-select',
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      selectedItem: null,
      page: {
        list: null,
      },
    };
  },
  mounted() {},
  computed: {
    list() {
      const systemName = this.car.system;
      const regex = new RegExp(`^${systemName}`, 'i');
      return this.page.list.map(item => {
        if (item.name) {
          item.name = item.name.replace(regex, '');
        }
        return item;
      });
    },
    car() {
      const item = this.page.list[0];
      return {
        name: item.brand,
        logo: item.brandLogo,
        desc: `${item.manufacturer || ''} ${item.carSysName}`,
        system: item.carSysName,
      };
    },
  },
  methods: {
    initPageData() {
      const series = this.$route.params.series;
      let fetchFunc =
        this.$route.query.carType == CarType.NEW_ENERGY
          ? getNewEnergyCarList
          : getCarList;
      fetchFunc(series)
        .then(list => {
          this.page.list = list;
          this.status = AppStatus.READY;
        })
        .catch(e => {
          this.status = AppStatus.ERROR;
          console.error(e);
        });
    },
    init() {
      this.initPageData();
    },
    next(item) {
      const from = this.$route.query.from;
      if (from === 'account-my-car-edit') {
        this.setItem2(item);
      } else {
        this.setItem1(item);
      }
    },
    // 选择车型后跳转到补充车牌号等信息界面
    setItem1(item) {
      const carType = this.$route.query.carType;
      const path = `/account/car/${item.id}/add?carType=${carType}`;
      this.$_router_replace(path);
    },
    // 旧逻辑：选完车型触发选择车型事件并返回到上一页
    setItem2(item) {
      console.log(item);
      // 为避免界面切换卡顿，事件延迟到页面leave事件触发时再派发
      // this.selectedItem = item;
      const from = this.$route.query.from;
      emit(Events.AFTER_CAR_MODEL_SELECTED, item, from);
      back();
      // const backLength = this.$route.params.length || -3;
      // console.log('backLength', backLength);
      // this.$router.go(backLength);
    },
    onLeave() {
      /* if (this.selectedItem) {
          const from = this.$route.query.from;
          emit(Events.AFTER_CAR_MODEL_SELECTED, this.selectedItem, from);
          this.selectedItem = null;
        } */
      this.status = AppStatus.LOADING;
    },
    onResume() {},
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
  },
};
</script>
