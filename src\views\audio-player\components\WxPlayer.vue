<template>
  <div class="wx-player">
    <div class="album">
      <p class="album-title">{{ currentSong.name }}</p>
      <div class="album-img">
        <img
          class="image"
          :src="currentSong.cover"
          alt=""
        />
        <img v-if="playing" @click="togglePlaying(false)" class="control-btn" :src="pauseIcon" alt="">
        <img v-else @click="togglePlaying(true)" class="control-btn" :src="playIcon" alt="">
      </div>
      <progress-bar
        @onPercentChange="onPercentChange"
        theme="gray"
        :percent="percent"
        :time="currentTime"
        :current-song="currentSong"
      ></progress-bar>
      <van-button type="primary" round>打开APP 完整收听</van-button>
    </div>
    <audio
      @error="error"
      @timeupdate="timeupdate"
      ref="audio"
      @ended="end"
      :src="currentSong.url"
    ></audio>
  </div>
</template>

<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { isInWeixin } from '@/common/env';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { durationTrans } from '@/common/audio';
import ProgressBar from './ProgressBar';
import PlayList from './PlayList';
import { Button } from 'vant';
const ICON_PLAY = require('../assets/images/play.png');
const ICON_PAUSE = require('../assets/images/pause.png');
export default {
  name: 'Player',
  mixins: [mixinAuthRouter, mixinShare],
  props: ['playList', 'currentSong'],
  data() {
    return {
      playIcon: ICON_PLAY,
      pauseIcon: ICON_PAUSE,
      currentTime: 0,
      percent: 0,
      isFlag: false,
      title: '小疯的简史生活',
      moderator: '谢小疯 张弛',
      playing: false
    };
  },
  components: {
    [Button.name]: Button,
    ProgressBar,
    PlayList,
  },
  computed: {

  },
  methods: {
    onPercentChange(percent) {
      this.$refs.audio.currentTime = percent * this.currentSong.duration;
    },
    timeupdate(e) {
      this.currentTime = e.target.currentTime;
      this.percent = e.target.currentTime / this.currentSong.duration;
    },
    end() {
      this.playing = false
    },
    _play() {
      this.playing = true
      this.$refs.audio.play();
    },
    // getDuration() {
    //   this.currentSong = Object.assign({
    //     duration: this.$refs.audio.duration
    //   }, this.currentSong)
    // },
    /*
     * 事件 error 会在因为一些错误（如网络连接错误）导致无法加载资源的时候触发。
     * */
    error() {
      console.log('加载音频资源');
    },
    togglePlaying(flag) {
      this.playing = flag
    },
    share(action = 'update') {
      // 设置邀请链接分享信息
      const path = `/cm/shop/${this.title}`;
      const link = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share' : '?jglh',
      });
      const shareInfo = {
        link: link,
        title: `${this.currentSong.name}`,
        desc: this.currentSong.name,
        imgUrl: getImageURL(this.title),
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    formatTime(time) {
      if (!time) {
        return '00:00'
      } else {
        return durationTrans(time)
      }
    },
  },
  watch: {
    currentSong(newSong, oldSong) {
      if (!newSong.id) {
        return;
      }
      if (newSong.id === oldSong.id) {
        return;
      }
      /*
       * 一般是等dom的数据更新后 然后才会播放歌曲的
       * */
      setTimeout(() => {
        this._play();
      }, 30);
    },
    playing(newPlaying, oldValue) {
      this.$nextTick(() => {
        newPlaying ? this.$refs.audio.play() : this.$refs.audio.pause();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/variable/global.scss';
.album{
  padding: 15px 15px 20px;
  background: #fff;
  text-align: center;
  p.album-title{
    font-size: 18px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20px;
    text-align: center;
  }
  .album-img{
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 auto 30px;
    text-align: center;
    .image{
      display: block;
      width: 100%;
      height: 100%;

    }
    .control-btn{
      width: 60px;
      height: 60px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .van-button{
    width: 250px;
    height: 44px;
    background: $lh-2022-primary-color;
    border-color: $lh-2022-primary-color;
    margin-top: 18px;
    font-size: 18px;
    line-height: 1;
  }
}

</style>
