<template>
  <div class="player-wrap">
    <!-- 左侧选手：当前用户 -->
    <div class="animate player player-left">
      <!-- 头像 -->

      <biz-image class="avatar" :src="leftPlayer.portrait" immediate>
      </biz-image>

      <!-- 昵称 -->
      <p class="player-name">{{ leftPlayer.name }}</p>
    </div>

    <!-- VS 标志，可以是文字或图片 -->
    <div class="vs"></div>

    <!-- 右侧选手：对手 -->
    <div class="animate player player-right">
      <template v-if="rightPlayer">
        <biz-image class="avatar" :src="rightPlayer.portrait" immediate>
        </biz-image>
        <p class="player-name">{{ rightPlayer.name }}</p>
      </template>
      <div v-else class="waiting">
        匹配中
        <span class="dots">
          <span class="dot">.</span>
          <span class="dot">.</span>
          <span class="dot">.</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { mixinAuth } from '@/mixins/auth';
import { mapState } from 'vuex';
export default {
  name: 'PlayerCard',
  mixins: [mixinAuth],
  props: {
    // 头像
    // avatar: {
    //   type: String,
    //   default: '',
    // },
  },
  data() {
    return {
      // 选手信息
    };
  },
  computed: {
    ...mapState('quiz', {
      quizResults: state => state.quiz.results,
      quizPlayers: state => state.quiz.players,
      quizUser: state => state.user,
    }),
    leftPlayer() {
      let player = {
        portrait: this.$_auth_userInfo.portrait,
        name: this.$_auth_userInfo.name,
      };
      return player;
    },
    rightPlayer() {
      if (!this.quizPlayers.opponent) {
        return null;
      }
      return this.quizUser.isHouseOwner
        ? this.quizPlayers.opponent.userDetail
        : this.quizPlayers.promoter.userDetail;
    },
  },
  watch: {
    rightPlayer: {
      handler(val) {
        if (val) {
          setTimeout(() => {
            this.hidePlayers();
          }, 3000);
        }
      },
      deep: true,
    },
  },
  methods: {
    // 隐藏对战选手
    hidePlayers() {
      this.$emit('hide');
    },
  },
};
</script>

<style lang="scss" scoped>
.player-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #5944ff;
  // VS 标志
  .vs {
    width: 110px;
    height: 110px;
    background: url('~@/views/live-battle/assets/images/vs.png') no-repeat;
    background-size: 100% 100%;
    animation: vs-pulse 1.5s ease-in-out infinite;
  }

  // 选手区域
  .player {
    width: 100%;
    height: 164px; // 根据设计图调节
    background: url('~@/views/live-battle/assets/images/player_lf.png')
      no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    z-index: 2;
    text-align: center;

    .avatar {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      margin-right: 6px;
      flex-shrink: 0;
    }
    .player-name {
      font-size: 18px;
      color: #ffffff;
      word-break: break-all;
    }
  }
  // 左侧选手
  .player-left {
    padding-left: 47px;
    transform: translateX(-50%); // 初始位置在屏幕左侧外
    &.animate {
      animation: slide-in-left 1s forwards;
    }
  }
  // 右侧选手
  .player-right {
    flex-direction: row-reverse;
    padding-right: 47px;
    background-image: url('~@/views/live-battle/assets/images/player_rt.png');
    transform: translateX(50%); // 初始位置在屏幕右侧外
    &.animate {
      animation: slide-in-right 1s forwards;
    }
    .avatar {
      margin-left: 6px;
      margin-right: 0;
    }
  }
  .waiting {
    font-size: 18px;
    color: #ffffff;
    display: flex;
    align-items: center;
    word-break: keep-all;
    .dots {
      display: flex;
      margin-left: 2px;
    }

    .dot {
      animation: dotFlashing 1.2s infinite linear;
      margin-left: 2px;
      color: #ffffff;
      width: 3px;
      height: 3px;
      border-radius: 50%;
      background-color: #ffffff;
    }

    .dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .dot:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

@keyframes dotFlashing {
  0%,
  20% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  80%,
  100% {
    opacity: 0;
  }
}
/* 左侧进场动画 */
@keyframes slide-in-left {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0%);
  }
}

/* 右侧进场动画 */
@keyframes slide-in-right {
  0% {
    transform: translateX(50%);
  }
  100% {
    transform: translateX(0%);
  }
}

@keyframes vs-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
