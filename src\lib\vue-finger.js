import AlloyFinger from 'alloyfinger';

const VueFinger = {};

VueFinger.install = function(Vue) {
  Vue.directive('finger', {
    bind: function (el, binding, vnode) {
      if (!el.finger) {
        el.finger = new AlloyFinger(el, {
  
        })
      }
      const finger = el.finger;
      if (binding.arg) {
        finger.on(binding.arg, binding.value);
      }
    },
    inserted: function () {},
    update: function () {},
    componentUpdated: function () {},
    unbind: function () {}
  })
}

export default VueFinger;
