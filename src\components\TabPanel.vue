<template>
  <div class="tab-panel">
    <div class="tab-panel__items">
      <div v-for="(item, $index) in list" class="tab-panel__item" :class="{'tab-panel-item-active':activeIndex===$index}" @click="setActive($index)">
        <span>{{item.name}}</span>
      </div>
    </div>
    <div class="tab-panel__content" >
      <transition name="slide">
        <div class="panel-content-inner" v-html="content">
          <slot></slot>
        </div>
      </transition>
    </div>
  </div>
</template>

<style lang="scss">
  @import '~styles/variable/global.scss';
  $tab-item-padding:5px;
  $tab-active-border: 2px;
  .tab-panel{
    width:100%;
  }
  .tab-panel__items{
    display:flex;
    flex-direction: row;
    position:relative;
    border-bottom:1px solid #E9E9E9;
  }
  .tab-panel__item{
    flex:1;
    text-align:center;
    padding: $tab-item-padding;
  }
  .tab-panel-item-active>span{
    color: $lh-primary-color;
    border-bottom:2px solid $lh-primary-color;
    padding:0 10px $tab-item-padding + 1px;
  }
  .tab-panel__content{
    padding:5px;
  }
</style>

<script>
    export default{
      name: 'tab-panel',
      props: {
        list: Array,
        defaultActive: {
          type: Number,
          default: 0,
        },
      },
      data() {
        return {
          activeIndex: 0
        };
      },
      computed: {
        activeItem() {
          return this.list[this.activeIndex || this.defaultActive];
        },
        content() {
          return this.activeItem.value;
        },
      },
      methods: {
        setActive(index) {
          this.activeIndex = index;
        }
      }
    };
</script>
