import { handleError } from '@/router/error-handler';

const routers = [
  {
    path: '/grab-coupons',
    redirect: '/grab-coupons/index',
  },
  {
    path: '/grab-coupons/index',
    name: 'GrabCouponsIndex',
    component: resolve => {
      import(/* webpackChunkName: "grab-coupons-home" */ '@/views/packages/grab-coupons/Home.vue').then(resolve).catch(handleError);
    },
  },
];

export default routers;
