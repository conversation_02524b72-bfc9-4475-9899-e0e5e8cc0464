<template>
  <container class="account-red-pocket-qa" @ready="init">
    <x-header title="红包使用攻略">
      <x-button  slot="left" type="back"></x-button>
    </x-header>

    <content-view :status="status">
      <template v-if="status == AppStatus.READY">
        <div class="qa-contents">
          <div class="qa-block">
            <h3 class="qa-title">红包介绍</h3>
            <div class="qa-content">交广领航红包是交广领航提供的一项用户增值服务，交广领航APP用户可通过参与营销活动等方式获取红包，在交广领航APP任何需要支付的地方可抵扣现金。交广领航红包的金额等同现金金额，1元红包=1元现金。交广领航红包长期有效。</div>
          </div>
          <div class="qa-block">
            <h3 class="qa-title">红包使用范围</h3>
            <div class="qa-content">红包支持交广领航APP内任何需要支付的地方，例如：洗车美容，汽车保养，优惠团购，报名活动等。</div>
          </div>
          <div class="qa-block">
            <h3 class="qa-title">红包使用方法</h3>
            <div class="qa-content">在提交订单到支付界面后，系统会自动选中红包余额中的金额进行抵扣相等的金额，也可以选择不使用红包余额抵扣。</div>
          </div>
          <div class="qa-block">
            <h3 class="qa-title">其他说明</h3>
            <div class="qa-content">当用户在交易时使用交广领航红包抵扣，如发生撤销，退款，退货等操作，红包一般默认退回至用户交广领航APP-我的-我的红包中，其余支付金额原路径退回给您。</div>
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
  .account-red-pocket-qa {
    background: white;
  }
  .qa-contents {
    padding: 10px;
    .qa-block {
      margin: 10px auto;
    }
  }
</style>
<script>

import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinLoader } from '@/mixins';

export default {
  name: 'AccountRedPocketQA',
  mixins: [mixinAuthRouter, mixinLoader],
  components: {},
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
    };
  },
  computed: {},
  methods: {
    init() {
      this.status = AppStatus.READY;
    },
  }
};
</script>
