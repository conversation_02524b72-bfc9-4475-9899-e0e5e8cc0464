@import '../variable/global.scss';

@mixin font-dpr($size) {
  font-size: $size;
  [data-dpr="2"] & {
    font-size: $size * 2;
  }
  [data-dpr="3"] & {
    font-size: $size * 3;
  }
}

@mixin border-1px() {
  border-width: 1px; /* px */
  border-width: 0.5px; /* px */
}

/*
 模拟1px边框的选择器，之前是.android，
 现在ios暂时也采用dpr=1开发了，所以android和ios都暂时使用模拟的1px
 ios切换到采用dpr=1开发，原因是页面中用到的某些第三方控件如日期选择在dpr>1的情况下滑动距离是按dpr=1计算的
 此问题解决后可将$eborder1px-selector的值改为.android，ios采用dpr=2进行开发
*/
$eborder1px-selector: '[data-dpr="1"]';

$border-color: rgb(222, 222, 222);
$default-position: relative;
@mixin border($color: $border-color, $pseudo: 'before', $position: $default-position) {
  position: $position;
  z-index: 0;
  &::#{$pseudo} {
    content: "";
    position: absolute;
    background-color: $color;
    transform: scale(.5);
  }
}

@mixin border-right($color, $pseudo : 'after', $hidden: '', $position: $default-position) {
  border-right: 1PX solid $color;

  @if $hidden!='' {
    &:#{$hidden} {
      border-right:0;
    }
  }

  #{$eborder1px-selector} & {
    border-right: 0;
    @include border($color, $pseudo, $position);
    &::#{$pseudo} {
      right: 0;
      top: -50%;
      width: 1PX;
      height: 200%;
    }
    @if $hidden!='' {
      &:#{$hidden}{
        &::#{$pseudo} {
          display:none;
        }
      }
    }
  }
}

@mixin border-left($color, $pseudo : 'before', $hidden: '', $position: $default-position) {
  border-left:1PX solid $color;

  @if $hidden!='' {
    &:#{$hidden} {
      border-left:0;
    }
  }

  #{$eborder1px-selector} & {
    border-left: 0;
    @include border($color, $pseudo, $position);
    &::#{$pseudo} {
      left: 0;
      top: -50%;
      width: 1PX;
      height: 200%;
    }
    @if $hidden!='' {
      &:#{$hidden}{
        &::#{$pseudo} {
          display:none;
        }
      }
    }
  }
}

@mixin border-bottom($color, $pseudo : 'after', $hidden: '', $position: $default-position) {
  border-bottom: 1PX solid $color;
  @if $hidden!='' {
    &:#{$hidden} {
      border-bottom-color: transparent;
    }
  }

  #{$eborder1px-selector} & {
    border-bottom-color: transparent;
    @include border($color, $pseudo, $position);
    &::#{$pseudo} {
      bottom: 0;
      left: -50%;
      height: 1PX;
      width: 200%;
    }
    @if $hidden!='' {
      &:#{$hidden}{
        &::#{$pseudo} {
          display:none;
        }
      }
    }
  }
}

@mixin border-top($color, $pseudo : 'before', $hidden: '', $position: $default-position) {
  border-top:1PX solid $color;

  @if $hidden!='' {
    &:#{$hidden} {
      border-top-color: transparent;
    }
  }
  #{$eborder1px-selector} & {
    border-top-color: transparent;
    @include border($color, $pseudo, $position);
    &::#{$pseudo} {
      top: 0;
      left: -50%;
      height: 1PX;
      width: 200%;
    }
    @if $hidden!='' {
      &:#{$hidden}{
        &::#{$pseudo} {
          display:none;
        }
      }
    }
  }
}

// 单行省略号
@mixin singleline-ov() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

// 多行省略号
@mixin multiline-ov($num:2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $num;
  -webkit-box-orient: vertical;
}

/* flex布局 */
@mixin flex ($direction:row,$justify:normal,$align:normal){
	display: flex;
	justify-content: $justify;
	align-items: $align;
	flex-direction: $direction;
}