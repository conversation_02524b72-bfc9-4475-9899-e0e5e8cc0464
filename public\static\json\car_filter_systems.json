{"code": 200, "msg": "成功", "data": [{"id": 164, "name": "君威", "brandId": 38}, {"id": 166, "name": "别克GL8", "brandId": 38}, {"id": 344, "name": "荣御", "brandId": 38}, {"id": 525, "name": "林荫大道", "brandId": 38}, {"id": 592, "name": "昂科雷", "brandId": 38}, {"id": 719, "name": "君越(海外)", "brandId": 38}, {"id": 834, "name": "君越", "brandId": 38}, {"id": 875, "name": "凯越", "brandId": 38}, {"id": 982, "name": "英朗", "brandId": 38}, {"id": 985, "name": "君威(海外)", "brandId": 38}, {"id": 2317, "name": "昂科威(海外)", "brandId": 38}, {"id": 2511, "name": "VERANO(海外)", "brandId": 38}, {"id": 2896, "name": "昂科拉", "brandId": 38}, {"id": 3067, "name": "Roadmaster", "brandId": 38}, {"id": 3076, "name": "Special", "brandId": 38}, {"id": 3078, "name": "Riviera", "brandId": 38}, {"id": 3554, "name": "昂科威", "brandId": 38}, {"id": 3707, "name": "别克Cascada", "brandId": 38}, {"id": 3708, "name": "Avenir", "brandId": 38}, {"id": 3751, "name": "威朗", "brandId": 38}, {"id": 4000, "name": "Avista", "brandId": 38}, {"id": 4239, "name": "<PERSON><PERSON><PERSON>", "brandId": 38}]}