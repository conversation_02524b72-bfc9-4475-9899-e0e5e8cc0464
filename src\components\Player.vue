<template>
  <div class="lh-player">
    <div id="mse"></div>
    <slot name="controls"></slot>
  </div>
</template>
<script>
/*
 * 使用西瓜播放器 https://github.com/bytedance/xgplayer
 * 与官方配置不同的地方:
 * controls.root 指定控制挂载的容器，此处修改为指定class
 *
 */
import Player, { Events } from 'xgplayer';
import 'xgplayer/dist/index.min.css';
import MobilePreset from 'xgplayer/es/presets/mobile';
export default {
  name: 'Player',
  props: {
    options: {
      type: Object,
      default: {},
    },
    value: String,
    close: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      show: true,
      player: null,
    };
  },
  computed: {
    btnClass() {
      return `tip tip-${this.type}`;
    },
  },
  watch: {
    options(val) {
      if (this.player && val.url) {
        this.player.reset();
        this.player.play();
      } else if (!this.player) {
        this.initPlayer();
        setTimeout(() => {
          this.player.play();
        }, 200);
      }
    },
  },
  mounted() {
    this.initPlayer();
  },
  methods: {
    initPlayer() {
      if (!this.options.url) {
        return;
      }
      let propsOptions = JSON.parse(JSON.stringify(this.options));
      let newOptions = {
        id: 'mse',
        height: '100%',
        width: '100%',
        playsinline: true,
        presets: [MobilePreset],
        closeVideoDblclick: true,
        playbackRate: false,
        fullscreen: {
          useCssFullscreen: true,
          needBackIcon: true,
        },
        mobile: {
          gestureX: false,
        },
        // enter: {
        //   innerHtml: '',
        // },
      };
      // 指定控制挂载的容器
      if (propsOptions.controls && propsOptions.controls.root) {
        newOptions.controls = { ...propsOptions.controls };
        newOptions.controls.root = document.getElementById(
          propsOptions.controls.root
        );
        delete propsOptions.controls;
      }
      // 设置seek操作结束之后播放器的状态seekedStatus,play 播放, pause 暂停, auto 保持操作前的状态
      let seekNum = 0;
      if (propsOptions.autoplay) {
        newOptions.seekedStatus = 'play';
      } else {
        newOptions.seekedStatus = 'auto';
      }
      // 设置seek时间点
      if (propsOptions.startTime) {
        seekNum = propsOptions.startTime;
      }
      // 初始化播放器
      let player = new Player({
        ...newOptions,
        ...propsOptions,
      });
      this.player = player;
      // 监听视频起播数据加载完成, 尝试解决初始化时视频没有封面黑屏问题
      player.on(Events.LOADED_DATA, () => {
        player.seek(seekNum);
      });
    },
    pauseVideo() {
      this.player && this.player.pause();
    },
    playVideo() {
      if (!this.player) {
        this.initPlayer();
        setTimeout(() => {
          this.player.play();
        }, 200);
        return;
      }
      this.player && this.player.play();
    },
  },
};
</script>

<style lang="scss" scoped>
.lh-player {
  width: 100%;
  height: 100%;
}
</style>
