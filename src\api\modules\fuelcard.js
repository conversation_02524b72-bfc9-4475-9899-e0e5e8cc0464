import { doGet, doPost, postJSON } from '../request/';
import APIs from '../apis';

const debug = false;

const baseParam = {
  // v: 394,
  sid: 'HN0001'
};

APIs.extend({
  // 获取加油卡列表
  '/fuelcard/mycard': '/Radio/finance/oilcard/my/list',

  // 获取充值金额列表
  '/fuelcard/recharge': '/Radio/finance/oilcard/charge/amount/list',

  // 提交充值订单
  '/fuelcard/submitCharge': '/Radio/finance/oilcard/oilcard/recharge/save',

  // 获取支付 详情 
  // '/fuelcard/order/detail': '/Radio/finance/oilcard/apply/record/detail/search',

  // 获取充值列表
  '/fuelcard/rechargerecord': '/Radio/finance/oilcard/recharge/list/search',

  // 获取充值卡详情
  '/fuelcard/card': '/Radio/finance/oilcard/detail/search',

  // 删除充值卡
  '/fuelcard/delCard': '/Radio/finance/oilcard/cancle/oil/card',

  // 获取申办记录列表
  '/fuelcard/recordlist': '/Radio/finance/oilcard/apply/record/list/search',

  // 确认收货
  '/fuelcard/confirmreceipt': '/Radio/finance/oilcard/apply/confirm/save',

  // 删除无效记录
  '/fuelcard/delRecord': '/Radio/finance/oilcard/apply/delete',

  // 获取油卡申办详情
  '/fuelcard/getRecordInfo': '/Radio/finance/oilcard/apply/record/detail/search',

  // 提交油卡办理信息
  '/fuelcard/applyCardSubmit': '/Radio/finance/oilcard/apply/info/save',

  // 获取油卡预存列表
  'fuelcard/getPrestore': '/Radio/finance/oilcard/prestore/list',

  // 提交退款申请
  'fuelcard/submitRefund': '/Radio/finance/oilcard/apply/refund/save',
  // 修改提交信息
  'fuelcard/updateApplyCard': '/Radio/finance/oilcard/apply/info/update',

  // 绑定油卡信息
  'fuelcard/bindCard': '/Radio/finance/oilcard/user/oilcard/save',

  // 问题反馈
  'fuelcard/reportProblem': '/Radio/finance/oilcard/feedback/save',

  // 油卡充值 前置接口
  '/fuelcard/rechargeConfirm': '/Radio/finance/oilcard/recharge/limit/search',

  // 充值详情
  '/fuelcard/rechargeInfo': '/Radio/finance/oilcard/recharge/detail/search',

  // 验证车牌号是否已经办理过加油卡
  '/fuelcard/checkCarNo': '/Radio/finance/oilcard/carNo/check',

  // 检查油卡申请记录是否有状态更新
  '/fuelcard/changeApplyStatus': '/Radio/finance/oilcard/apply/red/point',

  // 发票可以开票的充值列表
  '/fuelcard/invoice': '/Radio/finance/oilcard/invoice/recharge/list',
  // 发票开票信息提交
  '/fuelcard/applyinvoice': '/Radio/finance/oilcard/invoice/add',
  // 发票开票历史
  '/fuelcard/historyInvoice': '/Radio/finance/oilcard/invoice/my/list',
  // 发票详情
  '/fuelcard/invoiceInfo': '/Radio/finance/oilcard/invoice/info/{id}',
  // 发票详情-充值记录
  '/fuelcard/invoiceRecharge': '/Radio/finance/oilcard/invoice/info/{id}/recharge',

  // 油卡问题反馈列表
  '/fuelcard/feedback': '/Radio/finance/oilcard/feedback/list/search',

  // 油卡问题详情
  '/fulcard/feedbackInfo': '/Radio/finance/oilcard/feedback/detail',

  // 判断用户的会员资格
  '/fuelcard/checkVipLevel': '/Radio/finance/oilcard/member/type',
});

function createUrl(name, data, option) {
  return APIs.get(name, data, option) + `?sid=${baseParam.sid}`;
}

// 获取会员权限资格
export function checkVipLevel() {
  return doGet(createUrl('/fuelcard/checkVipLevel', {}, { debug: debug }));
}

// 油卡问题反馈详情
export function getFeedbackInfo(id) {
  const params = Object.assign({ id: id }, baseParam);
  return doGet(createUrl('/fulcard/feedbackInfo', {}, { debug: debug }), params);
}

// 油卡问题反馈列表
export function getFeedbackList(options) {
  let defaults = {
    rows: 10,
    page: 1
  };
  const params = Object.assign(defaults, options, baseParam);
  return doGet(createUrl('/fuelcard/feedback', {}, { debug: debug }), params);
}

// 发票详情 充值记录
export function invoiceRecharge(option) {
  let id = option.id;
  delete option.id;
  let page = option;
  return doGet(createUrl('/fuelcard/invoiceRecharge', { id: id }, { debug: debug }), page);
}

// 发票详情
export function invoiceInfo(id) {
  return doGet(createUrl('/fuelcard/invoiceInfo', { id: id }, { debug: debug }));
}

// 发票开票历史
export function historyInvoice(page) {
  return doGet(createUrl('/fuelcard/historyInvoice', {}, { debug: debug }), page);
}

// 发票开票信息提交
export function applyInvoice(data) {
  return postJSON(createUrl('/fuelcard/applyinvoice', {}, { debug: debug }), data);
}

// 发票可以开票的充值列表
export function invoiceList(page) {
  return doGet(createUrl('/fuelcard/invoice', {}, { debug: debug }), page);
}

// 检查油卡申请记录是否有状态更新
export function changeApplyStatus() {
  return doGet(createUrl('/fuelcard/changeApplyStatus', {}, { debug: debug }));
}

// 检查车牌号是否已经办理过加油卡
export function checkCarNo(carNo) {
  // const params = Object.assign({}, {carNo: carNo}, baseParam);
  return doGet(createUrl('/fuelcard/checkCarNo', {}, { debug: debug }), { carNo: carNo });
}

/**
 * 充值详情
 */
export async function getRechargeInfo(id) {
  // const params = Object.assign({}, {rechargeId: id}, baseParam);
  return doGet(createUrl('/fuelcard/rechargeInfo', {}, { debug: debug }), { rechargeId: id });
}

/**
 * 充值前确认接口
 */
export function rechargeConfirm(id) {
  // const params = Object.assign({}, {id: id}, baseParam);
  return doGet(createUrl('/fuelcard/rechargeConfirm', {}, { debug: debug }), { id: id });
}

/**
 * 获取订单详情
 */
export async function getFuelcardOrderDetail(id) {
  // const params = Object.assign({}, {id: id}, baseParam);
  // return doGet(createUrl('/fuelcard/order/detail', {}, {debug: debug}), params);
  return getRecordInfo(id);
}

/**
 * 获取油卡列表
 */
export function getCardList() {
  return doGet(createUrl('/fuelcard/mycard', {}, { debug: debug }));
}

/**
 * 获取充值金额列表
 */
export function getRecharges(cardTypeId) {
  // const params = Object.assign({cardTypeId: cardTypeId}, baseParam);
  return doGet(createUrl('/fuelcard/recharge', {}, { debug: debug }), { cardTypeId: cardTypeId });
}

/**
 * 充值金额ID
 * @param {number} id 
 */
export function submitCharge(cardID, oilcardConfigId) {
  // const params = Object.assign({id: cardID, oilcardConfigId: oilcardConfigId});
  return doPost(createUrl('/fuelcard/submitCharge', {}, { debug: debug }), { id: cardID, oilcardConfigId: oilcardConfigId });
}

/**
 * 获取充值记录列表
 */
export function getRechargeRecordList(options = {}) {
  let defaults = {
    rows: 10,
    page: 1
  };
  const params = Object.assign(defaults, options);
  return doGet(createUrl('/fuelcard/rechargerecord', {}, { debug: debug }), params);
}

/**
 * 获取充值卡详情
 */
export function getCardInfo(id) {
  // const params = Object.assign({id: id}, baseParam);
  return doGet(createUrl('/fuelcard/card', {}, { debug: debug }), { id: id });
}

/**
 * 删除充值卡
 */
export function delCard(id) {
  // const params = Object.assign({cardId: id}, baseParam);
  return doGet(createUrl('/fuelcard/delCard', {}, { debug: debug }), { cardId: id });
}

/**
 * 获取油卡申请列表
 */
export function getRecordList(options = {}) {
  let defaults = {
    rows: 10,
    page: 1
  };
  const params = Object.assign(defaults, options);
  return doGet(createUrl('/fuelcard/recordlist', {}, { debug: debug }), params);
}

/**
 * 确认收货
 */
export function ConfirmReceipt(id) {
  // var param = Object.assign({id: id}, baseParam);
  return doPost(createUrl('/fuelcard/confirmreceipt', {}, { debug: debug }), { id: id });
}

/**
 * 删除无效记录
 */
export function delRecord(id) {
  // var param = Object.assign({applyId: id}, baseParam);
  return doPost(createUrl('/fuelcard/delRecord', {}, { debug: debug }), { applyId: id });
}

/**
 * 获取油卡申办详情
 */
export function getRecordInfo(id) {
  // const params = Object.assign({}, {id: id}, baseParam);
  return doGet(createUrl('/fuelcard/getRecordInfo', {}, { debug: debug }), { id: id });
  // return doGet(createUrl('/fuelcard/getRecordInfo', {}, {debug: debug}), {id: id});
}

/**
 * 提交油卡办理信息
 */
export function applyCardSubmit(data) {  
  // data = Object.assign(data, baseParam);
  // var option = Object.assign({}, {debug: debug}, baseParam);
  return doPost(createUrl('/fuelcard/applyCardSubmit', {}, { debug: debug }), data);
}

/**
 * 获取油卡预存列表
 */
export function getPrestore() {
  // var option = Object.assign({}, {debug: debug}, baseParam);
  return doGet(createUrl('fuelcard/getPrestore', {}, { debug: debug }));
}

/**
 * 提交退款申请
 */
export function submitRefund(data) {
  // data = Object.assign(data, baseParam);
  return doPost(createUrl('fuelcard/submitRefund', {}, { debug: debug }), data);
}

/**
 * 更新提交信息
 */
export function updateApplyCard(data) {
  // data = Object.assign(data, baseParam);
  return doPost(createUrl('fuelcard/updateApplyCard', {}, { debug: debug }), data);
}

/**
 * 提交绑定油卡信息
 */
export function bindCard(data) {
  // data = Object.assign(data, baseParam);
  return doPost(createUrl('fuelcard/bindCard', {}, { debug: debug }), data);
}

/**
 * 问题反馈
 */
export function reportProblem(data) {
  // data = Object.assign(data, baseParam);
  return doPost(createUrl('fuelcard/reportProblem', {}, { debug: debug }), data);
}

/**
 * 获取token
*/
// export function getToken() {
//   return doPost('/Radio/resource/cloud/bulk/gettoken?type=1');
// }

// export const fuelcardApi = {
//   getCardList: getCardList,
//   getRecharges: getRecharges,
//   submitCharge: submitCharge,
//   getRechargeRecordList: getRechargeRecordList,
//   getCardInfo: getCardInfo,
//   delCard: delCard,
//   getRecordList: getRecordList,
//   ConfirmReceipt: ConfirmReceipt,
//   delRecord: delRecord,
//   getRecordInfo: getRecordInfo,
//   applyCardSubmit: applyCardSubmit,
//   getPrestore: getPrestore,
//   submitRefund: submitRefund,
//   updateApplyCard: updateApplyCard,
//   bindCard: bindCard,
//   reportProblem: reportProblem,
//   rechargeConfirm: rechargeConfirm,
//   checkCarNo: checkCarNo,
//   changeApplyStatus: changeApplyStatus,

//   invoiceList: invoiceList,
//   applyInvoice: applyInvoice,
//   historyInvoice: historyInvoice,
//   invoiceInfo: invoiceInfo,
//   invoiceRecharge: invoiceRecharge,

//   getFeedbackList: getFeedbackList,
//   getFeedbackInfo: getFeedbackInfo,

// }
