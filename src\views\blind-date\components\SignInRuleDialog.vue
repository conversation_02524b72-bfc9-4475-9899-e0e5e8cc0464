<template>
  <van-dialog
    v-model="showDialog"
    title="签到福利"
    :show-confirm-button="true"
    :show-cancel-button="true"
    confirm-button-text="立即签到"
    cancel-button-text="稍后再说"
    confirm-button-color="#FF6B8B"
    class="sign-in-rule-dialog"
    @confirm="onConfirm"
    @cancel="onCancel"
  >
    <div class="sign-in-rule-content">
      <div class="sign-in-rule-items">
        <p class="sign-in-rule-item">
          <span class="flower-icon">✿</span>
          每日可送出<span class="highlight">20朵</span>鲜花
        </p>
        <p class="sign-in-rule-item">
          <span class="flower-icon">✿</span>
          签到再额外获得<span class="highlight">5朵</span>鲜花(每日限领一次)
        </p>
        <p class="sign-in-rule-item">
          <span class="flower-icon">✿</span>
          连续签到奖励更多惊喜
        </p>
      </div>
      <div class="sign-in-rule-tip">
        每天签到，增加互动机会，提高脱单几率哦～
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { Dialog } from 'vant';

export default {
  name: 'SignInRuleDialog',
  components: {
    [Dialog.Component.name]: Dialog.Component,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showDialog: this.value,
    };
  },
  watch: {
    value(val) {
      this.showDialog = val;
    },
    showDialog(val) {
      this.$emit('input', val);
    },
  },
  methods: {
    onConfirm() {
      this.$emit('confirm');
    },
    onCancel() {
      this.$emit('cancel');
    },
  },
};
</script>

<style lang="scss" scoped>
.sign-in-rule-content {
  padding: 15px 0;

  .sign-in-rule-header {
    text-align: center;
    margin-bottom: 10px;

    .sign-in-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      box-shadow: 0 4px 12px rgba(255, 107, 139, 0.2);
    }
  }

  .sign-in-rule-title {
    font-size: 18px;
    font-weight: bold;
    color: #ff6b8b;
    margin-bottom: 15px;
    text-align: center;
  }

  .sign-in-rule-items {
    text-align: left;
    line-height: 1.5;
    background-color: #fff5f7;
    border-radius: 12px;
    padding: 15px;
    margin: 0 10px;

    .sign-in-rule-item {
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      font-size: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .flower-icon {
        color: #ff6b8b;
        font-weight: bold;
        margin-right: 8px;
        font-size: 16px;
      }

      .highlight {
        color: #ff6b8b;
        font-weight: bold;
        margin: 0 3px;
      }
    }
  }

  .sign-in-rule-tip {
    font-size: 12px;
    color: #999;
    margin-top: 15px;
    text-align: center;
  }
}

// 全局样式覆盖
::v-deep .van-dialog {
  border-radius: 12px;
  overflow: hidden;
  width: 85%;
  max-width: 320px;

  .van-dialog__header {
    padding-top: 20px;
    font-weight: 600;
    font-size: 18px;
    color: #333;
  }

  .van-dialog__content {
    padding: 20px 10px;
  }

  .van-dialog__footer {
    padding: 0 15px 15px;

    .van-button {
      border-radius: 20px;
      font-size: 16px;
      font-weight: 500;
      height: 40px;
      line-height: 38px;
    }

    .van-dialog__cancel {
      margin-right: 10px;
      color: #666;
    }
  }
}
</style>
