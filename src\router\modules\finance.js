import { handleError } from '../error-handler';

const routers = [
  {
    path: '/finance/index',
    name: 'FinanceIndex',
    meta: {
      title: '我的收益',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/index.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/finance/withdrawal',
    name: 'FinanceWithdrawal',
    meta: {
      title: '申请提现',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/withdrawal.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/finance/withdrawal/result',
    name: 'FinanceWithdrawalResult',
    meta: {
      title: '申请提现结果页',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/withdrawal-result.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/finance/withdrawal/record',
    name: 'WithdrawalRecord',
    meta: {
      title: '提现记录',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/withdrawal-record.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/finance/password/set',
    name: 'FinancePasswordEdit',
    meta: {
      title: '支付密码设置',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/set-password.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/finance/sms/verify',
    name: 'SmsVerify',
    meta: {
      title: '手机验证码',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/sms-code.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/finance/bank/set',
    name: 'FinanceBankEdit',
    meta: {
      title: '提现账户配置',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/set-bank.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/distribute/apply',
    name: 'DistributeApply',
    meta: {
      title: '我要带货',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/distribute.vue').then(resolve).catch(handleError);
    },
  },
  {
    path: '/distribute/agreement',
    name: 'DistributeAgreement',
    meta: {
      title: '我要带货',
    },
    component: resolve => {
      import(/* webpackChunkName: "finance-index" */ '@/views/packages/finance/agreement.vue').then(resolve).catch(handleError);
    },
  },
];

export default routers;
// export default BaseRouter;
