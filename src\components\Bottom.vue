<template>
  <div
    class="bottom"
    :class="{ 'is-fixed': fixed }">

  </div>
</template>

<script>
export default {
  name: 'header',
  props: {
    fixed: Boolean,
    title: String,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .header-button{
    flex:.5;
  }
  .is-left{
    text-align:left;
  }
  .is-right{
    text-align:right;
  }
  .header-title{
    flex:1;
    text-align:center;
    font-size:18px;
    margin: 5px 0 0;
  }
</style>
