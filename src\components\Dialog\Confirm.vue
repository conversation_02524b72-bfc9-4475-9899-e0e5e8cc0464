<template>
  <div v-show="show">
    <div class="weui-mask" @touchmove.prevent="()=>{}"></div>
    <div class="weui-dialog">
      <div class="weui-dialog__hd" name="head">
          <strong class="weui-dialog__title">{{settings.title}}</strong>
      </div>
      <div class="weui-dialog__bd">
        <slot><span v-html="settings.content"></span></slot>
      </div>
      <div class="weui-dialog__ft" slot="foot">
        <a href="javascript:;" :class="cancelClass" @click="onCancelClick">{{settings.cancelText}}</a>
        <a href="javascript:;" :class="okClass" @click="onOkClick">{{settings.okText}}</a>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
  .reverse {
    .weui-dialog__ft {
      flex-direction: row-reverse;      
    }
  }
</style>
<script>
  export default {
    name: 'confirm',
    props: {
      options: {
        type: Object,
        default() {
          return {};
        },
      },
      counter: {
        type: Number,
        default: 1
      },
      title: {
        type: String,
        default: '',
      },
      okText: {
        type: String,
        default: '确定',
      },
      ok: {
        type: Function,
      },
      cancelText: {
        type: String,
        default: '取消',
      },
      cancel: {
        type: Function,
      },
      content: {
        type: String,
        default: 'content'
      },
    },
    data() {
      return {
        show: false,
      };
    },
    mounted() {
      this.$on('hide', () => {
        this.show = false;
      });
    },
    computed: {
      cancelClass() {
        return {
          'weui-dialog__btn': true,
          'weui-dialog__btn_default': !this.settings.reversePrimary,
          'weui-dialog__btn_primary': this.settings.reversePrimary,
        }
      },
      okClass() {
        return {
          'weui-dialog__btn': true,
          'weui-dialog__btn_default': this.settings.reversePrimary,
          'weui-dialog__btn_primary': !this.settings.reversePrimary,
        }
      },
      settings() {
        return {
          title: this.options.title || this.title,
          content: this.options.content || this.content,
          ok: this.options.ok || this.ok,
          okText: this.options.okText || this.okText,
          cancel: this.options.cancel || this.cancel,
          cancelText: this.options.cancelText || this.cancelText,
          reversePrimary: this.options.reversePrimary || this.reversePrimary,
        };
      },
    },
    methods: {
      hide() {
        this.show = false;
      },
      close() {
        // console.log('onCancelClick...', e);
        this.hide();
        // this.onCancelClick();
      },
      onCancelClick(e) {
        // console.log('onCancelClick...', e);
        this.hide();
        if (this.settings.cancel) {
          this.settings.cancel();
        }
      },
      onOkClick() {
        if (this.settings.ok) {
          const res = this.settings.ok();
          if (res === false) return;
        }
        this.hide();
      },
    },
    watch: {
      options(val) {
        this.show = true;
      },
      show(val) {
        this.$overlay(val);
      }
    }
  };
</script>
