import { getImageURL } from './image'
const AUDIOHOST = 'https://audio1.jgrm.net'
export function getAudioURL(id, params = '') {
  if (!id) {
    return ''
  }
  return `${AUDIOHOST}/${id}${params}`;
}
export default class Song {
  constructor({ id, index, cover, duration, singer, name, audioId }) {
    this.id = id;
    this.index = index;
    this.cover = getImageURL(cover);
    this.duration = duration;
    this.singer = singer;
    this.url = getAudioURL(audioId);
    this.name = name;
  }
}
export const createSong = (musicData) => {
  return new Song(musicData);
}
export const durationTrans = (a) => {
  let b = ''
  let h = parseInt(a / 3600);
  let m = parseInt(a % 3600 / 60);
  let s = parseInt(a % 3600 % 60);
  if (h > 0) {
    h = h < 10 ? '0' + h : h
    b += h + ':'
  }
  m = m < 10 ? '0' + m : m
  s = s < 10 ? '0' + s : s
  b += m + ':' + s
  return b;
}
