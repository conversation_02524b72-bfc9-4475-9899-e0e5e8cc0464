<template>
  <container @ready="init" @leave="onLeave" :forceUpdate="false">
    <x-header title="评价打分">
      <x-button  slot="left" type="back"></x-button>
    </x-header>

    <content-view ref="view" class="cmt-post-view" :status="status" @reload="reload">
        <template v-if="order.shop">
          <shop-item :order-type="order.orderType" class="cmt-shop" :shop="order.shop" ></shop-item>
        </template>

        <panel class="cmt-score" v-if="order" title="为门店服务打分">
            <div class="weui-cell">
              <div class="weui-cell__hd">
                <label class="weui-label">店面环境</label>
              </div>
              <div class="weui-cell__bd">
                <div class="align-right">
                  <rater :disabled="false" v-model="comment.environment"></rater>
                </div>
              </div>
            </div>
            <div class="weui-cell">
              <div class="weui-cell__hd">
                <!-- <label class="weui-label" v-if="order.orderType == OrderType.WASH">清洁指数</label>
                <label class="weui-label" v-else-if="order.orderType == OrderType.MAINTAIN">技术水平</label> -->
                <label class="weui-label">服务质量</label>
              </div>
              <div class="weui-cell__bd">
                <div class="align-right">
                  <rater :disabled="false" v-model="comment.cleanliness"></rater>
                </div>
              </div>
            </div>
            <div class="weui-cell">
              <div class="weui-cell__hd">
                <label class="weui-label">服务态度</label>
              </div>
              <div class="weui-cell__bd">
                <div class="align-right">
                  <rater :disabled="false" v-model="comment.attitude"></rater>
                </div>
              </div>
            </div>
        </panel>

        <!-- 之前的iOS的某个交广领航版本 -->
        <!-- <panel title="评价内容" @click.native="blurInput()" class="panel-input" :class="{'panel-inputting' : inputting}"  ref="comment">
          <div class="weui-cell"  @click.prevent.stop="focusInput()">
            <div class="weui-cell__bd">
              <div class="textarea">
                <textarea class="weui-textarea" ref="input" v-model="comment.content" @blur="blurInput()"  @click="scrollToInput" placeholder="对本次服务有什么想说的，都可以写在这里" rows="3"></textarea>
              </div>
              <div class="weui-textarea-counter">
                <span v-text="comment.content.length" :class="[comment.content.length > 200 ? 'counter-warn' : '']">0</span>/200
              </div>
            </div>
          </div>
        </panel> -->
        <panel title="评价内容" ref="comment">
          <div class="weui-cell">
            <div class="weui-cell__bd">
              <div class="textarea">
                <textarea class="weui-textarea" ref="input" v-model="comment.content"  placeholder="对本次服务有什么想说的，都可以写在这里" rows="3"></textarea>
              </div>
              <div class="weui-textarea-counter">
                <span v-text="comment.content.length" :class="[comment.content.length>200?'counter-warn':'']">0</span>/200
              </div>
            </div>
          </div>
        </panel>
        <panel title="图片">
          <div class="weui-cell">
            <div class="weui-cell__bd">
              <!-- <uploader :config="uploadSettings" v-model="comment.images"></uploader> -->
              <biz-image-upload ref="uploader" :auto-upload="false" :max-files="3" v-model="comment.images"></biz-image-upload>
            </div>
          </div>
          <div class="weui-cells weui-cells_radio">
              <label class="weui-cell weui-check__label" for="s11">
                <div class="weui-cell__hd">
                  <input type="checkbox" class="weui-check" v-model="comment.anonymous" :true-value="1" :false-value="0" name="checkbox1" id="s11">
                  <i class="weui-icon-checked"></i>
                </div>
                <div class="weui-cell__bd">
                    <div class="anonymous-tip">匿名评论
                      <span>头像和昵称将会以匿名形式展示</span>
                    </div>
                </div>
              </label>
          </div>
        </panel>

        <div class="btn-area">
          <a href="javascript:;" class="weui-btn weui-btn_primary" @click="postComment">{{order.comment ? '提交修改': '发布评价'}}</a>
        </div>
    </content-view>
  </container>
</template>
<style lang="scss">
.cmt-post-view {
  .counter-warn {
    font-weight:700;
    color:red;
  }
  .weui-textarea{
    font-size:14px;
  }
  .shop-sales, .shop-distance{
    display:none;
  }
  .cmt-shop {
    background: white;
    padding: 10px;
    border-bottom: 1px solid #eaeaea;
    .shop-address{
      width:100%;
    }
  }
  .cmt-score {
    .vux-rater-box{
      width:22px;
      height:22px;
      background-size:20px;
    }
  }
  .anonymous-tip {
    margin-left: 5px;
    >span {
      font-size: 0.8em;
      color: gray;
    }
  }
}
</style>
<script>
import { dialog, toast, loading, back } from '@/bus';
import { formatDate } from '@/utils';
import { Panel, Rater } from '@/components';
// import BizImageUpload from '@/components/biz/BizImageUpload';
import ShopInfo from '@/views/components/ShopInfo';
import { OrderStatus, AppStatus, OrderType } from '@/enums';
import { getOrderDetail, postOrderComment, updateOrderComment } from '@/api';

export default {
  name: 'post-comment',
  components: {
    Rater,
    Panel,
    ShopItem: ShopInfo,
    // BizImageUpload
  },
  data() {
    const that = this;
    return {
      OrderStatus,
      AppStatus,
      OrderType,
      status: AppStatus.LOADING,
      inputting: false,
      order: {
        shop: {},
        comment: null
      },
      comment: {
        environment: 0,
        cleanliness: 0,
        attitude: 0,
        content: '',
        images: [],
        anonymous: 0,
      },
    };
  },
  computed: {
  },
  methods: {
    init() {
      const orderId = this.$route.params.id;
      getOrderDetail(orderId).then(order => {
        this.order = order;
        if (order.comment) {
          const comment = order.comment;
          this.comment = {
            id: comment.id,
            anonymous: comment.anonymous,
            environment: comment.type == OrderType.MAINTAIN ? comment.mtEnvironmentScore : comment.environment,
            cleanliness: comment.type == OrderType.MAINTAIN ? comment.mtTechnicalScore : comment.cleanliness,
            attitude: comment.type == OrderType.MAINTAIN ? comment.mtAttitudeScore : comment.attitude,
            content: order.comment.content || '',
            images: order.comment.images ? order.comment.images.split(',') : [],
          };
        }
        this.status = AppStatus.READY;
      }, err => {
        this.status = AppStatus.ERROR;
        console.error(err);
      });
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    go(url) {
      this.$router.push(url);
    },
    goShop() {
      const shopId = this.order.shop.id;
      this.go(`/shop/${shopId}`);
    },
    checkComment() {
      const { environment, cleanliness, attitude, content } = this.comment;
      if (!environment || !cleanliness || !attitude) return '请对门店服务进行评分！';
      if (content && content.length > 200) return '评论内容最多200字！';
      return false;
    },
    // 执行异步队列上传图片
    uploadImages() {
      return this.$refs.uploader.submit();
    },
    postComment() {
      const that = this;
      const errTip = this.checkComment();
      if (errTip) {
        toast().tip(errTip);
        return;
      }
      const showError = dialog().alert;
      loading(true, '正在上传图片...');
      that.uploadImages().then(() => {
        loading(true, '正在提交评价...');
        const params = { ...that.comment };
        params.images = that.comment.images.join(',');
        params.oid = that.order.id;
        const promiseComment = that.order.comment ? updateOrderComment(params) : postOrderComment(params);
        promiseComment.then(() => {
          loading(false);
          toast().tip('评价成功');

          /**
              2018.07.09
              延迟后退，让用户能看到评价成功提示
              由于评价页面有输入框，从无原生标题栏的页面跳转到此页面时可能采用pushWebView方法
              因此上一页与当前页可能不在一个webview中，toast提示是非原生控件，不能跨webview展示
              因此，若不延迟，用户可能看不到成功提示
              */
          setTimeout(() => {
            back();
          }, 1000);
        }, msg => {
          loading(false);
          showError(msg);
        });
      }, err => {
        showError(err, { title: '上传图片失败' });
        loading(false);
      }).catch(e => {
        loading(false);
        console.error(e);
        showError('图片上传失败！');
      });
    },
    formatDate(t, style = 'YYYY-MM-DD HH:MM:ss') {
      return formatDate(t, style);
    },
  }
};
</script>
