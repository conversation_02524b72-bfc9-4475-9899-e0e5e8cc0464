
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <script src="iconfont.js"></script>

    <style type="text/css">
        .icon {
          /* 通过设置 font-size 来改变图标大小 */
          width: 1em; height: 1em;
          /* 图标和文字相邻时，垂直对齐 */
          vertical-align: -0.15em;
          /* 通过设置 color 来改变 SVG 的颜色/fill */
          fill: currentColor;
          /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
             normalize.css 中也包含这行 */
          overflow: hidden;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tupian"></use>
                    </svg>
                    <div class="name">图片</div>
                    <div class="fontclass">#icon-tupian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-jiajian02"></use>
                    </svg>
                    <div class="name">加</div>
                    <div class="fontclass">#icon-jiajian02</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-jinggao"></use>
                    </svg>
                    <div class="name">警告</div>
                    <div class="fontclass">#icon-jinggao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-guanbi"></use>
                    </svg>
                    <div class="name">关闭</div>
                    <div class="fontclass">#icon-guanbi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-daohang"></use>
                    </svg>
                    <div class="name">导航</div>
                    <div class="fontclass">#icon-daohang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dianhua"></use>
                    </svg>
                    <div class="name">电话</div>
                    <div class="fontclass">#icon-dianhua</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-gonggao"></use>
                    </svg>
                    <div class="name">公告</div>
                    <div class="fontclass">#icon-gonggao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-zuojiantou"></use>
                    </svg>
                    <div class="name">左箭头</div>
                    <div class="fontclass">#icon-zuojiantou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-vip"></use>
                    </svg>
                    <div class="name">vip</div>
                    <div class="fontclass">#icon-vip</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-youjiantou"></use>
                    </svg>
                    <div class="name">右箭头</div>
                    <div class="fontclass">#icon-youjiantou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shangjiantou"></use>
                    </svg>
                    <div class="name">上箭头</div>
                    <div class="fontclass">#icon-shangjiantou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xiajiantou"></use>
                    </svg>
                    <div class="name">下箭头</div>
                    <div class="fontclass">#icon-xiajiantou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dingdan"></use>
                    </svg>
                    <div class="name">订单</div>
                    <div class="fontclass">#icon-dingdan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shoucang"></use>
                    </svg>
                    <div class="name">收藏</div>
                    <div class="fontclass">#icon-shoucang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-time"></use>
                    </svg>
                    <div class="name">时间</div>
                    <div class="fontclass">#icon-time</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-weihuguanli"></use>
                    </svg>
                    <div class="name">维护管理</div>
                    <div class="fontclass">#icon-weihuguanli</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dianhua1"></use>
                    </svg>
                    <div class="name">电话</div>
                    <div class="fontclass">#icon-dianhua1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-loading"></use>
                    </svg>
                    <div class="name">Loading</div>
                    <div class="fontclass">#icon-loading</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-ditu"></use>
                    </svg>
                    <div class="name">地图</div>
                    <div class="fontclass">#icon-ditu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shoucang1"></use>
                    </svg>
                    <div class="name">收藏</div>
                    <div class="fontclass">#icon-shoucang1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-phone"></use>
                    </svg>
                    <div class="name">电话</div>
                    <div class="fontclass">#icon-phone</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-p-reply-comment"></use>
                    </svg>
                    <div class="name">回复评论</div>
                    <div class="fontclass">#icon-p-reply-comment</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-zhengque"></use>
                    </svg>
                    <div class="name">正确</div>
                    <div class="fontclass">#icon-zhengque</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-weibiaoti105"></use>
                    </svg>
                    <div class="name">通知</div>
                    <div class="fontclass">#icon-weibiaoti105</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-huifu"></use>
                    </svg>
                    <div class="name">回复</div>
                    <div class="fontclass">#icon-huifu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-map"></use>
                    </svg>
                    <div class="name">地址</div>
                    <div class="fontclass">#icon-map</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-editing"></use>
                    </svg>
                    <div class="name">编辑</div>
                    <div class="fontclass">#icon-editing</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-che"></use>
                    </svg>
                    <div class="name">车</div>
                    <div class="fontclass">#icon-che</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-share"></use>
                    </svg>
                    <div class="name">分享</div>
                    <div class="fontclass">#icon-share</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-guzhangjiance"></use>
                    </svg>
                    <div class="name">故障检测</div>
                    <div class="fontclass">#icon-guzhangjiance</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-touxiang1"></use>
                    </svg>
                    <div class="name">头像</div>
                    <div class="fontclass">#icon-touxiang1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-daohang1"></use>
                    </svg>
                    <div class="name">导航</div>
                    <div class="fontclass">#icon-daohang1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tongzhi"></use>
                    </svg>
                    <div class="name">通知</div>
                    <div class="fontclass">#icon-tongzhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-service"></use>
                    </svg>
                    <div class="name">客服</div>
                    <div class="fontclass">#icon-service</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-nav"></use>
                    </svg>
                    <div class="name">导航</div>
                    <div class="fontclass">#icon-nav</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-search"></use>
                    </svg>
                    <div class="name">搜索-搜索</div>
                    <div class="fontclass">#icon-search</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-refresh"></use>
                    </svg>
                    <div class="name">刷新</div>
                    <div class="fontclass">#icon-refresh</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shop"></use>
                    </svg>
                    <div class="name">店铺</div>
                    <div class="fontclass">#icon-shop</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-delete"></use>
                    </svg>
                    <div class="name">删除</div>
                    <div class="fontclass">#icon-delete</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-qa"></use>
                    </svg>
                    <div class="name">常见问题</div>
                    <div class="fontclass">#icon-qa</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-inspection-mian"></use>
                    </svg>
                    <div class="name">免检审车</div>
                    <div class="fontclass">#icon-inspection-mian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-inspection-shen"></use>
                    </svg>
                    <div class="name">上线审车图标</div>
                    <div class="fontclass">#icon-inspection-shen</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-service-cn"></use>
                    </svg>
                    <div class="name">客服</div>
                    <div class="fontclass">#icon-service-cn</div>
                </li>
            
        </ul>


        <h2 id="symbol-">symbol引用</h2>
        <hr>

        <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
        这种用法其实是做了一个svg的集合，与另外两种相比具有如下特点：</p>
        <ul>
          <li>支持多色图标了，不再受单色限制。</li>
          <li>通过一些技巧，支持像字体那样，通过<code>font-size</code>,<code>color</code>来调整样式。</li>
          <li>兼容性较差，支持 ie9+,及现代浏览器。</li>
          <li>浏览器渲染svg的性能一般，还不如png。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-symbol-">第一步：引入项目下面生成的symbol代码：</h3>
        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;</span></code></pre>
        <h3 id="-css-">第二步：加入通用css代码（引入一次就行）：</h3>
        <pre><code class="lang-js hljs javascript">&lt;style type=<span class="hljs-string">"text/css"</span>&gt;
.icon {
   width: <span class="hljs-number">1</span>em; height: <span class="hljs-number">1</span>em;
   vertical-align: <span class="hljs-number">-0.15</span>em;
   fill: currentColor;
   overflow: hidden;
}
&lt;<span class="hljs-regexp">/style&gt;</span></code></pre>
        <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-js hljs javascript">&lt;svg <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"icon"</span> aria-hidden=<span class="hljs-string">"true"</span>&gt;<span class="xml"><span class="hljs-tag">
  &lt;<span class="hljs-name">use</span> <span class="hljs-attr">xlink:href</span>=<span class="hljs-string">"#icon-xxx"</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">use</span>&gt;</span>
</span>&lt;<span class="hljs-regexp">/svg&gt;
        </span></code></pre>
    </div>
</body>
</html>
