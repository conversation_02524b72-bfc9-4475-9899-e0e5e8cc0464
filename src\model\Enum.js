export default class Enum {
  constructor(name, value, desc, disabled) {
    this.name = name;
    this.value = value;
    this.desc = desc;
    this.disabled = !!disabled;
  }

  valueOf() {
    return this.value;
  }

  toString() {
    return this.value;
  }

  toJSON() {
    return this.value;
  }

  equals(value) {
    return String(this.value) === String(value);
  }

  getName() {
    return this.name;
  }

  getDesc() {
    return this.desc;
  }

  setDesc(desc) {
    this.desc = desc;
  }

  setDisabled(flag) {
    this.disabled = flag;
  }
}
