<style lang="scss" scoped>
.vip-msg {
  background: white;
  padding-left: 10px;
  margin-top: 10px;
  font-weight: 500;
  // display: flex;
  // justify-content: center;
  .vip-msg__content {
    // flex: 1;
    display: flex;
    justify-content: space-between;
  }
  .vip-msg__title {
    font-weight: bold;
    font-size: 14px;
  }

  .vip-msg__desc {
    color: #111;
    font-size: 12px;
    font-weight: bold;
    margin-top: 15px;

    .desc-right {
      color: #999;
    }
  }
  .rmb {
    font-size: 16px;
    font-weight: 700;
    color: #ff5e1b;
    &::before {
      font-size: 14px;
    }
  }
  .vip-msg__btn {
    color: white;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 30px;
    text-align: right;
    position: relative;
  }
}
.weui-check__label:active {
  background-color: transparent;
}
.vip-icon {
  background: url(./images/vip.png) center center no-repeat;
  background-size: cover;
  width: 42px; /* px */
  height: 16px; /* px */
  display: inline-block;
  vertical-align: -3px; /* px */
  margin-right: 5px;
}
.vip-checkbox {
  background: url(./images/label-unchecked.png) center center no-repeat;
  background-size: cover;
  position: absolute;
  width: 55px;
  height: 23px;
  top: -20px;
  left: -20px;
  &.vip-checkbox-checked {
    left: -60px;
    width: 93px;
    background-image: url(./images/label-checked.png);
  }
}
.weui-icon-checked {
  width: 15px;
  height: 15px;
  line-height: initial;
  &::before {
    margin-left: 0;
    margin-right: 0;
  }
}
.weui-cells_radio .weui-check:checked + .weui-icon-checked {
  background-color: #fd4925;
  border-color: #fd4925;
}
</style>

<template>
  <div class="vip-msg">
    <div class="vip-msg__content">
      <div class="vip-msg__title">
        <!-- <span class="vip-icon"></span> -->
        勾选开通VIP会员，本单再减
        <span class="rmb">{{ formatMoney(discountAmount) }}</span>
      </div>
      <div class="vip-msg__btn">
        <!-- <input type="checkbox" v-model="joinVip"> -->
        <label class="weui-cells_radio weui-check__label">
          <input type="checkbox" class="weui-check" v-model="joinVip" />
          <span class="weui-icon-checked" @click.stop.prevent="toggle"></span>
        </label>
        <span
          class="vip-checkbox"
          :class="{ 'vip-checkbox-checked': joinVip }"
        ></span>
      </div>
    </div>
    <div class="vip-msg__desc">
      <slot name="desc">开通VIP会员，专享11项特权</slot>
    </div>
  </div>
</template>
<script>
/**
 * 是否开通vip会员的checkbox
 */
import { formatMoney } from '@/utils';
import { Icon, Collapse, CollapseItem } from 'vant';
export default {
  name: 'VipJoiningCheckbox',
  props: {
    // 开通vip后可优惠金额
    discountAmount: {
      type: Number,
      default() {
        return 0;
      }
    },
    value: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  components: {
    [Icon.name]: Icon,
    [Collapse.name]: Collapse,
    [CollapseItem.name]: CollapseItem
  },
  data() {
    return {
      joinVip: this.value
    };
  },
  watch: {
    joinVip(val) {
      this.$emit('input', val);
    }
  },
  methods: {
    ...{ formatMoney },
    toggle(e) {
      // console.log(e);
      this.joinVip = !this.joinVip;
    }
  }
};
</script>
