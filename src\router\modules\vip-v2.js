import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';
import { back } from '@/bus';

export default [
  {
    path: '/vip',
    name: '交广领航VIP-v2',
    redirect: '/vip/rights',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-Home.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
    children: [
      {
        path: '/vip/rights',
        name: '会员权益',
        component: resolve => {
          import(
            /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-Rights/VIP-Rights.vue'
          )
            .then(resolve)
            .catch(handleError);
        },
      },
      {
        path: '/vip/guide',
        name: '省钱攻略',
        component: resolve => {
          import(
            /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-Rights/VIP-Guide.vue'
          )
            .then(resolve)
            .catch(handleError);
        },
      },
      {
        path: '/vip/user',
        name: '我的会员',
        component: resolve => {
          import(
            /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-Rights/VIP-User.vue'
          )
            .then(resolve)
            .catch(handleError);
        },
      },
    ],
  },
  {
    path: '/vip/buy/record',
    name: '开通记录',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-Buy-Records.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/share',
    name: '现金红包-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-Share/VipShare.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/share/rules',
    name: '分享红包规则-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-Share/VipShareRules.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/share/invite',
    name: '分享邀请-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-interests" */ '@/views/packages/vip-v2/VIP-ShareInvite/VIP-ShareInvite.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/rights/detail',
    name: '交广领航VIP-权益详情-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-interest" */ '@/views/packages/vip-v2/VIP-Rights/VIP-RightsDetail.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/contact/customer',
    name: '联系客服',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-interest" */ '@/views/packages/vip-v2/VIP-Rights/_ContactCustomer.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/buy',
    name: '购买交广领航VIP-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-buy" */ '@/views/packages/vip-v2/VIP-Buy/VIP-Buy.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/buy/fill-icode',
    name: '输入邀请码-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-buy" */ '@/views/packages/vip-v2/VIP-Buy_FillInviteCode.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/exchange',
    name: '兑换会员-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-buy" */ '@/views/packages/vip-v2/VIP-Exchange.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/save/list',
    name: '省钱记录',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-save" */ '@/views/packages/vip-v2/VIP-Saved/VIP-SaveRecord.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/saved/ranking',
    name: '省钱小达人',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-save" */ '@/views/packages/vip-v2/VIP-Saved/VIP-Savings-Ranking.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/save/rules',
    name: '省钱计算说明',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-save" */ '@/views/packages/vip-v2/VipSavedMoneyRules.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/gift/receive',
    name: '礼品领取',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-gift" */ '@/views/packages/vip-v2/VIP-Gift/VIP-GiftReceive.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/gift/box',
    name: '礼品领取盲盒',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-gift" */ '@/views/packages/vip-v2/VIP-Gift/VIP-GiftReceiveBox.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/gift/bag',
    name: 'giftBag',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-gift" */ '@/views/packages/vip-v2/VIP-Gift/VIP-GiftBag.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/gift/record',
    name: '领取记录',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-gift" */ '@/views/packages/vip-v2/VIP-Gift/VIP-GiftRecord.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/exchange/jgyh',
    name: '兑换交广银合VIP会员-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-exchange-jgyh" */ '@/views/packages/vip-v2/VIP-ExchangeOfJgyh.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/qa',
    name: 'vip-qa-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-qa" */ '@/views/packages/vip-v2/VipQa.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/agreement',
    name: 'VIP会员-用户协议-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-exchange" */ '@/views/packages/vip-v2/VipAgreement.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/result',
    name: 'VIP-提示-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "vip-result" */ '@/views/packages/vip-v2/VIP-Result.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/invite',
    name: '我的邀请-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "my-invite" */ '@/views/packages/vip-v2/MyInvite.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/invite/records',
    name: '我的邀请记录-v2',
    component: resolve => {
      import(
        /* webpackChunkName: "my-invite-records" */ '@/views/packages/vip-v2/MyInviteRecords.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/combined',
    name: '联合会员',
    component: resolve => {
      import(
        /* webpackChunkName: "my-invite-records" */ '@/views/packages/vip-combined/index.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/vip/combined/rule',
    name: '联合会员说明',
    component: resolve => {
      import(
        /* webpackChunkName: "my-invite-records" */ '@/views/packages/vip-combined/union-member-rule.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
];
