import VueRouter from '@/lib/vue-router.esm.js';
// import VueRouter from 'vue-router';
import {
  push,
} from '@/bus';

export default class Router extends VueRouter {
  // 覆盖push方法，在 replace 操作前通知SPA做一些准备工作
  push(...args) {
    push();
    // clearTimeout(this._push_timeCode);
    // 注：transition组件的name值变更后似乎需要一定时间反应，若反应时间不够，其动画可能与其name不一致
    // 测试发现，delay值较小时，在iOS12端的webview中，切换页面，能稳定复现transition组件的此问题，增大此值到一定程度，异常现象降低，但似乎偶尔还会出现异常
    // iOS12以下，暂未见异常
    const delay = 10;
    this._push_timeCode = setTimeout(() => {
      VueRouter.prototype.push.call(this, ...args);
    }, delay);
  }

  // 覆盖 replace 方法，在 replace 操作前通知SPA做一些准备工作
  replace(...args) {
    push();
    setTimeout(() => {
      VueRouter.prototype.replace.call(this, ...args);
    }, 10);
  }
}
