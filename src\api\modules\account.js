import { doGet, doPost } from '../request/';
import APIs from '../apis';
import { saveTokenToStorage } from '@/store/storage';

APIs.extend({
  // 获取用户个人资料信息
  '/user/info': '/Auth/v3/user/info',
  // 获取手机号验证码
  '/phone/code': '/Auth/phone/code',

  // 用户登录-获取手机号验证码
  '/auth/phone/code': '/Auth/v3/auth/verificate/code',

  // 用户登录-获取手机号验证码——2024-1-12 AES加密手机号
  '/auth/phone/code/v2': '/Auth/v3/auth/verificate/code/v2',

  // 用户登录-手机号验证码登录
  '/login/by/phonecode': '/Auth/v3/login/or/regist/by/phonecode',

  // 根据第三方app传递的参数获取session
  '/get/thirdapp/session': '/Auth/v3/third/platform/user/bind',

  '/account/paypass/reset': '/app/car/reset/pay/pass',
  '/account/paypass/getcode': '/app/car/phone/code',

  // 提交充值订单
  '/account/charge': '/app/car/wallet/charge/create',

  // 提交充值订单（新）— 2019年3月28日11:16:32
  '/account/topup': '/app/car/wallet/charge/order/create',

  // 获取充值套餐列表
  '/charge/packages': '/app/car/wallet/charge/items',

  // 提交账户余额支付请求
  '/account/pay': '/account/v3/wallet/pay',

  // 获取账户信息
  '/account/info': '/account/v3/wallet',

  // 红包账户信息
  '/account/redpocket': '/account/v3/red/envelope/info',
  // 会员类型
  '/vip/ads/infos': '/Radio/vip/ads/infos',
  // 我的服务
  '/user/mycenter/list': '/Radio/mycenter/menu/list',
  // 订单模块
  '/user/order/list': '/Radio/mycenter/order/menu/list',

  // 红包记录
  '/account/redpocket/records': '/account/v3/red/envelope/detail/list',

  // 账户交易记录
  '/account/trades': '/account/v3/wallet/list',

  /* 汽车保养 */

  // 我的车辆信息
  '/account/mycar': '/app/car/mycar/info',

  // 我的车辆列表
  '/account/mycars': '/app/car/mycar/list',

  // 添加车辆
  '/account/mycar/add': '/app/car/mycar/add',

  // 更新车辆信息
  '/account/mycar/update': '/app/car/mycar/update',

  // 更新车辆信息
  '/account/mycar/setdefault': '/app/car/mycar/set/default',

  // 删除车辆
  '/account/mycar/remove': '/app/car/mycar/delete',

  // 汽车品牌列表
  '/car/filter/brands': '/app/car/brand/list',

  // 汽车品牌车系列表
  '/car/filter/systems': '/app/car/carsystem/list',

  // 汽车型号详情
  '/car/model/info': '/app/car/model/info',

  // 汽车型号列表
  '/car/type/list': '/app/car/carmodel/list',

  'newEnergy/car/brand/list': '/app/car/newEnergy/car/brand/list', // 新能源汽车品牌列表
  'newEnergy/car/series/list': '/app/car/newEnergy/car/series/list', // 新能源汽车品牌车系列表
  'newEnergy/car/model/list': '/app/car/newEnergy/car/model/list', // 新能源汽车品牌车型列表
  'newEnergy/car/model/info': '/app/car/newEnergy/car/model/info', // 新能源车型详情

  'get/carinfo/task': '/app/car/get/carinfo/task', // 添加爱车奖励金币数
});

/**
 * 手机验证码登录
 * @param data
 * @returns {Promise}
 */
export function loginByPhoneCode(data) {
  return doPost(APIs.get('/login/by/phonecode'), data);
}

/**
 * 获取用户账户资料信息
 */
export function getUserAccountInfo() {
  return doGet(APIs.get('/account/info'))
    .then(res => {
      // 可临时修改balance，方便测试
      // res.balance = 5.23;
      return res;
    })
    .catch(err => {
      return Promise.reject(err);
    });
}

/**
 * 获取用户红包账户信息
 */
export function getUserRedPocketAccountInfo() {
  return doGet(APIs.get('/account/redpocket'))
    .then(res => {
      // 可临时修改balance，方便测试，之后一定记得注释掉
      // res.balance = 87;
      return res;
    })
    .catch(err => {
      return Promise.reject(err);
    });
}

/**
 * 获取会员类型
 */
export function getVipAdsInfo() {
  return doGet(APIs.get('/vip/ads/infos'), { v: 458 })
    .then(res => {
      return res;
    })
    .catch(err => {
      return Promise.reject(err);
    });
}

/**
 * 我的服务
 */
export function getMycenterList() {
  return doGet(APIs.get('/user/mycenter/list'))
    .then(res => {
      return res;
    })
    .catch(err => {
      return Promise.reject(err);
    });
}

/**
 * 订单模块
 */
export function getUserOrderList() {
  return doGet(APIs.get('/user/order/list'))
    .then(res => {
      return res;
    })
    .catch(err => {
      return Promise.reject(err);
    });
}

/**
 * 获取红包记录
 */
export function getUserRedPocketRecords(data) {
  const params = Object.assign({}, data);
  return doGet(APIs.get('/account/redpocket/records'), params);
}

/**
 * 获取汽车品牌列表
 */
export function getCarBrandList() {
  return doGet(APIs.get('/car/filter/brands'));
}

/**
 * 获取汽车车系列表
 * @param { number } brand 汽车品牌id
 */
export function getCarSystemList(brand) {
  const params = {
    brandId: brand,
  };
  return doGet(APIs.get('/car/filter/systems'), params);
}

/**
 * 获取汽车型号列表
 * @param { number } carSystem 车系id
 */
export function getCarList(carSystem) {
  const params = {
    carSysId: carSystem,
  };
  return doGet(APIs.get('/car/type/list'), params);
}

/**
 * 获取车型详细信息
 * @param {*} carModelId
 */
export function getCarModelInfo(carModelId) {
  const params = {
    carModelId: carModelId,
  };
  return doGet(APIs.get('/car/model/info'), params);
}

/**
 * 更新我的爱车
  params: {
   id : string; //车辆id
   number? : string; //车牌号
   type? : string; //车型
   distance? : number; // 行驶里程
   id6? : string; //车辆识别码后6位
   date? : string; //上路事件
  }
 */
// ?registNumber=豫A61SY6&buyTime=*************&mileage=5400&identificationCode=123456&id=2
export function updateMyCar(data) {
  const params = {
    id: data.id,
    registNumber: data.number,
    buyTime: data.date,
    mileage: data.distance,
    identificationCode: data.id6,
    modelId: data.type,
    effectiveDate: data.effectiveDate,
    registDate: data.registDate,
    ownerName: data.ownerName,
  };
  return doPost(APIs.get('/account/mycar/update'), params);
}

/**
 * 添加爱车
 * @param {string} id 车型id
 */
export function addMyCar(id) {
  const params = {
    modelId: id,
  };
  return doPost(APIs.get('/account/mycar/add'), params);
}

/**
 * 添加爱车
 * @param {*} data
 */
export function addMyCarV2(data) {
  const params = {
    carno: data.number,
    modelId: data.model,
    registDate: data.registDate,
    effectiveDate: data.effectiveDate,
  };
  return doPost(APIs.get('/account/mycar/add'), params);
}

/**
 * 获取汽车品牌列表
 */
export function getNewEnergyCarBrandList() {
  return doGet(APIs.get('newEnergy/car/brand/list'));
}

/**
 * 获取汽车车型列表
 * @param { number } brand 汽车品牌id
 */
export function getNewEnergyCarSystemList(brand) {
  const params = {
    brandId: brand,
  };
  return doGet(APIs.get('newEnergy/car/series/list'), params);
}

/**
 * 获取汽车型号列表
 * @param { number } carSystem 车系id
 */
export function getNewEnergyCarList(carSystem) {
  const params = {
    carSysId: carSystem,
  };
  return doGet(APIs.get('newEnergy/car/model/list'), params);
}

/**
 * 获取车型详细信息
 * @param {*} carModelId
 */
export function getNewEnergyCarModelInfo(carModelId) {
  const params = {
    id: carModelId,
  };
  return doGet(APIs.get('newEnergy/car/model/info'), params);
}

/**
 * 添加爱车
 * @param {*} data
 */
export function addMyCarNewEnergy(data) {
  const params = {
    newEnergy: true,
    ownerName: data.ownerName,
    identificationCode: data.identificationCode,
    carno: data.number,
    modelId: data.model,
    registDate: data.registDate,
    effectiveDate: data.effectiveDate,
  };
  return doPost(APIs.get('/account/mycar/add'), params);
}

/**
 *删除我的爱车
 * @param {string} id 车辆id
 */
export function removeMyCar(id) {
  return doPost(APIs.get('/account/mycar/remove'), {
    id,
  });
}

export function getCarinfoTask() {
  return doGet(APIs.get('get/carinfo/task'));
}

/**
 * 设置默认爱车
 * @param {string} id 车辆id
 */
export function setMyDefaultCar(id) {
  return doPost(APIs.get('/account/mycar/setdefault'), {
    id,
  });
}

/**
 * 获取我的汽车列表
 */
export function getMyCars() {
  return doGet(APIs.get('/account/mycars'));
}

/**
 * 获取我的默认车辆
 * 没有默认车辆时，取第一辆
 */
export function getMyDefaultCar() {
  return getMyCars().then(res => {
    if (res && res.length) {
      const defaultCar = res.find(item => item.isDefault);
      return defaultCar || (res.length ? res[0] : null);
    }
  });
}

/**
 * 获取车辆详细信息
 */
export function getMyCar(id) {
  return doPost(APIs.get('/account/mycar'), {
    id,
  });
}

export async function getAccountViewData() {
  const user = await doGet(APIs.get('/user/info'));
  const account = await doGet(APIs.get('/account/info'));
  // const uid = account.uid;
  const trades = await doGet(APIs.get('/account/trades'), {
    page: 1,
  });
  return Promise.resolve({
    account,
    user,
    trades,
  });
}

/**
 * 获取账户交易记录
 * @param {object} params
 */
export async function getAccountTrades(params) {
  const trades = await doGet(APIs.get('/account/trades'), params);
  return Promise.resolve(trades);
}

/**
 * 获取充值套餐列表
 */
export function getChargePackages() {
  return doGet(APIs.get('/charge/packages'));
}

/**
 * 账户余额支付
 * @param {*} data
 */
export function accountPay(data) {
  return doPost(APIs.get('/account/pay'), data);
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return doGet(APIs.get('/user/info'));
}

/**
 * 获取手机号验证码
 */
export function getPhoneCode(params) {
  return doGet(APIs.get('/phone/code'), params);
}

/**
 * 获取手机号验证码
 */
export function getAuthPhoneCode(params) {
  return doGet(APIs.get('/auth/phone/code'), params);
}

/**
 * 获取手机号验证码-2024-1-12 AES加密手机号
 */
export function getAuthPhoneCodeV2(params) {
  return doGet(APIs.get('/auth/phone/code/v2'), params);
}

/**
 * 创建充值订单
 * @param {*} cid
 */
export function submitChargeOrder(cid) {
  return doPost(APIs.get('/account/charge'), {
    cid,
  });
}

/**
 * 提交账户充值订单
 * @param {sting|number} cid 套餐id
 * @param {number} rechargeAmount 充值金额，仅当套餐为自定义金额套餐时有效
 */
export function submitTopUpOrder(cid, rechargeAmount = 0) {
  return doPost(APIs.get('/account/topup'), {
    cid,
    rechargeAmount,
  });
}

/**
 * 重置支付密码
 * @param data
 * @returns {Promise}
 */
export function resetPayPassword(data) {
  return doPost(APIs.get('/account/paypass/reset'), data);
}

/**
 * 请求重置支付密码的验证码
 * @param data
 * @returns {Promise}
 */
export function getResetPayPasswordCode(data) {
  return doGet(APIs.get('/account/paypass/getcode'), data);
}

/**
 * 注销登录状态
 */
export function logout() {
  saveTokenToStorage('');
}

/**
 * 获取用户sessionid，目前仅在第三方app中加载时，使用此接口获取sessionid
 * @param {*} data
 */
export function getThirdAppSessionId(data) {
  return doPost(APIs.get('/get/thirdapp/session'), data);
}
