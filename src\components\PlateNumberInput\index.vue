<template>
  <div class="so-mask">
    <div class="so-plate animation-scale-up">
      <div class="so-plate-head">
        <div class="so-plate-type">
          <van-radio-group
            @change="typeChange"
            v-model="type"
            direction="horizontal"
            checked-color="#FD4925"
          >
            <van-radio :name="1">普通车牌</van-radio>
            <van-radio :name="2">新能源车牌</van-radio>
          </van-radio-group>
        </div>
      </div>
      <div class="so-plate-body">
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 0 }"
          @click="inputSwitch"
          data-index="0"
        >
          <span>{{ currentInputValue[0] }}</span>
        </div>
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 1 }"
          @click="inputSwitch"
          data-index="1"
        >
          <span>{{ currentInputValue[1] }}</span>
        </div>
        <div class="so-plate-dot"></div>
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 2 }"
          @click="inputSwitch"
          data-index="2"
        >
          <span>{{ currentInputValue[2] }}</span>
        </div>
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 3 }"
          @click="inputSwitch"
          data-index="3"
        >
          <span>{{ currentInputValue[3] }}</span>
        </div>
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 4 }"
          @click="inputSwitch"
          data-index="4"
        >
          <span>{{ currentInputValue[4] }}</span>
        </div>
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 5 }"
          @click="inputSwitch"
          data-index="5"
        >
          <span>{{ currentInputValue[5] }}</span>
        </div>
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 6 }"
          @click="inputSwitch"
          data-index="6"
        >
          <span>{{ currentInputValue[6] }}</span>
        </div>
        <div
          class="so-plate-word"
          :class="{ active: currentInputIndex == 7 }"
          @click="inputSwitch"
          v-if="type == 2"
          data-index="7"
        >
          <span>{{ currentInputValue[7] }}</span>
        </div>
      </div>
      <div class="so-plate-foot">
        <div class="so-plate-keyboard" :style="{ height: keyboardHeight }">
          <div id="keyboard" ref="keyboard">
            <template v-if="inputType == 1">
              <div
                class="so-plate-key"
                v-for="el of provinceText"
                :key="el"
                :data-value="el"
                @click="chooseKey"
              >
                {{ el }}
              </div>
            </template>
            <template v-if="inputType == 1">
              <span class="so-plate-key fill-block"></span>
            </template>
            <template v-if="inputType >= 3">
              <div
                class="so-plate-key"
                v-for="el of numberText"
                :key="el"
                :data-value="el"
                @click="chooseKey"
              >
                {{ el }}
              </div>
            </template>
            <template v-if="inputType >= 2">
              <div
                class="so-plate-key"
                v-for="el of wordText"
                :key="el"
                :data-value="el"
                @click="chooseKey"
              >
                {{ el }}
              </div>
            </template>
            <template v-if="inputType == 3">
              <span
                v-for="el of fillBlock"
                :key="el.num"
                class="so-plate-key fill-block"
              ></span>
            </template>
            <template v-if="inputType == 4">
              <div
                class="so-plate-key"
                v-for="el of lastWordText"
                :key="el"
                :data-value="el"
                @click="chooseKey"
              >
                {{ el }}
              </div>
            </template>
            <span v-if="inputType == 4" class="so-plate-key fill-block"></span>
          </div>
        </div>
        <div class="so-plate-btn-group">
          <div>
            <span
              class="so-plate-btn so-plate-btn--cancel"
              @click="$emit('close')"
            >
              取消
            </span>
          </div>
          <div>
            <span class="so-plate-btn so-plate-btn--delete" @click="deleteKey">
              删除
            </span>
            <span
              class="so-plate-btn so-plate-btn--submit"
              @click="exportPlate"
            >
              完成
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { RadioGroup, Radio, Button, Toast } from 'vant';
export default {
  // emit事件: typeChange export close
  name: 'PlateNumberInput',
  components: {
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Button.name]: Button,
    [Toast.name]: Toast,
  },
  props: {
    // 车牌号
    plate: {
      type: String,
    },
    // 是否新能源车牌
    isEnergy: {
      type: Boolean,
    },
  },
  data() {
    return {
      type: 1, // 车牌类型
      currentInputIndex: 0, // 当前编辑的输入框
      currentInputValue: ['', '', '', '', '', '', ''],
      fillBlock: [
        { num: 11 },
        { num: 12 },
        { num: 13 },
        { num: 14 },
        { num: 15 },
        { num: 16 },
      ], // 避免:key报错
      keyboardHeightInit: false,
      keyboardHeight: 'auto',
      provinceText: [
        '豫',
        '粤',
        '京',
        '冀',
        '沪',
        '津',
        '晋',
        '蒙',
        '辽',
        '吉',
        '黑',
        '苏',
        '浙',
        '皖',
        '闽',
        '赣',
        '鲁',
        '鄂',
        '湘',
        '桂',
        '琼',
        '渝',
        '川',
        '贵',
        '云',
        '藏',
        '陕',
        '甘',
        '青',
        '宁',
        '新',
      ],
      numberText: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
      wordText: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'J',
        'K',
        'L',
        'M',
        'N',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
      ],
      lastWordText: ['挂', '港', '学', '领', '警'],
    };
  },
  computed: {
    // 输入框类型
    inputType() {
      let type = 1;
      switch (this.currentInputIndex) {
        case 0:
          type = 1;
          break;
        case 1:
          type = 2;
          break;
        case 2:
          type = 3;
          break;
        case 3:
          type = 3;
          break;
        case 4:
          type = 3;
          break;
        case 5:
          type = 3;
          break;
        case 6:
          type = this.type == 2 ? 3 : 4;
          break;
        case 7:
          type = 4;
          break;
        default:
          type = 1;
          break;
      }
      return type;
    },
  },
  watch: {
    currentInputIndex: function (n, o) {
      if (!this.keyboardHeightInit) return;
      this.$nextTick(() => {
        this.changeKeyboardHeight();
      });
    },
  },
  mounted() {
    // console.log(this.plate);
    const plateKey = this.plate.toUpperCase().split('');
    if (plateKey.length === 7) {
      this.type = 1;
    } else if (plateKey.length === 8 || this.isEnergy) {
      this.type = 2;
    }
    // 默认车牌(非完整) 如：豫A
    if (plateKey.length > 0 && plateKey.length < 7) {
      this.currentInputValue = plateKey
        .concat(['', '', '', '', '', ''])
        .splice(0, 7); // 默认蓝牌
      this.currentInputIndex = plateKey.length;
    }
    // 默认车牌(完整)
    if (plateKey.length === 7 || plateKey.length === 8) {
      this.currentInputValue = plateKey;
      this.currentInputIndex = plateKey.length - 1;
    }

    setTimeout(() => {
      // 在动画结束之后才开始获取
      this.$nextTick(() => {
        this.changeKeyboardHeight();
      });
    }, 500);
  },
  methods: {
    // 车牌类型切换
    typeChange(type) {
      this.$emit('typeChange', type);
      this.type = parseInt(type);
      if (!this.keyboardHeightInit) return;
      this.currentInputIndex = 0;
      if (type == 1) {
        this.currentInputValue = ['', '', '', '', '', '', ''];
      } else {
        this.currentInputValue = ['', '', '', '', '', '', '', ''];
      }
    },
    inputSwitch(e) {
      const { index } = e.currentTarget.dataset;
      this.currentInputIndex = parseInt(index);
    },
    chooseKey(e) {
      const { value } = e.currentTarget.dataset;
      this.$set(this.currentInputValue, this.currentInputIndex, value);
      if (this.type == 1 && this.currentInputIndex < 6) {
        this.currentInputIndex++;
      }
      if (this.type == 2 && this.currentInputIndex < 7) {
        this.currentInputIndex++;
      }
    },
    deleteKey() {
      this.$set(this.currentInputValue, this.currentInputIndex, '');
      if (this.currentInputIndex != 0) this.currentInputIndex--;
    },
    exportPlate() {
      const plate = this.currentInputValue.join('');
      let err = false;
      if (this.type === 1 && plate.length != 7) {
        err = true;
      } else if (this.type === 2 && plate.length != 8) {
        err = true;
      }
      if (err) {
        this.$toast('请输入完整的车牌号码');
        return false;
      }
      let plateReg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/;
      if (!plateReg.test(plate)) {
        this.$toast('请核对车牌号码格式');
        return false;
      }
      this.$emit('export', plate);
    },
    changeKeyboardHeight() {
      const rect = this.$refs.keyboard.getBoundingClientRect();
      this.keyboardHeight = rect.height + 15 + 'px';
      this.keyboardHeightInit = true;
    },
  },
};
</script>
<style scoped lang="less">
@import './index';
</style>
