<template>
  <div key="loadmore" class="scroll-loading" :class="{'loading' : loading && !end}" @click="onClick">
    <span class="scroll-loading-icon"><slot>{{text}}</slot></span>
  </div>
</template>
<style lang="scss" scoped>
  .scroll-loading{
    text-align:center;
    color:#bfbfbf;
    padding:5px;
    font-size: 0.9em;
    padding: 15px;
  }
  .scroll-loading-icon {
    &::before {
      width: 24px; /* px */
      height: 24px; /* px */
      display: none;
      content: '\e60c';
      font-family: iconfont;
      animation: rotate 600ms infinite linear;
      .loading & {
        display: inline-block;
      }
    }
  }
  @keyframes rotate {
    from {
      transform: rotate(0);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
<script>
  export default {
    name: 'list-loader',
    props: {
      options: {
        type: Object,
        default() {
          return {
            loading: false,
            end: false,
            error: false,
          }
        },
      },
      emptyText: {
        type: String,
        default: '没有更多了'
      }
    },
    computed: {
      loading() {
        return this.options.loading;
      },
      error() {
        return this.options.error;
      },
      end() {
        return this.options.end;
      },
      text() {
        if (this.error) {
          return this.loading ? '正在加载...' : '加载出错，点击重试';
        }
        if (this.end) return this.emptyText;
        return this.loading ? '加载中...' : '点击加载更多';
      },
    },
    methods: {
      onClick() {
        if (this.error) {
          this.$emit('reload');
        }
        if (this.error || !this.end) {
          this.$emit('load');
        }
      }
    }
  };
</script>
