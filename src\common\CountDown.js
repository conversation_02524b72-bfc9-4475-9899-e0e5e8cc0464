export default class CountDown {
  constructor(name = 'default') {
    this.code = 0;
  }

  run(fn, seconds) {
    const that = this;
    this.stop(name);
    let remaining = seconds;
    fn(seconds);
    this.code = setInterval(() => {
      let value = --remaining;
      fn(value);
      if (value == 0) {
        that.stop(name);
      }
    }, 1000);
  }

  stop(name = 'default') {
    clearInterval(this.code);
  }
}
