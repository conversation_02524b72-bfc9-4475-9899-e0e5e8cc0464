import { doGet, doPost } from '../request/';
import { setShareConfig } from '@/bridge';
import API from '../apis';
import weapp from '@/common/env'

API.extend({
  // 微信页面签名
  'weixin/page/sign': '/Auth/wechat_pub/jsapi/config?url={url}',
  'order/pay/info': '/Radio/pay/charge',
});

// 获取js-sdk签名信息
let cachedWeixinConfig = {
  url: '',
  config: {},
};
function getWeixinSign(url) {
  let value = encodeURIComponent(url.split('#')[0]);
  const api = API.get('weixin/page/sign', { url: value });
  return new Promise((resolve, reject) => {
    if (cachedWeixinConfig.url === url) resolve(cachedWeixinConfig.config);
    else {
      doGet(api).then(config => {
        cachedWeixinConfig = {
          url, config,
        };
        resolve(config);
      }).catch(e => reject)
    }
  })
  // return doGet(api);
}

/**
 * 获取订单支付信息
 * @param {*} data
 */
export function getOrderPayInfo(data) {
  return doPost(API.get('order/pay/info'), data);
}

// 初始化微信js-sdk配置信息
export function initWeiXinConfig(url) {
  let that = this;
  return new Promise(function(resolve, reject) {
    let wx = window.wx;
    if (wx) {
      getWeixinSign(url)
        .then(function(res) {
          let config = {
            debug: window.location.search.indexOf('dev') > -1, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: res.appId, // 必填，公众号的唯一标识
            timestamp: res.timestamp, // 必填，生成签名的时间戳
            nonceStr: res.nonceStr, // 必填，生成签名的随机串
            signature: res.signature, // 必填，签名，见附录1
            // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
            jsApiList: [
              'chooseWXPay',
              'openLocation',
              'getLocation',
              'hideMenuItems',
              'onMenuShareTimeline',
              'onMenuShareAppMessage',
              'onMenuShareQQ',
              'onMenuShareWeibo',
              'onMenuShareQZone'
            ],
            openTagList: ['wx-open-launch-weapp', 'wx-open-launch-app'],
          };
          wx.config(config);
          wx.ready(function(res) {
            console.log('wx config ready', res)
            resolve();
          });
          // wx.complete(function(res) {
          //   console.log('wx config complete:', res);
          // })
          wx.error(function(res) {
            console.error('wx config error', res);
            reject(res);
          });
        })
        .catch(function(err) {
          reject(err);
        });
    } else {
      reject('微信JS-SDK未配置');
    }
  });
}
