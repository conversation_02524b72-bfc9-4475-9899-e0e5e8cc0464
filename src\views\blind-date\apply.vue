<template>
  <container @ready="onReady" @leave="onLeave" @resume="onResume">
    <x-header title="活动报名">
      <x-button slot="left" type="back" />
    </x-header>
    <content-view ref="content" :status="status" @reload="init">
      <template v-if="status === AppStatus.READY">
        <div class="registration-container">
          <!-- 步骤条 -->
          <!-- <van-steps :active="activeStep" active-color="#FF6AA8">
            <van-step>基本信息</van-step>
            <van-step>补充资料</van-step>
            <van-step>确认提交</van-step>
          </van-steps> -->
          <StepProgress :active-step="activeStep" />

          <div v-if="isEditMode" class="info-tip">
            <van-icon name="info-o" color="#f87c98" />
            <span>修改资料，系统会重新审核，审核通过后公开显示</span>
          </div>
          <van-form @submit="onSubmit" @failed="onStepFailed" v-show="activeStep === 0" :show-error-message="false">
            <!-- 第一步：基本信息 -->
            <div class="block-card">
              <div class="section-title">上传照片信息</div>

              <div class="photo-upload-section">
                <div class="photo-item">
                  <div class="photo-label">生活照</div>
                  <biz-image-upload class="uploader-photo life-photo van-clearfix" ref="lifeUploader"
                    :auto-upload="false" :max-files="6" v-model="lifePhoto">
                    <div class="upload-box" slot="uploadIconSlot">
                      <van-icon name="plus" size="24" />
                      <div>上传生活照</div>
                    </div>
                  </biz-image-upload>
                </div>
              </div>

              <div class="photo-upload-section">
                <div class="photo-label">身份证照片</div>
                <div class="id-card-uploads">
                  <biz-image-upload class="uploader-photo id-card-photo van-clearfix" ref="idCardFrontUploader"
                    :auto-upload="false" :multiple="false" :max-files="1" :upload-url="uploadUrl" uploadName="data"
                    bed="custom" :onUploaded="handleIdCardFrontUploaded" :onPreview="handleIdCardPreview"
                    :image-url-getter="imageUrlGetter" v-model="idCardFront">
                    <div class="upload-box" slot="uploadIconSlot">
                      <van-icon name="plus" size="24" />
                      <div>上传身份证正面</div>
                    </div>
                  </biz-image-upload>

                  <biz-image-upload class="uploader-photo id-card-photo van-clearfix" ref="idCardBackUploader"
                    :auto-upload="false" :multiple="false" :max-files="1" :upload-url="uploadUrl" uploadName="data"
                    bed="custom" :onUploaded="handleIdCardBackUploaded" :onPreview="handleIdCardPreview"
                    :image-url-getter="imageUrlGetter" v-model="idCardBack">
                    <div class="upload-box" slot="uploadIconSlot">
                      <van-icon name="plus" size="24" />
                      <div>上传身份证反面</div>
                    </div>
                  </biz-image-upload>
                </div>
              </div>
            </div>

            <div class="block-card">
              <div class="section-title">基本信息</div>

              <van-field v-model="formData.nickName" name="昵称" label="昵称" placeholder="请输入昵称"
                :rules="[{ required: true, message: '请填写昵称' }]" required />

              <van-field v-model="formData.name" name="姓名" label="姓名" placeholder="请输入姓名"
                :rules="[{ required: true, message: '请填写姓名' }]" required />

              <van-field v-model.number="formData.age" type="number" name="年龄" label="年龄" maxlength="2"
                placeholder="请输入年龄" :rules="[
                  { required: true, message: '请填写年龄' },
                  {
                    validator: validateAge,
                    trigger: 'blur',
                    message: '未成年禁止报名',
                  },
                ]" required />

              <van-field name="性别" label="性别" :rules="[{ required: true, message: '请选择性别' }]" required>
                <template #input>
                  <van-radio-group v-model="formData.gender" checked-color="#FF69B4" direction="horizontal">
                    <van-radio name="男" icon-size="16px">男</van-radio>
                    <van-radio name="女" icon-size="16px">女</van-radio>
                  </van-radio-group>
                </template>
              </van-field>

              <van-field name="婚姻状况" label="婚姻状况" :rules="[{ required: true, message: '请选择婚姻状况' }]" required>
                <template #input>
                  <van-radio-group v-model="formData.maritalStatus" checked-color="#FF69B4" direction="horizontal">
                    <van-radio name="未婚" icon-size="16px">未婚</van-radio>
                    <van-radio name="离异" icon-size="16px">离异</van-radio>
                    <van-radio name="丧偶" icon-size="16px">丧偶</van-radio>
                  </van-radio-group>
                </template>
              </van-field>

              <van-field name="有无子女" label="有无子女" placeholder="请选择有无子女情况" readonly clickable is-link
                :value="formData.hasChildren" @click="showPicker('hasChildren')"
                :rules="[{ required: true, message: '请选择有无子女情况' }]" required />

              <van-field v-model="formData.phone" type="tel" name="手机号" label="手机号" placeholder="请输入手机号" maxlength="11"
                :rules="[
                  { required: true, message: '请填写手机号' },
                  { pattern: /^1\d{10}$/, message: '手机号格式错误' },
                ]" required />

              <div class="wechat-field">
                <van-field v-model="formData.wechatId" name="微信号" label="微信号" placeholder="请输入微信号"
                  :rules="[{ required: true, message: '请填写微信号' }]" required />
                <van-button native-type="button" size="small" class="same-as-phone"
                  @click="usePhoneAsWechat">与手机号相同</van-button>
              </div>

              <van-field v-model="formData.idCardNumber" name="身份证号" label="身份证号" maxlength="18" placeholder="请输入身份证号码"
                :rules="[
                  { required: true, message: '请填写身份证号' },
                  {
                    pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
                    message: '身份证号格式错误',
                  },
                ]" required />

              <!-- <van-field name="checkboxGroup" label="兴趣爱好">
                <template #input>
                  <van-checkbox-group
                    v-model="formData.interests"
                    direction="horizontal"
                    checked-color="#FF69B4"
                  >
                    <van-checkbox name="运动" shape="square" icon-size="16px"
                      >运动</van-checkbox
                    >
                    <van-checkbox name="音乐" shape="square" icon-size="16px"
                      >音乐</van-checkbox
                    >
                    <van-checkbox name="阅读" shape="square" icon-size="16px"
                      >阅读</van-checkbox
                    >
                    <van-checkbox name="旅行" shape="square" icon-size="16px"
                      >旅行</van-checkbox
                    >
                  </van-checkbox-group>
                </template>
              </van-field> -->

              <van-field class="intro-field" v-model="formData.introduction" rows="4" autosize type="textarea"
                name="个人简介" label="个人简介" placeholder="请输入个人简介" maxlength="100" required />

              <!-- <van-field class="intro-field" v-model="formData.standardOfSpouseSelection" rows="4" autosize
                type="textarea" name="择偶标准" label="择偶标准" placeholder="请输入择偶标准" maxlength="100"
                :rules="[{ required: true, message: '请填写择偶标准' }]" required /> -->
            </div>

            <div class="button-container">
              <van-button round block type="primary" native-type="submit" color="#FF6AA8">下一步</van-button>
            </div>
          </van-form>

          <van-form @submit="onNextStep" @failed="onStepFailed" v-show="activeStep === 1" :show-error-message="false">
            <!-- 第二步：补充资料 -->
            <div class="info-tip">
              <van-icon name="info-o" color="#f87c98" />
              <span>填完善更多展示信息，帮助TA更好地了解你。</span>
            </div>

            <div class="block-card">
              <van-field name="购房情况" label="购房情况" placeholder="请选择购房情况" readonly clickable is-link
                :value="formData.hasHouse" @click="showPicker('hasHouse')"
                :rules="[{ required: true, message: '请选择购房情况' }]" required />

              <van-field name="购车情况" label="购车情况" :rules="[{ required: true, message: '请选择购车情况' }]" required>
                <template #input>
                  <van-radio-group v-model="formData.hasCar" checked-color="#FF69B4" direction="horizontal">
                    <van-radio name="已购车" icon-size="16px">已购车</van-radio>
                    <van-radio name="未购车" icon-size="16px">未购车</van-radio>
                  </van-radio-group>
                </template>
              </van-field>

              <van-field v-model="formData.occupation" name="职业" label="职业" placeholder="请输入职业"
                :rules="[{ required: true, message: '请填写职业' }]" required />

              <van-field v-model="formData.workplace" name="工作单位" label="工作单位" placeholder="请输入工作单位"
                :rules="[{ required: true, message: '请填写工作单位' }]" required />

              <van-field name="incomeLevel" label="收入水平" placeholder="请选择月收入水平" readonly clickable is-link
                :value="formData.incomeLevel" @click="showPicker('incomeLevel')"
                :rules="[{ required: true, message: '请选择月收入水平' }]" required />
              <van-field name="收入情况" label="收入情况" :rules="[{ required: true, message: '请选择收入情况' }]" required>
                <template #input>
                  <van-radio-group v-model="formData.showIncomeLevel" checked-color="#FF69B4" direction="horizontal">
                    <van-radio :name="1" icon-size="16px">公开展示</van-radio>
                    <van-radio :name="0" icon-size="16px">不公开展示</van-radio>
                  </van-radio-group>
                </template>
              </van-field>

              <van-field name="education" label="学历" placeholder="请选择学历" readonly clickable is-link
                :value="formData.education" @click="showPicker('education')"
                :rules="[{ required: true, message: '请选择学历' }]" required />

              <van-field v-model="formData.graduationSchool" name="毕业院校" label="毕业院校" placeholder="请输入毕业院校"
                :rules="[{ required: true, message: '请填写毕业院校' }]" required />

              <van-field name="location" label="所在区县" placeholder="请选择所在区县" readonly clickable is-link
                :value="formData.location" @click="showPicker('location')"
                :rules="[{ required: true, message: '请选择所在区县' }]" required />

              <!-- <van-field
                name="household"
                label="户籍"
                placeholder="请选择户籍"
                readonly
                clickable
                :value="formData.household"
                @click="showPicker('householdShow')"
                :rules="[{ required: true, message: '请选择户籍' }]"
                required
              /> -->

              <van-field name="race" label="民族" placeholder="请选择民族" readonly clickable is-link :value="formData.race"
                @click="showPicker('race')" :rules="[{ required: true, message: '请选择民族' }]" required />

              <van-field name="hometown" label="家乡" placeholder="请选择家乡" readonly clickable is-link
                :value="formData.hometown" @click="showPicker('hometown')"
                :rules="[{ required: true, message: '请选择家乡' }]" required />

              <van-field name="bloodType" label="血型" placeholder="请选择血型" readonly clickable is-link
                :value="formData.bloodType" @click="showPicker('bloodType')"
                :rules="[{ required: true, message: '请选择血型' }]" required />

              <van-field v-model.number="formData.weight" type="number" name="体重" label="体重" placeholder="请输入体重(KG)"
                :rules="[
                  { required: true, message: '请填写体重' },
                  {
                    validator: validateWeight,
                    trigger: 'blur',
                    message: '请输入有效的体重',
                  },
                ]" required />

              <van-field v-model.number="formData.height" type="number" name="身高" label="身高" placeholder="请输入身高(厘米)"
                :rules="[
                  { required: true, message: '请填写身高' },
                  {
                    validator: validateHeight,
                    trigger: 'blur',
                    message: '请输入有效的身高',
                  },
                ]" required />

              <van-field v-model="formData.hobbies" name="兴趣爱好" label="兴趣爱好" placeholder="请输入兴趣爱好"
                :rules="[{ required: true, message: '请填写兴趣爱好' }]" required />

              <van-field v-model="formData.talents" name="才艺" label="才艺" placeholder="请输入才艺"
                :rules="[{ required: true, message: '请填写才艺' }]" required />

              <van-field name="MBTI" label="MBTI" placeholder="请选择MBTI性格类型" readonly clickable is-link
                :value="formData.mbti" @click="showPicker('mbti')" />

            </div>

            <div class="button-container">
              <van-button round block type="primary" native-type="submit" color="#FF6AA8">下一步</van-button>
              <van-button round block plain hairline native-type="button" class="back-btn"
                @click="goBack">返回上一步</van-button>
            </div>
          </van-form>

          <van-form @submit="onHopeNextStep" @failed="onStepFailed" v-show="activeStep === 2"
            :show-error-message="false">
            <!-- 第三步：择偶要求 -->
            <div class="info-tip">
              <van-icon name="info-o" color="#f87c98" />
              <span>填写你心仪对象的条件,帮助我们为你匹配到合适的TA</span>
            </div>

            <div class="block-card">
              <van-field name="hopeAgeRange" label="Ta的年龄" placeholder="请选择TA的年龄(岁)" readonly clickable is-link
                :value="formData.hopeAgeRange" @click="showPicker('hopeAgeRange')"
                :rules="[{ required: true, message: '请选择Ta的年龄' }]" required />

              <van-field name="hopeIncomeLevel" label="Ta的月收入" placeholder="请选择Ta的月收入(元)" readonly clickable is-link
                :value="formData.hopeIncomeLevel" @click="showPicker('hopeIncomeLevel')"
                :rules="[{ required: true, message: '请选择Ta的月收入' }]" required />

              <van-field name="hopeHeight" label="Ta的身高" placeholder="请选择Ta的身高(cm)" readonly clickable is-link
                :value="formData.hopeHeight" @click="showPicker('hopeHeight')"
                :rules="[{ required: true, message: '请选择Ta的身高' }]" required />

              <van-field name="hopeMaritalStatus" label="Ta的婚姻状况" placeholder="请选择Ta的婚姻状况" readonly clickable is-link
                :value="formData.hopeMaritalStatus" @click="showPicker('hopeMaritalStatus')"
                :rules="[{ required: true, message: '请选择Ta的婚姻状况' }]" required />

              <van-field name="hopeEducation" label="Ta的学历" placeholder="请选择Ta的学历" readonly clickable is-link
                :value="formData.hopeEducation" @click="showPicker('hopeEducation')"
                :rules="[{ required: true, message: '请选择Ta的学历' }]" required />

              <van-field class="intro-field" v-model="formData.hopeOtherRequirements" rows="4" autosize type="textarea"
                name="其他要求" label="其他要求" placeholder="请输入其他期望和要求" maxlength="100" />
            </div>

            <div class="button-container">
              <van-button round block type="primary" native-type="submit" color="#FF6AA8">下一步</van-button>
              <van-button round block plain hairline native-type="button" class="back-btn"
                @click="goBack">返回上一步</van-button>
            </div>
          </van-form>

          <div v-show="activeStep === 3" class="submit-container">
            <!-- 第四步：确认提交 -->
            <div class="confirm-title">确认信息</div>
            <div class="confirm-content">
              <div class="confirm-item" v-for="(value, key) in displayFormData" :key="key">
                <div class="confirm-label">{{ getFieldLabel(key) }}</div>
                <div class="confirm-value">{{ value }}</div>
              </div>
            </div>

            <div class="button-container">
              <van-button round block type="primary" @click="submitForm" :loading="isLoading"
                color="#FF6AA8">确认提交</van-button>
              <van-button round block plain hairline native-type="button" class="back-btn"
                @click="goBack">返回上一步</van-button>
            </div>
          </div>

          <!-- 选择器弹出层 -->
          <van-popup v-model="pickerShow" position="bottom">
            <van-picker show-toolbar :columns="currentColumns" :default-index="currentDefaultIndex"
              @confirm="onPickerConfirm" @cancel="pickerShow = false" />
          </van-popup>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { playPhotos } from '@/bridge';
import {
  Form,
  Field,
  RadioGroup,
  Radio,
  Button,
  Toast,
  Popup,
  Steps,
  Step,
  Icon,
  CheckboxGroup,
  Checkbox,
  Picker,
  Uploader,
  Dialog,
} from 'vant';
import { getAeraPCAData } from '@pkg/finance/api';
import { applyActivity, applyActivityWithNeedPay, getProfile } from './api';
import StepProgress from './components/StepProgress.vue';
import { getQueryParams } from '@/utils';
export default {
  name: 'ApplyPage',
  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Steps.name]: Steps,
    [Step.name]: Step,
    [Icon.name]: Icon,
    [CheckboxGroup.name]: CheckboxGroup,
    [Checkbox.name]: Checkbox,
    [Picker.name]: Picker,
    [Uploader.name]: Uploader,
    [Toast.name]: Toast,
    [Dialog.name]: Dialog,
    StepProgress,
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      activeStep: 0,
      isEditMode: false, // 是否为编辑模式
      formData: {
        // 第一步表单数据
        idCardFront: '',
        idCardBack: '',
        nickName: '',
        name: '',
        age: '',
        gender: '男',
        maritalStatus: '未婚',
        hasChildren: '', // 添加有无子女字段
        phone: '',
        wechatId: '',
        idCardNumber: '',
        introduction: '',
        // standardOfSpouseSelection: '',

        // 第二步表单数据
        hasCar: '', // 添加购车情况
        hasHouse: '', // 添加购房情况
        mbti: '', // 添加MBTI性格类型
        occupation: '',
        workplace: '',
        incomeLevel: '',
        showIncomeLevel: '',
        education: '',
        graduationSchool: '',
        location: '',
        hometown: '',
        race: '',
        bloodType: '',
        weight: '',
        height: '',
        hobbies: '',
        talents: '',

        // 第三步表单数据 - 择偶要求
        hopeAgeRange: '',
        hopeIncomeLevel: '',
        hopeHeight: '',
        hopeMaritalStatus: '',
        hopeEducation: '',
        hopeOtherRequirements: '',

        status: '0',
      },

      // 上传图片
      uploadUrl: '/resource/upload',
      lifePhoto: [],
      idCardFront: [],
      idCardBack: [],

      // 通用选择器相关
      pickerShow: false,
      currentField: '',
      currentColumns: [],
      currentDefaultIndex: 0,

      // 选择器选项
      areaList: {},
      incomeLevelColumns: ['5千以下', '5千-1万', '1万-2万', '2万以上'],
      educationColumns: ['高中', '中专', '大专', '本科', '硕士', '博士及以上'],
      locationColumns: [],
      householdColumns: [],
      hometownColumns: [],
      defaultHometownIndex: 0,
      defaultMbtiIndex: 0,
      bloodTypeColumns: ['A型', 'B型', 'O型', 'AB型', '其他'],
      xingzuoColumns: [
        '摩羯',
        '水瓶',
        '双鱼',
        '白羊',
        '金牛',
        '双子',
        '巨蟹',
        '狮子',
        '处女',
        '天秤',
        '天蝎',
        '射手',
        '摩羯',
      ],
      sxColumns: [
        '鼠',
        '牛',
        '虎',
        '兔',
        '龙',
        '蛇',
        '马',
        '羊',
        '猴',
        '鸡',
        '狗',
        '猪',
      ],
      minzuColumns: [
        '汉族',
        '满族',
        '蒙古族',
        '回族',
        '藏族',
        '维吾尔族',
        '苗族',
        '彝族',
        '壮族',
        '布依族',
        '侗族',
        '瑶族',
        '白族',
        '土家族',
        '哈尼族',
        '哈萨克族',
        '傣族',
        '黎族',
        '傈僳(音：素)族',
        '佤族',
        '畲族',
        '高山族',
        '拉祜(音：护)族',
        '水族',
        '东乡族',
        '纳西族',
        '景颇族',
        '柯尔克孜族',
        '土族',
        '达斡(音：握)尔族',
        '仫(音：目)佬族',
        '羌族',
        '布朗族',
        '撒拉族',
        '毛南族',
        '仡佬族',
        '锡伯族',
        '阿昌族',
        '普米族',
        '朝鲜族',
        '塔吉克族',
        '怒族',
        '乌孜别克族',
        '俄罗斯族',
        '鄂温克族',
        '德昂族',
        '保安族',
        '裕固族',
        '京族',
        '塔塔尔族',
        '独龙族',
        '鄂伦春族',
        '赫哲族',
        '门巴族',
        '珞巴族',
        '基诺族',
        '其他',
      ],
      hasChildrenColumns: ['无子女', '有,和我住一起', '有,不和我住一起', '有,有时和我住一起'],
      hasHouseColumns: [
        '暂未购房',
        '需要随时可买',
        '已购住房（非郑州）',
        '已购住房（郑州）',
        '与父母同住',
        '单位住房',
      ],
      hasCarColumns: ['已购车', '未购车'], // 添加购车情况选项
      mbtiColumns: [
        'ISTJ',
        'ISFJ',
        'INFJ',
        'INFP',
        'ISTP',
        'ISFP',
        'ESTJ',
        'ESFJ',
        'ENFJ',
        'ENFP',
        'ESTP',
        'ESFP',
        'ENTJ',
        'ENTP',
      ],
      hopeAgeRangeColumns: ['不限', '20-25', '26-30', '31-35', '36-40', '41-45', '46-50', '51-55', '56-60', '60以上'],
      hopeIncomeLevelColumns: ['不限', '5千以下', '5千-1万', '1万-2万', '2万以上'],
      hopeHeightRangeColumns: ['不限', '150-159', '160-165', '166-169', '170-175', '176-180', '181-185', '186-190', '191以上'],
      hopeMaritalStatusColumns: ['未婚', '离异(无子)', '离异(有子)', '未婚或离异(无子)', '都可以'],
      hopeEducationColumns: ['不限', '高中', '中专', '大专', '本科', '硕士', '博士及以上'],
      isLoading: false,
      isBacking: false,
    };
  },
  computed: {
    displayFormData() {
      const displayData = {};
      const keysToShow = [
        'nickName',
        'name',
        'age',
        'gender',
        'maritalStatus',
        'hasChildren',
        'phone',
        'wechatId',
        'idCardNumber',
        'introduction',
        // 'standardOfSpouseSelection',
        'hasHouse',
        'hasCar',
        'occupation',
        'workplace',
        'incomeLevel',
        'showIncomeLevel',
        'education',
        'graduationSchool',
        'location',
        'hometown',
        'race',
        'bloodType',
        'weight',
        'height',
        'hobbies',
        'talents',
        'mbti',
        // 择偶要求字段
        'hopeAgeRange',
        'hopeIncomeLevel',
        'hopeHeight',
        'hopeMaritalStatus',
        'hopeEducation',
        'hopeOtherRequirements',
      ];

      keysToShow.forEach(key => {
        if (this.formData[key]) {
          displayData[key] = this.formData[key];
        }

        if (key === 'showIncomeLevel') {
          displayData[key] = this.formData[key] === 1 ? '公开展示' : '不公开展示';
        }
      });

      return displayData;
    },
  },
  watch: {
    activeStep(val) {
      this.$refs.content && this.$refs.content.animateScrollTo(0);
    },
  },
  mounted() {
    getAeraPCAData().then(res => {
      this.initAreaData(res);
    });

    // 检查是否存在编辑模式参数
    const query = this.$route.query;
    if (query.id) {
      this.isEditMode = true;
    }
  },
  methods: {
    onReady() {
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
    init() {
      // 页面初始化逻辑
      this.status = AppStatus.READY;

      // 如果是编辑模式，获取用户数据
      if (this.isEditMode) {
        this.fetchUserData();
      }
    },

    // 获取用户数据
    fetchUserData() {
      Toast.loading({
        message: '加载数据中...',
        forbidClick: true,
      });

      getProfile()
        .then(res => {
          Toast.clear();

          if (res) {
            this.fillFormData(res);
          } else {
            Toast('获取数据失败，请重试');
          }
        })
        .catch(err => {
          Toast.clear();
          Toast('获取数据失败，请重试');
          console.error('获取用户数据失败:', err);
        });
    },

    // 填充表单数据
    fillFormData(data) {
      // 填充基本字段
      const fields = [
        'nickName',
        'name',
        'age',
        'gender',
        'maritalStatus',
        'hasChildren',
        'phone',
        'wechatId',
        'idCardNumber',
        'introduction',
        // 'standardOfSpouseSelection',
        'hasHouse',
        'hasCar',
        'occupation',
        'workplace',
        'incomeLevel',
        'showIncomeLevel',
        'education',
        'graduationSchool',
        'location',
        'hometown',
        'race',
        'bloodType',
        'weight',
        'height',
        'hobbies',
        'talents',
        'mbti',
        // 择偶要求字段
        'hopeAgeRange',
        'hopeIncomeLevel',
        'hopeHeight',
        'hopeMaritalStatus',
        'hopeEducation',
        'hopeOtherRequirements',
      ];

      fields.forEach(field => {
        if (data[field] !== undefined) {
          this.formData[field] = data[field];
        }
      });
      // 初始化选择器的默认值
      this.defaultMbtiIndex = this.mbtiColumns.indexOf(this.formData.mbti) || 0;
      // 处理图片数据
      this.handleImageData(data);
    },

    // 处理图片数据
    handleImageData(data) {
      // 处理个人照片
      if (data.profileImages) {
        const images = data.profileImages.split(',');
        if (images.length > 0) {
          this.lifePhoto = images;
        }
      }

      // 处理身份证照片
      if (data.idCardImage) {
        const images = data.idCardImage.split(',');
        if (images.length >= 1) {
          this.idCardFront = [images[0]];
        }

        if (images.length >= 2) {
          this.idCardBack = [images[1]];
        }
      }
    },
    // 表单提交处理函数
    onSubmit() {
      // 验证第一步表单必填项
      if (
        !this.formData.nickName ||
        !this.formData.name ||
        !this.formData.age ||
        !this.formData.maritalStatus ||
        !this.formData.hasChildren ||
        !this.formData.phone ||
        !this.formData.wechatId ||
        !this.formData.idCardNumber ||
        !this.formData.introduction
        // ||
        // !this.formData.standardOfSpouseSelection
      ) {
        Toast('请填写所有必填项');
        return;
      }

      // 验证手机号格式
      const phoneRegex = /^1\d{10}$/;
      if (!phoneRegex.test(this.formData.phone)) {
        Toast('手机号格式错误');
        return;
      }

      // 验证身份证号格式
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardRegex.test(this.formData.idCardNumber)) {
        Toast('身份证号格式错误');
        return;
      }

      this.activeStep = 1;
    },

    onNextStep(values) {
      // 验证第二步表单必填项
      if (
        !this.formData.hasHouse ||
        !this.formData.hasCar ||
        !this.formData.occupation ||
        !this.formData.workplace ||
        !this.formData.incomeLevel ||
        this.formData.showIncomeLevel === undefined ||
        !this.formData.education ||
        !this.formData.graduationSchool ||
        !this.formData.location ||
        !this.formData.hometown ||
        !this.formData.race ||
        !this.formData.bloodType ||
        !this.formData.weight ||
        !this.formData.height ||
        !this.formData.hobbies ||
        !this.formData.talents
      ) {
        Toast('请填写所有必填项');
        return;
      }

      this.activeStep = 2;
    },

    onHopeNextStep(values) {
      // 验证第三步(择偶要求)必填项
      if (
        !this.formData.hopeAgeRange ||
        !this.formData.hopeIncomeLevel ||
        !this.formData.hopeHeight ||
        !this.formData.hopeMaritalStatus ||
        !this.formData.hopeEducation
      ) {
        Toast('请填写所有必填项');
        return;
      }

      this.activeStep = 3;
    },

    onStepFailed(values) {
      if (values.errors && values.errors.length > 0) {
        Toast(values.errors[0].message);
      }
    },
    // 返回上一步
    goBack() {
      if (this.activeStep > 0) {
        this.activeStep -= 1;
      }
    },
    handleIdCardFrontUploaded(res) {
      return this.onUploaded(res, 'idCardFront')
    },
    handleIdCardBackUploaded(res) {
      return this.onUploaded(res, 'idCardBack')
    },
    onUploaded(res, field) {
      if (!res || res.data === undefined || res.data === null) return;

      const fieldMap = {
        lifePhoto: 'lifePhoto',
        idCardFront: 'idCardFront',
        idCardBack: 'idCardBack',
      };

      if (fieldMap[field]) {
        this[fieldMap[field]] = [res.data];
      }
      return res.data;
    },
    handleIdCardPreview(img, index, images) {
      this.playPhotos(images, index);
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map((item, i) => {
        return {
          title: `图片${i}`,
          url: this.imageUrlGetter(item),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },
    imageUrlGetter(resourceId) {
      // 正式环境——https://radio.jgrm.net/resource/image/{{resourceId}}
      // 测试环境——http://dev.jgrm.net/resource/image/{{resourceId}}
      const baseUrl =
        process.env.NODE_ENV === 'production'
          ? 'https://radio.jgrm.net'
          : 'http://dev.jgrm.net';
      return `${baseUrl}/resource/image/${resourceId}`;
    },
    // 执行异步队列上传图片
    uploadImages() {
      // 此处要同时执行三个上传
      return Promise.all([
        this.$refs.lifeUploader.submit(),
        this.$refs.idCardFrontUploader.submit(),
        this.$refs.idCardBackUploader.submit(),
      ]);
    },
    // 最终提交表单
    submitForm() {
      if (this.isLoading || this.isBacking) return;
      this.isLoading = true;

      this.uploadImages().then(() => {
        // 验证表单数据
        if (!this.validateForm()) {
          this.isLoading = false;
          return;
        }

        // 准备提交数据
        const apiData = { ...this.formData };
        // 处理图片数据
        apiData.profileImages =
          this.lifePhoto.length > 0 ? this.lifePhoto.join(',') : '';
        apiData.idCardImage = this.processIdCardImages();

        delete apiData.idCardFront;
        delete apiData.idCardBack;

        // 提交表单数据到后端
        // 获取URL参数cop_channel，判断是否需要支付
        const urlObj = getQueryParams(location.href);
        const copChannel = urlObj.cop_channel;
        const notNeedToPay = copChannel !== undefined;
        let applyFn = applyActivityWithNeedPay;
        if (notNeedToPay) {
          applyFn = applyActivity;
        }
        applyFn(apiData)
          .then(res => {
            Toast('提交成功！');
            // 提交成功后的逻辑，例如跳转到其他页面
            this.isBacking = true;
            setTimeout(() => {
              this.isBacking = false;
              this.$router.back();
            }, 1000);
          })
          .catch(err => {
            if (err.msg.includes('费用')) {
              Dialog.confirm({
                title: '温馨提示',
                message: err.msg,
                confirmButtonColor: '#f15b5b',
              }).then(() => {
                this.$router.push('/blindDate/payment');
              });
            } else {
              Toast.fail(err.msg);
            }
            console.log(err);
          })
          .finally(() => {
            this.isLoading = false;
          });
      })
        .catch(() => {
          this.isLoading = false;
        });
    },
    // 验证表单数据
    validateForm() {
      // 检查必填字段
      const requiredFields = [
        'nickName',
        'name',
        'age',
        'gender',
        'maritalStatus',
        'hasChildren',
        'phone',
        'wechatId',
        'idCardNumber',
        'introduction',
        // 'standardOfSpouseSelection',
        'hasHouse',
        'hasCar',
        'occupation',
        'workplace',
        'incomeLevel',
        'showIncomeLevel',
        'education',
        'graduationSchool',
        'location',
        'hometown',
        'race',
        'bloodType',
        'weight',
        'height',
        'hobbies',
        'talents',
        // 择偶要求必填字段
        'hopeAgeRange',
        'hopeIncomeLevel',
        'hopeHeight',
        'hopeMaritalStatus',
        'hopeEducation',
      ];

      for (const field of requiredFields) {
        if (!this.formData[field] && field !== 'showIncomeLevel') {
          Toast(`请填写${this.getFieldLabel(field)}`);
          return false;
        }
      }

      // 检查照片上传
      if (this.lifePhoto.length === 0) {
        Toast('请上传生活照');
        return false;
      }

      // 验证手机号格式
      const phoneRegex = /^1\d{10}$/;
      if (!phoneRegex.test(this.formData.phone)) {
        Toast('手机号格式错误');
        return false;
      }

      // 验证身份证号格式
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardRegex.test(this.formData.idCardNumber)) {
        Toast('身份证号格式错误');
        return false;
      }

      // 验证年龄和体重是否为有效数字
      if (isNaN(this.formData.age) || parseInt(this.formData.age) <= 0) {
        Toast('请输入有效的年龄');
        return false;
      }

      if (this.formData.showIncomeLevel === undefined) {
        Toast('请选择是否展示收入情况');
        return false;
      }

      if (
        isNaN(this.formData.weight) ||
        parseFloat(this.formData.weight) <= 0 ||
        parseFloat(this.formData.weight) > 300
      ) {
        Toast('请输入有效的体重');
        return false;
      }

      if (
        isNaN(this.formData.height) ||
        parseFloat(this.formData.height) <= 0 ||
        parseFloat(this.formData.height) > 300
      ) {
        Toast('请输入有效的身高');
        return false;
      }

      return true;
    },
    validateHeight(value) {
      if (isNaN(value) || value <= 0 || value > 300) {
        return false;
      } else {
        return true;
      }
    },
    validateWeight(value) {
      if (isNaN(value) || value <= 0 || value > 300) {
        return false;
      } else {
        return true;
      }
    },
    validateAge(value) {
      if (value < 18) {
        return false;
      } else {
        return true;
      }
    },
    // 处理身份证图片，将正反面合并
    processIdCardImages() {
      const frontImage = this.idCardFront.length > 0 ? this.idCardFront[0] : '';
      const backImage = this.idCardBack.length > 0 ? this.idCardBack[0] : '';

      // 如果两张图片都存在，返回逗号分隔的字符串，否则返回存在的那一张
      if (frontImage && backImage) {
        return `${frontImage},${backImage}`;
      }
      return frontImage || backImage || '';
    },

    // 使用手机号作为微信号
    usePhoneAsWechat() {
      if (this.formData.phone) {
        this.formData.wechatId = this.formData.phone;
      } else {
        Toast('请先填写手机号');
      }
    },

    // 显示选择器
    showPicker(field) {
      this.currentField = field.replace('Show', '');

      // 设置当前选择器的列和默认值
      switch (this.currentField) {
        case 'incomeLevel':
          this.currentColumns = this.incomeLevelColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'education':
          this.currentColumns = this.educationColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'location':
          this.currentColumns = this.locationColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'household':
          this.currentColumns = this.householdColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'hometown':
          this.currentColumns = this.hometownColumns;
          this.currentDefaultIndex = this.defaultHometownIndex;
          break;
        case 'race':
          this.currentColumns = this.minzuColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'bloodType':
          this.currentColumns = this.bloodTypeColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'hasChildren':
          this.currentColumns = this.hasChildrenColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'hasHouse':
          this.currentColumns = this.hasHouseColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'mbti':
          this.currentColumns = this.mbtiColumns;
          this.currentDefaultIndex = this.defaultMbtiIndex;
          break;
        case 'hopeAgeRange':
          this.currentColumns = this.hopeAgeRangeColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'hopeIncomeLevel':
          this.currentColumns = this.hopeIncomeLevelColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'hopeHeight':
          this.currentColumns = this.hopeHeightRangeColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'hopeMaritalStatus':
          this.currentColumns = this.hopeMaritalStatusColumns;
          this.currentDefaultIndex = 0;
          break;
        case 'hopeEducation':
          this.currentColumns = this.hopeEducationColumns;
          this.currentDefaultIndex = 0;
          break;
      }

      this.pickerShow = true;
    },

    // 选择器确认选择
    onPickerConfirm(value, index) {
      this.formData[this.currentField] = value;
      this.pickerShow = false;
    },

    // 获取字段标签
    getFieldLabel(key) {
      const labelMap = {
        nickName: '昵称',
        name: '姓名',
        age: '年龄',
        gender: '性别',
        maritalStatus: '婚姻状况',
        hasChildren: '有无子女',
        phone: '手机号',
        wechatId: '微信号',
        idCardNumber: '身份证号',
        occupation: '职业',
        workplace: '工作单位',
        incomeLevel: '收入水平',
        showIncomeLevel: '收入情况',
        education: '学历',
        graduationSchool: '毕业院校',
        location: '所在区县',
        hometown: '家乡',
        race: '民族',
        bloodType: '血型',
        weight: '体重',
        height: '身高',
        hobbies: '兴趣爱好',
        talents: '才艺',
        introduction: '个人简介',
        // standardOfSpouseSelection: '择偶标准',
        hasHouse: '购房情况',
        hasCar: '购车情况',
        mbti: 'MBTI',
        // 择偶要求字段标签
        hopeAgeRange: 'Ta的年龄',
        hopeIncomeLevel: 'Ta的月收入',
        hopeHeight: 'Ta的身高',
        hopeMaritalStatus: 'Ta的婚姻状况',
        hopeEducation: 'Ta的学历',
        hopeOtherRequirements: '其他要求',
      };

      return labelMap[key] || key;
    },
    initAreaData(data) {
      this.householdColumns = [];
      this.hometownColumns = [];
      this.locationColumns = [];
      data.forEach((province, index) => {
        this.householdColumns.push(province.name);
        this.hometownColumns.push(province.name);
        if (province.code == '41') {
          this.defaultHometownIndex = index;
        }
        province.children.forEach(city => {
          const cityCode = city.code; // 市级区域码

          if (city.children && cityCode == '4101') {
            // 郑州市
            city.children.forEach(county => {
              this.locationColumns.push(county.name); // 区县区域码
            });
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.registration-container {
  padding: 20px 15px;
  background: #f9fafb;
  min-height: 100%;
  box-sizing: border-box;
  padding-bottom: 66px;
}

.van-steps ::v-deep {
  background: transparent;

  .van-step__circle-container {
    background: transparent;
  }
}

.block-card {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 0px auto 10px;
}

.photo-upload-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.photo-label {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 10px;
}

.upload-box {
  width: 100%;
  height: 60px;
  border: 1px dashed #dcdee0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f7f8fa;
  color: #6b7280;
  font-size: 12px;
  position: relative;
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer;
}

.uploader-photo ::v-deep {
  position: relative;
  width: 48%;

  .van-uploader__input-wrapper,
  .van-uploader__preview,
  .van-uploader__preview-image {
    width: 100%;
    margin: 0;
  }

  .file {
    img {
      object-fit: cover;
    }
  }
}

.life-photo ::v-deep {
  width: 100%;

  .file {
    width: 95px;
    height: 95px;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .file-select {
    width: 95px;
    height: 95px;
    border: none;
    background: transparent;
    margin: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
}

.id-card-uploads {
  display: flex;
  justify-content: space-between;

  .uploader-photo {
    width: 48%;
  }
}

.id-card-photo ::v-deep {
  width: 48%;
  height: 90px;

  .file-select {
    border: none;
    background: transparent;
    margin: 0;
  }

  .file,
  .file-select {
    width: 100%;
    height: 100%;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
}

.field-label {
  margin: 10px 0px;
  font-size: 14px;
  color: #000000;

  &.required::before {
    content: '*';
    color: #ee0a24;
    margin-right: 4px;
  }
}

.wechat-field {
  display: flex;
  align-items: center;
  position: relative;

  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0.42667rem;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }

  .van-cell {
    &::after {
      display: none;
    }
  }

  .same-as-phone {
    flex-shrink: 0;
    height: 30px;
    font-size: 12px;
    border-radius: 5px;
    background: #f3f4f6;
    border: none;
  }
}

.button-container {
  padding: 20px 0px;

  .back-btn {
    margin-top: 10px;
    color: #999;
    border-color: #dcdee0;
  }
}

.confirm-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin: 20px 0;
}

.confirm-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.confirm-item {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.confirm-label {
  width: 80px;
  color: #666;
}

.confirm-value {
  flex: 1;
  color: #333;
}

.info-tip {
  background-color: #fff7f8;
  color: #f87c98;
  font-size: 12px;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  line-height: 1;

  span {
    margin-left: 5px;
  }
}

.intro-field ::v-deep {
  .van-field__control {
    padding: 5px;
    border-radius: 5px;
    background-color: #f3f4f6;
  }
}

// 覆盖一些vant默认样式
::v-deep .van-step--finish {
  color: #ff6aa8 !important;
}

::v-deep .van-button--primary {
  background-color: #ff6aa8;
  border-color: #ff6aa8;
}

::v-deep .van-field__label {
  width: 80px;
  color: #000000;
  margin-right: 0;

  &::before {
    content: '*';
    color: #ee0a24;
    position: absolute;
    left: 6px;
    top: 50%;
    transform: translateY(-50%);
  }
}

::v-deep .van-field__label:not(.van-field__label--required)::before {
  display: none;
}

::v-deep .van-cell {
  padding: 10px 0 10px 10px;

  &::before {
    left: 0;
  }

  &::after {
    left: 10px;
    right: 0px;
  }
}
</style>
