import Contacts from '@/views/Child_Contacts';
import { handleError } from '../error-handler';

export default [
  {
    path: '/orders/:status?/:type?',
    name: '订单',
    component: resolve => {
      import(/* webpackChunkName: "orders" */ '@/views/order/Orders.vue').then(resolve).catch(handleError);
    },
    children: [
      {
        path: 'contact',
        name: 'order/contact',
        component: resolve => {
          import(/* webpackChunkName: "contacts" */ '@/views/Child_Contacts.vue').then(resolve).catch(handleError);
        }
      }
    ]
  },
  {
    path: '/order/:id/comment',
    name: '订单评价',
    component: resolve => {
      import(/* webpackChunkName: "post-comment" */ '@/views/order/PostComment').then(resolve).catch(handleError);
    },
  },
  {
    path: '/order/:id/refund/:type?',
    name: '订单退款',
    component: resolve => {
      import(/* webpackChunkName: "order-refund" */ '@/views/order/OrderRefund').then(resolve).catch(handleError);
    },
  },
  {
    path: '/order/:id',
    name: '订单详情',
    component: resolve => {
      import(/* webpackChunkName: "order-detail" */ '@/views/order/OrderDetail').then(resolve).catch(handleError);
    },
  },
]
