import { md5 } from "./md5-v2";
import { formatDate } from '@/utils'

function serialize(obj) {
  return Object.keys(obj).reduce((previous, key) => {
    const value = obj[key];
    previous.push(`${key}=${value}`)
    return previous;
  }, []).join('&')
}

const MERCHANTID = "***************"; // 商户代码
const POSID = "*********"; // 商户柜台代码
const BRANCHID = "*********"; // 分行代码 

// const ORDERID = "ORDERID"; // 订单号
// const PAYMENT = "PAYMENT"; // 付款金额

const CURCODE = "01"; // 币种
const TXCODE = "520100"; // 交易码
const PUB = '385335ce499b540635175b9f020111';
// const REMARK1 = "REMARK1"; // 备注1
// const REMARK2 = "REMARK2"; // 备注2

// const TIMEOUT = Date.now() + 1000 * 60 * 60;

const BANK_URL = 'https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain';

const SAFE_FLAG = true; // 防钓鱼开关，目前必须为true，否则无法付款
function createPaymentParams(order, ip) {
  // 防钓鱼参数，字段顺序固定
  const safeParams = {
    GATEWAY: '', // UnionPay
    // GATEWAY: '',
    CLIENTIP: ip, // 客户在商户系统中的IP
    // CLIENTIP: '***********', // 客户在商户系统中的IP
    // REGINFO: '***********', // 客户在商户系统中注册的信息，中文需使用escape编码
    REGINFO: '', // 客户在商户系统中注册的信息，中文需使用escape编码
    PROINFO: escape(order.title), // 商品信息
    REFERER: '',
    THIRDAPPINFO: `comccbpay${MERCHANTID}alipay`,
    // TIMEOUT: formatDate(Date.now() + 30 * 60 * 1000, 'YYYYMMDDHHmmS'),
  }

  // 基本参数，字段顺序固定
  const baseParams = {
    MERCHANTID,
    POSID,
    BRANCHID,
    ORDERID: order.id,
    PAYMENT: order.amount,
    CURCODE,
    TXCODE,
    // REMARK1: escape(order.title),
    // REMARK2: escape(order.remark),
    REMARK1: '',
    REMARK2: '',
    TYPE: SAFE_FLAG ? 1 : 0,
    PUB,
  }
  // MAC校验参数
  let macParams = SAFE_FLAG ? { ...baseParams, ...safeParams } : {...baseParams};
  let str = serialize(macParams);
  const mac = md5(str);
  // debugger
  // 跳转支付传递参数
  const queryParams = {...macParams};
  delete queryParams.PUB; // PUB仅作为源串参加MD5摘要，不作为参数传递
  // Object.assign(queryParams, {
  //   CCB_IBSVersion: 'V6',
  //   QRCODE: '1',
  //   CHANNEL: '1',
  // })
  return serialize({
    ...queryParams,
    MAC: mac,
  })
  // tmp ='MERCHANTID='+MERCHANTID+'&POSID='+POSID+'&BRANCHID='+BRANCHID+'&ORDERID='+ORDERID+'&PAYMENT='+PAYMENT+'&CURCODE='+CURCODE+'&TXCODE='+TXCODE+'&REMARK1='+REMARK1+'&REMARK2='+REMARK2;
}

/**
 * 创建建行龙支付H5付款url
 * @param { object } order 订单信息
 * @param { string } order.id 订单id
 * @param { number } order.amount 订单金额
 * @param { string } order.title 订单标题
 * @param { string } order.remark 订单备注
 */
export function createCCBPaymentURL(order, ip) {
  const params = createPaymentParams(order, ip)
  return `${BANK_URL}?${params}`;
}

// console.log(createCCBPaymentURL({
//   id: 123,
//   amount: 5.45,
//   title: '测试',
//   remark: '备注',
// }))

console.log('md5: ', md5('中文'))